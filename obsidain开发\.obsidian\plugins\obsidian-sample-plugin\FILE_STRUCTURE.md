# 文件结构详解

## 📁 核心文件说明

### 🎯 主要源码文件

#### `main.ts` - 主插件文件 (1960行)
**作用**：插件的核心入口文件，包含所有主要功能
**主要内容**：
- `MindMapPlugin` 类：插件主类
- `MindMapView` 类：思维导图视图类
- 接口定义：`MindMapNode`、`MindMapViewState`
- 核心方法：
  - `onload()` - 插件初始化
  - `toggleMindMapMarkdown()` - 视图切换
  - `parseMarkdownToMindMap()` - Markdown解析
  - `renderMindMap()` - 思维导图渲染

**关键功能模块**：
```typescript
// 插件注册和命令绑定
this.registerView(MIND_MAP_VIEW_TYPE, (leaf) => new MindMapView(leaf, this));
this.addCommand({
    id: 'toggle-mindmap-markdown',
    hotkeys: [{ modifiers: ['Ctrl'], key: 'M' }],
    callback: () => this.toggleMindMapMarkdown()
});

// Markdown解析核心逻辑
parseMarkdownToMindMap(content: string, title: string): MindMapNode | null

// 思维导图渲染核心
async renderMindMap(): Promise<void>
```

#### `styles.css` - 样式文件 (152行)
**作用**：定义思维导图的视觉样式和布局
**主要样式模块**：
- `.mindmap-container` - 主容器样式
- `.mindmap-node-input` - 节点编辑输入框
- `.markmap-node` - 思维导图节点样式
- `.selected-node` - 选中节点高亮
- 响应式布局和动画效果

**关键样式特性**：
```css
/* 响应式容器 */
.mindmap-container {
    width: 100%;
    height: calc(100vh - 100px);
    padding: 20px;
    overflow: auto;
}

/* 节点编辑框 */
.mindmap-node-input {
    position: fixed;
    z-index: 9999;
    background: var(--background-primary);
    border: 2px solid var(--interactive-accent);
}
```

### ⚙️ 配置文件

#### `manifest.json` - 插件清单
**作用**：定义插件的基本信息和权限
```json
{
    "id": "obsidian-mindmap-plugin",
    "name": "Mind Map",
    "version": "1.0.0",
    "minAppVersion": "0.15.0",
    "description": "Create interactive mind maps from markdown files",
    "author": "Your Name"
}
```

#### `package.json` - 项目配置
**作用**：定义项目依赖和构建脚本
**关键依赖**：
- `obsidian` - Obsidian API
- `markmap-lib` - 思维导图核心库
- `markmap-view` - 思维导图视图
- `d3` - 数据可视化库
- `esbuild` - 构建工具

#### `tsconfig.json` - TypeScript配置
**作用**：配置TypeScript编译选项
**关键配置**：
```json
{
    "compilerOptions": {
        "target": "ES6",
        "module": "ESNext",
        "moduleResolution": "node",
        "strictNullChecks": true,
        "types": ["node", "d3"]
    }
}
```

#### `esbuild.config.mjs` - 构建配置
**作用**：配置esbuild构建流程
**关键配置**：
- 入口文件：`main.ts`
- 输出文件：`main.js`
- 外部依赖：`obsidian`、`electron`等
- 开发/生产模式切换

### 🏗️ 构建和开发文件

#### `main.js` - 构建输出文件
**作用**：编译后的插件代码，Obsidian实际加载的文件

#### `main.ts.backup` - 备份文件
**作用**：原始完整功能的备份，用于恢复和参考

#### `version-bump.mjs` - 版本管理
**作用**：自动化版本号更新脚本

#### `versions.json` - 版本历史
**作用**：记录插件版本兼容性信息

### 📚 模块化源码目录 `src/`

#### `src/types/` - 类型定义
- `mindmap-node.ts` - 思维导图节点类型
- `view-state.ts` - 视图状态类型
- `index.ts` - 类型导出文件

#### `src/utils/` - 工具函数
- `constants.ts` - 常量定义
- `node-operations.ts` - 节点操作工具
- `file-sync.ts` - 文件同步工具

#### `src/core/` - 核心功能
- `mindmap-plugin.ts` - 主插件类
- `mindmap-parser.ts` - Markdown解析器
- `mindmap-renderer.ts` - 思维导图渲染器

#### `src/views/` - 视图组件
- `mindmap-view.ts` - 思维导图视图类

#### `src/main.ts` - 模块化入口
**作用**：模块化版本的入口文件（当前为简化版本）

### 📖 文档文件

#### `README.md` - 项目说明
**作用**：项目的基本介绍和使用说明

#### `README_MINDMAP.md` - 详细文档
**作用**：详细的功能说明和开发指南

#### `debug.md` - 调试文档
**作用**：调试信息和问题排查指南

#### `LICENSE` - 开源协议
**作用**：定义项目的开源许可证

## 🔄 文件依赖关系

```
main.ts (主文件)
├── obsidian API (外部依赖)
├── markmap-lib (思维导图库)
├── markmap-view (视图库)
├── d3 (可视化库)
└── styles.css (样式文件)

esbuild.config.mjs
├── main.ts (入口)
└── main.js (输出)

package.json
├── 依赖管理
└── 构建脚本

manifest.json
└── Obsidian 插件注册
```

## 📊 代码统计

- **总代码行数**：约 2000+ 行
- **主要文件**：`main.ts` (1960行)
- **样式文件**：`styles.css` (152行)
- **配置文件**：5个
- **文档文件**：4个
- **模块化文件**：8个（开发中）

## 🎯 开发重点

1. **main.ts** - 核心功能实现，是理解插件的关键
2. **styles.css** - 视觉效果和用户体验
3. **src/** - 未来模块化重构的目标结构
4. **配置文件** - 构建和部署的基础
