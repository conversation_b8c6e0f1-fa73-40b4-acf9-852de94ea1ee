/*

This CSS file will be included with your plugin, and
available in the app when your plugin is enabled.

If your plugin does not need CSS, delete this file.

*/

/* 思维导图容器样式 */
.mindmap-container {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
}

/* 节点基础样式 */
.markmap-node {
    cursor: pointer;
    transition: all 0.3s ease;
}

.markmap-node text {
    font-family: var(--font-text);
    font-size: 14px;
}

/* 节点悬停效果 */
.markmap-node:hover text {
    fill: var(--text-accent) !important;
    font-weight: 500;
}

/* 选中节点样式 */
.markmap-node.selected text {
    fill: var(--text-accent) !important;
    font-weight: 600;
}

.selected-node text {
    fill: var(--text-accent) !important;
    font-weight: 600;
}

.selected-node circle {
    stroke: var(--text-accent) !important;
    stroke-width: 2px !important;
}

.selected-node rect {
    stroke: var(--text-accent) !important;
    stroke-width: 2px !important;
}

/* 节点编辑输入框样式 */
.mindmap-node-input {
    position: absolute;
    background: var(--background-primary);
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 14px;
    color: var(--text-normal);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.mindmap-node-input:focus {
    outline: none;
    border-color: var(--interactive-accent);
    box-shadow: 0 0 0 2px var(--background-modifier-border-hover);
}

/* 连接线样式 */
.markmap-link {
    stroke: var(--background-modifier-border);
    stroke-width: 1.5;
    fill: none;
}

/* 展开/折叠按钮样式 */
.markmap-fold {
    stroke: var(--background-modifier-border);
    stroke-width: 1.5;
    fill: var(--background-primary);
    cursor: pointer;
}
