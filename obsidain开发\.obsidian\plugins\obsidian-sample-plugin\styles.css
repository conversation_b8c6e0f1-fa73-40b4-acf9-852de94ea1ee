/*

This CSS file will be included with your plugin, and
available in the app when your plugin is enabled.

If your plugin does not need CSS, delete this file.

*/

/* 思维导图容器样式 */
.mindmap-container {
    width: 100%;
    height: 100%;
    min-height: 500px;
    overflow: auto; /* 改为auto以支持滚动 */
    position: relative;
    display: block;
    padding: 20px; /* 添加内边距 */
    box-sizing: border-box;
    background: var(--background-primary);
}

.mindmap-container svg {
    display: block;
    width: 100%;
    height: 100%;
    min-width: 600px; /* 确保最小宽度 */
    min-height: 400px; /* 确保最小高度 */
    background: transparent;
}

/* 节点基础样式 */
.markmap-node {
    cursor: pointer;
    transition: all 0.3s ease;
}

.markmap-node text {
    font-family: var(--font-text);
    font-size: 14px;
}

/* 节点悬停效果 */
.markmap-node:hover text {
    fill: var(--text-accent) !important;
    font-weight: 500;
}

/* 选中节点样式 */
.markmap-node.selected text {
    fill: var(--text-accent) !important;
    font-weight: 600;
}

.selected-node text {
    fill: var(--text-accent) !important;
    font-weight: 600;
}

.selected-node circle {
    stroke: var(--text-accent) !important;
    stroke-width: 2px !important;
}

.selected-node rect {
    stroke: var(--text-accent) !important;
    stroke-width: 2px !important;
}

/* 节点编辑输入框样式 */
.mindmap-node-input {
    position: fixed; /* 改为fixed确保在最顶层 */
    background: var(--background-primary);
    border: 2px solid var(--interactive-accent);
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 14px;
    color: var(--text-normal);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    z-index: 9999; /* 提高z-index */
    min-width: 100px;
    max-width: 300px;
}

.mindmap-node-input:focus {
    outline: none;
    border-color: var(--interactive-accent);
    box-shadow: 0 0 0 2px var(--background-modifier-border-hover);
}

/* 连接线样式 */
.markmap-link {
    stroke: var(--background-modifier-border);
    stroke-width: 1.5;
    fill: none;
}

/* 展开/折叠按钮样式 */
.markmap-fold {
    stroke: var(--background-modifier-border);
    stroke-width: 1.5;
    fill: var(--background-primary);
    cursor: pointer;
}

/* 思维导图视图整体布局 */
.view-content .mindmap-container {
    height: calc(100vh - 100px); /* 确保有足够高度 */
    max-height: none;
}

/* 防止内容被遮挡的额外样式 */
.mindmap-container .markmap {
    width: 100%;
    height: 100%;
}

/* 节点文本样式优化 */
.markmap-node text {
    font-family: var(--font-text);
    font-size: 14px;
    text-anchor: start; /* 确保文本对齐 */
    dominant-baseline: middle;
}

/* 根节点特殊样式 */
.markmap-node[data-depth="0"] text {
    font-size: 18px;
    font-weight: 600;
    fill: var(--text-accent);
}

/* 二级节点样式 */
.markmap-node[data-depth="1"] text {
    font-size: 16px;
    font-weight: 500;
}

/* 确保SVG内容不会被裁剪 */
.mindmap-container svg {
    overflow: visible;
}

/* 加载状态样式 */
.mindmap-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--text-muted);
    font-size: 14px;
}
