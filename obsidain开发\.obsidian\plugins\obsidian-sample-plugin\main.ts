import { <PERSON><PERSON>, Editor, ItemView, Plugin, PluginSettingTab, Setting, WorkspaceLeaf, ViewStateResult, Notice } from 'obsidian';
import { Transformer } from 'markmap-lib';
import { Markmap } from 'markmap-view';
import * as d3 from 'd3';

// 定义思维导图节点接口
interface MindMapNode {
    id: string;
    content: string;
    children: MindMapNode[];
    parent?: MindMapNode;
    isSelected?: boolean;
    isExpanded?: boolean;
    isHeading?: boolean; // 标识是否为原始标题节点
    originalLevel?: number; // 原始标题级别
}

// 定义视图状态接口
interface MindMapViewState {
    [key: string]: unknown;
    data?: MindMapNode;
    selectedNodeId?: string;
    sourceFile?: string; // 添加源文件路径
}

const MIND_MAP_VIEW_TYPE = "mindmap";

export default class MindMapPlugin extends Plugin {
    private transformer: Transformer;
    private mindmap: Markmap | null = null;
    private rootNode: MindMapNode | null = null;
    private currentNode: MindMapNode | null = null;
    private editingNode: MindMapNode | null = null;
    private selectedNode: MindMapNode | null = null;


    async onload() {
        // 初始化转换器
        this.transformer = new Transformer();

        // 注册视图
        this.registerView(
            MIND_MAP_VIEW_TYPE,
            (leaf: WorkspaceLeaf) => new MindMapView(leaf, this)
        );

        // 添加命令
        this.addCommand({
            id: 'create-mindmap',
            name: '创建新的思维导图',
            callback: () => this.createNewMindMap()
        });

        // 添加更多命令
        this.addCommand({
            id: 'select-parent-node',
            name: '选择父节点',
            callback: () => this.selectParentNode()
        });

        this.addCommand({
            id: 'select-first-child',
            name: '选择第一个子节点',
            callback: () => this.selectFirstChild()
        });

        this.addCommand({
            id: 'select-next-sibling',
            name: '选择下一个同级节点',
            callback: () => this.selectNextSibling()
        });

        this.addCommand({
            id: 'select-previous-sibling',
            name: '选择上一个同级节点',
            callback: () => this.selectPreviousSibling()
        });

        // 添加调试命令
        this.addCommand({
            id: 'debug-mindmap',
            name: '调试思维导图状态',
            callback: () => this.debugMindMapState()
        });

        this.addCommand({
            id: 'force-select-root',
            name: '强制选择根节点',
            callback: () => {
                if (this.rootNode) {
                    console.log('Force selecting root node:', this.rootNode);
                    this.selectNode(this.rootNode);
                }
            }
        });

        this.addCommand({
            id: 'create-simple-test',
            name: '创建简单测试视图',
            callback: () => this.createSimpleTestView()
        });

        this.addCommand({
            id: 'test-edit-selected',
            name: '测试编辑选中节点',
            callback: () => {
                if (this.selectedNode) {
                    console.log('Testing edit for selected node:', this.selectedNode.content);
                    const nodeEl = document.querySelector(`[data-node-id="${this.selectedNode.id}"]`) as HTMLElement;
                    if (nodeEl) {
                        this.startSimpleEditing(this.selectedNode, nodeEl);
                    } else {
                        console.log('Node element not found');
                    }
                } else {
                    console.log('No node selected');
                }
            }
        });

        this.addCommand({
            id: 'force-simple-view',
            name: '强制使用简单HTML视图',
            callback: () => {
                if (this.rootNode) {
                    this.createSimpleHTMLMindMap();
                } else {
                    this.createSimpleTestView();
                }
            }
        });

        this.addCommand({
            id: 'edit-root-node',
            name: '编辑根节点',
            callback: () => {
                if (this.rootNode) {
                    console.log('Forcing edit of root node');
                    this.startEditing(this.rootNode);
                } else {
                    console.log('No root node to edit');
                }
            }
        });

        this.addCommand({
            id: 'export-to-markdown',
            name: '导出为Markdown文档',
            callback: () => this.exportToMarkdown()
        });

        this.addCommand({
            id: 'import-from-markdown',
            name: '从Markdown文档导入',
            callback: () => this.importFromMarkdown()
        });

        this.addCommand({
            id: 'toggle-mindmap-markdown',
            name: '切换思维导图/Markdown视图',
            hotkeys: [{ modifiers: ['Ctrl'], key: 'M' }],
            callback: () => this.toggleMindMapMarkdown()
        });

        // 注册键盘事件
        this.registerDomEvent(document, 'keydown', (evt: KeyboardEvent) => {
            // 只在思维导图视图激活时处理键盘事件
            const activeView = this.app.workspace.getActiveViewOfType(MindMapView);
            if (!activeView || !this.selectedNode) return;

            switch(evt.key) {
                case 'Tab':
                    evt.preventDefault();
                    if (evt.shiftKey) {
                        this.createSiblingNode(this.selectedNode);
                    } else {
                        this.createChildNode(this.selectedNode);
                    }
                    break;
                case 'Enter':
                    evt.preventDefault();
                    if (this.editingNode) {
                        this.finishEditing().catch(console.error);
                    } else {
                        this.startEditing(this.selectedNode).catch(console.error);
                    }
                    break;
                case 'Delete':
                case 'Backspace':
                    if (!this.editingNode && evt.ctrlKey) {
                        evt.preventDefault();
                        this.deleteNode(this.selectedNode);
                    }
                    break;
                case 'ArrowUp':
                    evt.preventDefault();
                    this.selectPreviousSibling();
                    break;
                case 'ArrowDown':
                    evt.preventDefault();
                    this.selectNextSibling();
                    break;
                case 'ArrowLeft':
                    evt.preventDefault();
                    if (this.selectedNode && this.selectedNode.isExpanded) {
                        this.collapseNode(this.selectedNode);
                    } else {
                        this.selectParentNode();
                    }
                    break;
                case 'ArrowRight':
                    evt.preventDefault();
                    if (this.selectedNode && !this.selectedNode.isExpanded) {
                        this.expandNode(this.selectedNode);
                    } else {
                        this.selectFirstChild();
                    }
                    break;
                case 'Escape':
                    if (this.editingNode) {
                        evt.preventDefault();
                        this.cancelEditing();
                    }
                    break;
            }
        });
    }

    // 调试思维导图状态
    private debugMindMapState() {
        console.log('=== 思维导图调试信息 ===');
        console.log('Root node:', this.rootNode);
        console.log('Selected node:', this.selectedNode);
        console.log('Editing node:', this.editingNode);
        console.log('Mindmap instance:', this.mindmap);

        const view = this.app.workspace.getActiveViewOfType(MindMapView);
        console.log('Active view:', view);

        if (view) {
            const container = view.containerEl.querySelector('.mindmap-container');
            console.log('Container:', container);

            if (container) {
                const svg = container.querySelector('svg');
                console.log('SVG element:', svg);

                if (svg) {
                    const groups = svg.querySelectorAll('g');
                    console.log('Found groups:', groups.length);

                    const textElements = svg.querySelectorAll('text');
                    console.log('Found text elements:', textElements.length);

                    for (let i = 0; i < textElements.length; i++) {
                        const textEl = textElements[i];
                        console.log(`Text ${i}:`, textEl.textContent, textEl);
                    }
                }
            }
        }

        if (this.rootNode) {
            console.log('All nodes:', this.getAllNodes(this.rootNode));
        }
        console.log('=== 调试信息结束 ===');
    }

    // 创建简单测试视图
    async createSimpleTestView() {
        this.rootNode = {
            id: 'root',
            content: '中心主题',
            children: [
                {
                    id: 'child1',
                    content: '子节点1',
                    children: [],
                    parent: undefined,
                    isExpanded: true
                },
                {
                    id: 'child2',
                    content: '子节点2',
                    children: [],
                    parent: undefined,
                    isExpanded: true
                }
            ],
            isExpanded: true
        };

        // 设置父节点引用
        this.rootNode.children.forEach(child => {
            child.parent = this.rootNode!;
        });

        const leaf = this.app.workspace.getLeaf(true);
        await leaf.setViewState({
            type: MIND_MAP_VIEW_TYPE,
            state: { data: this.rootNode }
        });

        this.app.workspace.revealLeaf(leaf);

        // 等待视图加载后创建简单HTML版本
        setTimeout(() => {
            this.createSimpleHTMLMindMap();
        }, 200);
    }

    // 创建简单的HTML思维导图（不使用markmap）
    private createSimpleHTMLMindMap() {
        const view = this.app.workspace.getActiveViewOfType(MindMapView);
        if (!view || !this.rootNode) return;

        const container = view.containerEl.querySelector('.mindmap-container') as HTMLElement;
        if (!container) return;

        container.innerHTML = '';
        container.style.cssText = `
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            background: var(--background-primary);
        `;

        // 创建根节点
        const rootElement = this.createNodeElement(this.rootNode, 0);
        container.appendChild(rootElement);

        // 创建子节点
        const childrenContainer = container.createDiv('children-container');
        childrenContainer.style.cssText = `
            display: flex;
            gap: 20px;
            margin-top: 20px;
        `;

        this.rootNode.children.forEach(child => {
            const childElement = this.createNodeElement(child, 1);
            childrenContainer.appendChild(childElement);
        });

        // 选中根节点
        this.selectNode(this.rootNode);
    }

    // 创建节点元素
    private createNodeElement(node: MindMapNode, level: number): HTMLElement {
        const nodeEl = document.createElement('div');
        nodeEl.className = 'simple-mindmap-node';
        nodeEl.setAttribute('data-node-id', node.id);
        nodeEl.setAttribute('tabindex', '0'); // 使元素可以获得焦点

        const isSelected = this.selectedNode === node;

        nodeEl.style.cssText = `
            padding: 10px 15px;
            border: 2px solid ${isSelected ? 'var(--text-accent)' : 'var(--background-modifier-border)'};
            border-radius: 8px;
            background: var(--background-primary);
            cursor: pointer;
            user-select: none;
            font-weight: ${level === 0 ? '600' : '400'};
            font-size: ${level === 0 ? '16px' : '14px'};
            color: ${isSelected ? 'var(--text-accent)' : 'var(--text-normal)'};
            transition: all 0.2s ease;
            min-width: 100px;
            text-align: center;
            outline: none;
        `;

        nodeEl.textContent = node.content;

        // 添加事件监听器
        nodeEl.addEventListener('click', (e) => {
            e.stopPropagation();
            console.log('Simple node clicked:', node.content);
            this.selectNode(node);
            this.updateSimpleNodeStyles();
        });

        nodeEl.addEventListener('dblclick', (e) => {
            e.stopPropagation();
            e.preventDefault();
            console.log('Simple node double-clicked:', node.content);
            this.startSimpleEditing(node, nodeEl);
        });

        // 添加键盘事件支持
        nodeEl.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && this.selectedNode === node) {
                e.preventDefault();
                console.log('Enter pressed on selected node:', node.content);
                this.startSimpleEditing(node, nodeEl);
            }
        });

        return nodeEl;
    }

    // 更新简单节点样式
    private updateSimpleNodeStyles() {
        const nodes = document.querySelectorAll('.simple-mindmap-node');
        nodes.forEach(nodeEl => {
            const nodeId = nodeEl.getAttribute('data-node-id');
            const isSelected = this.selectedNode?.id === nodeId;

            (nodeEl as HTMLElement).style.borderColor = isSelected ? 'var(--text-accent)' : 'var(--background-modifier-border)';
            (nodeEl as HTMLElement).style.color = isSelected ? 'var(--text-accent)' : 'var(--text-normal)';
        });
    }

    // 简单编辑功能
    private startSimpleEditing(node: MindMapNode, nodeEl: HTMLElement) {
        console.log('Starting simple editing for node:', node.content);

        const input = document.createElement('input');
        input.type = 'text';
        input.value = node.content;
        input.className = 'simple-mindmap-input';

        // 复制原有样式并添加编辑样式
        input.style.cssText = `
            padding: 10px 15px;
            border: 2px solid var(--text-accent);
            border-radius: 8px;
            background: var(--background-primary);
            font-weight: ${node === this.rootNode ? '600' : '400'};
            font-size: ${node === this.rootNode ? '16px' : '14px'};
            color: var(--text-accent);
            min-width: 100px;
            text-align: center;
            outline: none;
            box-shadow: 0 0 0 2px var(--background-modifier-border-hover);
        `;

        console.log('Replacing node element with input');
        nodeEl.parentNode?.replaceChild(input, nodeEl);

        // 确保输入框获得焦点
        setTimeout(() => {
            input.focus();
            input.select();
        }, 10);

        const finishEdit = () => {
            console.log('Finishing edit, new content:', input.value);
            node.content = input.value;

            // 重新创建整个简单思维导图以确保一致性
            this.createSimpleHTMLMindMap();
            this.saveData();
        };

        const cancelEdit = () => {
            console.log('Cancelling edit');
            // 重新创建整个简单思维导图
            this.createSimpleHTMLMindMap();
        };

        input.addEventListener('blur', finishEdit);
        input.addEventListener('keydown', (e) => {
            console.log('Key pressed in input:', e.key);
            if (e.key === 'Enter') {
                e.preventDefault();
                finishEdit();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                cancelEdit();
            }
        });
    }

    // 处理节点选中
    private selectNode(node: MindMapNode | null) {
        if (this.selectedNode) {
            this.selectedNode.isSelected = false;
        }

        this.selectedNode = node;
        if (node) {
            node.isSelected = true;

            // 高亮选中的节点
            this.highlightSelectedNode();
        }
    }

    // 选择父节点
    private selectParentNode() {
        if (this.selectedNode?.parent) {
            this.selectNode(this.selectedNode.parent);
        }
    }

    // 选择第一个子节点
    private selectFirstChild() {
        if (this.selectedNode && this.selectedNode.children && this.selectedNode.children.length > 0) {
            this.selectNode(this.selectedNode.children[0]);
        }
    }

    // 选择下一个同级节点
    private selectNextSibling() {
        if (!this.selectedNode?.parent) return;
        
        const siblings = this.selectedNode.parent.children;
        const currentIndex = siblings.indexOf(this.selectedNode);
        if (currentIndex < siblings.length - 1) {
            this.selectNode(siblings[currentIndex + 1]);
        }
    }

    // 选择上一个同级节点
    private selectPreviousSibling() {
        if (!this.selectedNode?.parent) return;
        
        const siblings = this.selectedNode.parent.children;
        const currentIndex = siblings.indexOf(this.selectedNode);
        if (currentIndex > 0) {
            this.selectNode(siblings[currentIndex - 1]);
        }
    }

    // 展开节点
    private async expandNode(node: MindMapNode) {
        node.isExpanded = true;
        await this.renderMindMap();
    }

    // 折叠节点
    private async collapseNode(node: MindMapNode) {
        node.isExpanded = false;
        await this.renderMindMap();
    }

    // 删除节点
    private deleteNode(node: MindMapNode) {
        if (!node.parent || node === this.rootNode) return;
        
        const siblings = node.parent.children;
        const index = siblings.indexOf(node);
        siblings.splice(index, 1);
        
        // 选择相邻节点或父节点
        if (siblings.length > 0) {
            this.selectNode(siblings[Math.min(index, siblings.length - 1)]);
        } else {
            this.selectNode(node.parent);
        }
        
        this.saveData().catch(console.error);
    }



    // 从 DOM 元素中提取节点 ID
    private findNodeIdFromElement(element: Element): string | null {
        // markmap 会为每个节点生成唯一的 ID
        const transform = element.getAttribute('transform');
        if (!transform) return null;
        
        // 遍历所有节点找到匹配的
        const allNodes = this.getAllNodes(this.rootNode!);
        for (const node of allNodes) {
            if (element.textContent?.includes(node.content)) {
                return node.id;
            }
        }
        return null;
    }

    // 获取所有节点
    private getAllNodes(root: MindMapNode): MindMapNode[] {
        const nodes: MindMapNode[] = [root];
        for (const child of root.children) {
            nodes.push(...this.getAllNodes(child));
        }
        return nodes;
    }

    // 开始编辑节点
    private async startEditing(node: MindMapNode) {
        console.log('Starting editing for node:', node.content);

        if (this.editingNode === node) {
            console.log('Already editing this node');
            return;
        }

        await this.finishEditing();
        this.editingNode = node;

        // 检查是否在简单HTML模式
        const simpleNode = document.querySelector(`[data-node-id="${node.id}"]`) as HTMLElement;
        if (simpleNode) {
            console.log('Using simple HTML editing');
            this.startSimpleEditing(node, simpleNode);
            return;
        }

        // markmap模式的编辑
        console.log('Using markmap editing mode');
        this.startMarkmapEditing(node);
    }

    // markmap模式的编辑
    private startMarkmapEditing(node: MindMapNode) {
        // 查找包含该节点内容的文本元素
        const svg = document.querySelector('.mindmap-container svg');
        if (!svg) {
            console.log('No SVG found for editing');
            return;
        }

        // 查找所有可能的文本元素
        const textElements = svg.querySelectorAll('text, div[xmlns], div');
        let targetElement: Element | null = null;

        for (let i = 0; i < textElements.length; i++) {
            const el = textElements[i];
            if (el.textContent?.trim() === node.content) {
                targetElement = el;
                break;
            }
        }

        if (!targetElement) {
            console.log('No target element found for editing');
            return;
        }

        console.log('Found target element for editing:', targetElement);

        const input = document.createElement('input');
        input.value = node.content;
        input.className = 'mindmap-node-input';

        // 获取元素位置
        const rect = targetElement.getBoundingClientRect();

        Object.assign(input.style, {
            position: 'fixed',
            left: `${rect.left}px`,
            top: `${rect.top}px`,
            width: `${Math.max(rect.width + 40, 100)}px`,
            height: `${Math.max(rect.height + 8, 30)}px`,
            zIndex: '10000',
            fontSize: '14px',
            fontFamily: 'var(--font-text)',
            background: 'var(--background-primary)',
            border: '2px solid var(--text-accent)',
            borderRadius: '4px',
            padding: '4px 8px',
            color: 'var(--text-normal)',
            outline: 'none'
        });

        document.body.appendChild(input);

        // 确保输入框获得焦点
        setTimeout(() => {
            input.focus();
            input.select();
        }, 10);

        input.addEventListener('blur', () => {
            this.finishEditing().catch(console.error);
        });

        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.finishEditing().catch(console.error);
            } else if (e.key === 'Escape') {
                e.preventDefault();
                this.cancelEditing();
            }
        });
    }

    // 获取 SVG 元素的页面坐标
    private getSVGElementPosition(element: SVGTextElement) {
        const svg = element.ownerSVGElement!;
        const point = svg.createSVGPoint();
        const bbox = element.getBBox();
        point.x = bbox.x;
        point.y = bbox.y + bbox.height;
        
        // 转换为页面坐标
        const ctm = element.getScreenCTM();
        if (ctm) {
            const globalPoint = point.matrixTransform(ctm);
            return { x: globalPoint.x, y: globalPoint.y };
        }
        return { x: 0, y: 0 };
    }

    // 完成编辑
    private async finishEditing() {
        const input = document.querySelector('.mindmap-node-input');
        if (input && this.editingNode) {
            this.editingNode.content = (input as HTMLInputElement).value;

            // 安全地移除输入框
            try {
                if (input.parentNode) {
                    input.parentNode.removeChild(input);
                }
            } catch (error) {
                console.warn('Error removing input element:', error);
            }

            this.editingNode = null;
            this.renderMindMap().catch(console.error);

            // 异步保存数据
            try {
                await this.saveData();
                console.log('Data saved successfully after editing');
            } catch (error) {
                console.error('Error saving data after editing:', error);
            }
        }
    }

    // 取消编辑
    private cancelEditing() {
        const input = document.querySelector('.mindmap-node-input');
        if (input && this.editingNode) {
            // 安全地移除输入框
            try {
                if (input.parentNode) {
                    input.parentNode.removeChild(input);
                }
            } catch (error) {
                console.warn('Error removing input element:', error);
            }
            this.editingNode = null;
        }
    }

    // 创建新的思维导图
    async createNewMindMap() {
        this.rootNode = {
            id: 'root',
            content: '中心主题',
            children: [],
            isExpanded: true
        };

        // 添加一些测试子节点
        const child1: MindMapNode = {
            id: 'child1',
            content: '子节点1',
            children: [],
            parent: this.rootNode,
            isExpanded: true
        };

        const child2: MindMapNode = {
            id: 'child2',
            content: '子节点2',
            children: [],
            parent: this.rootNode,
            isExpanded: true
        };

        this.rootNode.children.push(child1, child2);

        const leaf = this.app.workspace.getLeaf(true);
        await leaf.setViewState({
            type: MIND_MAP_VIEW_TYPE,
            state: { data: this.rootNode }
        });

        this.app.workspace.revealLeaf(leaf);

        // 选中根节点
        this.selectNode(this.rootNode);
        this.saveData().catch(console.error); // 保存数据
    }

    // 保存数据并同步到源文件
    async saveData(): Promise<void> {
        console.log('saveData called, rootNode:', this.rootNode);
        if (!this.rootNode) return Promise.resolve();

        const view = this.app.workspace.getActiveViewOfType(MindMapView);
        console.log('Found MindMapView:', view);

        if (view) {
            // 获取当前视图状态中的源文件路径
            const currentState = view.leaf.getViewState().state as MindMapViewState;
            const sourceFilePath = currentState?.sourceFile || view.sourceFilePath;

            console.log('Current view state:', currentState);
            console.log('Source file path from state:', currentState?.sourceFile);
            console.log('Source file path from view:', view.sourceFilePath);
            console.log('Final source file path:', sourceFilePath);

            // 保存到视图状态
            const savePromise = view.leaf.setViewState({
                type: MIND_MAP_VIEW_TYPE,
                state: {
                    data: this.rootNode,
                    sourceFile: sourceFilePath // 保持源文件路径
                }
            });

            // 如果有源文件，同步保存到Markdown文件
            if (sourceFilePath) {
                console.log('Attempting to sync to source file:', sourceFilePath);
                try {
                    await this.syncToSourceFile(sourceFilePath);
                } catch (error) {
                    console.error('Error syncing to source file:', error);
                    new Notice('同步到源文件失败: ' + error.message);
                }
            } else {
                console.log('No source file path found, skipping sync');
            }

            return savePromise;
        } else {
            console.log('No MindMapView found');
        }
        return Promise.resolve();
    }

    // 同步到源文件
    private async syncToSourceFile(filePath: string) {
        if (!this.rootNode) return;

        try {
            console.log('Starting sync to source file:', filePath);
            console.log('Root node data:', this.rootNode);

            // 生成markdown内容
            const markdownContent = this.generateMarkdownFromMindMap(this.rootNode);
            console.log('Generated markdown content:', markdownContent);

            // 获取文件对象
            const file = this.app.vault.getAbstractFileByPath(filePath);
            if (file) {
                // 暂时禁用文件监听器以避免循环更新
                const view = this.app.workspace.getActiveViewOfType(MindMapView);
                let wasWatchingFile = false;
                if (view && view.fileWatcher && view.sourceFilePath === filePath) {
                    console.log('Temporarily disabling file watcher for sync');
                    this.app.vault.offref(view.fileWatcher);
                    view.fileWatcher = null;
                    wasWatchingFile = true;
                }

                // 更新文件内容
                await this.app.vault.modify(file as any, markdownContent);
                console.log(`Successfully synced mind map to source file: ${filePath}`);

                // 重新启用文件监听器
                if (view && wasWatchingFile) {
                    setTimeout(() => {
                        console.log('Re-enabling file watcher after sync');
                        view.setupFileWatcher(filePath);
                    }, 200); // 稍长的延迟以确保文件写入完成
                }

                // 显示成功提示（可选，避免过于频繁的提示）
                // new Notice('已同步到源文件');
            } else {
                console.error('Source file not found:', filePath);
            }
        } catch (error) {
            console.error('Error syncing to source file:', error);
            throw error;
        }
    }

    // 加载数据
    loadData(): Promise<any>;
    loadData(data: MindMapNode): Promise<void>;
    async loadData(data?: MindMapNode): Promise<any> {
        if (data) {
            this.rootNode = this.reconstructNode(data);
            this.currentNode = this.rootNode;
            await this.renderMindMap();
        }
    }

    // 获取根节点数据
    getRootNode(): MindMapNode | null {
        return this.rootNode;
    }

    // 更新思维导图数据（用于文件监听器）
    updateMindMapData(data: MindMapNode) {
        console.log('Updating mind map data');
        this.rootNode = this.reconstructNode(data);
        this.currentNode = this.rootNode;
        this.selectedNode = this.rootNode;
        console.log('Mind map data updated');
    }

    // 重建节点树（恢复父节点引用）
    private reconstructNode(node: MindMapNode, parent: MindMapNode | undefined = undefined): MindMapNode {
        const newNode: MindMapNode = {
            id: node.id,
            content: node.content,
            children: [],
            parent: parent
        };
        
        for (const child of node.children) {
            newNode.children.push(this.reconstructNode(child, newNode));
        }
        
        return newNode;
    }



    // 创建同级节点
    private createSiblingNode(node: MindMapNode) {
        if (!node.parent) return; // 根节点不能创建同级节点

        const newNode: MindMapNode = {
            id: Date.now().toString(),
            content: '新节点',
            children: [],
            parent: node.parent,
            isExpanded: true
        };

        const index = node.parent.children.indexOf(node);
        node.parent.children.splice(index + 1, 0, newNode);

        this.renderMindMap().catch(console.error);
        this.selectNode(newNode);
        this.saveData().catch(console.error); // 保存数据
    }

    // 创建父节点
    private createParentNode(node: MindMapNode) {
        const newNode: MindMapNode = {
            id: Date.now().toString(),
            content: '新父节点',
            children: [node]
        };

        if (node === this.rootNode) {
            this.rootNode = newNode;
        } else if (node.parent) {
            const parentChildren = node.parent.children;
            const index = parentChildren.indexOf(node);
            parentChildren[index] = newNode;
            newNode.parent = node.parent;
        }

        node.parent = newNode;
        this.currentNode = newNode;
        this.renderMindMap().catch(console.error);
        this.saveData().catch(console.error); // 保存数据
    }

    // 创建子节点
    private createChildNode(parentNode: MindMapNode) {
        const newNode: MindMapNode = {
            id: Date.now().toString(),
            content: '新节点',
            children: [],
            parent: parentNode,
            isExpanded: true
        };

        parentNode.children.push(newNode);
        parentNode.isExpanded = true; // 确保父节点展开

        this.renderMindMap().catch(console.error);
        this.selectNode(newNode);
        this.saveData().catch(console.error); // 保存数据
    }

    // 查找节点
    private findNode(root: MindMapNode, id: string): MindMapNode | null {
        if (root.id === id) return root;
        for (const child of root.children) {
            const found = this.findNode(child, id);
            if (found) return found;
        }
        return null;
    }

    // 渲染思维导图
    async renderMindMap() {
        const view = this.app.workspace.getActiveViewOfType(MindMapView);
        if (!view || !this.rootNode) {
            return;
        }

        const container = view.containerEl.querySelector('.mindmap-container') as HTMLElement;
        if (!container) return;

        // 清空容器
        container.innerHTML = '';

        // 等待容器完全渲染后再获取尺寸
        await new Promise(resolve => setTimeout(resolve, 50));

        // 确保容器有明确的尺寸
        const containerRect = container.getBoundingClientRect();
        let width = containerRect.width;
        let height = containerRect.height;

        // 减去内边距（CSS中设置了20px padding）
        const padding = 40; // 左右各20px
        width = Math.max(width - padding, 600); // 最小宽度600px
        height = Math.max(height - padding, 400); // 最小高度400px

        // 如果容器尺寸仍然无效，使用默认值
        if (!width || width <= 0 || isNaN(width)) {
            width = 800;
        }
        if (!height || height <= 0 || isNaN(height)) {
            height = 600;
        }

        console.log('Container dimensions (after padding):', width, height);

        // 创建SVG元素，使用响应式尺寸
        const svg = d3.select(container)
            .append('svg')
            .attr('width', '100%')
            .attr('height', '100%')
            .attr('viewBox', `0 0 ${width} ${height}`)
            .attr('preserveAspectRatio', 'xMidYMid meet')
            .style('width', '100%')
            .style('height', '100%')
            .style('min-width', width + 'px')
            .style('min-height', height + 'px');

        // 等待下一帧再创建markmap，确保SVG已经渲染
        requestAnimationFrame(() => {
            try {
                // 使用markmap渲染，优化配置参数
                this.mindmap = Markmap.create(svg.node() as SVGSVGElement, {
                    autoFit: true,
                    duration: 300,
                    maxWidth: Math.max(width - 100, 200), // 确保最小宽度
                    initialExpandLevel: 3, // 增加初始展开层级
                    spacingVertical: 10, // 垂直间距
                    spacingHorizontal: 80, // 水平间距
                    paddingX: 20, // X轴内边距
                });

                // 转换数据格式并更新
                const markdown = this.mindmapNodeToMarkdown(this.rootNode!);
                const { root: data } = this.transformer.transform(markdown);

                console.log('Markmap data:', data);

                // 为每个节点添加自定义ID
                this.addCustomNodeIds(data, this.rootNode!);

                this.mindmap.setData(data);

                // 设置事件监听器
                this.setupEventListeners(container);

                // 如果有选中的节点，高亮显示
                if (this.selectedNode) {
                    setTimeout(() => this.highlightSelectedNode(), 100);
                }
            } catch (error) {
                console.error('Error creating markmap:', error);
                // 如果markmap失败，创建一个简单的备用显示
                this.createFallbackDisplay(container);
            }
        });
    }

    // 创建备用显示
    private createFallbackDisplay(container: HTMLElement) {
        container.innerHTML = '';
        const fallbackDiv = container.createDiv('mindmap-fallback');
        fallbackDiv.style.cssText = `
            padding: 20px;
            text-align: center;
            color: var(--text-muted);
            font-size: 14px;
        `;

        fallbackDiv.innerHTML = `
            <h3>思维导图渲染失败</h3>
            <p>请尝试以下操作：</p>
            <ul style="text-align: left; display: inline-block;">
                <li>重新加载插件</li>
                <li>检查浏览器控制台的错误信息</li>
                <li>使用调试命令查看详细信息</li>
            </ul>
            <button onclick="this.closest('.mindmap-container').dispatchEvent(new CustomEvent('retry-render'))">
                重试渲染
            </button>
        `;

        // 添加重试事件监听器
        container.addEventListener('retry-render', () => {
            setTimeout(() => this.renderMindMap().catch(console.error), 100);
        });
    }

    // 为markmap数据添加自定义ID
    private addCustomNodeIds(markmapNode: any, mindmapNode: MindMapNode) {
        markmapNode.customId = mindmapNode.id;
        if (markmapNode.children && mindmapNode.children) {
            for (let i = 0; i < markmapNode.children.length; i++) {
                if (mindmapNode.children[i]) {
                    this.addCustomNodeIds(markmapNode.children[i], mindmapNode.children[i]);
                }
            }
        }
    }

    // 设置事件监听器
    private setupEventListeners(container: Element) {
        // 移除旧的事件监听器
        const svg = container.querySelector('svg');
        if (!svg) {
            console.log('No SVG found in container');
            return;
        }

        console.log('Setting up event listeners on SVG:', svg);

        // 使用更强的事件监听器，确保能捕获到所有事件
        const clickHandler = (evt: MouseEvent) => {
            console.log('Click event captured on:', evt.target);
            this.handleSVGClick(evt);
        };

        const dblClickHandler = (evt: MouseEvent) => {
            console.log('Double click event captured on:', evt.target);
            evt.preventDefault();
            evt.stopPropagation();
            this.handleSVGDoubleClick(evt);
        };

        // 在多个层级添加事件监听器
        svg.addEventListener('click', clickHandler, true);
        svg.addEventListener('dblclick', dblClickHandler, true);

        container.addEventListener('click', clickHandler, true);
        container.addEventListener('dblclick', dblClickHandler, true);

        // 添加到document级别作为最后的备用
        const documentHandler = (evt: MouseEvent) => {
            const target = evt.target as Element;
            if (target.closest('.mindmap-container')) {
                if (evt.type === 'dblclick') {
                    console.log('Document level double click captured');
                    evt.preventDefault();
                    evt.stopPropagation();
                    this.handleSVGDoubleClick(evt);
                }
            }
        };

        document.addEventListener('dblclick', documentHandler, true);

        // 保存处理器引用以便清理
        (container as any)._mindmapHandlers = {
            clickHandler,
            dblClickHandler,
            documentHandler
        };
    }

    // 处理SVG点击事件
    private handleSVGClick(evt: MouseEvent) {
        console.log('SVG Click event triggered');
        console.log('Event target:', evt.target);

        const target = evt.target as Element;
        console.log('Target element:', target.tagName, target.className);

        // 尝试多种方式查找节点组和文本内容
        let nodeContent: string | null = null;
        let nodeGroup: Element | null = null;

        // 方法1：直接从目标元素获取文本
        if (target.textContent && target.textContent.trim()) {
            nodeContent = target.textContent.trim();
            nodeGroup = target.closest('g[data-depth]') || target.closest('g');
        }

        // 方法2：查找父级节点组中的文本
        if (!nodeContent) {
            const parentGroup = target.parentElement?.closest('g') || null;
            nodeGroup = target.closest('g[data-depth]') || target.closest('g') || parentGroup;
            if (nodeGroup) {
                // 查找文本元素（可能是text标签或div标签）
                const textElement = nodeGroup.querySelector('text') || nodeGroup.querySelector('div[xmlns]') || nodeGroup.querySelector('div');
                if (textElement && textElement.textContent) {
                    nodeContent = textElement.textContent.trim();
                }
            }
        }

        console.log('Found node group:', nodeGroup);
        console.log('Extracted content:', nodeContent);

        if (nodeContent && this.rootNode) {
            const node = this.findNodeByContent(this.rootNode, nodeContent);
            if (node) {
                console.log('Found matching node:', node);
                this.selectNode(node);
            } else {
                console.log('Node not found for content:', nodeContent);
                console.log('Available nodes:', this.getAllNodes(this.rootNode));
            }
        } else {
            console.log('No content found or no root node');
            if (this.editingNode) {
                console.log('Finishing editing');
                this.finishEditing().catch(console.error);
            }
        }
    }

    // 处理SVG双击事件
    private handleSVGDoubleClick(evt: MouseEvent) {
        console.log('SVG Double click event triggered');
        evt.preventDefault();
        evt.stopPropagation();

        const target = evt.target as Element;
        console.log('Double click target:', target);

        // 尝试多种方式查找节点内容
        let nodeContent: string | null = null;

        // 方法1：直接从目标元素获取文本
        if (target.textContent && target.textContent.trim()) {
            nodeContent = target.textContent.trim();
        }

        // 方法2：查找父级节点组中的文本
        if (!nodeContent) {
            const nodeGroup = target.closest('g[data-depth]') || target.closest('g');
            if (nodeGroup) {
                const textElement = nodeGroup.querySelector('text') || nodeGroup.querySelector('div[xmlns]') || nodeGroup.querySelector('div');
                if (textElement && textElement.textContent) {
                    nodeContent = textElement.textContent.trim();
                }
            }
        }

        console.log('Double click extracted content:', nodeContent);

        if (nodeContent && this.rootNode) {
            const node = this.findNodeByContent(this.rootNode, nodeContent);
            if (node) {
                console.log('Starting editing for double-clicked node:', node);
                this.startEditing(node).catch(console.error);
            } else {
                console.log('Node not found for double-click content:', nodeContent);
            }
        }
    }

    // 根据内容查找节点
    private findNodeByContent(root: MindMapNode, content: string): MindMapNode | null {
        if (root.content === content) return root;
        for (const child of root.children) {
            const found = this.findNodeByContent(child, content);
            if (found) return found;
        }
        return null;
    }

    // 高亮选中的节点
    private highlightSelectedNode() {
        if (!this.selectedNode || !this.mindmap) return;

        // 移除之前的高亮
        const svg = document.querySelector('.mindmap-container svg');
        if (svg) {
            const selectedElements = svg.querySelectorAll('.selected-node');
            for (let i = 0; i < selectedElements.length; i++) {
                selectedElements[i].classList.remove('selected-node');
            }

            // 查找并高亮当前选中的节点
            const textElements = svg.querySelectorAll('text');
            for (let i = 0; i < textElements.length; i++) {
                const textEl = textElements[i];
                if (textEl.textContent?.trim() === this.selectedNode!.content) {
                    const nodeGroup = textEl.closest('g');
                    if (nodeGroup) {
                        nodeGroup.classList.add('selected-node');
                    }
                }
            }
        }
    }

    // 将节点树转换为Markdown格式
    private mindmapNodeToMarkdown(node: MindMapNode, depth = 0): string {
        const indent = '  '.repeat(depth);
        let markdown = `${indent}- ${node.content}\n`;
        if (node.children) {
            for (const child of node.children) {
                markdown += this.mindmapNodeToMarkdown(child, depth + 1);
            }
        }
        return markdown;
    }

    // 导出为Markdown文档
    async exportToMarkdown() {
        if (!this.rootNode) {
            console.log('No mind map data to export');
            return;
        }

        try {
            // 生成markdown内容
            const markdownContent = this.generateMarkdownFromMindMap(this.rootNode);

            // 创建文件名（基于根节点内容）
            const fileName = this.sanitizeFileName(this.rootNode.content) + '.md';

            // 检查文件是否已存在
            const existingFile = this.app.vault.getAbstractFileByPath(fileName);
            if (existingFile) {
                // 如果文件存在，询问用户是否覆盖
                const shouldOverwrite = await this.confirmOverwrite(fileName);
                if (!shouldOverwrite) {
                    return;
                }
            }

            // 创建或更新文件
            await this.app.vault.create(fileName, markdownContent);

            console.log(`Mind map exported to: ${fileName}`);

            // 打开新创建的文件
            const file = this.app.vault.getAbstractFileByPath(fileName);
            if (file) {
                const leaf = this.app.workspace.getLeaf(false);
                await leaf.openFile(file as any);
            }

        } catch (error) {
            console.error('Error exporting to markdown:', error);
        }
    }

    // 生成完整的Markdown内容
    private generateMarkdownFromMindMap(rootNode: MindMapNode): string {
        console.log('开始生成Markdown，根节点:', rootNode);

        // 处理根节点内容
        const lines = rootNode.content.split('\n');
        const title = lines[0]; // 第一行作为标题
        const description = lines.slice(1).join('\n').trim(); // 其余行作为描述

        let markdown = `# ${title}\n\n`;

        // 如果有描述内容，添加为段落
        if (description) {
            markdown += `${description}\n\n`;
        }

        // 如果根节点有子节点，生成层级结构
        if (rootNode.children && rootNode.children.length > 0) {
            for (const child of rootNode.children) {
                markdown += this.nodeToMarkdownContent(child, 2);
            }
        }

        return markdown;
    }

    // 智能转换节点为Markdown内容
    private nodeToMarkdownContent(node: MindMapNode, level: number): string {
        console.log(`转换节点: "${node.content}", isHeading: ${node.isHeading}, level: ${level}`);

        let markdown = '';

        // 检查节点是否为原始标题节点
        if (node.isHeading) {
            // 使用原始标题级别或当前级别
            const headingLevel = node.originalLevel || level;
            const headingPrefix = '#'.repeat(Math.min(headingLevel, 6));

            // 处理包含换行符的标题内容
            const lines = node.content.split('\n');
            const title = lines[0]; // 第一行作为标题
            const description = lines.slice(1).join('\n').trim(); // 其余行作为描述

            markdown += `${headingPrefix} ${title}\n\n`;

            // 如果有描述内容，添加为段落
            if (description) {
                markdown += `${description}\n\n`;
            }
        } else {
            // 非标题节点（如列表项）使用列表格式
            markdown += this.nodeToMarkdownList(node, 0);
            return markdown; // 列表项直接返回，不处理子节点
        }

        // 处理子节点
        if (node.children && node.children.length > 0) {
            for (const child of node.children) {
                if (child.isHeading && level < 6) {
                    // 子标题节点
                    markdown += this.nodeToMarkdownContent(child, level + 1);
                } else {
                    // 列表项或超过6级的节点
                    markdown += this.nodeToMarkdownList(child, 0);
                }
            }
        }

        return markdown;
    }

    // 将节点转换为Markdown列表
    private nodeToMarkdownList(node: MindMapNode, depth: number): string {
        const indent = '  '.repeat(depth);
        let markdown = `${indent}- ${node.content}\n`;

        if (node.children && node.children.length > 0) {
            for (const child of node.children) {
                markdown += this.nodeToMarkdownList(child, depth + 1);
            }
        }

        return markdown;
    }

    // 清理文件名
    private sanitizeFileName(name: string): string {
        // 移除或替换不允许的字符
        return name
            .replace(/[<>:"/\\|?*]/g, '-')
            .replace(/\s+/g, '_')
            .substring(0, 100); // 限制长度
    }

    // 确认覆盖文件
    private async confirmOverwrite(fileName: string): Promise<boolean> {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.className = 'modal-container';
            modal.innerHTML = `
                <div class="modal">
                    <div class="modal-title">文件已存在</div>
                    <div class="modal-content">
                        <p>文件 "${fileName}" 已存在。是否要覆盖它？</p>
                    </div>
                    <div class="modal-button-container">
                        <button class="mod-cta" id="confirm-overwrite">覆盖</button>
                        <button id="cancel-overwrite">取消</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            modal.querySelector('#confirm-overwrite')?.addEventListener('click', () => {
                document.body.removeChild(modal);
                resolve(true);
            });

            modal.querySelector('#cancel-overwrite')?.addEventListener('click', () => {
                document.body.removeChild(modal);
                resolve(false);
            });
        });
    }

    // 从Markdown文档导入
    async importFromMarkdown() {
        try {
            // 获取当前活动的文件
            const activeFile = this.app.workspace.getActiveFile();
            if (!activeFile || !activeFile.path.endsWith('.md')) {
                console.log('Please open a markdown file to import');
                new Notice('请先打开一个Markdown文件');
                return;
            }

            console.log('正在导入文件:', activeFile.path);

            // 读取文件内容
            const content = await this.app.vault.read(activeFile);
            console.log('读取到的文件内容:', content);

            // 解析Markdown内容为思维导图结构
            const mindMapData = this.parseMarkdownToMindMap(content, activeFile.basename);
            console.log('解析后的思维导图数据:', mindMapData);

            if (mindMapData) {
                // 设置为当前的思维导图数据
                this.rootNode = mindMapData;
                this.selectedNode = this.rootNode;

                // 创建新的思维导图视图
                const leaf = this.app.workspace.getLeaf(true);
                await leaf.setViewState({
                    type: MIND_MAP_VIEW_TYPE,
                    state: { data: this.rootNode }
                });

                this.app.workspace.revealLeaf(leaf);

                console.log(`Imported mind map from: ${activeFile.path}`);
                new Notice(`成功从 ${activeFile.basename} 导入思维导图`);
            } else {
                console.log('Failed to parse markdown content');
                new Notice('解析Markdown内容失败');
            }

        } catch (error) {
            console.error('Error importing from markdown:', error);
            new Notice('导入失败: ' + error.message);
        }
    }

    // 解析Markdown内容为思维导图结构
    parseMarkdownToMindMap(content: string, fileName: string): MindMapNode | null {
        try {
            console.log('开始解析Markdown内容:', content);

            // 移除前置元数据
            const cleanContent = content.replace(/^---[\s\S]*?---\n/, '');

            // 按行分割
            const lines = cleanContent.split('\n');
            console.log('分割后的行数:', lines.length);

            // 创建根节点 - 使用第一个一级标题作为根节点，如果没有则使用文件名
            let rootNode: MindMapNode | null = null;
            const nodeStack: { node: MindMapNode; level: number; isHeading: boolean }[] = [];
            let nodeIdCounter = 1;

            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];
                const trimmedLine = line.trim();

                console.log(`处理第${i+1}行: "${trimmedLine}"`);

                // 跳过空行和特殊格式行
                if (!trimmedLine ||
                    trimmedLine.startsWith('>') ||
                    trimmedLine.startsWith('---') ||
                    trimmedLine.startsWith('```') ||
                    trimmedLine.match(/^-\s*\*.*\*\s*$/) || // 跳过 "- *由 Mind Map 插件生成*" 这样的行
                    trimmedLine.match(/^\*.*\*$/) || // 跳过斜体注释行
                    trimmedLine.match(/^\|.*\|$/)) { // 表格行
                    console.log('跳过特殊行');
                    continue;
                }

                // 解析标题 (# ## ### 等)
                const headingMatch = trimmedLine.match(/^(#{1,6})\s+(.+)$/);
                if (headingMatch) {
                    const level = headingMatch[1].length;
                    const content = headingMatch[2];

                    console.log(`发现标题: 级别${level}, 内容"${content}"`);

                    const newNode: MindMapNode = {
                        id: `node_${nodeIdCounter++}`,
                        content: content,
                        children: [],
                        isExpanded: true,
                        isHeading: true,
                        originalLevel: level
                    };

                    // 如果是第一个节点或者是一级标题，设为根节点
                    if (!rootNode || level === 1) {
                        if (!rootNode) {
                            rootNode = newNode;
                            nodeStack.push({ node: rootNode, level, isHeading: true });
                            console.log('设置根节点:', content);
                            continue;
                        } else if (level === 1) {
                            // 如果已经有根节点，新的一级标题作为根节点的子节点
                            newNode.parent = rootNode;
                            rootNode.children.push(newNode);
                            // 清理栈，只保留根节点
                            nodeStack.splice(1);
                            nodeStack.push({ node: newNode, level, isHeading: true });
                            console.log('添加一级子节点:', content);
                            continue;
                        }
                    }

                    // 找到合适的父节点
                    while (nodeStack.length > 0 && nodeStack[nodeStack.length - 1].level >= level) {
                        nodeStack.pop();
                    }

                    if (nodeStack.length === 0) {
                        // 如果栈为空，说明这是一个新的顶级节点
                        if (!rootNode) {
                            rootNode = newNode;
                            nodeStack.push({ node: rootNode, level, isHeading: true });
                        } else {
                            newNode.parent = rootNode;
                            rootNode.children.push(newNode);
                            nodeStack.push({ node: rootNode, level: 0, isHeading: true });
                            nodeStack.push({ node: newNode, level, isHeading: true });
                        }
                    } else {
                        const parentNode = nodeStack[nodeStack.length - 1].node;
                        newNode.parent = parentNode;
                        parentNode.children.push(newNode);
                        nodeStack.push({ node: newNode, level, isHeading: true });
                    }

                    console.log(`添加节点"${content}"到父节点"${newNode.parent?.content || 'root'}"`);
                    continue;
                }

                // 解析列表项 (- * + 或数字列表)
                const listMatch = trimmedLine.match(/^(\s*)([-*+]|\d+\.)\s+(.+)$/);
                if (listMatch) {
                    const indent = listMatch[1].length;
                    const content = listMatch[3];

                    // 跳过插件生成的注释
                    if (content.includes('Mind Map 插件生成')) {
                        console.log('跳过插件注释');
                        continue;
                    }

                    // 列表项的级别应该比最近的标题高一级
                    let level = Math.floor(indent / 2) + 1;

                    // 找到最近的标题节点，列表项应该比它高一级
                    for (let j = nodeStack.length - 1; j >= 0; j--) {
                        const stackItem = nodeStack[j];
                        if (stackItem.isHeading) {
                            level = stackItem.level + 1 + Math.floor(indent / 2);
                            break;
                        }
                    }

                    console.log(`发现列表项: 缩进${indent}, 级别${level}, 内容"${content}"`);
                    console.log('列表项处理前栈状态:', nodeStack.map(item => `${item.node.content}(level:${item.level}, isHeading:${item.isHeading})`));

                    const newNode: MindMapNode = {
                        id: `node_${nodeIdCounter++}`,
                        content: content,
                        children: [],
                        isExpanded: true,
                        isHeading: false // 列表项不是标题
                    };

                    // 确保有根节点
                    if (!rootNode) {
                        rootNode = {
                            id: 'root',
                            content: fileName || '导入的思维导图',
                            children: [],
                            isExpanded: true
                        };
                        nodeStack.push({ node: rootNode, level: 0, isHeading: true });
                    }

                    // 找到合适的父节点
                    console.log(`寻找级别小于${level}的父节点`);
                    while (nodeStack.length > 0 && nodeStack[nodeStack.length - 1].level >= level) {
                        const removed = nodeStack.pop();
                        console.log(`从栈中移除: ${removed?.node.content}(level:${removed?.level})`);
                    }

                    const parentNode = nodeStack.length > 0 ? nodeStack[nodeStack.length - 1].node : rootNode;
                    newNode.parent = parentNode;
                    parentNode.children.push(newNode);

                    nodeStack.push({ node: newNode, level, isHeading: false });
                    console.log(`添加列表项"${content}"到父节点"${parentNode.content}"`);
                    continue;
                }

                // 解析普通段落文本（正文内容）
                if (trimmedLine.length > 0) {
                    console.log(`发现正文段落: "${trimmedLine}"`);

                    // 确保有根节点
                    if (!rootNode) {
                        rootNode = {
                            id: 'root',
                            content: fileName || '导入的思维导图',
                            children: [],
                            isExpanded: true
                        };
                        nodeStack.push({ node: rootNode, level: 0, isHeading: true });
                    }

                    // 找到最近的标题节点作为父节点
                    let parentNode = rootNode;

                    // 从栈中找到最近的标题节点（isHeading: true）
                    console.log('当前栈状态:', nodeStack.map(item => `${item.node.content}(level:${item.level}, isHeading:${item.isHeading})`));
                    for (let j = nodeStack.length - 1; j >= 0; j--) {
                        const stackItem = nodeStack[j];
                        console.log(`检查栈项 ${j}: "${stackItem.node.content}", level:${stackItem.level}, isHeading:${stackItem.isHeading}`);
                        // 如果是标题节点，则作为父节点
                        if (stackItem.isHeading) {
                            parentNode = stackItem.node;
                            console.log(`找到标题节点作为父节点: "${parentNode.content}"`);
                            break;
                        }
                    }

                    // 将正文内容直接添加到父节点的内容中，而不是创建新的子节点
                    if (parentNode.content && !parentNode.content.includes(trimmedLine)) {
                        // 如果父节点已有内容，用换行符连接
                        parentNode.content += '\n' + trimmedLine;
                        console.log(`将段落内容追加到节点"${parentNode.content.split('\n')[0]}"`);
                    } else if (!parentNode.content) {
                        // 如果父节点没有内容，直接设置
                        parentNode.content = trimmedLine;
                        console.log(`设置节点内容为"${trimmedLine}"`);
                    }

                    continue;
                }
            }

            // 如果没有根节点，创建一个默认根节点
            if (!rootNode) {
                rootNode = {
                    id: 'root',
                    content: fileName || '导入的思维导图',
                    children: [],
                    isExpanded: true
                };

                const defaultChild: MindMapNode = {
                    id: 'default_child',
                    content: '从Markdown导入的内容',
                    children: [],
                    parent: rootNode,
                    isExpanded: true
                };
                rootNode.children.push(defaultChild);
            }

            console.log('解析完成，根节点:', rootNode);
            console.log('根节点子节点数量:', rootNode.children.length);

            return rootNode;

        } catch (error) {
            console.error('Error parsing markdown:', error);
            return null;
        }
    }

    // 切换思维导图和Markdown视图 - 改为预览模式，不修改原文件
    async toggleMindMapMarkdown() {
        const activeLeaf = this.app.workspace.getActiveViewOfType(ItemView)?.leaf || this.app.workspace.getMostRecentLeaf();
        if (!activeLeaf) return;

        const currentView = activeLeaf.view;

        if (currentView.getViewType() === MIND_MAP_VIEW_TYPE) {
            // 当前是思维导图视图，关闭思维导图视图
            await this.closeMindMapView(activeLeaf);
        } else if (currentView.getViewType() === 'markdown') {
            // 当前是Markdown视图，创建思维导图预览
            await this.createMindMapPreview();
        } else {
            // 其他视图类型，创建新的思维导图
            await this.createNewMindMap();
        }
    }

    // 创建思维导图预览（类似原始插件的预览模式）
    private async createMindMapPreview() {
        try {
            const currentFile = this.app.workspace.getActiveFile();

            if (!currentFile || currentFile.extension !== 'md') {
                new Notice('请先打开一个Markdown文件');
                return;
            }

            // 检查是否已经有思维导图视图打开
            const existingMindMapLeaves = this.app.workspace.getLeavesOfType(MIND_MAP_VIEW_TYPE);
            if (existingMindMapLeaves.length > 0) {
                // 如果已经有思维导图视图，激活它并更新内容
                const existingLeaf = existingMindMapLeaves[0];

                // 重新解析当前文件
                const content = await this.app.vault.read(currentFile);
                const mindMapData = this.parseMarkdownToMindMap(content, currentFile.basename);

                if (mindMapData) {
                    this.rootNode = mindMapData;
                    this.selectedNode = this.rootNode;

                    // 更新现有视图
                    await existingLeaf.setViewState({
                        type: MIND_MAP_VIEW_TYPE,
                        state: {
                            data: this.rootNode,
                            sourceFile: currentFile.path
                        }
                    });
                }

                this.app.workspace.revealLeaf(existingLeaf);
                return;
            }

            // 读取当前文件内容
            const content = await this.app.vault.read(currentFile);
            const mindMapData = this.parseMarkdownToMindMap(content, currentFile.basename);

            if (mindMapData) {
                // 设置思维导图数据
                this.rootNode = mindMapData;
                this.selectedNode = this.rootNode;

                // 创建新的分割视图
                const newLeaf = this.app.workspace.getLeaf('split', 'vertical');

                // 在新leaf中打开思维导图视图
                await newLeaf.setViewState({
                    type: MIND_MAP_VIEW_TYPE,
                    state: {
                        data: this.rootNode,
                        sourceFile: currentFile.path
                    }
                });

                this.app.workspace.revealLeaf(newLeaf);

                console.log(`Created mind map preview for: ${currentFile.path}`);
                new Notice(`为 ${currentFile.basename} 创建了思维导图预览`);
            } else {
                console.log('Failed to parse markdown content');
                new Notice('解析Markdown内容失败');
            }

        } catch (error) {
            console.error('Error creating mind map preview:', error);
            new Notice('创建思维导图预览失败');
        }
    }

    // 关闭思维导图视图
    private async closeMindMapView(leaf: any) {
        try {
            // 简单地关闭当前leaf
            leaf.detach();
            console.log('Closed mind map view');
        } catch (error) {
            console.error('Error closing mind map view:', error);
        }
    }



    onunload() {
        this.mindmap = null;
    }
}

// 思维导图视图类
class MindMapView extends ItemView {
    plugin: MindMapPlugin;
    sourceFilePath: string | null = null;
    fileWatcher: any = null;

    constructor(leaf: WorkspaceLeaf, plugin: MindMapPlugin) {
        super(leaf);
        this.plugin = plugin;
    }

    getViewType() {
        return MIND_MAP_VIEW_TYPE;
    }

    getDisplayText() {
        return "思维导图";
    }

    getState(): Record<string, unknown> {
        return {
            type: MIND_MAP_VIEW_TYPE,
            data: this.plugin.getRootNode()
        };
    }

    async setState(state: Record<string, unknown>, _result: ViewStateResult) {
        const data = state.data as unknown;
        const sourceFile = state.sourceFile as string;

        if (this.isMindMapNode(data)) {
            await this.plugin.loadData(data);

            // 设置源文件监听
            console.log('setState - sourceFile:', sourceFile, 'current sourceFilePath:', this.sourceFilePath);
            if (sourceFile) {
                if (sourceFile !== this.sourceFilePath) {
                    console.log('Setting up file watcher because source file changed');
                    this.setupFileWatcher(sourceFile);
                } else {
                    console.log('Source file unchanged, keeping existing watcher');
                }
            } else {
                console.log('No source file provided in state');
            }
        }
    }

    // 设置文件监听器
    setupFileWatcher(filePath: string) {
        console.log('Setting up file watcher for:', filePath);

        // 清除之前的监听器
        if (this.fileWatcher) {
            console.log('Clearing previous file watcher');
            this.app.vault.offref(this.fileWatcher);
        }

        this.sourceFilePath = filePath;

        // 监听文件修改
        this.fileWatcher = this.app.vault.on('modify', async (file) => {
            console.log('File modified event:', file.path, 'watching:', this.sourceFilePath);
            if (file.path === this.sourceFilePath) {
                console.log('Source file modified, updating mind map:', file.path);
                await this.updateFromSourceFile(file);
            }
        });

        console.log('Mind map view linked to source file:', filePath);
        console.log('File watcher active:', !!this.fileWatcher);
    }

    // 从源文件更新思维导图
    private async updateFromSourceFile(file: any) {
        try {
            console.log('Reading source file content:', file.path);
            const content = await this.app.vault.read(file);
            console.log('File content length:', content.length);

            const mindMapData = this.plugin.parseMarkdownToMindMap(content, file.basename);
            console.log('Parsed mind map data:', mindMapData);

            if (mindMapData) {
                console.log('Updating mind map view with new data');

                // 使用public方法更新数据
                this.plugin.updateMindMapData(mindMapData);

                // 触发视图重新渲染
                this.refreshView();

                console.log('Mind map view updated from source file successfully');
            } else {
                console.log('Failed to parse mind map data from source file');
            }
        } catch (error) {
            console.error('Error updating mind map from source file:', error);
        }
    }

    private isMindMapNode(data: unknown): data is MindMapNode {
        if (!data || typeof data !== 'object') return false;
        
        const node = data as Partial<MindMapNode>;
        return (
            typeof node.id === 'string' &&
            typeof node.content === 'string' &&
            Array.isArray(node.children) &&
            node.children.every(child => this.isMindMapNode(child))
        );
    }

    async onOpen() {
        const container = this.containerEl.children[1];
        container.empty();
        const mindmapContainer = container.createDiv('mindmap-container');

        // 设置容器样式，确保有明确的尺寸
        mindmapContainer.style.width = '100%';
        mindmapContainer.style.height = '100%';
        mindmapContainer.style.minHeight = '400px';
        mindmapContainer.style.position = 'relative';
        mindmapContainer.style.overflow = 'hidden';

        console.log('MindMap view opened, container created');

        // 等待容器渲染完成
        setTimeout(async () => {
            // 从状态中加载数据
            const state = this.getState();
            if (state.data && this.isMindMapNode(state.data)) {
                await this.plugin.loadData(state.data);
            } else if (this.plugin.getRootNode()) {
                // 如果插件已有根节点，渲染它
                this.plugin.renderMindMap().catch(console.error);
            } else {
                // 创建默认的思维导图
                await this.plugin.createNewMindMap();
            }
        }, 100);
    }

    // 刷新视图
    async refreshView() {
        console.log('Refreshing mind map view');
        const container = this.containerEl.querySelector('.mindmap-container') as HTMLElement;
        if (container && this.plugin.getRootNode()) {
            console.log('Container found, triggering render');
            // 清空容器并重新渲染
            container.innerHTML = '';
            await this.plugin.renderMindMap();
        } else {
            console.log('Container or root node not found for refresh');
        }
    }

    async onClose() {
        // 清理文件监听器
        if (this.fileWatcher) {
            this.app.vault.offref(this.fileWatcher);
            this.fileWatcher = null;
        }
        this.sourceFilePath = null;
    }
}
