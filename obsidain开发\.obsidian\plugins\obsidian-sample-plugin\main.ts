import { <PERSON><PERSON>, Editor, ItemView, Plugin, PluginSettingTab, Setting, WorkspaceLeaf, ViewStateResult } from 'obsidian';
import { Transformer } from 'markmap-lib';
import { Markmap } from 'markmap-view';
import * as d3 from 'd3';

// 定义思维导图节点接口
interface MindMapNode {
    id: string;
    content: string;
    children: MindMapNode[];
    parent?: MindMapNode;
    isSelected?: boolean;
    isExpanded?: boolean;
}

// 定义视图状态接口
interface MindMapViewState {
    [key: string]: unknown;
    data?: MindMapNode;
    selectedNodeId?: string;
}

const MIND_MAP_VIEW_TYPE = "mindmap";

export default class MindMapPlugin extends Plugin {
    private transformer: Transformer;
    private mindmap: Markmap | null = null;
    private rootNode: MindMapNode | null = null;
    private currentNode: MindMapNode | null = null;
    private editingNode: MindMapNode | null = null;
    private selectedNode: MindMapNode | null = null;

    async onload() {
        // 初始化转换器
        this.transformer = new Transformer();

        // 注册视图
        this.registerView(
            MIND_MAP_VIEW_TYPE,
            (leaf: WorkspaceLeaf) => new MindMapView(leaf, this)
        );

        // 添加命令
        this.addCommand({
            id: 'create-mindmap',
            name: '创建新的思维导图',
            callback: () => this.createNewMindMap()
        });

        // 添加更多命令
        this.addCommand({
            id: 'select-parent-node',
            name: '选择父节点',
            callback: () => this.selectParentNode()
        });

        this.addCommand({
            id: 'select-first-child',
            name: '选择第一个子节点',
            callback: () => this.selectFirstChild()
        });

        this.addCommand({
            id: 'select-next-sibling',
            name: '选择下一个同级节点',
            callback: () => this.selectNextSibling()
        });

        this.addCommand({
            id: 'select-previous-sibling',
            name: '选择上一个同级节点',
            callback: () => this.selectPreviousSibling()
        });

        // 添加调试命令
        this.addCommand({
            id: 'debug-mindmap',
            name: '调试思维导图状态',
            callback: () => this.debugMindMapState()
        });

        this.addCommand({
            id: 'force-select-root',
            name: '强制选择根节点',
            callback: () => {
                if (this.rootNode) {
                    console.log('Force selecting root node:', this.rootNode);
                    this.selectNode(this.rootNode);
                }
            }
        });

        this.addCommand({
            id: 'create-simple-test',
            name: '创建简单测试视图',
            callback: () => this.createSimpleTestView()
        });

        this.addCommand({
            id: 'test-edit-selected',
            name: '测试编辑选中节点',
            callback: () => {
                if (this.selectedNode) {
                    console.log('Testing edit for selected node:', this.selectedNode.content);
                    const nodeEl = document.querySelector(`[data-node-id="${this.selectedNode.id}"]`) as HTMLElement;
                    if (nodeEl) {
                        this.startSimpleEditing(this.selectedNode, nodeEl);
                    } else {
                        console.log('Node element not found');
                    }
                } else {
                    console.log('No node selected');
                }
            }
        });

        this.addCommand({
            id: 'force-simple-view',
            name: '强制使用简单HTML视图',
            callback: () => {
                if (this.rootNode) {
                    this.createSimpleHTMLMindMap();
                } else {
                    this.createSimpleTestView();
                }
            }
        });

        this.addCommand({
            id: 'edit-root-node',
            name: '编辑根节点',
            callback: () => {
                if (this.rootNode) {
                    console.log('Forcing edit of root node');
                    this.startEditing(this.rootNode);
                } else {
                    console.log('No root node to edit');
                }
            }
        });

        this.addCommand({
            id: 'export-to-markdown',
            name: '导出为Markdown文档',
            callback: () => this.exportToMarkdown()
        });

        this.addCommand({
            id: 'import-from-markdown',
            name: '从Markdown文档导入',
            callback: () => this.importFromMarkdown()
        });

        // 注册键盘事件
        this.registerDomEvent(document, 'keydown', (evt: KeyboardEvent) => {
            // 只在思维导图视图激活时处理键盘事件
            const activeView = this.app.workspace.getActiveViewOfType(MindMapView);
            if (!activeView || !this.selectedNode) return;

            switch(evt.key) {
                case 'Tab':
                    evt.preventDefault();
                    if (evt.shiftKey) {
                        this.createSiblingNode(this.selectedNode);
                    } else {
                        this.createChildNode(this.selectedNode);
                    }
                    break;
                case 'Enter':
                    evt.preventDefault();
                    if (this.editingNode) {
                        this.finishEditing();
                    } else {
                        this.startEditing(this.selectedNode);
                    }
                    break;
                case 'Delete':
                case 'Backspace':
                    if (!this.editingNode && evt.ctrlKey) {
                        evt.preventDefault();
                        this.deleteNode(this.selectedNode);
                    }
                    break;
                case 'ArrowUp':
                    evt.preventDefault();
                    this.selectPreviousSibling();
                    break;
                case 'ArrowDown':
                    evt.preventDefault();
                    this.selectNextSibling();
                    break;
                case 'ArrowLeft':
                    evt.preventDefault();
                    if (this.selectedNode && this.selectedNode.isExpanded) {
                        this.collapseNode(this.selectedNode);
                    } else {
                        this.selectParentNode();
                    }
                    break;
                case 'ArrowRight':
                    evt.preventDefault();
                    if (this.selectedNode && !this.selectedNode.isExpanded) {
                        this.expandNode(this.selectedNode);
                    } else {
                        this.selectFirstChild();
                    }
                    break;
                case 'Escape':
                    if (this.editingNode) {
                        evt.preventDefault();
                        this.cancelEditing();
                    }
                    break;
            }
        });
    }

    // 调试思维导图状态
    private debugMindMapState() {
        console.log('=== 思维导图调试信息 ===');
        console.log('Root node:', this.rootNode);
        console.log('Selected node:', this.selectedNode);
        console.log('Editing node:', this.editingNode);
        console.log('Mindmap instance:', this.mindmap);

        const view = this.app.workspace.getActiveViewOfType(MindMapView);
        console.log('Active view:', view);

        if (view) {
            const container = view.containerEl.querySelector('.mindmap-container');
            console.log('Container:', container);

            if (container) {
                const svg = container.querySelector('svg');
                console.log('SVG element:', svg);

                if (svg) {
                    const groups = svg.querySelectorAll('g');
                    console.log('Found groups:', groups.length);

                    const textElements = svg.querySelectorAll('text');
                    console.log('Found text elements:', textElements.length);

                    for (let i = 0; i < textElements.length; i++) {
                        const textEl = textElements[i];
                        console.log(`Text ${i}:`, textEl.textContent, textEl);
                    }
                }
            }
        }

        if (this.rootNode) {
            console.log('All nodes:', this.getAllNodes(this.rootNode));
        }
        console.log('=== 调试信息结束 ===');
    }

    // 创建简单测试视图
    async createSimpleTestView() {
        this.rootNode = {
            id: 'root',
            content: '中心主题',
            children: [
                {
                    id: 'child1',
                    content: '子节点1',
                    children: [],
                    parent: undefined,
                    isExpanded: true
                },
                {
                    id: 'child2',
                    content: '子节点2',
                    children: [],
                    parent: undefined,
                    isExpanded: true
                }
            ],
            isExpanded: true
        };

        // 设置父节点引用
        this.rootNode.children.forEach(child => {
            child.parent = this.rootNode!;
        });

        const leaf = this.app.workspace.getLeaf(true);
        await leaf.setViewState({
            type: MIND_MAP_VIEW_TYPE,
            state: { data: this.rootNode }
        });

        this.app.workspace.revealLeaf(leaf);

        // 等待视图加载后创建简单HTML版本
        setTimeout(() => {
            this.createSimpleHTMLMindMap();
        }, 200);
    }

    // 创建简单的HTML思维导图（不使用markmap）
    private createSimpleHTMLMindMap() {
        const view = this.app.workspace.getActiveViewOfType(MindMapView);
        if (!view || !this.rootNode) return;

        const container = view.containerEl.querySelector('.mindmap-container') as HTMLElement;
        if (!container) return;

        container.innerHTML = '';
        container.style.cssText = `
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            background: var(--background-primary);
        `;

        // 创建根节点
        const rootElement = this.createNodeElement(this.rootNode, 0);
        container.appendChild(rootElement);

        // 创建子节点
        const childrenContainer = container.createDiv('children-container');
        childrenContainer.style.cssText = `
            display: flex;
            gap: 20px;
            margin-top: 20px;
        `;

        this.rootNode.children.forEach(child => {
            const childElement = this.createNodeElement(child, 1);
            childrenContainer.appendChild(childElement);
        });

        // 选中根节点
        this.selectNode(this.rootNode);
    }

    // 创建节点元素
    private createNodeElement(node: MindMapNode, level: number): HTMLElement {
        const nodeEl = document.createElement('div');
        nodeEl.className = 'simple-mindmap-node';
        nodeEl.setAttribute('data-node-id', node.id);
        nodeEl.setAttribute('tabindex', '0'); // 使元素可以获得焦点

        const isSelected = this.selectedNode === node;

        nodeEl.style.cssText = `
            padding: 10px 15px;
            border: 2px solid ${isSelected ? 'var(--text-accent)' : 'var(--background-modifier-border)'};
            border-radius: 8px;
            background: var(--background-primary);
            cursor: pointer;
            user-select: none;
            font-weight: ${level === 0 ? '600' : '400'};
            font-size: ${level === 0 ? '16px' : '14px'};
            color: ${isSelected ? 'var(--text-accent)' : 'var(--text-normal)'};
            transition: all 0.2s ease;
            min-width: 100px;
            text-align: center;
            outline: none;
        `;

        nodeEl.textContent = node.content;

        // 添加事件监听器
        nodeEl.addEventListener('click', (e) => {
            e.stopPropagation();
            console.log('Simple node clicked:', node.content);
            this.selectNode(node);
            this.updateSimpleNodeStyles();
        });

        nodeEl.addEventListener('dblclick', (e) => {
            e.stopPropagation();
            e.preventDefault();
            console.log('Simple node double-clicked:', node.content);
            this.startSimpleEditing(node, nodeEl);
        });

        // 添加键盘事件支持
        nodeEl.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && this.selectedNode === node) {
                e.preventDefault();
                console.log('Enter pressed on selected node:', node.content);
                this.startSimpleEditing(node, nodeEl);
            }
        });

        return nodeEl;
    }

    // 更新简单节点样式
    private updateSimpleNodeStyles() {
        const nodes = document.querySelectorAll('.simple-mindmap-node');
        nodes.forEach(nodeEl => {
            const nodeId = nodeEl.getAttribute('data-node-id');
            const isSelected = this.selectedNode?.id === nodeId;

            (nodeEl as HTMLElement).style.borderColor = isSelected ? 'var(--text-accent)' : 'var(--background-modifier-border)';
            (nodeEl as HTMLElement).style.color = isSelected ? 'var(--text-accent)' : 'var(--text-normal)';
        });
    }

    // 简单编辑功能
    private startSimpleEditing(node: MindMapNode, nodeEl: HTMLElement) {
        console.log('Starting simple editing for node:', node.content);

        const input = document.createElement('input');
        input.type = 'text';
        input.value = node.content;
        input.className = 'simple-mindmap-input';

        // 复制原有样式并添加编辑样式
        input.style.cssText = `
            padding: 10px 15px;
            border: 2px solid var(--text-accent);
            border-radius: 8px;
            background: var(--background-primary);
            font-weight: ${node === this.rootNode ? '600' : '400'};
            font-size: ${node === this.rootNode ? '16px' : '14px'};
            color: var(--text-accent);
            min-width: 100px;
            text-align: center;
            outline: none;
            box-shadow: 0 0 0 2px var(--background-modifier-border-hover);
        `;

        console.log('Replacing node element with input');
        nodeEl.parentNode?.replaceChild(input, nodeEl);

        // 确保输入框获得焦点
        setTimeout(() => {
            input.focus();
            input.select();
        }, 10);

        const finishEdit = () => {
            console.log('Finishing edit, new content:', input.value);
            node.content = input.value;

            // 重新创建整个简单思维导图以确保一致性
            this.createSimpleHTMLMindMap();
            this.saveData();
        };

        const cancelEdit = () => {
            console.log('Cancelling edit');
            // 重新创建整个简单思维导图
            this.createSimpleHTMLMindMap();
        };

        input.addEventListener('blur', finishEdit);
        input.addEventListener('keydown', (e) => {
            console.log('Key pressed in input:', e.key);
            if (e.key === 'Enter') {
                e.preventDefault();
                finishEdit();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                cancelEdit();
            }
        });
    }

    // 处理节点选中
    private selectNode(node: MindMapNode | null) {
        if (this.selectedNode) {
            this.selectedNode.isSelected = false;
        }

        this.selectedNode = node;
        if (node) {
            node.isSelected = true;

            // 高亮选中的节点
            this.highlightSelectedNode();
        }
    }

    // 选择父节点
    private selectParentNode() {
        if (this.selectedNode?.parent) {
            this.selectNode(this.selectedNode.parent);
        }
    }

    // 选择第一个子节点
    private selectFirstChild() {
        if (this.selectedNode && this.selectedNode.children && this.selectedNode.children.length > 0) {
            this.selectNode(this.selectedNode.children[0]);
        }
    }

    // 选择下一个同级节点
    private selectNextSibling() {
        if (!this.selectedNode?.parent) return;
        
        const siblings = this.selectedNode.parent.children;
        const currentIndex = siblings.indexOf(this.selectedNode);
        if (currentIndex < siblings.length - 1) {
            this.selectNode(siblings[currentIndex + 1]);
        }
    }

    // 选择上一个同级节点
    private selectPreviousSibling() {
        if (!this.selectedNode?.parent) return;
        
        const siblings = this.selectedNode.parent.children;
        const currentIndex = siblings.indexOf(this.selectedNode);
        if (currentIndex > 0) {
            this.selectNode(siblings[currentIndex - 1]);
        }
    }

    // 展开节点
    private expandNode(node: MindMapNode) {
        node.isExpanded = true;
        this.renderMindMap();
    }

    // 折叠节点
    private collapseNode(node: MindMapNode) {
        node.isExpanded = false;
        this.renderMindMap();
    }

    // 删除节点
    private deleteNode(node: MindMapNode) {
        if (!node.parent || node === this.rootNode) return;
        
        const siblings = node.parent.children;
        const index = siblings.indexOf(node);
        siblings.splice(index, 1);
        
        // 选择相邻节点或父节点
        if (siblings.length > 0) {
            this.selectNode(siblings[Math.min(index, siblings.length - 1)]);
        } else {
            this.selectNode(node.parent);
        }
        
        this.saveData();
    }



    // 从 DOM 元素中提取节点 ID
    private findNodeIdFromElement(element: Element): string | null {
        // markmap 会为每个节点生成唯一的 ID
        const transform = element.getAttribute('transform');
        if (!transform) return null;
        
        // 遍历所有节点找到匹配的
        const allNodes = this.getAllNodes(this.rootNode!);
        for (const node of allNodes) {
            if (element.textContent?.includes(node.content)) {
                return node.id;
            }
        }
        return null;
    }

    // 获取所有节点
    private getAllNodes(root: MindMapNode): MindMapNode[] {
        const nodes: MindMapNode[] = [root];
        for (const child of root.children) {
            nodes.push(...this.getAllNodes(child));
        }
        return nodes;
    }

    // 开始编辑节点
    private startEditing(node: MindMapNode) {
        console.log('Starting editing for node:', node.content);

        if (this.editingNode === node) {
            console.log('Already editing this node');
            return;
        }

        this.finishEditing();
        this.editingNode = node;

        // 检查是否在简单HTML模式
        const simpleNode = document.querySelector(`[data-node-id="${node.id}"]`) as HTMLElement;
        if (simpleNode) {
            console.log('Using simple HTML editing');
            this.startSimpleEditing(node, simpleNode);
            return;
        }

        // markmap模式的编辑
        console.log('Using markmap editing mode');
        this.startMarkmapEditing(node);
    }

    // markmap模式的编辑
    private startMarkmapEditing(node: MindMapNode) {
        // 查找包含该节点内容的文本元素
        const svg = document.querySelector('.mindmap-container svg');
        if (!svg) {
            console.log('No SVG found for editing');
            return;
        }

        // 查找所有可能的文本元素
        const textElements = svg.querySelectorAll('text, div[xmlns], div');
        let targetElement: Element | null = null;

        for (let i = 0; i < textElements.length; i++) {
            const el = textElements[i];
            if (el.textContent?.trim() === node.content) {
                targetElement = el;
                break;
            }
        }

        if (!targetElement) {
            console.log('No target element found for editing');
            return;
        }

        console.log('Found target element for editing:', targetElement);

        const input = document.createElement('input');
        input.value = node.content;
        input.className = 'mindmap-node-input';

        // 获取元素位置
        const rect = targetElement.getBoundingClientRect();

        Object.assign(input.style, {
            position: 'fixed',
            left: `${rect.left}px`,
            top: `${rect.top}px`,
            width: `${Math.max(rect.width + 40, 100)}px`,
            height: `${Math.max(rect.height + 8, 30)}px`,
            zIndex: '10000',
            fontSize: '14px',
            fontFamily: 'var(--font-text)',
            background: 'var(--background-primary)',
            border: '2px solid var(--text-accent)',
            borderRadius: '4px',
            padding: '4px 8px',
            color: 'var(--text-normal)',
            outline: 'none'
        });

        document.body.appendChild(input);

        // 确保输入框获得焦点
        setTimeout(() => {
            input.focus();
            input.select();
        }, 10);

        input.addEventListener('blur', () => {
            this.finishEditing();
        });

        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.finishEditing();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                this.cancelEditing();
            }
        });
    }

    // 获取 SVG 元素的页面坐标
    private getSVGElementPosition(element: SVGTextElement) {
        const svg = element.ownerSVGElement!;
        const point = svg.createSVGPoint();
        const bbox = element.getBBox();
        point.x = bbox.x;
        point.y = bbox.y + bbox.height;
        
        // 转换为页面坐标
        const ctm = element.getScreenCTM();
        if (ctm) {
            const globalPoint = point.matrixTransform(ctm);
            return { x: globalPoint.x, y: globalPoint.y };
        }
        return { x: 0, y: 0 };
    }

    // 完成编辑
    private finishEditing() {
        const input = document.querySelector('.mindmap-node-input');
        if (input && this.editingNode) {
            this.editingNode.content = (input as HTMLInputElement).value;
            input.remove();
            this.editingNode = null;
            this.renderMindMap();
            this.saveData(); // 保存数据
        }
    }

    // 取消编辑
    private cancelEditing() {
        const input = document.querySelector('.mindmap-node-input');
        if (input && this.editingNode) {
            input.remove();
            this.editingNode = null;
        }
    }

    // 创建新的思维导图
    async createNewMindMap() {
        this.rootNode = {
            id: 'root',
            content: '中心主题',
            children: [],
            isExpanded: true
        };

        // 添加一些测试子节点
        const child1: MindMapNode = {
            id: 'child1',
            content: '子节点1',
            children: [],
            parent: this.rootNode,
            isExpanded: true
        };

        const child2: MindMapNode = {
            id: 'child2',
            content: '子节点2',
            children: [],
            parent: this.rootNode,
            isExpanded: true
        };

        this.rootNode.children.push(child1, child2);

        const leaf = this.app.workspace.getLeaf(true);
        await leaf.setViewState({
            type: MIND_MAP_VIEW_TYPE,
            state: { data: this.rootNode }
        });

        this.app.workspace.revealLeaf(leaf);

        // 选中根节点
        this.selectNode(this.rootNode);
        this.saveData(); // 保存数据
    }

    // 保存数据
    saveData(): Promise<void> {
        if (!this.rootNode) return Promise.resolve();
        
        const view = this.app.workspace.getActiveViewOfType(MindMapView);
        if (view) {
            return view.leaf.setViewState({
                type: MIND_MAP_VIEW_TYPE,
                state: { data: this.rootNode }
            });
        }
        return Promise.resolve();
    }

    // 加载数据
    loadData(): Promise<any>;
    loadData(data: MindMapNode): Promise<void>;
    async loadData(data?: MindMapNode): Promise<any> {
        if (data) {
            this.rootNode = this.reconstructNode(data);
            this.currentNode = this.rootNode;
            this.renderMindMap();
        }
    }

    // 获取根节点数据
    getRootNode(): MindMapNode | null {
        return this.rootNode;
    }

    // 重建节点树（恢复父节点引用）
    private reconstructNode(node: MindMapNode, parent: MindMapNode | undefined = undefined): MindMapNode {
        const newNode: MindMapNode = {
            id: node.id,
            content: node.content,
            children: [],
            parent: parent
        };
        
        for (const child of node.children) {
            newNode.children.push(this.reconstructNode(child, newNode));
        }
        
        return newNode;
    }



    // 创建同级节点
    private createSiblingNode(node: MindMapNode) {
        if (!node.parent) return; // 根节点不能创建同级节点

        const newNode: MindMapNode = {
            id: Date.now().toString(),
            content: '新节点',
            children: [],
            parent: node.parent,
            isExpanded: true
        };

        const index = node.parent.children.indexOf(node);
        node.parent.children.splice(index + 1, 0, newNode);

        this.renderMindMap();
        this.selectNode(newNode);
        this.saveData(); // 保存数据
    }

    // 创建父节点
    private createParentNode(node: MindMapNode) {
        const newNode: MindMapNode = {
            id: Date.now().toString(),
            content: '新父节点',
            children: [node]
        };

        if (node === this.rootNode) {
            this.rootNode = newNode;
        } else if (node.parent) {
            const parentChildren = node.parent.children;
            const index = parentChildren.indexOf(node);
            parentChildren[index] = newNode;
            newNode.parent = node.parent;
        }

        node.parent = newNode;
        this.currentNode = newNode;
        this.renderMindMap();
        this.saveData(); // 保存数据
    }

    // 创建子节点
    private createChildNode(parentNode: MindMapNode) {
        const newNode: MindMapNode = {
            id: Date.now().toString(),
            content: '新节点',
            children: [],
            parent: parentNode,
            isExpanded: true
        };

        parentNode.children.push(newNode);
        parentNode.isExpanded = true; // 确保父节点展开

        this.renderMindMap();
        this.selectNode(newNode);
        this.saveData(); // 保存数据
    }

    // 查找节点
    private findNode(root: MindMapNode, id: string): MindMapNode | null {
        if (root.id === id) return root;
        for (const child of root.children) {
            const found = this.findNode(child, id);
            if (found) return found;
        }
        return null;
    }

    // 渲染思维导图
    renderMindMap() {
        const view = this.app.workspace.getActiveViewOfType(MindMapView);
        if (!view || !this.rootNode) {
            return;
        }

        const container = view.containerEl.querySelector('.mindmap-container') as HTMLElement;
        if (!container) return;

        // 清空容器
        container.innerHTML = '';

        // 确保容器有明确的尺寸
        const containerRect = container.getBoundingClientRect();
        let width = containerRect.width;
        let height = containerRect.height;

        // 如果容器尺寸为0或无效，使用默认值
        if (!width || width <= 0 || isNaN(width)) {
            width = 800;
            container.style.width = '800px';
        }
        if (!height || height <= 0 || isNaN(height)) {
            height = 600;
            container.style.height = '600px';
        }

        console.log('Container dimensions:', width, height);

        // 创建SVG元素，使用明确的像素尺寸
        const svg = d3.select(container)
            .append('svg')
            .attr('width', width)
            .attr('height', height)
            .style('width', width + 'px')
            .style('height', height + 'px');

        // 等待下一帧再创建markmap，确保SVG已经渲染
        requestAnimationFrame(() => {
            try {
                // 使用markmap渲染
                this.mindmap = Markmap.create(svg.node() as SVGSVGElement, {
                    autoFit: true,
                    duration: 300,
                    maxWidth: width - 100,
                    initialExpandLevel: 2,
                });

                // 转换数据格式并更新
                const markdown = this.mindmapNodeToMarkdown(this.rootNode!);
                const { root: data } = this.transformer.transform(markdown);

                console.log('Markmap data:', data);

                // 为每个节点添加自定义ID
                this.addCustomNodeIds(data, this.rootNode!);

                this.mindmap.setData(data);

                // 设置事件监听器
                this.setupEventListeners(container);

                // 如果有选中的节点，高亮显示
                if (this.selectedNode) {
                    setTimeout(() => this.highlightSelectedNode(), 100);
                }
            } catch (error) {
                console.error('Error creating markmap:', error);
                // 如果markmap失败，创建一个简单的备用显示
                this.createFallbackDisplay(container);
            }
        });
    }

    // 创建备用显示
    private createFallbackDisplay(container: HTMLElement) {
        container.innerHTML = '';
        const fallbackDiv = container.createDiv('mindmap-fallback');
        fallbackDiv.style.cssText = `
            padding: 20px;
            text-align: center;
            color: var(--text-muted);
            font-size: 14px;
        `;

        fallbackDiv.innerHTML = `
            <h3>思维导图渲染失败</h3>
            <p>请尝试以下操作：</p>
            <ul style="text-align: left; display: inline-block;">
                <li>重新加载插件</li>
                <li>检查浏览器控制台的错误信息</li>
                <li>使用调试命令查看详细信息</li>
            </ul>
            <button onclick="this.closest('.mindmap-container').dispatchEvent(new CustomEvent('retry-render'))">
                重试渲染
            </button>
        `;

        // 添加重试事件监听器
        container.addEventListener('retry-render', () => {
            setTimeout(() => this.renderMindMap(), 100);
        });
    }

    // 为markmap数据添加自定义ID
    private addCustomNodeIds(markmapNode: any, mindmapNode: MindMapNode) {
        markmapNode.customId = mindmapNode.id;
        if (markmapNode.children && mindmapNode.children) {
            for (let i = 0; i < markmapNode.children.length; i++) {
                if (mindmapNode.children[i]) {
                    this.addCustomNodeIds(markmapNode.children[i], mindmapNode.children[i]);
                }
            }
        }
    }

    // 设置事件监听器
    private setupEventListeners(container: Element) {
        // 移除旧的事件监听器
        const svg = container.querySelector('svg');
        if (!svg) {
            console.log('No SVG found in container');
            return;
        }

        console.log('Setting up event listeners on SVG:', svg);

        // 使用更强的事件监听器，确保能捕获到所有事件
        const clickHandler = (evt: MouseEvent) => {
            console.log('Click event captured on:', evt.target);
            this.handleSVGClick(evt);
        };

        const dblClickHandler = (evt: MouseEvent) => {
            console.log('Double click event captured on:', evt.target);
            evt.preventDefault();
            evt.stopPropagation();
            this.handleSVGDoubleClick(evt);
        };

        // 在多个层级添加事件监听器
        svg.addEventListener('click', clickHandler, true);
        svg.addEventListener('dblclick', dblClickHandler, true);

        container.addEventListener('click', clickHandler, true);
        container.addEventListener('dblclick', dblClickHandler, true);

        // 添加到document级别作为最后的备用
        const documentHandler = (evt: MouseEvent) => {
            const target = evt.target as Element;
            if (target.closest('.mindmap-container')) {
                if (evt.type === 'dblclick') {
                    console.log('Document level double click captured');
                    evt.preventDefault();
                    evt.stopPropagation();
                    this.handleSVGDoubleClick(evt);
                }
            }
        };

        document.addEventListener('dblclick', documentHandler, true);

        // 保存处理器引用以便清理
        (container as any)._mindmapHandlers = {
            clickHandler,
            dblClickHandler,
            documentHandler
        };
    }

    // 处理SVG点击事件
    private handleSVGClick(evt: MouseEvent) {
        console.log('SVG Click event triggered');
        console.log('Event target:', evt.target);

        const target = evt.target as Element;
        console.log('Target element:', target.tagName, target.className);

        // 尝试多种方式查找节点组和文本内容
        let nodeContent: string | null = null;
        let nodeGroup: Element | null = null;

        // 方法1：直接从目标元素获取文本
        if (target.textContent && target.textContent.trim()) {
            nodeContent = target.textContent.trim();
            nodeGroup = target.closest('g[data-depth]') || target.closest('g');
        }

        // 方法2：查找父级节点组中的文本
        if (!nodeContent) {
            const parentGroup = target.parentElement?.closest('g') || null;
            nodeGroup = target.closest('g[data-depth]') || target.closest('g') || parentGroup;
            if (nodeGroup) {
                // 查找文本元素（可能是text标签或div标签）
                const textElement = nodeGroup.querySelector('text') || nodeGroup.querySelector('div[xmlns]') || nodeGroup.querySelector('div');
                if (textElement && textElement.textContent) {
                    nodeContent = textElement.textContent.trim();
                }
            }
        }

        console.log('Found node group:', nodeGroup);
        console.log('Extracted content:', nodeContent);

        if (nodeContent && this.rootNode) {
            const node = this.findNodeByContent(this.rootNode, nodeContent);
            if (node) {
                console.log('Found matching node:', node);
                this.selectNode(node);
            } else {
                console.log('Node not found for content:', nodeContent);
                console.log('Available nodes:', this.getAllNodes(this.rootNode));
            }
        } else {
            console.log('No content found or no root node');
            if (this.editingNode) {
                console.log('Finishing editing');
                this.finishEditing();
            }
        }
    }

    // 处理SVG双击事件
    private handleSVGDoubleClick(evt: MouseEvent) {
        console.log('SVG Double click event triggered');
        evt.preventDefault();
        evt.stopPropagation();

        const target = evt.target as Element;
        console.log('Double click target:', target);

        // 尝试多种方式查找节点内容
        let nodeContent: string | null = null;

        // 方法1：直接从目标元素获取文本
        if (target.textContent && target.textContent.trim()) {
            nodeContent = target.textContent.trim();
        }

        // 方法2：查找父级节点组中的文本
        if (!nodeContent) {
            const nodeGroup = target.closest('g[data-depth]') || target.closest('g');
            if (nodeGroup) {
                const textElement = nodeGroup.querySelector('text') || nodeGroup.querySelector('div[xmlns]') || nodeGroup.querySelector('div');
                if (textElement && textElement.textContent) {
                    nodeContent = textElement.textContent.trim();
                }
            }
        }

        console.log('Double click extracted content:', nodeContent);

        if (nodeContent && this.rootNode) {
            const node = this.findNodeByContent(this.rootNode, nodeContent);
            if (node) {
                console.log('Starting editing for double-clicked node:', node);
                this.startEditing(node);
            } else {
                console.log('Node not found for double-click content:', nodeContent);
            }
        }
    }

    // 根据内容查找节点
    private findNodeByContent(root: MindMapNode, content: string): MindMapNode | null {
        if (root.content === content) return root;
        for (const child of root.children) {
            const found = this.findNodeByContent(child, content);
            if (found) return found;
        }
        return null;
    }

    // 高亮选中的节点
    private highlightSelectedNode() {
        if (!this.selectedNode || !this.mindmap) return;

        // 移除之前的高亮
        const svg = document.querySelector('.mindmap-container svg');
        if (svg) {
            const selectedElements = svg.querySelectorAll('.selected-node');
            for (let i = 0; i < selectedElements.length; i++) {
                selectedElements[i].classList.remove('selected-node');
            }

            // 查找并高亮当前选中的节点
            const textElements = svg.querySelectorAll('text');
            for (let i = 0; i < textElements.length; i++) {
                const textEl = textElements[i];
                if (textEl.textContent?.trim() === this.selectedNode!.content) {
                    const nodeGroup = textEl.closest('g');
                    if (nodeGroup) {
                        nodeGroup.classList.add('selected-node');
                    }
                }
            }
        }
    }

    // 将节点树转换为Markdown格式
    private mindmapNodeToMarkdown(node: MindMapNode, depth = 0): string {
        const indent = '  '.repeat(depth);
        let markdown = `${indent}- ${node.content}\n`;
        if (node.children) {
            for (const child of node.children) {
                markdown += this.mindmapNodeToMarkdown(child, depth + 1);
            }
        }
        return markdown;
    }

    // 导出为Markdown文档
    async exportToMarkdown() {
        if (!this.rootNode) {
            console.log('No mind map data to export');
            return;
        }

        try {
            // 生成markdown内容
            const markdownContent = this.generateMarkdownFromMindMap(this.rootNode);

            // 创建文件名（基于根节点内容）
            const fileName = this.sanitizeFileName(this.rootNode.content) + '.md';

            // 检查文件是否已存在
            const existingFile = this.app.vault.getAbstractFileByPath(fileName);
            if (existingFile) {
                // 如果文件存在，询问用户是否覆盖
                const shouldOverwrite = await this.confirmOverwrite(fileName);
                if (!shouldOverwrite) {
                    return;
                }
            }

            // 创建或更新文件
            await this.app.vault.create(fileName, markdownContent);

            console.log(`Mind map exported to: ${fileName}`);

            // 打开新创建的文件
            const file = this.app.vault.getAbstractFileByPath(fileName);
            if (file) {
                const leaf = this.app.workspace.getLeaf(false);
                await leaf.openFile(file as any);
            }

        } catch (error) {
            console.error('Error exporting to markdown:', error);
        }
    }

    // 生成完整的Markdown内容
    private generateMarkdownFromMindMap(rootNode: MindMapNode): string {
        const title = rootNode.content;
        const date = new Date().toISOString().split('T')[0];

        let markdown = `# ${title}\n\n`;
        markdown += `> 思维导图导出 - ${date}\n\n`;

        // 如果根节点有子节点，生成层级结构
        if (rootNode.children && rootNode.children.length > 0) {
            for (const child of rootNode.children) {
                markdown += this.nodeToMarkdownSection(child, 2);
            }
        } else {
            markdown += `## ${title}\n\n`;
            markdown += `*此思维导图暂无子节点*\n\n`;
        }

        markdown += `---\n\n`;
        markdown += `*由 Mind Map 插件生成*\n`;

        return markdown;
    }

    // 将节点转换为Markdown章节
    private nodeToMarkdownSection(node: MindMapNode, level: number): string {
        const headingPrefix = '#'.repeat(level);
        let markdown = `${headingPrefix} ${node.content}\n\n`;

        // 如果有子节点，递归处理
        if (node.children && node.children.length > 0) {
            for (const child of node.children) {
                if (level < 6) { // Markdown最多支持6级标题
                    markdown += this.nodeToMarkdownSection(child, level + 1);
                } else {
                    // 超过6级时使用列表格式
                    markdown += this.nodeToMarkdownList(child, 0);
                }
            }
        }

        return markdown;
    }

    // 将节点转换为Markdown列表
    private nodeToMarkdownList(node: MindMapNode, depth: number): string {
        const indent = '  '.repeat(depth);
        let markdown = `${indent}- ${node.content}\n`;

        if (node.children && node.children.length > 0) {
            for (const child of node.children) {
                markdown += this.nodeToMarkdownList(child, depth + 1);
            }
        }

        return markdown;
    }

    // 清理文件名
    private sanitizeFileName(name: string): string {
        // 移除或替换不允许的字符
        return name
            .replace(/[<>:"/\\|?*]/g, '-')
            .replace(/\s+/g, '_')
            .substring(0, 100); // 限制长度
    }

    // 确认覆盖文件
    private async confirmOverwrite(fileName: string): Promise<boolean> {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.className = 'modal-container';
            modal.innerHTML = `
                <div class="modal">
                    <div class="modal-title">文件已存在</div>
                    <div class="modal-content">
                        <p>文件 "${fileName}" 已存在。是否要覆盖它？</p>
                    </div>
                    <div class="modal-button-container">
                        <button class="mod-cta" id="confirm-overwrite">覆盖</button>
                        <button id="cancel-overwrite">取消</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            modal.querySelector('#confirm-overwrite')?.addEventListener('click', () => {
                document.body.removeChild(modal);
                resolve(true);
            });

            modal.querySelector('#cancel-overwrite')?.addEventListener('click', () => {
                document.body.removeChild(modal);
                resolve(false);
            });
        });
    }

    // 从Markdown文档导入
    async importFromMarkdown() {
        try {
            // 获取当前活动的文件
            const activeFile = this.app.workspace.getActiveFile();
            if (!activeFile || !activeFile.path.endsWith('.md')) {
                console.log('Please open a markdown file to import');
                return;
            }

            // 读取文件内容
            const content = await this.app.vault.read(activeFile);

            // 解析Markdown内容为思维导图结构
            const mindMapData = this.parseMarkdownToMindMap(content, activeFile.basename);

            if (mindMapData) {
                // 设置为当前的思维导图数据
                this.rootNode = mindMapData;
                this.selectedNode = this.rootNode;

                // 创建新的思维导图视图
                const leaf = this.app.workspace.getLeaf(true);
                await leaf.setViewState({
                    type: MIND_MAP_VIEW_TYPE,
                    state: { data: this.rootNode }
                });

                this.app.workspace.revealLeaf(leaf);

                console.log(`Imported mind map from: ${activeFile.path}`);
            } else {
                console.log('Failed to parse markdown content');
            }

        } catch (error) {
            console.error('Error importing from markdown:', error);
        }
    }

    // 解析Markdown内容为思维导图结构
    private parseMarkdownToMindMap(content: string, fileName: string): MindMapNode | null {
        try {
            // 移除前置元数据
            const cleanContent = content.replace(/^---[\s\S]*?---\n/, '');

            // 按行分割
            const lines = cleanContent.split('\n');

            // 创建根节点
            const rootNode: MindMapNode = {
                id: 'root',
                content: fileName || '导入的思维导图',
                children: [],
                isExpanded: true
            };

            // 解析标题和列表
            const nodeStack: { node: MindMapNode; level: number }[] = [{ node: rootNode, level: 0 }];
            let nodeIdCounter = 1;

            for (const line of lines) {
                const trimmedLine = line.trim();
                if (!trimmedLine) continue;

                // 解析标题 (# ## ### 等)
                const headingMatch = trimmedLine.match(/^(#{1,6})\s+(.+)$/);
                if (headingMatch) {
                    const level = headingMatch[1].length;
                    const content = headingMatch[2];

                    const newNode: MindMapNode = {
                        id: `node_${nodeIdCounter++}`,
                        content: content,
                        children: [],
                        isExpanded: true
                    };

                    // 找到合适的父节点
                    while (nodeStack.length > 1 && nodeStack[nodeStack.length - 1].level >= level) {
                        nodeStack.pop();
                    }

                    const parentNode = nodeStack[nodeStack.length - 1].node;
                    newNode.parent = parentNode;
                    parentNode.children.push(newNode);

                    nodeStack.push({ node: newNode, level });
                    continue;
                }

                // 解析列表项 (- * + 或数字列表)
                const listMatch = trimmedLine.match(/^(\s*)([-*+]|\d+\.)\s+(.+)$/);
                if (listMatch) {
                    const indent = listMatch[1].length;
                    const content = listMatch[3];
                    const level = Math.floor(indent / 2) + 1; // 每2个空格为一级

                    const newNode: MindMapNode = {
                        id: `node_${nodeIdCounter++}`,
                        content: content,
                        children: [],
                        isExpanded: true
                    };

                    // 找到合适的父节点
                    while (nodeStack.length > 1 && nodeStack[nodeStack.length - 1].level >= level) {
                        nodeStack.pop();
                    }

                    const parentNode = nodeStack[nodeStack.length - 1].node;
                    newNode.parent = parentNode;
                    parentNode.children.push(newNode);

                    nodeStack.push({ node: newNode, level });
                    continue;
                }

                // 如果是普通文本行，且不为空，添加到当前节点
                if (trimmedLine && !trimmedLine.startsWith('>') && !trimmedLine.startsWith('---')) {
                    const newNode: MindMapNode = {
                        id: `node_${nodeIdCounter++}`,
                        content: trimmedLine,
                        children: [],
                        isExpanded: true
                    };

                    const parentNode = nodeStack[nodeStack.length - 1].node;
                    newNode.parent = parentNode;
                    parentNode.children.push(newNode);
                }
            }

            // 如果根节点没有子节点，创建一个默认子节点
            if (rootNode.children.length === 0) {
                const defaultChild: MindMapNode = {
                    id: 'default_child',
                    content: '从Markdown导入的内容',
                    children: [],
                    parent: rootNode,
                    isExpanded: true
                };
                rootNode.children.push(defaultChild);
            }

            return rootNode;

        } catch (error) {
            console.error('Error parsing markdown:', error);
            return null;
        }
    }

    onunload() {
        this.mindmap = null;
    }
}

// 思维导图视图类
class MindMapView extends ItemView {
    plugin: MindMapPlugin;

    constructor(leaf: WorkspaceLeaf, plugin: MindMapPlugin) {
        super(leaf);
        this.plugin = plugin;
    }

    getViewType() {
        return MIND_MAP_VIEW_TYPE;
    }

    getDisplayText() {
        return "思维导图";
    }

    getState(): Record<string, unknown> {
        return {
            type: MIND_MAP_VIEW_TYPE,
            data: this.plugin.getRootNode()
        };
    }

    async setState(state: Record<string, unknown>, _result: ViewStateResult) {
        const data = state.data as unknown;
        if (this.isMindMapNode(data)) {
            await this.plugin.loadData(data);
        }
    }

    private isMindMapNode(data: unknown): data is MindMapNode {
        if (!data || typeof data !== 'object') return false;
        
        const node = data as Partial<MindMapNode>;
        return (
            typeof node.id === 'string' &&
            typeof node.content === 'string' &&
            Array.isArray(node.children) &&
            node.children.every(child => this.isMindMapNode(child))
        );
    }

    async onOpen() {
        const container = this.containerEl.children[1];
        container.empty();
        const mindmapContainer = container.createDiv('mindmap-container');

        // 设置容器样式，确保有明确的尺寸
        mindmapContainer.style.width = '100%';
        mindmapContainer.style.height = '100%';
        mindmapContainer.style.minHeight = '400px';
        mindmapContainer.style.position = 'relative';
        mindmapContainer.style.overflow = 'hidden';

        console.log('MindMap view opened, container created');

        // 等待容器渲染完成
        setTimeout(async () => {
            // 从状态中加载数据
            const state = this.getState();
            if (state.data && this.isMindMapNode(state.data)) {
                await this.plugin.loadData(state.data);
            } else if (this.plugin.getRootNode()) {
                // 如果插件已有根节点，渲染它
                this.plugin.renderMindMap();
            } else {
                // 创建默认的思维导图
                await this.plugin.createNewMindMap();
            }
        }, 100);
    }

    async onClose() {
        // Future: clean up logic
    }
}
