import { <PERSON><PERSON>, Editor, ItemView, Plugin, PluginSettingTab, Setting, WorkspaceLeaf, ViewStateResult } from 'obsidian';
import { Transformer } from 'markmap-lib';
import { Markmap } from 'markmap-view';
import * as d3 from 'd3';

// 定义思维导图节点接口
interface MindMapNode {
    id: string;
    content: string;
    children: MindMapNode[];
    parent?: MindMapNode;
    isSelected?: boolean;
    isExpanded?: boolean;
}

// 定义视图状态接口
interface MindMapViewState {
    [key: string]: unknown;
    data?: MindMapNode;
    selectedNodeId?: string;
}

const MIND_MAP_VIEW_TYPE = "mindmap";

export default class MindMapPlugin extends Plugin {
    private transformer: Transformer;
    private mindmap: Markmap | null = null;
    private rootNode: MindMapNode | null = null;
    private currentNode: MindMapNode | null = null;
    private editingNode: MindMapNode | null = null;
    private selectedNode: MindMapNode | null = null;

    async onload() {
        // 初始化转换器
        this.transformer = new Transformer();

        // 注册视图
        this.registerView(
            MIND_MAP_VIEW_TYPE,
            (leaf: WorkspaceLeaf) => new MindMapView(leaf, this)
        );

        // 添加命令
        this.addCommand({
            id: 'create-mindmap',
            name: '创建新的思维导图',
            callback: () => this.createNewMindMap()
        });

        // 添加更多命令
        this.addCommand({
            id: 'select-parent-node',
            name: '选择父节点',
            callback: () => this.selectParentNode()
        });

        this.addCommand({
            id: 'select-first-child',
            name: '选择第一个子节点',
            callback: () => this.selectFirstChild()
        });

        this.addCommand({
            id: 'select-next-sibling',
            name: '选择下一个同级节点',
            callback: () => this.selectNextSibling()
        });

        this.addCommand({
            id: 'select-previous-sibling',
            name: '选择上一个同级节点',
            callback: () => this.selectPreviousSibling()
        });

        // 添加调试命令
        this.addCommand({
            id: 'debug-mindmap',
            name: '调试思维导图状态',
            callback: () => this.debugMindMapState()
        });

        this.addCommand({
            id: 'force-select-root',
            name: '强制选择根节点',
            callback: () => {
                if (this.rootNode) {
                    console.log('Force selecting root node:', this.rootNode);
                    this.selectNode(this.rootNode);
                }
            }
        });

        this.addCommand({
            id: 'create-simple-test',
            name: '创建简单测试视图',
            callback: () => this.createSimpleTestView()
        });

        this.addCommand({
            id: 'test-edit-selected',
            name: '测试编辑选中节点',
            callback: () => {
                if (this.selectedNode) {
                    console.log('Testing edit for selected node:', this.selectedNode.content);
                    const nodeEl = document.querySelector(`[data-node-id="${this.selectedNode.id}"]`) as HTMLElement;
                    if (nodeEl) {
                        this.startSimpleEditing(this.selectedNode, nodeEl);
                    } else {
                        console.log('Node element not found');
                    }
                } else {
                    console.log('No node selected');
                }
            }
        });

        // 注册键盘事件
        this.registerDomEvent(document, 'keydown', (evt: KeyboardEvent) => {
            // 只在思维导图视图激活时处理键盘事件
            const activeView = this.app.workspace.getActiveViewOfType(MindMapView);
            if (!activeView || !this.selectedNode) return;

            switch(evt.key) {
                case 'Tab':
                    evt.preventDefault();
                    if (evt.shiftKey) {
                        this.createSiblingNode(this.selectedNode);
                    } else {
                        this.createChildNode(this.selectedNode);
                    }
                    break;
                case 'Enter':
                    evt.preventDefault();
                    if (this.editingNode) {
                        this.finishEditing();
                    } else {
                        this.startEditing(this.selectedNode);
                    }
                    break;
                case 'Delete':
                case 'Backspace':
                    if (!this.editingNode && evt.ctrlKey) {
                        evt.preventDefault();
                        this.deleteNode(this.selectedNode);
                    }
                    break;
                case 'ArrowUp':
                    evt.preventDefault();
                    this.selectPreviousSibling();
                    break;
                case 'ArrowDown':
                    evt.preventDefault();
                    this.selectNextSibling();
                    break;
                case 'ArrowLeft':
                    evt.preventDefault();
                    if (this.selectedNode && this.selectedNode.isExpanded) {
                        this.collapseNode(this.selectedNode);
                    } else {
                        this.selectParentNode();
                    }
                    break;
                case 'ArrowRight':
                    evt.preventDefault();
                    if (this.selectedNode && !this.selectedNode.isExpanded) {
                        this.expandNode(this.selectedNode);
                    } else {
                        this.selectFirstChild();
                    }
                    break;
                case 'Escape':
                    if (this.editingNode) {
                        evt.preventDefault();
                        this.cancelEditing();
                    }
                    break;
            }
        });
    }

    // 调试思维导图状态
    private debugMindMapState() {
        console.log('=== 思维导图调试信息 ===');
        console.log('Root node:', this.rootNode);
        console.log('Selected node:', this.selectedNode);
        console.log('Editing node:', this.editingNode);
        console.log('Mindmap instance:', this.mindmap);

        const view = this.app.workspace.getActiveViewOfType(MindMapView);
        console.log('Active view:', view);

        if (view) {
            const container = view.containerEl.querySelector('.mindmap-container');
            console.log('Container:', container);

            if (container) {
                const svg = container.querySelector('svg');
                console.log('SVG element:', svg);

                if (svg) {
                    const groups = svg.querySelectorAll('g');
                    console.log('Found groups:', groups.length);

                    const textElements = svg.querySelectorAll('text');
                    console.log('Found text elements:', textElements.length);

                    for (let i = 0; i < textElements.length; i++) {
                        const textEl = textElements[i];
                        console.log(`Text ${i}:`, textEl.textContent, textEl);
                    }
                }
            }
        }

        if (this.rootNode) {
            console.log('All nodes:', this.getAllNodes(this.rootNode));
        }
        console.log('=== 调试信息结束 ===');
    }

    // 创建简单测试视图
    async createSimpleTestView() {
        this.rootNode = {
            id: 'root',
            content: '中心主题',
            children: [
                {
                    id: 'child1',
                    content: '子节点1',
                    children: [],
                    parent: undefined,
                    isExpanded: true
                },
                {
                    id: 'child2',
                    content: '子节点2',
                    children: [],
                    parent: undefined,
                    isExpanded: true
                }
            ],
            isExpanded: true
        };

        // 设置父节点引用
        this.rootNode.children.forEach(child => {
            child.parent = this.rootNode!;
        });

        const leaf = this.app.workspace.getLeaf(true);
        await leaf.setViewState({
            type: MIND_MAP_VIEW_TYPE,
            state: { data: this.rootNode }
        });

        this.app.workspace.revealLeaf(leaf);

        // 等待视图加载后创建简单HTML版本
        setTimeout(() => {
            this.createSimpleHTMLMindMap();
        }, 200);
    }

    // 创建简单的HTML思维导图（不使用markmap）
    private createSimpleHTMLMindMap() {
        const view = this.app.workspace.getActiveViewOfType(MindMapView);
        if (!view || !this.rootNode) return;

        const container = view.containerEl.querySelector('.mindmap-container') as HTMLElement;
        if (!container) return;

        container.innerHTML = '';
        container.style.cssText = `
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            background: var(--background-primary);
        `;

        // 创建根节点
        const rootElement = this.createNodeElement(this.rootNode, 0);
        container.appendChild(rootElement);

        // 创建子节点
        const childrenContainer = container.createDiv('children-container');
        childrenContainer.style.cssText = `
            display: flex;
            gap: 20px;
            margin-top: 20px;
        `;

        this.rootNode.children.forEach(child => {
            const childElement = this.createNodeElement(child, 1);
            childrenContainer.appendChild(childElement);
        });

        // 选中根节点
        this.selectNode(this.rootNode);
    }

    // 创建节点元素
    private createNodeElement(node: MindMapNode, level: number): HTMLElement {
        const nodeEl = document.createElement('div');
        nodeEl.className = 'simple-mindmap-node';
        nodeEl.setAttribute('data-node-id', node.id);
        nodeEl.setAttribute('tabindex', '0'); // 使元素可以获得焦点

        const isSelected = this.selectedNode === node;

        nodeEl.style.cssText = `
            padding: 10px 15px;
            border: 2px solid ${isSelected ? 'var(--text-accent)' : 'var(--background-modifier-border)'};
            border-radius: 8px;
            background: var(--background-primary);
            cursor: pointer;
            user-select: none;
            font-weight: ${level === 0 ? '600' : '400'};
            font-size: ${level === 0 ? '16px' : '14px'};
            color: ${isSelected ? 'var(--text-accent)' : 'var(--text-normal)'};
            transition: all 0.2s ease;
            min-width: 100px;
            text-align: center;
            outline: none;
        `;

        nodeEl.textContent = node.content;

        // 添加事件监听器
        nodeEl.addEventListener('click', (e) => {
            e.stopPropagation();
            console.log('Simple node clicked:', node.content);
            this.selectNode(node);
            this.updateSimpleNodeStyles();
        });

        nodeEl.addEventListener('dblclick', (e) => {
            e.stopPropagation();
            e.preventDefault();
            console.log('Simple node double-clicked:', node.content);
            this.startSimpleEditing(node, nodeEl);
        });

        // 添加键盘事件支持
        nodeEl.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && this.selectedNode === node) {
                e.preventDefault();
                console.log('Enter pressed on selected node:', node.content);
                this.startSimpleEditing(node, nodeEl);
            }
        });

        return nodeEl;
    }

    // 更新简单节点样式
    private updateSimpleNodeStyles() {
        const nodes = document.querySelectorAll('.simple-mindmap-node');
        nodes.forEach(nodeEl => {
            const nodeId = nodeEl.getAttribute('data-node-id');
            const isSelected = this.selectedNode?.id === nodeId;

            (nodeEl as HTMLElement).style.borderColor = isSelected ? 'var(--text-accent)' : 'var(--background-modifier-border)';
            (nodeEl as HTMLElement).style.color = isSelected ? 'var(--text-accent)' : 'var(--text-normal)';
        });
    }

    // 简单编辑功能
    private startSimpleEditing(node: MindMapNode, nodeEl: HTMLElement) {
        console.log('Starting simple editing for node:', node.content);

        const input = document.createElement('input');
        input.type = 'text';
        input.value = node.content;
        input.className = 'simple-mindmap-input';

        // 复制原有样式并添加编辑样式
        input.style.cssText = `
            padding: 10px 15px;
            border: 2px solid var(--text-accent);
            border-radius: 8px;
            background: var(--background-primary);
            font-weight: ${node === this.rootNode ? '600' : '400'};
            font-size: ${node === this.rootNode ? '16px' : '14px'};
            color: var(--text-accent);
            min-width: 100px;
            text-align: center;
            outline: none;
            box-shadow: 0 0 0 2px var(--background-modifier-border-hover);
        `;

        console.log('Replacing node element with input');
        nodeEl.parentNode?.replaceChild(input, nodeEl);

        // 确保输入框获得焦点
        setTimeout(() => {
            input.focus();
            input.select();
        }, 10);

        const finishEdit = () => {
            console.log('Finishing edit, new content:', input.value);
            node.content = input.value;

            // 重新创建整个简单思维导图以确保一致性
            this.createSimpleHTMLMindMap();
            this.saveData();
        };

        const cancelEdit = () => {
            console.log('Cancelling edit');
            // 重新创建整个简单思维导图
            this.createSimpleHTMLMindMap();
        };

        input.addEventListener('blur', finishEdit);
        input.addEventListener('keydown', (e) => {
            console.log('Key pressed in input:', e.key);
            if (e.key === 'Enter') {
                e.preventDefault();
                finishEdit();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                cancelEdit();
            }
        });
    }

    // 处理节点选中
    private selectNode(node: MindMapNode | null) {
        if (this.selectedNode) {
            this.selectedNode.isSelected = false;
        }

        this.selectedNode = node;
        if (node) {
            node.isSelected = true;

            // 高亮选中的节点
            this.highlightSelectedNode();
        }
    }

    // 选择父节点
    private selectParentNode() {
        if (this.selectedNode?.parent) {
            this.selectNode(this.selectedNode.parent);
        }
    }

    // 选择第一个子节点
    private selectFirstChild() {
        if (this.selectedNode && this.selectedNode.children && this.selectedNode.children.length > 0) {
            this.selectNode(this.selectedNode.children[0]);
        }
    }

    // 选择下一个同级节点
    private selectNextSibling() {
        if (!this.selectedNode?.parent) return;
        
        const siblings = this.selectedNode.parent.children;
        const currentIndex = siblings.indexOf(this.selectedNode);
        if (currentIndex < siblings.length - 1) {
            this.selectNode(siblings[currentIndex + 1]);
        }
    }

    // 选择上一个同级节点
    private selectPreviousSibling() {
        if (!this.selectedNode?.parent) return;
        
        const siblings = this.selectedNode.parent.children;
        const currentIndex = siblings.indexOf(this.selectedNode);
        if (currentIndex > 0) {
            this.selectNode(siblings[currentIndex - 1]);
        }
    }

    // 展开节点
    private expandNode(node: MindMapNode) {
        node.isExpanded = true;
        this.renderMindMap();
    }

    // 折叠节点
    private collapseNode(node: MindMapNode) {
        node.isExpanded = false;
        this.renderMindMap();
    }

    // 删除节点
    private deleteNode(node: MindMapNode) {
        if (!node.parent || node === this.rootNode) return;
        
        const siblings = node.parent.children;
        const index = siblings.indexOf(node);
        siblings.splice(index, 1);
        
        // 选择相邻节点或父节点
        if (siblings.length > 0) {
            this.selectNode(siblings[Math.min(index, siblings.length - 1)]);
        } else {
            this.selectNode(node.parent);
        }
        
        this.saveData();
    }



    // 从 DOM 元素中提取节点 ID
    private findNodeIdFromElement(element: Element): string | null {
        // markmap 会为每个节点生成唯一的 ID
        const transform = element.getAttribute('transform');
        if (!transform) return null;
        
        // 遍历所有节点找到匹配的
        const allNodes = this.getAllNodes(this.rootNode!);
        for (const node of allNodes) {
            if (element.textContent?.includes(node.content)) {
                return node.id;
            }
        }
        return null;
    }

    // 获取所有节点
    private getAllNodes(root: MindMapNode): MindMapNode[] {
        const nodes: MindMapNode[] = [root];
        for (const child of root.children) {
            nodes.push(...this.getAllNodes(child));
        }
        return nodes;
    }

    // 开始编辑节点
    private startEditing(node: MindMapNode) {
        if (this.editingNode === node) return;

        this.finishEditing();
        this.editingNode = node;

        // 查找包含该节点内容的文本元素
        const svg = document.querySelector('.mindmap-container svg');
        if (!svg) return;

        const textElements = svg.querySelectorAll('text');
        let targetTextElement: SVGTextElement | null = null;

        for (let i = 0; i < textElements.length; i++) {
            const textEl = textElements[i] as SVGTextElement;
            if (textEl.textContent?.trim() === node.content) {
                targetTextElement = textEl;
                break;
            }
        }

        if (!targetTextElement) return;

        const input = document.createElement('input');
        input.value = node.content;
        input.className = 'mindmap-node-input';

        const bbox = targetTextElement.getBBox();
        const point = this.getSVGElementPosition(targetTextElement);

        Object.assign(input.style, {
            position: 'absolute',
            left: `${point.x}px`,
            top: `${point.y - bbox.height/2}px`,
            width: `${Math.max(bbox.width + 40, 100)}px`,
            height: `${bbox.height + 8}px`,
            zIndex: '1000',
            fontSize: '14px',
            fontFamily: 'var(--font-text)'
        });

        document.body.appendChild(input);
        input.focus();
        input.select();

        input.addEventListener('blur', () => {
            this.finishEditing();
        });

        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.finishEditing();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                this.cancelEditing();
            }
        });
    }

    // 获取 SVG 元素的页面坐标
    private getSVGElementPosition(element: SVGTextElement) {
        const svg = element.ownerSVGElement!;
        const point = svg.createSVGPoint();
        const bbox = element.getBBox();
        point.x = bbox.x;
        point.y = bbox.y + bbox.height;
        
        // 转换为页面坐标
        const ctm = element.getScreenCTM();
        if (ctm) {
            const globalPoint = point.matrixTransform(ctm);
            return { x: globalPoint.x, y: globalPoint.y };
        }
        return { x: 0, y: 0 };
    }

    // 完成编辑
    private finishEditing() {
        const input = document.querySelector('.mindmap-node-input');
        if (input && this.editingNode) {
            this.editingNode.content = (input as HTMLInputElement).value;
            input.remove();
            this.editingNode = null;
            this.renderMindMap();
            this.saveData(); // 保存数据
        }
    }

    // 取消编辑
    private cancelEditing() {
        const input = document.querySelector('.mindmap-node-input');
        if (input && this.editingNode) {
            input.remove();
            this.editingNode = null;
        }
    }

    // 创建新的思维导图
    async createNewMindMap() {
        this.rootNode = {
            id: 'root',
            content: '中心主题',
            children: [],
            isExpanded: true
        };

        // 添加一些测试子节点
        const child1: MindMapNode = {
            id: 'child1',
            content: '子节点1',
            children: [],
            parent: this.rootNode,
            isExpanded: true
        };

        const child2: MindMapNode = {
            id: 'child2',
            content: '子节点2',
            children: [],
            parent: this.rootNode,
            isExpanded: true
        };

        this.rootNode.children.push(child1, child2);

        const leaf = this.app.workspace.getLeaf(true);
        await leaf.setViewState({
            type: MIND_MAP_VIEW_TYPE,
            state: { data: this.rootNode }
        });

        this.app.workspace.revealLeaf(leaf);

        // 选中根节点
        this.selectNode(this.rootNode);
        this.saveData(); // 保存数据
    }

    // 保存数据
    saveData(): Promise<void> {
        if (!this.rootNode) return Promise.resolve();
        
        const view = this.app.workspace.getActiveViewOfType(MindMapView);
        if (view) {
            return view.leaf.setViewState({
                type: MIND_MAP_VIEW_TYPE,
                state: { data: this.rootNode }
            });
        }
        return Promise.resolve();
    }

    // 加载数据
    loadData(): Promise<any>;
    loadData(data: MindMapNode): Promise<void>;
    async loadData(data?: MindMapNode): Promise<any> {
        if (data) {
            this.rootNode = this.reconstructNode(data);
            this.currentNode = this.rootNode;
            this.renderMindMap();
        }
    }

    // 获取根节点数据
    getRootNode(): MindMapNode | null {
        return this.rootNode;
    }

    // 重建节点树（恢复父节点引用）
    private reconstructNode(node: MindMapNode, parent: MindMapNode | undefined = undefined): MindMapNode {
        const newNode: MindMapNode = {
            id: node.id,
            content: node.content,
            children: [],
            parent: parent
        };
        
        for (const child of node.children) {
            newNode.children.push(this.reconstructNode(child, newNode));
        }
        
        return newNode;
    }



    // 创建同级节点
    private createSiblingNode(node: MindMapNode) {
        if (!node.parent) return; // 根节点不能创建同级节点

        const newNode: MindMapNode = {
            id: Date.now().toString(),
            content: '新节点',
            children: [],
            parent: node.parent,
            isExpanded: true
        };

        const index = node.parent.children.indexOf(node);
        node.parent.children.splice(index + 1, 0, newNode);

        this.renderMindMap();
        this.selectNode(newNode);
        this.saveData(); // 保存数据
    }

    // 创建父节点
    private createParentNode(node: MindMapNode) {
        const newNode: MindMapNode = {
            id: Date.now().toString(),
            content: '新父节点',
            children: [node]
        };

        if (node === this.rootNode) {
            this.rootNode = newNode;
        } else if (node.parent) {
            const parentChildren = node.parent.children;
            const index = parentChildren.indexOf(node);
            parentChildren[index] = newNode;
            newNode.parent = node.parent;
        }

        node.parent = newNode;
        this.currentNode = newNode;
        this.renderMindMap();
        this.saveData(); // 保存数据
    }

    // 创建子节点
    private createChildNode(parentNode: MindMapNode) {
        const newNode: MindMapNode = {
            id: Date.now().toString(),
            content: '新节点',
            children: [],
            parent: parentNode,
            isExpanded: true
        };

        parentNode.children.push(newNode);
        parentNode.isExpanded = true; // 确保父节点展开

        this.renderMindMap();
        this.selectNode(newNode);
        this.saveData(); // 保存数据
    }

    // 查找节点
    private findNode(root: MindMapNode, id: string): MindMapNode | null {
        if (root.id === id) return root;
        for (const child of root.children) {
            const found = this.findNode(child, id);
            if (found) return found;
        }
        return null;
    }

    // 渲染思维导图
    renderMindMap() {
        const view = this.app.workspace.getActiveViewOfType(MindMapView);
        if (!view || !this.rootNode) {
            return;
        }

        const container = view.containerEl.querySelector('.mindmap-container') as HTMLElement;
        if (!container) return;

        // 清空容器
        container.innerHTML = '';

        // 确保容器有明确的尺寸
        const containerRect = container.getBoundingClientRect();
        const width = containerRect.width || 800;
        const height = containerRect.height || 600;

        console.log('Container dimensions:', width, height);

        // 创建SVG元素，使用明确的像素尺寸
        const svg = d3.select(container)
            .append('svg')
            .attr('width', width)
            .attr('height', height)
            .style('width', width + 'px')
            .style('height', height + 'px');

        // 等待下一帧再创建markmap，确保SVG已经渲染
        requestAnimationFrame(() => {
            try {
                // 使用markmap渲染
                this.mindmap = Markmap.create(svg.node() as SVGSVGElement, {
                    autoFit: true,
                    duration: 300,
                    maxWidth: width - 100,
                    initialExpandLevel: 2,
                });

                // 转换数据格式并更新
                const markdown = this.mindmapNodeToMarkdown(this.rootNode!);
                const { root: data } = this.transformer.transform(markdown);

                console.log('Markmap data:', data);

                // 为每个节点添加自定义ID
                this.addCustomNodeIds(data, this.rootNode!);

                this.mindmap.setData(data);

                // 设置事件监听器
                this.setupEventListeners(container);

                // 如果有选中的节点，高亮显示
                if (this.selectedNode) {
                    setTimeout(() => this.highlightSelectedNode(), 100);
                }
            } catch (error) {
                console.error('Error creating markmap:', error);
                // 如果markmap失败，创建一个简单的备用显示
                this.createFallbackDisplay(container);
            }
        });
    }

    // 创建备用显示
    private createFallbackDisplay(container: HTMLElement) {
        container.innerHTML = '';
        const fallbackDiv = container.createDiv('mindmap-fallback');
        fallbackDiv.style.cssText = `
            padding: 20px;
            text-align: center;
            color: var(--text-muted);
            font-size: 14px;
        `;

        fallbackDiv.innerHTML = `
            <h3>思维导图渲染失败</h3>
            <p>请尝试以下操作：</p>
            <ul style="text-align: left; display: inline-block;">
                <li>重新加载插件</li>
                <li>检查浏览器控制台的错误信息</li>
                <li>使用调试命令查看详细信息</li>
            </ul>
            <button onclick="this.closest('.mindmap-container').dispatchEvent(new CustomEvent('retry-render'))">
                重试渲染
            </button>
        `;

        // 添加重试事件监听器
        container.addEventListener('retry-render', () => {
            setTimeout(() => this.renderMindMap(), 100);
        });
    }

    // 为markmap数据添加自定义ID
    private addCustomNodeIds(markmapNode: any, mindmapNode: MindMapNode) {
        markmapNode.customId = mindmapNode.id;
        if (markmapNode.children && mindmapNode.children) {
            for (let i = 0; i < markmapNode.children.length; i++) {
                if (mindmapNode.children[i]) {
                    this.addCustomNodeIds(markmapNode.children[i], mindmapNode.children[i]);
                }
            }
        }
    }

    // 设置事件监听器
    private setupEventListeners(container: Element) {
        // 移除旧的事件监听器
        const svg = container.querySelector('svg');
        if (!svg) {
            console.log('No SVG found in container');
            return;
        }

        console.log('Setting up event listeners on SVG:', svg);

        // 添加点击事件监听器 - 使用捕获阶段
        svg.addEventListener('click', (evt: MouseEvent) => {
            console.log('SVG click captured');
            this.handleSVGClick(evt);
        }, true);

        // 添加双击事件监听器
        svg.addEventListener('dblclick', (evt: MouseEvent) => {
            console.log('SVG double click captured');
            this.handleSVGDoubleClick(evt);
        }, true);

        // 添加到整个容器的事件监听器作为备用
        container.addEventListener('click', (evt: MouseEvent) => {
            console.log('Container click captured');
            this.handleSVGClick(evt);
        }, true);

        container.addEventListener('dblclick', (evt: MouseEvent) => {
            console.log('Container double click captured');
            this.handleSVGDoubleClick(evt);
        }, true);
    }

    // 处理SVG点击事件
    private handleSVGClick(evt: MouseEvent) {
        console.log('SVG Click event triggered');
        console.log('Event target:', evt.target);
        console.log('Root node:', this.rootNode);

        const target = evt.target as Element;
        console.log('Target element:', target.tagName, target.className);

        // 尝试多种方式查找节点组
        const nodeGroup = target.closest('g[data-depth]') || target.closest('g') || target.parentElement?.closest('g');
        console.log('Found node group:', nodeGroup);

        if (nodeGroup) {
            const textElement = nodeGroup.querySelector('text');
            console.log('Text element:', textElement);

            if (textElement) {
                const nodeContent = textElement.textContent?.trim();
                console.log('Clicked node content:', nodeContent);

                if (nodeContent) {
                    const node = this.findNodeByContent(this.rootNode!, nodeContent);
                    if (node) {
                        console.log('Found node:', node);
                        this.selectNode(node);
                    } else {
                        console.log('Node not found for content:', nodeContent);
                        console.log('Available nodes:', this.getAllNodes(this.rootNode!));
                    }
                }
            } else {
                console.log('No text element found in node group');
            }
        } else {
            console.log('No node group found, checking if editing...');
            if (this.editingNode) {
                console.log('Finishing editing');
                this.finishEditing();
            } else {
                console.log('No editing in progress, clicked on empty area');
            }
        }
    }

    // 处理SVG双击事件
    private handleSVGDoubleClick(evt: MouseEvent) {
        const target = evt.target as Element;
        const nodeGroup = target.closest('g[data-depth]');

        if (nodeGroup) {
            const textElement = nodeGroup.querySelector('text');
            if (textElement) {
                const nodeContent = textElement.textContent?.trim();
                if (nodeContent) {
                    const node = this.findNodeByContent(this.rootNode!, nodeContent);
                    if (node) {
                        this.startEditing(node);
                    }
                }
            }
        }
    }

    // 根据内容查找节点
    private findNodeByContent(root: MindMapNode, content: string): MindMapNode | null {
        if (root.content === content) return root;
        for (const child of root.children) {
            const found = this.findNodeByContent(child, content);
            if (found) return found;
        }
        return null;
    }

    // 高亮选中的节点
    private highlightSelectedNode() {
        if (!this.selectedNode || !this.mindmap) return;

        // 移除之前的高亮
        const svg = document.querySelector('.mindmap-container svg');
        if (svg) {
            const selectedElements = svg.querySelectorAll('.selected-node');
            for (let i = 0; i < selectedElements.length; i++) {
                selectedElements[i].classList.remove('selected-node');
            }

            // 查找并高亮当前选中的节点
            const textElements = svg.querySelectorAll('text');
            for (let i = 0; i < textElements.length; i++) {
                const textEl = textElements[i];
                if (textEl.textContent?.trim() === this.selectedNode!.content) {
                    const nodeGroup = textEl.closest('g');
                    if (nodeGroup) {
                        nodeGroup.classList.add('selected-node');
                    }
                }
            }
        }
    }

    // 将节点树转换为Markdown格式
    private mindmapNodeToMarkdown(node: MindMapNode, depth = 0): string {
        const indent = '  '.repeat(depth);
        let markdown = `${indent}- ${node.content}\n`;
        if (node.children) {
            for (const child of node.children) {
                markdown += this.mindmapNodeToMarkdown(child, depth + 1);
            }
        }
        return markdown;
    }

    onunload() {
        this.mindmap = null;
    }
}

// 思维导图视图类
class MindMapView extends ItemView {
    plugin: MindMapPlugin;

    constructor(leaf: WorkspaceLeaf, plugin: MindMapPlugin) {
        super(leaf);
        this.plugin = plugin;
    }

    getViewType() {
        return MIND_MAP_VIEW_TYPE;
    }

    getDisplayText() {
        return "思维导图";
    }

    getState(): Record<string, unknown> {
        return {
            type: MIND_MAP_VIEW_TYPE,
            data: this.plugin.getRootNode()
        };
    }

    async setState(state: Record<string, unknown>, _result: ViewStateResult) {
        const data = state.data as unknown;
        if (this.isMindMapNode(data)) {
            await this.plugin.loadData(data);
        }
    }

    private isMindMapNode(data: unknown): data is MindMapNode {
        if (!data || typeof data !== 'object') return false;
        
        const node = data as Partial<MindMapNode>;
        return (
            typeof node.id === 'string' &&
            typeof node.content === 'string' &&
            Array.isArray(node.children) &&
            node.children.every(child => this.isMindMapNode(child))
        );
    }

    async onOpen() {
        const container = this.containerEl.children[1];
        container.empty();
        const mindmapContainer = container.createDiv('mindmap-container');

        // 设置容器样式，确保有明确的尺寸
        mindmapContainer.style.width = '100%';
        mindmapContainer.style.height = '100%';
        mindmapContainer.style.minHeight = '400px';
        mindmapContainer.style.position = 'relative';
        mindmapContainer.style.overflow = 'hidden';

        console.log('MindMap view opened, container created');

        // 等待容器渲染完成
        setTimeout(async () => {
            // 从状态中加载数据
            const state = this.getState();
            if (state.data && this.isMindMapNode(state.data)) {
                await this.plugin.loadData(state.data);
            } else if (this.plugin.getRootNode()) {
                // 如果插件已有根节点，渲染它
                this.plugin.renderMindMap();
            } else {
                // 创建默认的思维导图
                await this.plugin.createNewMindMap();
            }
        }, 100);
    }

    async onClose() {
        // Future: clean up logic
    }
}
