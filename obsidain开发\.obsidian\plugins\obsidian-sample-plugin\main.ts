import { <PERSON><PERSON>, Editor, ItemView, Plugin, PluginSettingTab, Setting, WorkspaceLeaf, ViewStateResult } from 'obsidian';
import { Transformer } from 'markmap-lib';
import { Markmap } from 'markmap-view';
import * as d3 from 'd3';

// 定义思维导图节点接口
interface MindMapNode {
    id: string;
    content: string;
    children: MindMapNode[];
    parent?: MindMapNode;
    isSelected?: boolean;
    isExpanded?: boolean;
}

// 定义视图状态接口
interface MindMapViewState {
    [key: string]: unknown;
    data?: MindMapNode;
    selectedNodeId?: string;
}

const MIND_MAP_VIEW_TYPE = "mindmap";

export default class MindMapPlugin extends Plugin {
    private transformer: Transformer;
    private mindmap: Markmap | null = null;
    private rootNode: MindMapNode | null = null;
    private currentNode: MindMapNode | null = null;
    private editingNode: MindMapNode | null = null;
    private selectedNode: MindMapNode | null = null;

    async onload() {
        // 初始化转换器
        this.transformer = new Transformer();

        // 注册视图
        this.registerView(
            MIND_MAP_VIEW_TYPE,
            (leaf: WorkspaceLeaf) => new MindMapView(leaf, this)
        );

        // 添加命令
        this.addCommand({
            id: 'create-mindmap',
            name: '创建新的思维导图',
            callback: () => this.createNewMindMap()
        });

        // 添加更多命令
        this.addCommand({
            id: 'select-parent-node',
            name: '选择父节点',
            callback: () => this.selectParentNode()
        });

        this.addCommand({
            id: 'select-first-child',
            name: '选择第一个子节点',
            callback: () => this.selectFirstChild()
        });

        this.addCommand({
            id: 'select-next-sibling',
            name: '选择下一个同级节点',
            callback: () => this.selectNextSibling()
        });

        this.addCommand({
            id: 'select-previous-sibling',
            name: '选择上一个同级节点',
            callback: () => this.selectPreviousSibling()
        });

        // 注册键盘事件
        this.registerDomEvent(document, 'keydown', (evt: KeyboardEvent) => {
            // 只在思维导图视图激活时处理键盘事件
            const activeView = this.app.workspace.getActiveViewOfType(MindMapView);
            if (!activeView || !this.selectedNode) return;

            switch(evt.key) {
                case 'Tab':
                    evt.preventDefault();
                    if (evt.shiftKey) {
                        this.createSiblingNode(this.selectedNode);
                    } else {
                        this.createChildNode(this.selectedNode);
                    }
                    break;
                case 'Enter':
                    evt.preventDefault();
                    if (this.editingNode) {
                        this.finishEditing();
                    } else {
                        this.startEditing(this.selectedNode);
                    }
                    break;
                case 'Delete':
                case 'Backspace':
                    if (!this.editingNode && evt.ctrlKey) {
                        evt.preventDefault();
                        this.deleteNode(this.selectedNode);
                    }
                    break;
                case 'ArrowUp':
                    evt.preventDefault();
                    this.selectPreviousSibling();
                    break;
                case 'ArrowDown':
                    evt.preventDefault();
                    this.selectNextSibling();
                    break;
                case 'ArrowLeft':
                    evt.preventDefault();
                    if (this.selectedNode && this.selectedNode.isExpanded) {
                        this.collapseNode(this.selectedNode);
                    } else {
                        this.selectParentNode();
                    }
                    break;
                case 'ArrowRight':
                    evt.preventDefault();
                    if (this.selectedNode && !this.selectedNode.isExpanded) {
                        this.expandNode(this.selectedNode);
                    } else {
                        this.selectFirstChild();
                    }
                    break;
                case 'Escape':
                    if (this.editingNode) {
                        evt.preventDefault();
                        this.cancelEditing();
                    }
                    break;
            }
        });
    }

    // 处理节点选中
    private selectNode(node: MindMapNode | null) {
        if (this.selectedNode) {
            this.selectedNode.isSelected = false;
        }

        this.selectedNode = node;
        if (node) {
            node.isSelected = true;

            // 高亮选中的节点
            this.highlightSelectedNode();
        }
    }

    // 选择父节点
    private selectParentNode() {
        if (this.selectedNode?.parent) {
            this.selectNode(this.selectedNode.parent);
        }
    }

    // 选择第一个子节点
    private selectFirstChild() {
        if (this.selectedNode && this.selectedNode.children && this.selectedNode.children.length > 0) {
            this.selectNode(this.selectedNode.children[0]);
        }
    }

    // 选择下一个同级节点
    private selectNextSibling() {
        if (!this.selectedNode?.parent) return;
        
        const siblings = this.selectedNode.parent.children;
        const currentIndex = siblings.indexOf(this.selectedNode);
        if (currentIndex < siblings.length - 1) {
            this.selectNode(siblings[currentIndex + 1]);
        }
    }

    // 选择上一个同级节点
    private selectPreviousSibling() {
        if (!this.selectedNode?.parent) return;
        
        const siblings = this.selectedNode.parent.children;
        const currentIndex = siblings.indexOf(this.selectedNode);
        if (currentIndex > 0) {
            this.selectNode(siblings[currentIndex - 1]);
        }
    }

    // 展开节点
    private expandNode(node: MindMapNode) {
        node.isExpanded = true;
        this.renderMindMap();
    }

    // 折叠节点
    private collapseNode(node: MindMapNode) {
        node.isExpanded = false;
        this.renderMindMap();
    }

    // 删除节点
    private deleteNode(node: MindMapNode) {
        if (!node.parent || node === this.rootNode) return;
        
        const siblings = node.parent.children;
        const index = siblings.indexOf(node);
        siblings.splice(index, 1);
        
        // 选择相邻节点或父节点
        if (siblings.length > 0) {
            this.selectNode(siblings[Math.min(index, siblings.length - 1)]);
        } else {
            this.selectNode(node.parent);
        }
        
        this.saveData();
    }



    // 从 DOM 元素中提取节点 ID
    private findNodeIdFromElement(element: Element): string | null {
        // markmap 会为每个节点生成唯一的 ID
        const transform = element.getAttribute('transform');
        if (!transform) return null;
        
        // 遍历所有节点找到匹配的
        const allNodes = this.getAllNodes(this.rootNode!);
        for (const node of allNodes) {
            if (element.textContent?.includes(node.content)) {
                return node.id;
            }
        }
        return null;
    }

    // 获取所有节点
    private getAllNodes(root: MindMapNode): MindMapNode[] {
        const nodes: MindMapNode[] = [root];
        for (const child of root.children) {
            nodes.push(...this.getAllNodes(child));
        }
        return nodes;
    }

    // 开始编辑节点
    private startEditing(node: MindMapNode) {
        if (this.editingNode === node) return;

        this.finishEditing();
        this.editingNode = node;

        // 查找包含该节点内容的文本元素
        const svg = document.querySelector('.mindmap-container svg');
        if (!svg) return;

        const textElements = svg.querySelectorAll('text');
        let targetTextElement: SVGTextElement | null = null;

        for (let i = 0; i < textElements.length; i++) {
            const textEl = textElements[i] as SVGTextElement;
            if (textEl.textContent?.trim() === node.content) {
                targetTextElement = textEl;
                break;
            }
        }

        if (!targetTextElement) return;

        const input = document.createElement('input');
        input.value = node.content;
        input.className = 'mindmap-node-input';

        const bbox = targetTextElement.getBBox();
        const point = this.getSVGElementPosition(targetTextElement);

        Object.assign(input.style, {
            position: 'absolute',
            left: `${point.x}px`,
            top: `${point.y - bbox.height/2}px`,
            width: `${Math.max(bbox.width + 40, 100)}px`,
            height: `${bbox.height + 8}px`,
            zIndex: '1000',
            fontSize: '14px',
            fontFamily: 'var(--font-text)'
        });

        document.body.appendChild(input);
        input.focus();
        input.select();

        input.addEventListener('blur', () => {
            this.finishEditing();
        });

        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.finishEditing();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                this.cancelEditing();
            }
        });
    }

    // 获取 SVG 元素的页面坐标
    private getSVGElementPosition(element: SVGTextElement) {
        const svg = element.ownerSVGElement!;
        const point = svg.createSVGPoint();
        const bbox = element.getBBox();
        point.x = bbox.x;
        point.y = bbox.y + bbox.height;
        
        // 转换为页面坐标
        const ctm = element.getScreenCTM();
        if (ctm) {
            const globalPoint = point.matrixTransform(ctm);
            return { x: globalPoint.x, y: globalPoint.y };
        }
        return { x: 0, y: 0 };
    }

    // 完成编辑
    private finishEditing() {
        const input = document.querySelector('.mindmap-node-input');
        if (input && this.editingNode) {
            this.editingNode.content = (input as HTMLInputElement).value;
            input.remove();
            this.editingNode = null;
            this.renderMindMap();
            this.saveData(); // 保存数据
        }
    }

    // 取消编辑
    private cancelEditing() {
        const input = document.querySelector('.mindmap-node-input');
        if (input && this.editingNode) {
            input.remove();
            this.editingNode = null;
        }
    }

    // 创建新的思维导图
    async createNewMindMap() {
        this.rootNode = {
            id: 'root',
            content: '中心主题',
            children: [],
            isExpanded: true
        };

        const leaf = this.app.workspace.getLeaf(true);
        await leaf.setViewState({
            type: MIND_MAP_VIEW_TYPE,
            state: { data: this.rootNode }
        });

        this.app.workspace.revealLeaf(leaf);

        // 选中根节点
        this.selectNode(this.rootNode);
        this.saveData(); // 保存数据
    }

    // 保存数据
    saveData(): Promise<void> {
        if (!this.rootNode) return Promise.resolve();
        
        const view = this.app.workspace.getActiveViewOfType(MindMapView);
        if (view) {
            return view.leaf.setViewState({
                type: MIND_MAP_VIEW_TYPE,
                state: { data: this.rootNode }
            });
        }
        return Promise.resolve();
    }

    // 加载数据
    loadData(): Promise<any>;
    loadData(data: MindMapNode): Promise<void>;
    async loadData(data?: MindMapNode): Promise<any> {
        if (data) {
            this.rootNode = this.reconstructNode(data);
            this.currentNode = this.rootNode;
            this.renderMindMap();
        }
    }

    // 获取根节点数据
    getRootNode(): MindMapNode | null {
        return this.rootNode;
    }

    // 重建节点树（恢复父节点引用）
    private reconstructNode(node: MindMapNode, parent: MindMapNode | undefined = undefined): MindMapNode {
        const newNode: MindMapNode = {
            id: node.id,
            content: node.content,
            children: [],
            parent: parent
        };
        
        for (const child of node.children) {
            newNode.children.push(this.reconstructNode(child, newNode));
        }
        
        return newNode;
    }



    // 创建同级节点
    private createSiblingNode(node: MindMapNode) {
        if (!node.parent) return; // 根节点不能创建同级节点

        const newNode: MindMapNode = {
            id: Date.now().toString(),
            content: '新节点',
            children: [],
            parent: node.parent,
            isExpanded: true
        };

        const index = node.parent.children.indexOf(node);
        node.parent.children.splice(index + 1, 0, newNode);

        this.renderMindMap();
        this.selectNode(newNode);
        this.saveData(); // 保存数据
    }

    // 创建父节点
    private createParentNode(node: MindMapNode) {
        const newNode: MindMapNode = {
            id: Date.now().toString(),
            content: '新父节点',
            children: [node]
        };

        if (node === this.rootNode) {
            this.rootNode = newNode;
        } else if (node.parent) {
            const parentChildren = node.parent.children;
            const index = parentChildren.indexOf(node);
            parentChildren[index] = newNode;
            newNode.parent = node.parent;
        }

        node.parent = newNode;
        this.currentNode = newNode;
        this.renderMindMap();
        this.saveData(); // 保存数据
    }

    // 创建子节点
    private createChildNode(parentNode: MindMapNode) {
        const newNode: MindMapNode = {
            id: Date.now().toString(),
            content: '新节点',
            children: [],
            parent: parentNode,
            isExpanded: true
        };

        parentNode.children.push(newNode);
        parentNode.isExpanded = true; // 确保父节点展开

        this.renderMindMap();
        this.selectNode(newNode);
        this.saveData(); // 保存数据
    }

    // 查找节点
    private findNode(root: MindMapNode, id: string): MindMapNode | null {
        if (root.id === id) return root;
        for (const child of root.children) {
            const found = this.findNode(child, id);
            if (found) return found;
        }
        return null;
    }

    // 渲染思维导图
    renderMindMap() {
        const view = this.app.workspace.getActiveViewOfType(MindMapView);
        if (!view || !this.rootNode) {
            return;
        }

        const container = view.containerEl.querySelector('.mindmap-container');
        if (!container) return;

        // 清空容器
        container.innerHTML = '';

        // 创建SVG元素
        const svg = d3.select(container)
            .append('svg')
            .attr('width', '100%')
            .attr('height', '100%');

        // 使用markmap渲染
        this.mindmap = Markmap.create(svg.node() as SVGSVGElement, {
            autoFit: true,
            duration: 300,
        });

        // 转换数据格式并更新
        const markdown = this.mindmapNodeToMarkdown(this.rootNode!);
        const { root: data } = this.transformer.transform(markdown);

        // 为每个节点添加自定义ID
        this.addCustomNodeIds(data, this.rootNode!);

        this.mindmap.setData(data);

        // 设置事件监听器
        this.setupEventListeners(container);

        // 如果有选中的节点，高亮显示
        if (this.selectedNode) {
            this.highlightSelectedNode();
        }
    }

    // 为markmap数据添加自定义ID
    private addCustomNodeIds(markmapNode: any, mindmapNode: MindMapNode) {
        markmapNode.customId = mindmapNode.id;
        if (markmapNode.children && mindmapNode.children) {
            for (let i = 0; i < markmapNode.children.length; i++) {
                if (mindmapNode.children[i]) {
                    this.addCustomNodeIds(markmapNode.children[i], mindmapNode.children[i]);
                }
            }
        }
    }

    // 设置事件监听器
    private setupEventListeners(container: Element) {
        // 移除旧的事件监听器
        const svg = container.querySelector('svg');
        if (!svg) return;

        // 添加点击事件监听器
        svg.addEventListener('click', (evt: MouseEvent) => {
            this.handleSVGClick(evt);
        });

        // 添加双击事件监听器
        svg.addEventListener('dblclick', (evt: MouseEvent) => {
            this.handleSVGDoubleClick(evt);
        });
    }

    // 处理SVG点击事件
    private handleSVGClick(evt: MouseEvent) {
        const target = evt.target as Element;
        const nodeGroup = target.closest('g[data-depth]');

        if (nodeGroup) {
            const textElement = nodeGroup.querySelector('text');
            if (textElement) {
                const nodeContent = textElement.textContent?.trim();
                console.log('Clicked node content:', nodeContent);
                if (nodeContent) {
                    const node = this.findNodeByContent(this.rootNode!, nodeContent);
                    if (node) {
                        console.log('Found node:', node);
                        this.selectNode(node);
                    } else {
                        console.log('Node not found for content:', nodeContent);
                    }
                }
            }
        } else if (this.editingNode) {
            // 点击空白处完成编辑
            this.finishEditing();
        }
    }

    // 处理SVG双击事件
    private handleSVGDoubleClick(evt: MouseEvent) {
        const target = evt.target as Element;
        const nodeGroup = target.closest('g[data-depth]');

        if (nodeGroup) {
            const textElement = nodeGroup.querySelector('text');
            if (textElement) {
                const nodeContent = textElement.textContent?.trim();
                if (nodeContent) {
                    const node = this.findNodeByContent(this.rootNode!, nodeContent);
                    if (node) {
                        this.startEditing(node);
                    }
                }
            }
        }
    }

    // 根据内容查找节点
    private findNodeByContent(root: MindMapNode, content: string): MindMapNode | null {
        if (root.content === content) return root;
        for (const child of root.children) {
            const found = this.findNodeByContent(child, content);
            if (found) return found;
        }
        return null;
    }

    // 高亮选中的节点
    private highlightSelectedNode() {
        if (!this.selectedNode || !this.mindmap) return;

        // 移除之前的高亮
        const svg = document.querySelector('.mindmap-container svg');
        if (svg) {
            const selectedElements = svg.querySelectorAll('.selected-node');
            for (let i = 0; i < selectedElements.length; i++) {
                selectedElements[i].classList.remove('selected-node');
            }

            // 查找并高亮当前选中的节点
            const textElements = svg.querySelectorAll('text');
            for (let i = 0; i < textElements.length; i++) {
                const textEl = textElements[i];
                if (textEl.textContent?.trim() === this.selectedNode!.content) {
                    const nodeGroup = textEl.closest('g');
                    if (nodeGroup) {
                        nodeGroup.classList.add('selected-node');
                    }
                }
            }
        }
    }

    // 将节点树转换为Markdown格式
    private mindmapNodeToMarkdown(node: MindMapNode, depth = 0): string {
        const indent = '  '.repeat(depth);
        let markdown = `${indent}- ${node.content}\n`;
        if (node.children) {
            for (const child of node.children) {
                markdown += this.mindmapNodeToMarkdown(child, depth + 1);
            }
        }
        return markdown;
    }

    onunload() {
        this.mindmap = null;
    }
}

// 思维导图视图类
class MindMapView extends ItemView {
    plugin: MindMapPlugin;

    constructor(leaf: WorkspaceLeaf, plugin: MindMapPlugin) {
        super(leaf);
        this.plugin = plugin;
    }

    getViewType() {
        return MIND_MAP_VIEW_TYPE;
    }

    getDisplayText() {
        return "思维导图";
    }

    getState(): Record<string, unknown> {
        return {
            type: MIND_MAP_VIEW_TYPE,
            data: this.plugin.getRootNode()
        };
    }

    async setState(state: Record<string, unknown>, _result: ViewStateResult) {
        const data = state.data as unknown;
        if (this.isMindMapNode(data)) {
            await this.plugin.loadData(data);
        }
    }

    private isMindMapNode(data: unknown): data is MindMapNode {
        if (!data || typeof data !== 'object') return false;
        
        const node = data as Partial<MindMapNode>;
        return (
            typeof node.id === 'string' &&
            typeof node.content === 'string' &&
            Array.isArray(node.children) &&
            node.children.every(child => this.isMindMapNode(child))
        );
    }

    async onOpen() {
        const container = this.containerEl.children[1];
        container.empty();
        const mindmapContainer = container.createDiv('mindmap-container');
        mindmapContainer.style.width = '100%';
        mindmapContainer.style.height = '100%';

        // 从状态中加载数据
        const state = this.getState();
        if (state.data && this.isMindMapNode(state.data)) {
            await this.plugin.loadData(state.data);
        } else if (this.plugin.getRootNode()) {
            // 如果插件已有根节点，渲染它
            this.plugin.renderMindMap();
        } else {
            // 创建默认的思维导图
            await this.plugin.createNewMindMap();
        }
    }

    async onClose() {
        // Future: clean up logic
    }
}
