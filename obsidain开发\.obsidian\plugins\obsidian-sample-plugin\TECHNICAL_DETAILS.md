# 技术实现详解

## 🏗️ 核心技术架构

### 1. 插件架构设计

```typescript
// 插件主类继承 Obsidian Plugin
export default class MindMapPlugin extends Plugin {
    // 核心数据结构
    private transformer: Transformer;           // Markmap转换器
    private mindmap: Markmap | null = null;     // 思维导图实例
    private rootNode: MindMapNode | null = null; // 根节点数据
    private selectedNode: MindMapNode | null = null; // 当前选中节点
}
```

### 2. 数据结构设计

#### 思维导图节点结构
```typescript
interface MindMapNode {
    id: string;                    // 唯一标识符
    content: string;               // 节点内容
    children: MindMapNode[];       // 子节点数组
    parent?: MindMapNode;          // 父节点引用
    isSelected?: boolean;          // 选中状态
    isExpanded?: boolean;          // 展开状态
}
```

#### 视图状态管理
```typescript
interface MindMapViewState {
    [key: string]: unknown;
    data?: MindMapNode;           // 思维导图数据
    selectedNodeId?: string;      // 选中节点ID
    sourceFile?: string;          // 源文件路径
}
```

## 🔄 核心功能实现

### 1. Markdown 解析引擎

#### 解析算法
```typescript
parseMarkdownToMindMap(content: string, title: string): MindMapNode | null {
    const lines = content.split('\n');
    let rootNode: MindMapNode | null = null;
    const nodeStack: NodeStackItem[] = [];
    
    for (const line of lines) {
        // 解析标题 (# ## ### 等)
        const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);
        if (headingMatch) {
            const level = headingMatch[1].length;
            const content = headingMatch[2];
            // 创建节点并处理层级关系
        }
        
        // 解析列表项 (- * +)
        const listMatch = line.match(/^[\-\*\+]\s+(.+)$/);
        if (listMatch) {
            // 处理列表项节点
        }
    }
    
    return rootNode;
}
```

#### 层级关系处理
```typescript
interface NodeStackItem {
    node: MindMapNode;
    level: number;
}

// 使用栈结构维护层级关系
const nodeStack: NodeStackItem[] = [];

// 处理新节点的层级插入
while (nodeStack.length > 0 && nodeStack[nodeStack.length - 1].level >= level) {
    nodeStack.pop();
}
```

### 2. 思维导图渲染系统

#### D3.js + Markmap 渲染流程
```typescript
async renderMindMap(): Promise<void> {
    // 1. 容器尺寸计算
    const { width, height } = this.calculateContainerSize(container);
    
    // 2. 创建SVG元素
    const svg = d3.select(container)
        .append('svg')
        .attr('viewBox', `0 0 ${width} ${height}`)
        .attr('preserveAspectRatio', 'xMidYMid meet');
    
    // 3. 创建Markmap实例
    this.mindmap = Markmap.create(svg.node() as SVGSVGElement, {
        autoFit: true,
        duration: 300,
        maxWidth: Math.max(width - 100, 200),
        initialExpandLevel: 3,
        spacingVertical: 10,
        spacingHorizontal: 80,
        paddingX: 20,
    });
    
    // 4. 数据转换和渲染
    const markdown = this.mindmapNodeToMarkdown(this.rootNode!);
    const { root: data } = this.transformer.transform(markdown);
    this.mindmap.setData(data);
}
```

#### 响应式布局实现
```typescript
// 容器尺寸自适应
private calculateContainerSize(container: HTMLElement) {
    const containerRect = container.getBoundingClientRect();
    let width = Math.max(containerRect.width - 40, 600);  // 最小600px
    let height = Math.max(containerRect.height - 40, 400); // 最小400px
    return { width, height };
}

// SVG响应式配置
.attr('viewBox', `0 0 ${width} ${height}`)
.attr('preserveAspectRatio', 'xMidYMid meet')
.style('width', '100%')
.style('height', '100%')
```

### 3. 双向同步机制

#### 文件监听器
```typescript
// 设置文件监听
setupFileWatcher(filePath: string): void {
    this.fileWatcher = this.app.vault.on('modify', async (file) => {
        if (file.path === this.sourceFilePath) {
            await this.updateFromSourceFile(file);
        }
    });
}

// 文件变更处理
private async updateFromSourceFile(file: any): Promise<void> {
    const content = await this.app.vault.read(file);
    const mindMapData = this.parseMarkdownToMindMap(content, file.basename);
    
    if (mindMapData) {
        this.plugin.updateMindMapData(mindMapData);
        await this.refreshView();
    }
}
```

#### 防抖优化
```typescript
// 延迟配置
const TIMING_CONFIG = {
    RENDER_DELAY: 50,        // 渲染延迟
    HIGHLIGHT_DELAY: 100,    // 高亮延迟
    FILE_SYNC_DELAY: 200,    // 文件同步延迟
};

// 防抖实现
setTimeout(() => {
    this.renderMindMap().catch(console.error);
}, TIMING_CONFIG.RENDER_DELAY);
```

### 4. 交互系统实现

#### 事件委托机制
```typescript
// 统一事件处理
setupEventListeners(container: HTMLElement): void {
    container.addEventListener('click', (event) => {
        const target = event.target as Element;
        const nodeElement = target.closest('[data-mindmap-id]');
        
        if (nodeElement) {
            const nodeId = nodeElement.getAttribute('data-mindmap-id');
            this.handleNodeClick(nodeId);
        }
    });
    
    container.addEventListener('dblclick', (event) => {
        // 双击编辑处理
        this.handleNodeDoubleClick(nodeId);
    });
}
```

#### 键盘快捷键系统
```typescript
// 全局快捷键注册
this.addCommand({
    id: 'toggle-mindmap-markdown',
    name: '切换思维导图/Markdown视图',
    hotkeys: [{ modifiers: ['Ctrl'], key: 'M' }],
    callback: () => this.toggleMindMapMarkdown()
});

// 思维导图内快捷键
this.registerDomEvent(document, 'keydown', (evt: KeyboardEvent) => {
    if (evt.ctrlKey && evt.key === 'Enter') {
        this.addChildNode();
    }
    if (evt.key === 'Enter') {
        this.addSiblingNode();
    }
    if (evt.key === 'Delete') {
        this.deleteSelectedNode();
    }
});
```

## 🎨 样式系统设计

### 1. CSS变量系统
```css
/* 使用Obsidian主题变量 */
.mindmap-container {
    background: var(--background-primary);
    color: var(--text-normal);
    border: 1px solid var(--background-modifier-border);
}

.mindmap-node-input {
    background: var(--background-primary);
    border: 2px solid var(--interactive-accent);
    color: var(--text-normal);
}
```

### 2. 响应式布局
```css
/* 容器自适应 */
.mindmap-container {
    width: 100%;
    height: calc(100vh - 100px);
    padding: 20px;
    overflow: auto;
    position: relative;
}

/* SVG响应式 */
.mindmap-container svg {
    width: 100%;
    height: 100%;
    overflow: visible;
}
```

### 3. 动画效果
```css
/* 节点过渡动画 */
.markmap-node {
    transition: all 0.3s ease;
}

/* 选中状态高亮 */
.selected-node {
    stroke: var(--interactive-accent);
    stroke-width: 3px;
    filter: drop-shadow(0 0 5px var(--interactive-accent));
}
```

## 🔧 构建系统

### 1. esbuild 配置
```javascript
const context = await esbuild.context({
    entryPoints: ["main.ts"],           // 入口文件
    bundle: true,                       // 打包模式
    external: ["obsidian", "electron"], // 外部依赖
    format: "cjs",                      // CommonJS格式
    target: "es2018",                   // 目标ES版本
    sourcemap: prod ? false : "inline", // 开发模式源码映射
    minify: prod,                       // 生产模式压缩
});
```

### 2. TypeScript 配置
```json
{
    "compilerOptions": {
        "target": "ES6",
        "module": "ESNext",
        "moduleResolution": "node",
        "strictNullChecks": true,
        "isolatedModules": true,
        "lib": ["DOM", "ES5", "ES6", "ES7"],
        "types": ["node", "d3"]
    }
}
```

## 📊 性能优化策略

### 1. 渲染优化
- **异步渲染**：使用 `requestAnimationFrame` 避免阻塞
- **增量更新**：只更新变化的节点
- **视口裁剪**：只渲染可见区域的节点

### 2. 内存管理
- **事件清理**：组件销毁时移除事件监听器
- **对象池**：复用节点对象减少GC压力
- **弱引用**：避免循环引用导致内存泄漏

### 3. 数据优化
- **懒加载**：大型思维导图按需加载子树
- **缓存机制**：缓存解析结果避免重复计算
- **压缩存储**：优化数据结构减少内存占用
