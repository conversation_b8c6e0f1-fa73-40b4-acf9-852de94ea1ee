# 技术架构文档

## 🏗️ 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    Obsidian 思维导图插件                      │
├─────────────────────────────────────────────────────────────┤
│  用户界面层 (UI Layer)                                       │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │   Markdown      │  │   MindMap       │                   │
│  │   Editor        │  │   Viewer        │                   │
│  │                 │  │                 │                   │
│  └─────────────────┘  └─────────────────┘                   │
├─────────────────────────────────────────────────────────────┤
│  控制层 (Control Layer)                                      │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              MindMapPlugin                              │ │
│  │  • 视图切换管理    • 命令注册    • 事件处理              │ │
│  │  • 状态管理       • 快捷键      • 生命周期              │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  业务逻辑层 (Business Layer)                                 │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │   Markdown      │  │   Node          │                   │
│  │   Parser        │  │   Operations    │                   │
│  │                 │  │                 │                   │
│  └─────────────────┘  └─────────────────┘                   │
├─────────────────────────────────────────────────────────────┤
│  渲染层 (Render Layer)                                       │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │    Markmap      │  │      D3.js      │                   │
│  │   Renderer      │  │   Visualization │                   │
│  │                 │  │                 │                   │
│  └─────────────────┘  └─────────────────┘                   │
├─────────────────────────────────────────────────────────────┤
│  数据层 (Data Layer)                                         │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │   File System   │  │   Memory Store  │                   │
│  │   (.md files)   │  │  (Node Tree)    │                   │
│  │                 │  │                 │                   │
│  └─────────────────┘  └─────────────────┘                   │
└─────────────────────────────────────────────────────────────┘
```

## 🔄 数据流图

```
┌─────────────┐    解析     ┌─────────────┐    转换     ┌─────────────┐
│ Markdown    │ ────────→   │ MindMap     │ ────────→   │ Markmap     │
│ Content     │             │ Node Tree   │             │ Data        │
└─────────────┘             └─────────────┘             └─────────────┘
      ↑                           ↑                           ↓
      │                           │                           │
      │                           │                           ↓
┌─────────────┐    监听     ┌─────────────┐    渲染     ┌─────────────┐
│ File        │ ←────────   │ Event       │ ←────────   │ SVG         │
│ System      │             │ Listeners   │             │ Elements    │
└─────────────┘             └─────────────┘             └─────────────┘
```

## 🧩 核心组件

### 1. MindMapPlugin (主控制器)
```typescript
class MindMapPlugin extends Plugin {
    // 状态管理
    private rootNode: MindMapNode | null = null;
    private selectedNode: MindMapNode | null = null;
    
    // 核心方法
    async onload()                    // 插件初始化
    toggleMindMapMarkdown()           // 视图切换
    parseMarkdownToMindMap()          // 内容解析
    renderMindMap()                   // 渲染控制
}
```

### 2. MindMapView (视图管理器)
```typescript
class MindMapView extends ItemView {
    // 视图生命周期
    async onOpen()                    // 视图打开
    async onClose()                   // 视图关闭
    
    // 状态管理
    getState()                        // 获取状态
    setState()                        // 设置状态
    
    // 文件监听
    setupFileWatcher()                // 设置文件监听
}
```

### 3. 数据结构
```typescript
interface MindMapNode {
    id: string;                       // 唯一标识
    content: string;                  // 节点内容
    children: MindMapNode[];          // 子节点
    parent?: MindMapNode;             // 父节点引用
    isSelected?: boolean;             // 选中状态
    isExpanded?: boolean;             // 展开状态
}
```

## ⚡ 关键技术实现

### 1. Markdown 解析算法
```typescript
parseMarkdownToMindMap(content: string): MindMapNode | null {
    const lines = content.split('\n');
    const nodeStack: NodeStackItem[] = [];
    let rootNode: MindMapNode | null = null;
    
    for (const line of lines) {
        // 解析标题 (# ## ### 等)
        const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);
        if (headingMatch) {
            const level = headingMatch[1].length;
            const content = headingMatch[2];
            // 构建节点树...
        }
        
        // 解析列表项 (- * +)
        const listMatch = line.match(/^[\-\*\+]\s+(.+)$/);
        if (listMatch) {
            // 处理列表项...
        }
    }
    
    return rootNode;
}
```

### 2. 实时文件监听
```typescript
setupFileWatcher(filePath: string): void {
    this.fileWatcher = this.app.vault.on('modify', async (file) => {
        if (file.path === this.sourceFilePath) {
            await this.updateFromSourceFile(file);
        }
    });
}
```

### 3. 异步渲染优化
```typescript
async renderMindMap(): Promise<void> {
    // 等待容器渲染
    await new Promise(resolve => setTimeout(resolve, 50));
    
    // 使用 requestAnimationFrame 避免阻塞
    requestAnimationFrame(() => {
        this.mindmap = Markmap.create(svg.node(), options);
        this.mindmap.setData(data);
    });
}
```

## 🎯 性能优化策略

### 1. 渲染优化
- **异步渲染**：使用 `requestAnimationFrame`
- **增量更新**：只更新变化的节点
- **视口裁剪**：只渲染可见区域

### 2. 内存管理
- **事件清理**：组件销毁时清理事件监听器
- **引用管理**：避免循环引用导致内存泄漏
- **缓存策略**：合理使用缓存减少重复计算

### 3. 交互优化
- **防抖处理**：文件变化事件防抖
- **事件委托**：减少事件监听器数量
- **懒加载**：大型思维导图的懒加载

## 🔧 扩展点设计

### 1. 插件钩子
```typescript
// 节点渲染前钩子
onBeforeNodeRender(node: MindMapNode): MindMapNode

// 节点点击钩子
onNodeClick(node: MindMapNode): void

// 数据变化钩子
onDataChange(oldData: MindMapNode, newData: MindMapNode): void
```

### 2. 自定义渲染器
```typescript
interface CustomRenderer {
    render(node: MindMapNode, container: HTMLElement): void;
    update(node: MindMapNode, element: HTMLElement): void;
    destroy(element: HTMLElement): void;
}
```

### 3. 主题系统
```css
/* CSS 变量系统 */
:root {
    --mindmap-node-bg: var(--background-primary);
    --mindmap-node-text: var(--text-normal);
    --mindmap-link-color: var(--text-accent);
}
```

## 🚀 部署架构

### 1. 开发环境
```
Source Code (TypeScript) 
    ↓ (ESBuild)
Development Build (main.js)
    ↓ (Hot Reload)
Obsidian Plugin Folder
```

### 2. 生产环境
```
Source Code (TypeScript)
    ↓ (ESBuild + Minify)
Production Build (main.js)
    ↓ (Package)
Plugin Release (.zip)
    ↓ (Install)
User's Obsidian
```

## 📊 监控与调试

### 1. 日志系统
```typescript
// 分级日志
console.log('[MindMap] Info:', message);
console.warn('[MindMap] Warning:', message);
console.error('[MindMap] Error:', error);
```

### 2. 性能监控
```typescript
// 渲染性能监控
const startTime = performance.now();
await renderMindMap();
const endTime = performance.now();
console.log(`Render time: ${endTime - startTime}ms`);
```

### 3. 错误处理
```typescript
// 全局错误捕获
window.addEventListener('error', (event) => {
    console.error('[MindMap] Global Error:', event.error);
});
```

---

*技术架构文档 - 最后更新：2025年7月20日*
