# Obsidian 思维导图插件

一个功能强大的 Obsidian 插件，能够将 Markdown 文档自动转换为交互式思维导图，支持双向同步、实时预览和丰富的交互功能。

## ✨ 核心特性

- 🧠 **智能解析**：自动解析 Markdown 标题结构生成思维导图
- 🔄 **双向同步**：Markdown 文件与思维导图实时同步
- ✏️ **交互编辑**：支持节点编辑、添加、删除等操作
- ⚡ **快捷切换**：`Ctrl+M` 快速在 Markdown 和思维导图视图间切换
- 📱 **响应式设计**：自适应不同窗口大小，支持缩放和拖拽
- 🎨 **主题适配**：完美适配 Obsidian 明暗主题

## 🚀 快速开始

### 安装
1. 将插件文件夹复制到 `.obsidian/plugins/` 目录
2. 在 Obsidian 设置中启用插件
3. 重新加载 Obsidian

### 使用
1. 打开任意 Markdown 文件
2. 按 `Ctrl+M` 快捷键
3. 在右侧查看生成的思维导图

## 🛠️ 技术栈

- **前端框架**：TypeScript + Obsidian API
- **图形渲染**：D3.js + Markmap
- **构建工具**：esbuild + npm
- **样式系统**：CSS3 + CSS Variables

## 📚 文档目录

### 📖 用户文档
- **[用户使用指南](USER_GUIDE.md)** - 详细的使用说明和功能介绍
- **[项目概览](PROJECT_OVERVIEW.md)** - 项目简介和核心功能说明

### 🔧 开发文档
- **[文件结构详解](FILE_STRUCTURE.md)** - 详细的文件和目录说明
- **[技术实现详解](TECHNICAL_DETAILS.md)** - 核心技术架构和实现原理
- **[开发指南](DEVELOPMENT_GUIDE.md)** - 开发环境搭建和开发流程
- **[API 参考文档](API_REFERENCE.md)** - 完整的 API 接口文档

## 📁 项目结构

```
obsidian-sample-plugin/
├── 📄 main.ts                 # 主插件文件 (1960行)
├── 🎨 styles.css             # 样式文件 (152行)
├── 📋 manifest.json          # 插件清单
├── ⚙️ package.json           # 项目配置
├── 🔧 tsconfig.json          # TypeScript配置
├── 🏗️ esbuild.config.mjs     # 构建配置
├── 📚 src/                   # 模块化源码目录
│   ├── types/               # 类型定义
│   ├── core/                # 核心功能
│   ├── views/               # 视图组件
│   └── utils/               # 工具函数
└── 📖 docs/                 # 文档目录
    ├── PROJECT_OVERVIEW.md
    ├── FILE_STRUCTURE.md
    ├── TECHNICAL_DETAILS.md
    ├── USER_GUIDE.md
    ├── DEVELOPMENT_GUIDE.md
    └── API_REFERENCE.md
```

## 🎯 主要功能

### 1. Markdown 解析
- 支持 1-6 级标题解析
- 支持无序列表项解析
- 智能处理嵌套结构
- 保持原有格式

### 2. 思维导图渲染
- 基于 D3.js 的 SVG 渲染
- Markmap 提供专业布局算法
- 响应式设计适配不同屏幕
- 流畅的动画效果

### 3. 交互功能
- 节点点击选择
- 双击编辑内容
- 键盘快捷键支持
- 拖拽和缩放操作

### 4. 双向同步
- 文件监听器实时检测变化
- 增量更新避免全量重渲染
- 防抖机制优化性能

## 🔧 开发

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0
- TypeScript >= 4.5.0

### 开发命令
```bash
# 安装依赖
npm install

# 开发模式
npm run dev

# 构建生产版本
npm run build

# 代码检查
npm run lint
```

## 📊 代码统计

- **总代码行数**：约 2000+ 行
- **主要文件**：`main.ts` (1960行)
- **样式文件**：`styles.css` (152行)
- **配置文件**：5个
- **文档文件**：6个
- **模块化文件**：8个（开发中）

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

### 贡献指南
1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🔗 相关链接

- [Obsidian 官网](https://obsidian.md)
- [Obsidian Plugin API](https://docs.obsidian.md/Plugins)
- [D3.js 文档](https://d3js.org/)
- [Markmap 文档](https://markmap.js.org/)

## 📞 支持

如果您遇到问题或有建议，请：
1. 查看 [用户使用指南](USER_GUIDE.md)
2. 检查 [常见问题](USER_GUIDE.md#常见问题)
3. 提交 [GitHub Issue](../../issues)

---

**享受思维导图带来的高效笔记体验！** 🎉
