# 📚 文档索引

欢迎来到 Obsidian 思维导图插件的文档中心！这里包含了所有你需要了解的信息，从基本使用到深度开发。

## 📖 文档导航

### 🚀 快速开始
如果你是第一次使用这个插件，建议按以下顺序阅读：

1. **[项目概览](PROJECT_OVERVIEW.md)** - 了解插件的基本功能和特性
2. **[用户使用指南](USER_GUIDE.md)** - 学习如何使用插件的各种功能
3. **[README_MINDMAP_PLUGIN.md](README_MINDMAP_PLUGIN.md)** - 项目主页和快速入门

### 👥 用户文档

#### 📋 [项目概览](PROJECT_OVERVIEW.md)
- 项目简介和核心功能
- 技术栈介绍
- 项目结构概述
- 性能优化说明
- 未来规划

#### 📖 [用户使用指南](USER_GUIDE.md)
- 安装和配置
- 基本操作教程
- 支持的 Markdown 语法
- 交互功能详解
- 键盘快捷键
- 双向同步功能
- 视觉定制
- 常见问题解答
- 使用技巧和最佳实践

### 🔧 开发者文档

#### 🏗️ [文件结构详解](FILE_STRUCTURE.md)
- 核心文件说明
- 配置文件详解
- 模块化源码目录
- 文档文件介绍
- 文件依赖关系
- 代码统计信息

#### 💻 [技术实现详解](TECHNICAL_DETAILS.md)
- 核心技术架构
- 数据结构设计
- Markdown 解析引擎
- 思维导图渲染系统
- 双向同步机制
- 交互系统实现
- 样式系统设计
- 构建系统配置
- 性能优化策略

#### 🛠️ [开发指南](DEVELOPMENT_GUIDE.md)
- 开发环境搭建
- 项目架构设计
- 设计模式应用
- 核心功能开发
- 测试开发
- 部署和发布
- 调试技巧
- 常见问题解决
- 学习资源

#### 📚 [API 参考文档](API_REFERENCE.md)
- 插件主类 API
- 视图类 API
- 数据结构 API
- 工具函数 API
- 样式 API
- 配置 API
- 事件 API
- 错误处理 API

## 🎯 按需求查找文档

### 我想要...

#### 🆕 开始使用插件
→ [用户使用指南](USER_GUIDE.md) → [项目概览](PROJECT_OVERVIEW.md)

#### 🔧 了解插件工作原理
→ [技术实现详解](TECHNICAL_DETAILS.md) → [文件结构详解](FILE_STRUCTURE.md)

#### 💻 参与插件开发
→ [开发指南](DEVELOPMENT_GUIDE.md) → [API 参考文档](API_REFERENCE.md)

#### 🐛 解决使用问题
→ [用户使用指南 - 常见问题](USER_GUIDE.md#常见问题) → [开发指南 - 调试技巧](DEVELOPMENT_GUIDE.md#调试技巧)

#### 🎨 自定义插件样式
→ [用户使用指南 - 视觉定制](USER_GUIDE.md#视觉定制) → [API 参考文档 - 样式 API](API_REFERENCE.md#样式-api)

#### 📊 了解项目统计
→ [文件结构详解 - 代码统计](FILE_STRUCTURE.md#代码统计) → [项目概览](PROJECT_OVERVIEW.md)

## 📝 文档特色

### 🎨 丰富的视觉元素
- 📊 图表和流程图
- 🎯 代码示例
- 💡 使用技巧
- ⚠️ 注意事项
- ✅ 最佳实践

### 🔍 详细的内容覆盖
- **用户层面**：从安装到高级使用
- **开发层面**：从环境搭建到发布部署
- **技术层面**：从架构设计到实现细节
- **API层面**：从接口定义到使用示例

### 🚀 实用的指导信息
- 分步骤的操作指南
- 完整的代码示例
- 常见问题的解决方案
- 性能优化的建议

## 🔄 文档更新

### 版本信息
- **当前版本**：v1.0.0
- **最后更新**：2025-07-20
- **文档状态**：完整

### 更新日志
- **2025-07-20**：创建完整的文档体系
- **2025-07-20**：添加详细的 API 参考
- **2025-07-20**：完善用户使用指南
- **2025-07-20**：补充开发指南和技术详解

## 📞 获取帮助

### 文档问题
如果你在文档中发现错误或需要补充：
1. 检查是否有相关的其他文档
2. 查看 [常见问题](USER_GUIDE.md#常见问题)
3. 提交 Issue 或 Pull Request

### 使用问题
如果你在使用插件时遇到问题：
1. 查看 [用户使用指南](USER_GUIDE.md)
2. 检查 [技术实现详解](TECHNICAL_DETAILS.md)
3. 参考 [开发指南的调试部分](DEVELOPMENT_GUIDE.md#调试技巧)

### 开发问题
如果你在开发过程中遇到问题：
1. 查看 [开发指南](DEVELOPMENT_GUIDE.md)
2. 参考 [API 参考文档](API_REFERENCE.md)
3. 查看项目的 Issue 和 Discussion

## 🎉 开始探索

选择适合你的文档开始探索吧！

- 🆕 **新用户**：从 [用户使用指南](USER_GUIDE.md) 开始
- 🔧 **开发者**：从 [开发指南](DEVELOPMENT_GUIDE.md) 开始  
- 🧠 **技术爱好者**：从 [技术实现详解](TECHNICAL_DETAILS.md) 开始
- 📚 **API 使用者**：从 [API 参考文档](API_REFERENCE.md) 开始

---

**祝你使用愉快！如果这些文档对你有帮助，别忘了给项目一个 ⭐！**
