# 开发指南

## 🛠️ 开发环境搭建

### 前置要求
- **Node.js** >= 16.0.0
- **npm** >= 8.0.0
- **TypeScript** >= 4.5.0
- **Obsidian** >= 0.15.0

### 环境配置

1. **克隆项目**：
   ```bash
   git clone <repository-url>
   cd obsidian-mindmap-plugin
   ```

2. **安装依赖**：
   ```bash
   npm install
   ```

3. **开发模式**：
   ```bash
   npm run dev
   ```

4. **构建生产版本**：
   ```bash
   npm run build
   ```

### 开发工具配置

#### VSCode 配置
```json
// .vscode/settings.json
{
    "typescript.preferences.importModuleSpecifier": "relative",
    "typescript.suggest.autoImports": true,
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.fixAll.eslint": true
    }
}
```

#### ESLint 配置
```json
// .eslintrc.json
{
    "extends": ["@typescript-eslint/recommended"],
    "parser": "@typescript-eslint/parser",
    "plugins": ["@typescript-eslint"],
    "rules": {
        "@typescript-eslint/no-unused-vars": "warn",
        "@typescript-eslint/no-explicit-any": "warn"
    }
}
```

## 🏗️ 项目架构

### 核心模块设计

```
src/
├── types/              # 类型定义
│   ├── mindmap-node.ts # 节点类型
│   ├── view-state.ts   # 视图状态
│   └── index.ts        # 类型导出
├── core/               # 核心功能
│   ├── mindmap-plugin.ts   # 主插件类
│   ├── mindmap-parser.ts   # 解析器
│   └── mindmap-renderer.ts # 渲染器
├── views/              # 视图组件
│   └── mindmap-view.ts # 思维导图视图
├── utils/              # 工具函数
│   ├── constants.ts    # 常量定义
│   ├── node-operations.ts # 节点操作
│   └── file-sync.ts    # 文件同步
└── main.ts             # 入口文件
```

### 设计模式

#### 1. 插件模式 (Plugin Pattern)
```typescript
// 主插件类继承 Obsidian Plugin
export default class MindMapPlugin extends Plugin {
    async onload() {
        // 插件初始化逻辑
        this.registerView(MIND_MAP_VIEW_TYPE, this.createView);
        this.addCommands();
    }
    
    onunload() {
        // 清理资源
    }
}
```

#### 2. 观察者模式 (Observer Pattern)
```typescript
// 文件变更监听
this.app.vault.on('modify', (file) => {
    if (file.path === this.sourceFilePath) {
        this.updateMindMap(file);
    }
});
```

#### 3. 策略模式 (Strategy Pattern)
```typescript
// 不同的解析策略
interface ParseStrategy {
    parse(content: string): MindMapNode;
}

class HeadingParser implements ParseStrategy {
    parse(content: string): MindMapNode {
        // 标题解析逻辑
    }
}

class ListParser implements ParseStrategy {
    parse(content: string): MindMapNode {
        // 列表解析逻辑
    }
}
```

## 🔧 核心功能开发

### 1. 解析器开发

#### 标题解析器
```typescript
class HeadingParser {
    private static parseHeading(line: string): { level: number; content: string } | null {
        const match = line.match(/^(#{1,6})\s+(.+)$/);
        if (!match) return null;
        
        return {
            level: match[1].length,
            content: match[2].trim()
        };
    }
    
    static parse(content: string): MindMapNode {
        const lines = content.split('\n');
        const nodeStack: NodeStackItem[] = [];
        let rootNode: MindMapNode | null = null;
        
        for (const line of lines) {
            const heading = this.parseHeading(line.trim());
            if (heading) {
                const node = this.createNode(heading.content);
                this.insertNode(node, heading.level, nodeStack);
                
                if (!rootNode) rootNode = node;
            }
        }
        
        return rootNode;
    }
}
```

#### 列表解析器
```typescript
class ListParser {
    private static parseListItem(line: string): { content: string; indent: number } | null {
        const match = line.match(/^(\s*)([-*+])\s+(.+)$/);
        if (!match) return null;
        
        return {
            indent: match[1].length,
            content: match[3].trim()
        };
    }
    
    static parse(content: string): MindMapNode {
        // 列表解析实现
    }
}
```

### 2. 渲染器开发

#### SVG 渲染器
```typescript
class SVGRenderer {
    private svg: d3.Selection<SVGSVGElement, unknown, null, undefined>;
    private markmap: Markmap;
    
    constructor(container: HTMLElement) {
        this.svg = d3.select(container).append('svg');
        this.markmap = Markmap.create(this.svg.node()!);
    }
    
    render(data: MindMapNode): void {
        const markmapData = this.transformData(data);
        this.markmap.setData(markmapData);
    }
    
    private transformData(node: MindMapNode): any {
        return {
            content: node.content,
            children: node.children.map(child => this.transformData(child))
        };
    }
}
```

#### 事件处理器
```typescript
class EventHandler {
    private container: HTMLElement;
    private plugin: MindMapPlugin;
    
    constructor(container: HTMLElement, plugin: MindMapPlugin) {
        this.container = container;
        this.plugin = plugin;
        this.setupEventListeners();
    }
    
    private setupEventListeners(): void {
        // 节点点击事件
        this.container.addEventListener('click', this.handleNodeClick.bind(this));
        
        // 节点双击事件
        this.container.addEventListener('dblclick', this.handleNodeDoubleClick.bind(this));
        
        // 键盘事件
        document.addEventListener('keydown', this.handleKeyDown.bind(this));
    }
    
    private handleNodeClick(event: MouseEvent): void {
        const nodeElement = (event.target as Element).closest('[data-node-id]');
        if (nodeElement) {
            const nodeId = nodeElement.getAttribute('data-node-id');
            this.plugin.selectNode(nodeId);
        }
    }
}
```

### 3. 视图开发

#### 自定义视图类
```typescript
export class MindMapView extends ItemView {
    private plugin: MindMapPlugin;
    private renderer: SVGRenderer;
    private eventHandler: EventHandler;
    
    constructor(leaf: WorkspaceLeaf, plugin: MindMapPlugin) {
        super(leaf);
        this.plugin = plugin;
    }
    
    getViewType(): string {
        return MIND_MAP_VIEW_TYPE;
    }
    
    getDisplayText(): string {
        return "思维导图";
    }
    
    async onOpen(): Promise<void> {
        const container = this.containerEl.children[1];
        container.empty();
        
        const mindmapContainer = container.createDiv('mindmap-container');
        this.renderer = new SVGRenderer(mindmapContainer);
        this.eventHandler = new EventHandler(mindmapContainer, this.plugin);
    }
    
    async onClose(): Promise<void> {
        // 清理资源
        this.eventHandler?.destroy();
        this.renderer?.destroy();
    }
}
```

## 🧪 测试开发

### 单元测试

#### 解析器测试
```typescript
// tests/parser.test.ts
import { HeadingParser } from '../src/core/mindmap-parser';

describe('HeadingParser', () => {
    test('should parse simple heading', () => {
        const content = '# Main Title\n## Subtitle';
        const result = HeadingParser.parse(content);
        
        expect(result.content).toBe('Main Title');
        expect(result.children).toHaveLength(1);
        expect(result.children[0].content).toBe('Subtitle');
    });
    
    test('should handle nested headings', () => {
        const content = `
# Level 1
## Level 2
### Level 3
## Another Level 2
        `;
        
        const result = HeadingParser.parse(content);
        expect(result.children).toHaveLength(2);
    });
});
```

#### 渲染器测试
```typescript
// tests/renderer.test.ts
import { SVGRenderer } from '../src/core/mindmap-renderer';

describe('SVGRenderer', () => {
    let container: HTMLElement;
    let renderer: SVGRenderer;
    
    beforeEach(() => {
        container = document.createElement('div');
        document.body.appendChild(container);
        renderer = new SVGRenderer(container);
    });
    
    afterEach(() => {
        document.body.removeChild(container);
    });
    
    test('should create SVG element', () => {
        const svg = container.querySelector('svg');
        expect(svg).toBeTruthy();
    });
});
```

### 集成测试

#### 端到端测试
```typescript
// tests/e2e.test.ts
describe('MindMap Plugin E2E', () => {
    test('should create mindmap from markdown', async () => {
        // 模拟 Obsidian 环境
        const mockApp = createMockApp();
        const plugin = new MindMapPlugin(mockApp, manifest);
        
        await plugin.onload();
        
        // 创建测试文件
        const testContent = '# Main\n## Sub1\n## Sub2';
        const file = await mockApp.vault.create('test.md', testContent);
        
        // 触发思维导图创建
        await plugin.createMindMapPreview();
        
        // 验证结果
        const mindmapViews = mockApp.workspace.getLeavesOfType(MIND_MAP_VIEW_TYPE);
        expect(mindmapViews).toHaveLength(1);
    });
});
```

## 🚀 部署和发布

### 构建流程

#### 开发构建
```bash
# 开发模式 - 包含源码映射
npm run dev

# 监听文件变化
npm run watch
```

#### 生产构建
```bash
# 生产模式 - 压缩优化
npm run build

# 检查构建结果
npm run lint
npm run test
```

### 版本管理

#### 版本号更新
```bash
# 自动更新版本号
npm run version-bump

# 手动更新
npm version patch  # 1.0.0 -> 1.0.1
npm version minor  # 1.0.0 -> 1.1.0
npm version major  # 1.0.0 -> 2.0.0
```

#### 发布检查清单
- [ ] 所有测试通过
- [ ] 代码格式化完成
- [ ] 文档更新完成
- [ ] 版本号正确更新
- [ ] manifest.json 信息正确
- [ ] 构建文件无错误

### 插件发布

#### 社区插件发布
1. **准备发布包**：
   ```bash
   npm run build
   zip -r mindmap-plugin.zip main.js manifest.json styles.css
   ```

2. **提交到社区**：
   - Fork obsidian-releases 仓库
   - 添加插件信息到 community-plugins.json
   - 创建 Pull Request

3. **版本更新**：
   - 更新 versions.json
   - 创建 GitHub Release
   - 上传构建文件

## 🐛 调试技巧

### 开发者工具

#### 控制台调试
```typescript
// 添加调试日志
console.log('MindMap data:', mindmapData);
console.group('Parsing process');
console.time('parse-time');
// ... 解析逻辑
console.timeEnd('parse-time');
console.groupEnd();
```

#### 性能分析
```typescript
// 性能监控
performance.mark('render-start');
await this.renderMindMap();
performance.mark('render-end');
performance.measure('render-time', 'render-start', 'render-end');
```

### 常见问题解决

#### 1. 内存泄漏
```typescript
// 正确清理事件监听器
onunload() {
    if (this.fileWatcher) {
        this.app.vault.offref(this.fileWatcher);
    }
    
    if (this.mindmap) {
        this.mindmap.destroy();
    }
}
```

#### 2. 异步问题
```typescript
// 使用 Promise 处理异步操作
async renderMindMap(): Promise<void> {
    try {
        await this.waitForContainer();
        await this.loadData();
        await this.render();
    } catch (error) {
        console.error('Render failed:', error);
        this.showErrorMessage(error);
    }
}
```

#### 3. 类型安全
```typescript
// 使用类型守卫
function isMindMapNode(obj: any): obj is MindMapNode {
    return obj && 
           typeof obj.id === 'string' &&
           typeof obj.content === 'string' &&
           Array.isArray(obj.children);
}
```

## 📚 学习资源

### 官方文档
- [Obsidian Plugin API](https://docs.obsidian.md/Plugins/Getting+started/Build+a+plugin)
- [D3.js Documentation](https://d3js.org/)
- [Markmap Documentation](https://markmap.js.org/)

### 社区资源
- [Obsidian Plugin Examples](https://github.com/obsidianmd/obsidian-sample-plugin)
- [Plugin Development Discord](https://discord.gg/obsidianmd)
- [Community Plugins](https://github.com/obsidianmd/obsidian-releases)

### 推荐工具
- **TypeScript Playground** - 在线测试 TS 代码
- **D3 Observable** - D3.js 可视化实验
- **Obsidian Plugin Template** - 插件开发模板
