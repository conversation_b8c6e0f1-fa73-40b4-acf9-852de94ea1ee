/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var z1=Object.create;var Ln=Object.defineProperty;var I1=Object.getOwnPropertyDescriptor;var D1=Object.getOwnPropertyNames;var B1=Object.getPrototypeOf,O1=Object.prototype.hasOwnProperty;var os=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),R1=(e,t)=>{for(var r in t)Ln(e,r,{get:t[r],enumerable:!0})},ls=(e,t,r,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of D1(t))!O1.call(e,s)&&s!==r&&Ln(e,s,{get:()=>t[s],enumerable:!(o=I1(t,s))||o.enumerable});return e};var F1=(e,t,r)=>(r=e!=null?z1(B1(e)):{},ls(t||!e||!e.__esModule?Ln(r,"default",{value:e,enumerable:!0}):r,e)),L1=e=>ls(Ln({},"__esModule",{value:!0}),e);var Ls=os((Yr,Di)=>{(function(t,r){typeof Yr=="object"&&typeof Di=="object"?Di.exports=r():typeof define=="function"&&define.amd?define([],r):typeof Yr=="object"?Yr.katex=r():t.katex=r()})(typeof self!="undefined"?self:Yr,function(){return function(){"use strict";var e={};(function(){e.d=function(i,n){for(var l in n)e.o(n,l)&&!e.o(i,l)&&Object.defineProperty(i,l,{enumerable:!0,get:n[l]})}})(),function(){e.o=function(i,n){return Object.prototype.hasOwnProperty.call(i,n)}}();var t={};e.d(t,{default:function(){return T1}});class r{constructor(n,l){this.name=void 0,this.position=void 0,this.length=void 0,this.rawMessage=void 0;let a="KaTeX parse error: "+n,f,d,x=l&&l.loc;if(x&&x.start<=x.end){let A=x.lexer.input;f=x.start,d=x.end,f===A.length?a+=" at end of input: ":a+=" at position "+(f+1)+": ";let C=A.slice(f,d).replace(/[^]/g,"$&\u0332"),I;f>15?I="\u2026"+A.slice(f-15,f):I=A.slice(0,f);let B;d+15<A.length?B=A.slice(d,d+15)+"\u2026":B=A.slice(d),a+=I+C+B}let w=new Error(a);return w.name="ParseError",w.__proto__=r.prototype,w.position=f,f!=null&&d!=null&&(w.length=d-f),w.rawMessage=n,w}}r.prototype.__proto__=Error.prototype;var o=r;let s=function(i,n){return i.indexOf(n)!==-1},u=function(i,n){return i===void 0?n:i},c=/([A-Z])/g,h=function(i){return i.replace(c,"-$1").toLowerCase()},g={"&":"&amp;",">":"&gt;","<":"&lt;",'"':"&quot;","'":"&#x27;"},b=/[&><"']/g;function y(i){return String(i).replace(b,n=>g[n])}let S=function(i){return i.type==="ordgroup"||i.type==="color"?i.body.length===1?S(i.body[0]):i:i.type==="font"?S(i.body):i},T=function(i){let n=S(i);return n.type==="mathord"||n.type==="textord"||n.type==="atom"},N=function(i){if(!i)throw new Error("Expected non-null, but got "+String(i));return i};var z={contains:s,deflt:u,escape:y,hyphenate:h,getBaseElem:S,isCharacterBox:T,protocolFromUrl:function(i){let n=/^[\x00-\x20]*([^\\/#?]*?)(:|&#0*58|&#x0*3a|&colon)/i.exec(i);return n?n[2]!==":"||!/^[a-zA-Z][a-zA-Z0-9+\-.]*$/.test(n[1])?null:n[1].toLowerCase():"_relative"}};let P={displayMode:{type:"boolean",description:"Render math in display mode, which puts the math in display style (so \\int and \\sum are large, for example), and centers the math on the page on its own line.",cli:"-d, --display-mode"},output:{type:{enum:["htmlAndMathml","html","mathml"]},description:"Determines the markup language of the output.",cli:"-F, --format <type>"},leqno:{type:"boolean",description:"Render display math in leqno style (left-justified tags)."},fleqn:{type:"boolean",description:"Render display math flush left."},throwOnError:{type:"boolean",default:!0,cli:"-t, --no-throw-on-error",cliDescription:"Render errors (in the color given by --error-color) instead of throwing a ParseError exception when encountering an error."},errorColor:{type:"string",default:"#cc0000",cli:"-c, --error-color <color>",cliDescription:"A color string given in the format 'rgb' or 'rrggbb' (no #). This option determines the color of errors rendered by the -t option.",cliProcessor:i=>"#"+i},macros:{type:"object",cli:"-m, --macro <def>",cliDescription:"Define custom macro of the form '\\foo:expansion' (use multiple -m arguments for multiple macros).",cliDefault:[],cliProcessor:(i,n)=>(n.push(i),n)},minRuleThickness:{type:"number",description:"Specifies a minimum thickness, in ems, for fraction lines, `\\sqrt` top lines, `{array}` vertical lines, `\\hline`, `\\hdashline`, `\\underline`, `\\overline`, and the borders of `\\fbox`, `\\boxed`, and `\\fcolorbox`.",processor:i=>Math.max(0,i),cli:"--min-rule-thickness <size>",cliProcessor:parseFloat},colorIsTextColor:{type:"boolean",description:"Makes \\color behave like LaTeX's 2-argument \\textcolor, instead of LaTeX's one-argument \\color mode change.",cli:"-b, --color-is-text-color"},strict:{type:[{enum:["warn","ignore","error"]},"boolean","function"],description:"Turn on strict / LaTeX faithfulness mode, which throws an error if the input uses features that are not supported by LaTeX.",cli:"-S, --strict",cliDefault:!1},trust:{type:["boolean","function"],description:"Trust the input, enabling all HTML features such as \\url.",cli:"-T, --trust"},maxSize:{type:"number",default:1/0,description:"If non-zero, all user-specified sizes, e.g. in \\rule{500em}{500em}, will be capped to maxSize ems. Otherwise, elements and spaces can be arbitrarily large",processor:i=>Math.max(0,i),cli:"-s, --max-size <n>",cliProcessor:parseInt},maxExpand:{type:"number",default:1e3,description:"Limit the number of macro expansions to the specified number, to prevent e.g. infinite macro loops. If set to Infinity, the macro expander will try to fully expand as in LaTeX.",processor:i=>Math.max(0,i),cli:"-e, --max-expand <n>",cliProcessor:i=>i==="Infinity"?1/0:parseInt(i)},globalGroup:{type:"boolean",cli:!1}};function L(i){if(i.default)return i.default;let n=i.type,l=Array.isArray(n)?n[0]:n;if(typeof l!="string")return l.enum[0];switch(l){case"boolean":return!1;case"string":return"";case"number":return 0;case"object":return{}}}class X{constructor(n){this.displayMode=void 0,this.output=void 0,this.leqno=void 0,this.fleqn=void 0,this.throwOnError=void 0,this.errorColor=void 0,this.macros=void 0,this.minRuleThickness=void 0,this.colorIsTextColor=void 0,this.strict=void 0,this.trust=void 0,this.maxSize=void 0,this.maxExpand=void 0,this.globalGroup=void 0,n=n||{};for(let l in P)if(P.hasOwnProperty(l)){let a=P[l];this[l]=n[l]!==void 0?a.processor?a.processor(n[l]):n[l]:L(a)}}reportNonstrict(n,l,a){let f=this.strict;if(typeof f=="function"&&(f=f(n,l,a)),!(!f||f==="ignore")){if(f===!0||f==="error")throw new o("LaTeX-incompatible input and strict mode is set to 'error': "+(l+" ["+n+"]"),a);f==="warn"?typeof console!="undefined"&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(l+" ["+n+"]")):typeof console!="undefined"&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+f+"': "+l+" ["+n+"]"))}}useStrictBehavior(n,l,a){let f=this.strict;if(typeof f=="function")try{f=f(n,l,a)}catch(d){f="error"}return!f||f==="ignore"?!1:f===!0||f==="error"?!0:f==="warn"?(typeof console!="undefined"&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(l+" ["+n+"]")),!1):(typeof console!="undefined"&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+f+"': "+l+" ["+n+"]")),!1)}isTrusted(n){if(n.url&&!n.protocol){let a=z.protocolFromUrl(n.url);if(a==null)return!1;n.protocol=a}let l=typeof this.trust=="function"?this.trust(n):this.trust;return Boolean(l)}}class ne{constructor(n,l,a){this.id=void 0,this.size=void 0,this.cramped=void 0,this.id=n,this.size=l,this.cramped=a}sup(){return Xe[q[this.id]]}sub(){return Xe[D[this.id]]}fracNum(){return Xe[W[this.id]]}fracDen(){return Xe[G[this.id]]}cramp(){return Xe[ee[this.id]]}text(){return Xe[oe[this.id]]}isTight(){return this.size>=2}}let ue=0,me=1,be=2,Se=3,$e=4,ze=5,Ve=6,Oe=7,Xe=[new ne(ue,0,!1),new ne(me,0,!0),new ne(be,1,!1),new ne(Se,1,!0),new ne($e,2,!1),new ne(ze,2,!0),new ne(Ve,3,!1),new ne(Oe,3,!0)],q=[$e,ze,$e,ze,Ve,Oe,Ve,Oe],D=[ze,ze,ze,ze,Oe,Oe,Oe,Oe],W=[be,Se,$e,ze,Ve,Oe,Ve,Oe],G=[Se,Se,ze,ze,Oe,Oe,Oe,Oe],ee=[me,me,Se,Se,ze,ze,Oe,Oe],oe=[ue,me,be,Se,be,Se,be,Se];var Y={DISPLAY:Xe[ue],TEXT:Xe[be],SCRIPT:Xe[$e],SCRIPTSCRIPT:Xe[Ve]};let se=[{name:"latin",blocks:[[256,591],[768,879]]},{name:"cyrillic",blocks:[[1024,1279]]},{name:"armenian",blocks:[[1328,1423]]},{name:"brahmic",blocks:[[2304,4255]]},{name:"georgian",blocks:[[4256,4351]]},{name:"cjk",blocks:[[12288,12543],[19968,40879],[65280,65376]]},{name:"hangul",blocks:[[44032,55215]]}];function xe(i){for(let n=0;n<se.length;n++){let l=se[n];for(let a=0;a<l.blocks.length;a++){let f=l.blocks[a];if(i>=f[0]&&i<=f[1])return l.name}}return null}let ve=[];se.forEach(i=>i.blocks.forEach(n=>ve.push(...n)));function it(i){for(let n=0;n<ve.length;n+=2)if(i>=ve[n]&&i<=ve[n+1])return!0;return!1}let Ae=80,ot=function(i,n){return"M95,"+(622+i+n)+`
c-2.7,0,-7.17,-2.7,-13.5,-8c-5.8,-5.3,-9.5,-10,-9.5,-14
c0,-2,0.3,-3.3,1,-4c1.3,-2.7,23.83,-20.7,67.5,-54
c44.2,-33.3,65.8,-50.3,66.5,-51c1.3,-1.3,3,-2,5,-2c4.7,0,8.7,3.3,12,10
s173,378,173,378c0.7,0,35.3,-71,104,-213c68.7,-142,137.5,-285,206.5,-429
c69,-144,104.5,-217.7,106.5,-221
l`+i/2.075+" -"+i+`
c5.3,-9.3,12,-14,20,-14
H400000v`+(40+i)+`H845.2724
s-225.272,467,-225.272,467s-235,486,-235,486c-2.7,4.7,-9,7,-19,7
c-6,0,-10,-1,-12,-3s-194,-422,-194,-422s-65,47,-65,47z
M`+(834+i)+" "+n+"h400000v"+(40+i)+"h-400000z"},xt=function(i,n){return"M263,"+(601+i+n)+`c0.7,0,18,39.7,52,119
c34,79.3,68.167,158.7,102.5,238c34.3,79.3,51.8,119.3,52.5,120
c340,-704.7,510.7,-1060.3,512,-1067
l`+i/2.084+" -"+i+`
c4.7,-7.3,11,-11,19,-11
H40000v`+(40+i)+`H1012.3
s-271.3,567,-271.3,567c-38.7,80.7,-84,175,-136,283c-52,108,-89.167,185.3,-111.5,232
c-22.3,46.7,-33.8,70.3,-34.5,71c-4.7,4.7,-12.3,7,-23,7s-12,-1,-12,-1
s-109,-253,-109,-253c-72.7,-168,-109.3,-252,-110,-252c-10.7,8,-22,16.7,-34,26
c-22,17.3,-33.3,26,-34,26s-26,-26,-26,-26s76,-59,76,-59s76,-60,76,-60z
M`+(1001+i)+" "+n+"h400000v"+(40+i)+"h-400000z"},lt=function(i,n){return"M983 "+(10+i+n)+`
l`+i/3.13+" -"+i+`
c4,-6.7,10,-10,18,-10 H400000v`+(40+i)+`
H1013.1s-83.4,268,-264.1,840c-180.7,572,-277,876.3,-289,913c-4.7,4.7,-12.7,7,-24,7
s-12,0,-12,0c-1.3,-3.3,-3.7,-11.7,-7,-25c-35.3,-125.3,-106.7,-373.3,-214,-744
c-10,12,-21,25,-33,39s-32,39,-32,39c-6,-5.3,-15,-14,-27,-26s25,-30,25,-30
c26.7,-32.7,52,-63,76,-91s52,-60,52,-60s208,722,208,722
c56,-175.3,126.3,-397.3,211,-666c84.7,-268.7,153.8,-488.2,207.5,-658.5
c53.7,-170.3,84.5,-266.8,92.5,-289.5z
M`+(1001+i)+" "+n+"h400000v"+(40+i)+"h-400000z"},yt=function(i,n){return"M424,"+(2398+i+n)+`
c-1.3,-0.7,-38.5,-172,-111.5,-514c-73,-342,-109.8,-513.3,-110.5,-514
c0,-2,-10.7,14.3,-32,49c-4.7,7.3,-9.8,15.7,-15.5,25c-5.7,9.3,-9.8,16,-12.5,20
s-5,7,-5,7c-4,-3.3,-8.3,-7.7,-13,-13s-13,-13,-13,-13s76,-122,76,-122s77,-121,77,-121
s209,968,209,968c0,-2,84.7,-361.7,254,-1079c169.3,-717.3,254.7,-1077.7,256,-1081
l`+i/4.223+" -"+i+`c4,-6.7,10,-10,18,-10 H400000
v`+(40+i)+`H1014.6
s-87.3,378.7,-272.6,1166c-185.3,787.3,-279.3,1182.3,-282,1185
c-2,6,-10,9,-24,9
c-8,0,-12,-0.7,-12,-2z M`+(1001+i)+" "+n+`
h400000v`+(40+i)+"h-400000z"},Br=function(i,n){return"M473,"+(2713+i+n)+`
c339.3,-1799.3,509.3,-2700,510,-2702 l`+i/5.298+" -"+i+`
c3.3,-7.3,9.3,-11,18,-11 H400000v`+(40+i)+`H1017.7
s-90.5,478,-276.2,1466c-185.7,988,-279.5,1483,-281.5,1485c-2,6,-10,9,-24,9
c-8,0,-12,-0.7,-12,-2c0,-1.3,-5.3,-32,-16,-92c-50.7,-293.3,-119.7,-693.3,-207,-1200
c0,-1.3,-5.3,8.7,-16,30c-10.7,21.3,-21.3,42.7,-32,64s-16,33,-16,33s-26,-26,-26,-26
s76,-153,76,-153s77,-151,77,-151c0.7,0.7,35.7,202,105,604c67.3,400.7,102,602.7,104,
606zM`+(1001+i)+" "+n+"h400000v"+(40+i)+"H1017.7z"},lh=function(i){let n=i/2;return"M400000 "+i+" H0 L"+n+" 0 l65 45 L145 "+(i-80)+" H400000z"},sh=function(i,n,l){let a=l-54-n-i;return"M702 "+(i+n)+"H400000"+(40+i)+`
H742v`+a+`l-4 4-4 4c-.667.7 -2 1.5-4 2.5s-4.167 1.833-6.5 2.5-5.5 1-9.5 1
h-12l-28-84c-16.667-52-96.667 -294.333-240-727l-212 -643 -85 170
c-4-3.333-8.333-7.667-13 -13l-13-13l77-155 77-156c66 199.333 139 419.667
219 661 l218 661zM702 `+n+"H400000v"+(40+i)+"H742z"},ah=function(i,n,l){n=1e3*n;let a="";switch(i){case"sqrtMain":a=ot(n,Ae);break;case"sqrtSize1":a=xt(n,Ae);break;case"sqrtSize2":a=lt(n,Ae);break;case"sqrtSize3":a=yt(n,Ae);break;case"sqrtSize4":a=Br(n,Ae);break;case"sqrtTall":a=sh(n,Ae,l)}return a},uh=function(i,n){switch(i){case"\u239C":return"M291 0 H417 V"+n+" H291z M291 0 H417 V"+n+" H291z";case"\u2223":return"M145 0 H188 V"+n+" H145z M145 0 H188 V"+n+" H145z";case"\u2225":return"M145 0 H188 V"+n+" H145z M145 0 H188 V"+n+" H145z"+("M367 0 H410 V"+n+" H367z M367 0 H410 V"+n+" H367z");case"\u239F":return"M457 0 H583 V"+n+" H457z M457 0 H583 V"+n+" H457z";case"\u23A2":return"M319 0 H403 V"+n+" H319z M319 0 H403 V"+n+" H319z";case"\u23A5":return"M263 0 H347 V"+n+" H263z M263 0 H347 V"+n+" H263z";case"\u23AA":return"M384 0 H504 V"+n+" H384z M384 0 H504 V"+n+" H384z";case"\u23D0":return"M312 0 H355 V"+n+" H312z M312 0 H355 V"+n+" H312z";case"\u2016":return"M257 0 H300 V"+n+" H257z M257 0 H300 V"+n+" H257z"+("M478 0 H521 V"+n+" H478z M478 0 H521 V"+n+" H478z");default:return""}},Do={doubleleftarrow:`M262 157
l10-10c34-36 62.7-77 86-123 3.3-8 5-13.3 5-16 0-5.3-6.7-8-20-8-7.3
 0-12.2.5-14.5 1.5-2.3 1-4.8 4.5-7.5 10.5-49.3 97.3-121.7 169.3-217 216-28
 14-57.3 25-88 33-6.7 2-11 3.8-13 5.5-2 1.7-3 4.2-3 7.5s1 5.8 3 7.5
c2 1.7 6.3 3.5 13 5.5 68 17.3 128.2 47.8 180.5 91.5 52.3 43.7 93.8 96.2 124.5
 157.5 9.3 8 15.3 12.3 18 13h6c12-.7 18-4 18-10 0-2-1.7-7-5-15-23.3-46-52-87
-86-123l-10-10h399738v-40H218c328 0 0 0 0 0l-10-8c-26.7-20-65.7-43-117-69 2.7
-2 6-3.7 10-5 36.7-16 72.3-37.3 107-64l10-8h399782v-40z
m8 0v40h399730v-40zm0 194v40h399730v-40z`,doublerightarrow:`M399738 392l
-10 10c-34 36-62.7 77-86 123-3.3 8-5 13.3-5 16 0 5.3 6.7 8 20 8 7.3 0 12.2-.5
 14.5-1.5 2.3-1 4.8-4.5 7.5-10.5 49.3-97.3 121.7-169.3 217-216 28-14 57.3-25 88
-33 6.7-2 11-3.8 13-5.5 2-1.7 3-4.2 3-7.5s-1-5.8-3-7.5c-2-1.7-6.3-3.5-13-5.5-68
-17.3-128.2-47.8-180.5-91.5-52.3-43.7-93.8-96.2-124.5-157.5-9.3-8-15.3-12.3-18
-13h-6c-12 .7-18 4-18 10 0 2 1.7 7 5 15 23.3 46 52 87 86 123l10 10H0v40h399782
c-328 0 0 0 0 0l10 8c26.7 20 65.7 43 117 69-2.7 2-6 3.7-10 5-36.7 16-72.3 37.3
-107 64l-10 8H0v40zM0 157v40h399730v-40zm0 194v40h399730v-40z`,leftarrow:`M400000 241H110l3-3c68.7-52.7 113.7-120
 135-202 4-14.7 6-23 6-25 0-7.3-7-11-21-11-8 0-13.2.8-15.5 2.5-2.3 1.7-4.2 5.8
-5.5 12.5-1.3 4.7-2.7 10.3-4 17-12 48.7-34.8 92-68.5 130S65.3 228.3 18 247
c-10 4-16 7.7-18 11 0 8.7 6 14.3 18 17 47.3 18.7 87.8 47 121.5 85S196 441.3 208
 490c.7 2 1.3 5 2 9s1.2 6.7 1.5 8c.3 1.3 1 3.3 2 6s2.2 4.5 3.5 5.5c1.3 1 3.3
 1.8 6 2.5s6 1 10 1c14 0 21-3.7 21-11 0-2-2-10.3-6-25-20-79.3-65-146.7-135-202
 l-3-3h399890zM100 241v40h399900v-40z`,leftbrace:`M6 548l-6-6v-35l6-11c56-104 135.3-181.3 238-232 57.3-28.7 117
-45 179-50h399577v120H403c-43.3 7-81 15-113 26-100.7 33-179.7 91-237 174-2.7
 5-6 9-10 13-.7 1-7.3 1-20 1H6z`,leftbraceunder:`M0 6l6-6h17c12.688 0 19.313.3 20 1 4 4 7.313 8.3 10 13
 35.313 51.3 80.813 93.8 136.5 127.5 55.688 33.7 117.188 55.8 184.5 66.5.688
 0 2 .3 4 1 18.688 2.7 76 4.3 172 5h399450v120H429l-6-1c-124.688-8-235-61.7
-331-161C60.687 138.7 32.312 99.3 7 54L0 41V6z`,leftgroup:`M400000 80
H435C64 80 168.3 229.4 21 260c-5.9 1.2-18 0-18 0-2 0-3-1-3-3v-38C76 61 257 0
 435 0h399565z`,leftgroupunder:`M400000 262
H435C64 262 168.3 112.6 21 82c-5.9-1.2-18 0-18 0-2 0-3 1-3 3v38c76 158 257 219
 435 219h399565z`,leftharpoon:`M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3
-3.3 10.2-9.5 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5
-18.3 3-21-1.3-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7
-196 228-6.7 4.7-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40z`,leftharpoonplus:`M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3-3.3 10.2-9.5
 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5-18.3 3-21-1.3
-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7-196 228-6.7 4.7
-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40zM0 435v40h400000v-40z
m0 0v40h400000v-40z`,leftharpoondown:`M7 241c-4 4-6.333 8.667-7 14 0 5.333.667 9 2 11s5.333
 5.333 12 10c90.667 54 156 130 196 228 3.333 10.667 6.333 16.333 9 17 2 .667 5
 1 9 1h5c10.667 0 16.667-2 18-6 2-2.667 1-9.667-3-21-32-87.333-82.667-157.667
-152-211l-3-3h399907v-40zM93 281 H400000 v-40L7 241z`,leftharpoondownplus:`M7 435c-4 4-6.3 8.7-7 14 0 5.3.7 9 2 11s5.3 5.3 12
 10c90.7 54 156 130 196 228 3.3 10.7 6.3 16.3 9 17 2 .7 5 1 9 1h5c10.7 0 16.7
-2 18-6 2-2.7 1-9.7-3-21-32-87.3-82.7-157.7-152-211l-3-3h399907v-40H7zm93 0
v40h399900v-40zM0 241v40h399900v-40zm0 0v40h399900v-40z`,lefthook:`M400000 281 H103s-33-11.2-61-33.5S0 197.3 0 164s14.2-61.2 42.5
-83.5C70.8 58.2 104 47 142 47 c16.7 0 25 6.7 25 20 0 12-8.7 18.7-26 20-40 3.3
-68.7 15.7-86 37-10 12-15 25.3-15 40 0 22.7 9.8 40.7 29.5 54 19.7 13.3 43.5 21
 71.5 23h399859zM103 281v-40h399897v40z`,leftlinesegment:`M40 281 V428 H0 V94 H40 V241 H400000 v40z
M40 281 V428 H0 V94 H40 V241 H400000 v40z`,leftmapsto:`M40 281 V448H0V74H40V241H400000v40z
M40 281 V448H0V74H40V241H400000v40z`,leftToFrom:`M0 147h400000v40H0zm0 214c68 40 115.7 95.7 143 167h22c15.3 0 23
-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69-70-101l-7-8h399905v-40H95l7-8
c28.7-32 52-65.7 70-101 10.7-23.3 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 265.3
 68 321 0 361zm0-174v-40h399900v40zm100 154v40h399900v-40z`,longequal:`M0 50 h400000 v40H0z m0 194h40000v40H0z
M0 50 h400000 v40H0z m0 194h40000v40H0z`,midbrace:`M200428 334
c-100.7-8.3-195.3-44-280-108-55.3-42-101.7-93-139-153l-9-14c-2.7 4-5.7 8.7-9 14
-53.3 86.7-123.7 153-211 199-66.7 36-137.3 56.3-212 62H0V214h199568c178.3-11.7
 311.7-78.3 403-201 6-8 9.7-12 11-12 .7-.7 6.7-1 18-1s17.3.3 18 1c1.3 0 5 4 11
 12 44.7 59.3 101.3 106.3 170 141s145.3 54.3 229 60h199572v120z`,midbraceunder:`M199572 214
c100.7 8.3 195.3 44 280 108 55.3 42 101.7 93 139 153l9 14c2.7-4 5.7-8.7 9-14
 53.3-86.7 123.7-153 211-199 66.7-36 137.3-56.3 212-62h199568v120H200432c-178.3
 11.7-311.7 78.3-403 201-6 8-9.7 12-11 12-.7.7-6.7 1-18 1s-17.3-.3-18-1c-1.3 0
-5-4-11-12-44.7-59.3-101.3-106.3-170-141s-145.3-54.3-229-60H0V214z`,oiintSize1:`M512.6 71.6c272.6 0 320.3 106.8 320.3 178.2 0 70.8-47.7 177.6
-320.3 177.6S193.1 320.6 193.1 249.8c0-71.4 46.9-178.2 319.5-178.2z
m368.1 178.2c0-86.4-60.9-215.4-368.1-215.4-306.4 0-367.3 129-367.3 215.4 0 85.8
60.9 214.8 367.3 214.8 307.2 0 368.1-129 368.1-214.8z`,oiintSize2:`M757.8 100.1c384.7 0 451.1 137.6 451.1 230 0 91.3-66.4 228.8
-451.1 228.8-386.3 0-452.7-137.5-452.7-228.8 0-92.4 66.4-230 452.7-230z
m502.4 230c0-111.2-82.4-277.2-502.4-277.2s-504 166-504 277.2
c0 110 84 276 504 276s502.4-166 502.4-276z`,oiiintSize1:`M681.4 71.6c408.9 0 480.5 106.8 480.5 178.2 0 70.8-71.6 177.6
-480.5 177.6S202.1 320.6 202.1 249.8c0-71.4 70.5-178.2 479.3-178.2z
m525.8 178.2c0-86.4-86.8-215.4-525.7-215.4-437.9 0-524.7 129-524.7 215.4 0
85.8 86.8 214.8 524.7 214.8 438.9 0 525.7-129 525.7-214.8z`,oiiintSize2:`M1021.2 53c603.6 0 707.8 165.8 707.8 277.2 0 110-104.2 275.8
-707.8 275.8-606 0-710.2-165.8-710.2-275.8C311 218.8 415.2 53 1021.2 53z
m770.4 277.1c0-131.2-126.4-327.6-770.5-327.6S248.4 198.9 248.4 330.1
c0 130 128.8 326.4 772.7 326.4s770.5-196.4 770.5-326.4z`,rightarrow:`M0 241v40h399891c-47.3 35.3-84 78-110 128
-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20
 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7
 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85
-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5
-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67
 151.7 139 205zm0 0v40h399900v-40z`,rightbrace:`M400000 542l
-6 6h-17c-12.7 0-19.3-.3-20-1-4-4-7.3-8.3-10-13-35.3-51.3-80.8-93.8-136.5-127.5
s-117.2-55.8-184.5-66.5c-.7 0-2-.3-4-1-18.7-2.7-76-4.3-172-5H0V214h399571l6 1
c124.7 8 235 61.7 331 161 31.3 33.3 59.7 72.7 85 118l7 13v35z`,rightbraceunder:`M399994 0l6 6v35l-6 11c-56 104-135.3 181.3-238 232-57.3
 28.7-117 45-179 50H-300V214h399897c43.3-7 81-15 113-26 100.7-33 179.7-91 237
-174 2.7-5 6-9 10-13 .7-1 7.3-1 20-1h17z`,rightgroup:`M0 80h399565c371 0 266.7 149.4 414 180 5.9 1.2 18 0 18 0 2 0
 3-1 3-3v-38c-76-158-257-219-435-219H0z`,rightgroupunder:`M0 262h399565c371 0 266.7-149.4 414-180 5.9-1.2 18 0 18
 0 2 0 3 1 3 3v38c-76 158-257 219-435 219H0z`,rightharpoon:`M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3
-3.7-15.3-11-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2
-10.7 0-16.7 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58
 69.2 92 94.5zm0 0v40h399900v-40z`,rightharpoonplus:`M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3-3.7-15.3-11
-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2-10.7 0-16.7
 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58 69.2 92 94.5z
m0 0v40h399900v-40z m100 194v40h399900v-40zm0 0v40h399900v-40z`,rightharpoondown:`M399747 511c0 7.3 6.7 11 20 11 8 0 13-.8 15-2.5s4.7-6.8
 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3 8.5-5.8 9.5
-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3-64.7 57-92 95
-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 241v40h399900v-40z`,rightharpoondownplus:`M399747 705c0 7.3 6.7 11 20 11 8 0 13-.8
 15-2.5s4.7-6.8 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3
 8.5-5.8 9.5-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3
-64.7 57-92 95-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 435v40h399900v-40z
m0-194v40h400000v-40zm0 0v40h400000v-40z`,righthook:`M399859 241c-764 0 0 0 0 0 40-3.3 68.7-15.7 86-37 10-12 15-25.3
 15-40 0-22.7-9.8-40.7-29.5-54-19.7-13.3-43.5-21-71.5-23-17.3-1.3-26-8-26-20 0
-13.3 8.7-20 26-20 38 0 71 11.2 99 33.5 0 0 7 5.6 21 16.7 14 11.2 21 33.5 21
 66.8s-14 61.2-42 83.5c-28 22.3-61 33.5-99 33.5L0 241z M0 281v-40h399859v40z`,rightlinesegment:`M399960 241 V94 h40 V428 h-40 V281 H0 v-40z
M399960 241 V94 h40 V428 h-40 V281 H0 v-40z`,rightToFrom:`M400000 167c-70.7-42-118-97.7-142-167h-23c-15.3 0-23 .3-23
 1 0 1.3 5.3 13.7 16 37 18 35.3 41.3 69 70 101l7 8H0v40h399905l-7 8c-28.7 32
-52 65.7-70 101-10.7 23.3-16 35.7-16 37 0 .7 7.7 1 23 1h23c24-69.3 71.3-125 142
-167z M100 147v40h399900v-40zM0 341v40h399900v-40z`,twoheadleftarrow:`M0 167c68 40
 115.7 95.7 143 167h22c15.3 0 23-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69
-70-101l-7-8h125l9 7c50.7 39.3 85 86 103 140h46c0-4.7-6.3-18.7-19-42-18-35.3
-40-67.3-66-96l-9-9h399716v-40H284l9-9c26-28.7 48-60.7 66-96 12.7-23.333 19
-37.333 19-42h-46c-18 54-52.3 100.7-103 140l-9 7H95l7-8c28.7-32 52-65.7 70-101
 10.7-23.333 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 71.3 68 127 0 167z`,twoheadrightarrow:`M400000 167
c-68-40-115.7-95.7-143-167h-22c-15.3 0-23 .3-23 1 0 1.3 5.3 13.7 16 37 18 35.3
 41.3 69 70 101l7 8h-125l-9-7c-50.7-39.3-85-86-103-140h-46c0 4.7 6.3 18.7 19 42
 18 35.3 40 67.3 66 96l9 9H0v40h399716l-9 9c-26 28.7-48 60.7-66 96-12.7 23.333
-19 37.333-19 42h46c18-54 52.3-100.7 103-140l9-7h125l-7 8c-28.7 32-52 65.7-70
 101-10.7 23.333-16 35.7-16 37 0 .7 7.7 1 23 1h22c27.3-71.3 75-127 143-167z`,tilde1:`M200 55.538c-77 0-168 73.953-177 73.953-3 0-7
-2.175-9-5.437L2 97c-1-2-2-4-2-6 0-4 2-7 5-9l20-12C116 12 171 0 207 0c86 0
 114 68 191 68 78 0 168-68 177-68 4 0 7 2 9 5l12 19c1 2.175 2 4.35 2 6.525 0
 4.35-2 7.613-5 9.788l-19 13.05c-92 63.077-116.937 75.308-183 76.128
-68.267.847-113-73.952-191-73.952z`,tilde2:`M344 55.266c-142 0-300.638 81.316-311.5 86.418
-8.01 3.762-22.5 10.91-23.5 5.562L1 120c-1-2-1-3-1-4 0-5 3-9 8-10l18.4-9C160.9
 31.9 283 0 358 0c148 0 188 122 331 122s314-97 326-97c4 0 8 2 10 7l7 21.114
c1 2.14 1 3.21 1 4.28 0 5.347-3 9.626-7 10.696l-22.3 12.622C852.6 158.372 751
 181.476 676 181.476c-149 0-189-126.21-332-126.21z`,tilde3:`M786 59C457 59 32 175.242 13 175.242c-6 0-10-3.457
-11-10.37L.15 138c-1-7 3-12 10-13l19.2-6.4C378.4 40.7 634.3 0 804.3 0c337 0
 411.8 157 746.8 157 328 0 754-112 773-112 5 0 10 3 11 9l1 14.075c1 8.066-.697
 16.595-6.697 17.492l-21.052 7.31c-367.9 98.146-609.15 122.696-778.15 122.696
 -338 0-409-156.573-744-156.573z`,tilde4:`M786 58C457 58 32 177.487 13 177.487c-6 0-10-3.345
-11-10.035L.15 143c-1-7 3-12 10-13l22-6.7C381.2 35 637.15 0 807.15 0c337 0 409
 177 744 177 328 0 754-127 773-127 5 0 10 3 11 9l1 14.794c1 7.805-3 13.38-9
 14.495l-20.7 5.574c-366.85 99.79-607.3 139.372-776.3 139.372-338 0-409
 -175.236-744-175.236z`,vec:`M377 20c0-5.333 1.833-10 5.5-14S391 0 397 0c4.667 0 8.667 1.667 12 5
3.333 2.667 6.667 9 10 19 6.667 24.667 20.333 43.667 41 57 7.333 4.667 11
10.667 11 18 0 6-1 10-3 12s-6.667 5-14 9c-28.667 14.667-53.667 35.667-75 63
-1.333 1.333-3.167 3.5-5.5 6.5s-4 4.833-5 5.5c-1 .667-2.5 1.333-4.5 2s-4.333 1
-7 1c-4.667 0-9.167-1.833-13.5-5.5S337 184 337 178c0-12.667 15.667-32.333 47-59
H213l-171-1c-8.667-6-13-12.333-13-19 0-4.667 4.333-11.333 13-20h359
c-16-25.333-24-45-24-59z`,widehat1:`M529 0h5l519 115c5 1 9 5 9 10 0 1-1 2-1 3l-4 22
c-1 5-5 9-11 9h-2L532 67 19 159h-2c-5 0-9-4-11-9l-5-22c-1-6 2-12 8-13z`,widehat2:`M1181 0h2l1171 176c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 220h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widehat3:`M1181 0h2l1171 236c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 280h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widehat4:`M1181 0h2l1171 296c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 340h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widecheck1:`M529,159h5l519,-115c5,-1,9,-5,9,-10c0,-1,-1,-2,-1,-3l-4,-22c-1,
-5,-5,-9,-11,-9h-2l-512,92l-513,-92h-2c-5,0,-9,4,-11,9l-5,22c-1,6,2,12,8,13z`,widecheck2:`M1181,220h2l1171,-176c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,153l-1167,-153h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,widecheck3:`M1181,280h2l1171,-236c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,213l-1167,-213h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,widecheck4:`M1181,340h2l1171,-296c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,273l-1167,-273h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,baraboveleftarrow:`M400000 620h-399890l3 -3c68.7 -52.7 113.7 -120 135 -202
c4 -14.7 6 -23 6 -25c0 -7.3 -7 -11 -21 -11c-8 0 -13.2 0.8 -15.5 2.5
c-2.3 1.7 -4.2 5.8 -5.5 12.5c-1.3 4.7 -2.7 10.3 -4 17c-12 48.7 -34.8 92 -68.5 130
s-74.2 66.3 -121.5 85c-10 4 -16 7.7 -18 11c0 8.7 6 14.3 18 17c47.3 18.7 87.8 47
121.5 85s56.5 81.3 68.5 130c0.7 2 1.3 5 2 9s1.2 6.7 1.5 8c0.3 1.3 1 3.3 2 6
s2.2 4.5 3.5 5.5c1.3 1 3.3 1.8 6 2.5s6 1 10 1c14 0 21 -3.7 21 -11
c0 -2 -2 -10.3 -6 -25c-20 -79.3 -65 -146.7 -135 -202l-3 -3h399890z
M100 620v40h399900v-40z M0 241v40h399900v-40zM0 241v40h399900v-40z`,rightarrowabovebar:`M0 241v40h399891c-47.3 35.3-84 78-110 128-16.7 32
-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20 11 8 0
13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7 39
-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85-40.5
-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5
-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67
151.7 139 205zm96 379h399894v40H0zm0 0h399904v40H0z`,baraboveshortleftharpoon:`M507,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11
c1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17
c2,0.7,5,1,9,1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21
c-32,-87.3,-82.7,-157.7,-152,-211c0,0,-3,-3,-3,-3l399351,0l0,-40
c-398570,0,-399437,0,-399437,0z M593 435 v40 H399500 v-40z
M0 281 v-40 H399908 v40z M0 281 v-40 H399908 v40z`,rightharpoonaboveshortbar:`M0,241 l0,40c399126,0,399993,0,399993,0
c4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,
-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6
c-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z
M0 241 v40 H399908 v-40z M0 475 v-40 H399500 v40z M0 475 v-40 H399500 v40z`,shortbaraboveleftharpoon:`M7,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11
c1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17c2,0.7,5,1,9,
1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21c-32,-87.3,-82.7,-157.7,
-152,-211c0,0,-3,-3,-3,-3l399907,0l0,-40c-399126,0,-399993,0,-399993,0z
M93 435 v40 H400000 v-40z M500 241 v40 H400000 v-40z M500 241 v40 H400000 v-40z`,shortrightharpoonabovebar:`M53,241l0,40c398570,0,399437,0,399437,0
c4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,
-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6
c-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z
M500 241 v40 H399408 v-40z M500 435 v40 H400000 v-40z`},ch=function(i,n){switch(i){case"lbrack":return"M403 1759 V84 H666 V0 H319 V1759 v"+n+` v1759 h347 v-84
H403z M403 1759 V0 H319 V1759 v`+n+" v1759 h84z";case"rbrack":return"M347 1759 V0 H0 V84 H263 V1759 v"+n+` v1759 H0 v84 H347z
M347 1759 V0 H263 V1759 v`+n+" v1759 h84z";case"vert":return"M145 15 v585 v"+n+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-n+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M188 15 H145 v585 v`+n+" v585 h43z";case"doublevert":return"M145 15 v585 v"+n+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-n+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M188 15 H145 v585 v`+n+` v585 h43z
M367 15 v585 v`+n+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-n+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M410 15 H367 v585 v`+n+" v585 h43z";case"lfloor":return"M319 602 V0 H403 V602 v"+n+` v1715 h263 v84 H319z
MM319 602 V0 H403 V602 v`+n+" v1715 H319z";case"rfloor":return"M319 602 V0 H403 V602 v"+n+` v1799 H0 v-84 H319z
MM319 602 V0 H403 V602 v`+n+" v1715 H319z";case"lceil":return"M403 1759 V84 H666 V0 H319 V1759 v"+n+` v602 h84z
M403 1759 V0 H319 V1759 v`+n+" v602 h84z";case"rceil":return"M347 1759 V0 H0 V84 H263 V1759 v"+n+` v602 h84z
M347 1759 V0 h-84 V1759 v`+n+" v602 h84z";case"lparen":return`M863,9c0,-2,-2,-5,-6,-9c0,0,-17,0,-17,0c-12.7,0,-19.3,0.3,-20,1
c-5.3,5.3,-10.3,11,-15,17c-242.7,294.7,-395.3,682,-458,1162c-21.3,163.3,-33.3,349,
-36,557 l0,`+(n+84)+`c0.2,6,0,26,0,60c2,159.3,10,310.7,24,454c53.3,528,210,
949.7,470,1265c4.7,6,9.7,11.7,15,17c0.7,0.7,7,1,19,1c0,0,18,0,18,0c4,-4,6,-7,6,-9
c0,-2.7,-3.3,-8.7,-10,-18c-135.3,-192.7,-235.5,-414.3,-300.5,-665c-65,-250.7,-102.5,
-544.7,-112.5,-882c-2,-104,-3,-167,-3,-189
l0,-`+(n+92)+`c0,-162.7,5.7,-314,17,-454c20.7,-272,63.7,-513,129,-723c65.3,
-210,155.3,-396.3,270,-559c6.7,-9.3,10,-15.3,10,-18z`;case"rparen":return`M76,0c-16.7,0,-25,3,-25,9c0,2,2,6.3,6,13c21.3,28.7,42.3,60.3,
63,95c96.7,156.7,172.8,332.5,228.5,527.5c55.7,195,92.8,416.5,111.5,664.5
c11.3,139.3,17,290.7,17,454c0,28,1.7,43,3.3,45l0,`+(n+9)+`
c-3,4,-3.3,16.7,-3.3,38c0,162,-5.7,313.7,-17,455c-18.7,248,-55.8,469.3,-111.5,664
c-55.7,194.7,-131.8,370.3,-228.5,527c-20.7,34.7,-41.7,66.3,-63,95c-2,3.3,-4,7,-6,11
c0,7.3,5.7,11,17,11c0,0,11,0,11,0c9.3,0,14.3,-0.3,15,-1c5.3,-5.3,10.3,-11,15,-17
c242.7,-294.7,395.3,-681.7,458,-1161c21.3,-164.7,33.3,-350.7,36,-558
l0,-`+(n+144)+`c-2,-159.3,-10,-310.7,-24,-454c-53.3,-528,-210,-949.7,
-470,-1265c-4.7,-6,-9.7,-11.7,-15,-17c-0.7,-0.7,-6.7,-1,-18,-1z`;default:throw new Error("Unknown stretchy delimiter.")}};class Or{constructor(n){this.children=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.children=n,this.classes=[],this.height=0,this.depth=0,this.maxFontSize=0,this.style={}}hasClass(n){return z.contains(this.classes,n)}toNode(){let n=document.createDocumentFragment();for(let l=0;l<this.children.length;l++)n.appendChild(this.children[l].toNode());return n}toMarkup(){let n="";for(let l=0;l<this.children.length;l++)n+=this.children[l].toMarkup();return n}toText(){let n=l=>l.toText();return this.children.map(n).join("")}}var St={"AMS-Regular":{32:[0,0,0,0,.25],65:[0,.68889,0,0,.72222],66:[0,.68889,0,0,.66667],67:[0,.68889,0,0,.72222],68:[0,.68889,0,0,.72222],69:[0,.68889,0,0,.66667],70:[0,.68889,0,0,.61111],71:[0,.68889,0,0,.77778],72:[0,.68889,0,0,.77778],73:[0,.68889,0,0,.38889],74:[.16667,.68889,0,0,.5],75:[0,.68889,0,0,.77778],76:[0,.68889,0,0,.66667],77:[0,.68889,0,0,.94445],78:[0,.68889,0,0,.72222],79:[.16667,.68889,0,0,.77778],80:[0,.68889,0,0,.61111],81:[.16667,.68889,0,0,.77778],82:[0,.68889,0,0,.72222],83:[0,.68889,0,0,.55556],84:[0,.68889,0,0,.66667],85:[0,.68889,0,0,.72222],86:[0,.68889,0,0,.72222],87:[0,.68889,0,0,1],88:[0,.68889,0,0,.72222],89:[0,.68889,0,0,.72222],90:[0,.68889,0,0,.66667],107:[0,.68889,0,0,.55556],160:[0,0,0,0,.25],165:[0,.675,.025,0,.75],174:[.15559,.69224,0,0,.94666],240:[0,.68889,0,0,.55556],295:[0,.68889,0,0,.54028],710:[0,.825,0,0,2.33334],732:[0,.9,0,0,2.33334],770:[0,.825,0,0,2.33334],771:[0,.9,0,0,2.33334],989:[.08167,.58167,0,0,.77778],1008:[0,.43056,.04028,0,.66667],8245:[0,.54986,0,0,.275],8463:[0,.68889,0,0,.54028],8487:[0,.68889,0,0,.72222],8498:[0,.68889,0,0,.55556],8502:[0,.68889,0,0,.66667],8503:[0,.68889,0,0,.44445],8504:[0,.68889,0,0,.66667],8513:[0,.68889,0,0,.63889],8592:[-.03598,.46402,0,0,.5],8594:[-.03598,.46402,0,0,.5],8602:[-.13313,.36687,0,0,1],8603:[-.13313,.36687,0,0,1],8606:[.01354,.52239,0,0,1],8608:[.01354,.52239,0,0,1],8610:[.01354,.52239,0,0,1.11111],8611:[.01354,.52239,0,0,1.11111],8619:[0,.54986,0,0,1],8620:[0,.54986,0,0,1],8621:[-.13313,.37788,0,0,1.38889],8622:[-.13313,.36687,0,0,1],8624:[0,.69224,0,0,.5],8625:[0,.69224,0,0,.5],8630:[0,.43056,0,0,1],8631:[0,.43056,0,0,1],8634:[.08198,.58198,0,0,.77778],8635:[.08198,.58198,0,0,.77778],8638:[.19444,.69224,0,0,.41667],8639:[.19444,.69224,0,0,.41667],8642:[.19444,.69224,0,0,.41667],8643:[.19444,.69224,0,0,.41667],8644:[.1808,.675,0,0,1],8646:[.1808,.675,0,0,1],8647:[.1808,.675,0,0,1],8648:[.19444,.69224,0,0,.83334],8649:[.1808,.675,0,0,1],8650:[.19444,.69224,0,0,.83334],8651:[.01354,.52239,0,0,1],8652:[.01354,.52239,0,0,1],8653:[-.13313,.36687,0,0,1],8654:[-.13313,.36687,0,0,1],8655:[-.13313,.36687,0,0,1],8666:[.13667,.63667,0,0,1],8667:[.13667,.63667,0,0,1],8669:[-.13313,.37788,0,0,1],8672:[-.064,.437,0,0,1.334],8674:[-.064,.437,0,0,1.334],8705:[0,.825,0,0,.5],8708:[0,.68889,0,0,.55556],8709:[.08167,.58167,0,0,.77778],8717:[0,.43056,0,0,.42917],8722:[-.03598,.46402,0,0,.5],8724:[.08198,.69224,0,0,.77778],8726:[.08167,.58167,0,0,.77778],8733:[0,.69224,0,0,.77778],8736:[0,.69224,0,0,.72222],8737:[0,.69224,0,0,.72222],8738:[.03517,.52239,0,0,.72222],8739:[.08167,.58167,0,0,.22222],8740:[.25142,.74111,0,0,.27778],8741:[.08167,.58167,0,0,.38889],8742:[.25142,.74111,0,0,.5],8756:[0,.69224,0,0,.66667],8757:[0,.69224,0,0,.66667],8764:[-.13313,.36687,0,0,.77778],8765:[-.13313,.37788,0,0,.77778],8769:[-.13313,.36687,0,0,.77778],8770:[-.03625,.46375,0,0,.77778],8774:[.30274,.79383,0,0,.77778],8776:[-.01688,.48312,0,0,.77778],8778:[.08167,.58167,0,0,.77778],8782:[.06062,.54986,0,0,.77778],8783:[.06062,.54986,0,0,.77778],8785:[.08198,.58198,0,0,.77778],8786:[.08198,.58198,0,0,.77778],8787:[.08198,.58198,0,0,.77778],8790:[0,.69224,0,0,.77778],8791:[.22958,.72958,0,0,.77778],8796:[.08198,.91667,0,0,.77778],8806:[.25583,.75583,0,0,.77778],8807:[.25583,.75583,0,0,.77778],8808:[.25142,.75726,0,0,.77778],8809:[.25142,.75726,0,0,.77778],8812:[.25583,.75583,0,0,.5],8814:[.20576,.70576,0,0,.77778],8815:[.20576,.70576,0,0,.77778],8816:[.30274,.79383,0,0,.77778],8817:[.30274,.79383,0,0,.77778],8818:[.22958,.72958,0,0,.77778],8819:[.22958,.72958,0,0,.77778],8822:[.1808,.675,0,0,.77778],8823:[.1808,.675,0,0,.77778],8828:[.13667,.63667,0,0,.77778],8829:[.13667,.63667,0,0,.77778],8830:[.22958,.72958,0,0,.77778],8831:[.22958,.72958,0,0,.77778],8832:[.20576,.70576,0,0,.77778],8833:[.20576,.70576,0,0,.77778],8840:[.30274,.79383,0,0,.77778],8841:[.30274,.79383,0,0,.77778],8842:[.13597,.63597,0,0,.77778],8843:[.13597,.63597,0,0,.77778],8847:[.03517,.54986,0,0,.77778],8848:[.03517,.54986,0,0,.77778],8858:[.08198,.58198,0,0,.77778],8859:[.08198,.58198,0,0,.77778],8861:[.08198,.58198,0,0,.77778],8862:[0,.675,0,0,.77778],8863:[0,.675,0,0,.77778],8864:[0,.675,0,0,.77778],8865:[0,.675,0,0,.77778],8872:[0,.69224,0,0,.61111],8873:[0,.69224,0,0,.72222],8874:[0,.69224,0,0,.88889],8876:[0,.68889,0,0,.61111],8877:[0,.68889,0,0,.61111],8878:[0,.68889,0,0,.72222],8879:[0,.68889,0,0,.72222],8882:[.03517,.54986,0,0,.77778],8883:[.03517,.54986,0,0,.77778],8884:[.13667,.63667,0,0,.77778],8885:[.13667,.63667,0,0,.77778],8888:[0,.54986,0,0,1.11111],8890:[.19444,.43056,0,0,.55556],8891:[.19444,.69224,0,0,.61111],8892:[.19444,.69224,0,0,.61111],8901:[0,.54986,0,0,.27778],8903:[.08167,.58167,0,0,.77778],8905:[.08167,.58167,0,0,.77778],8906:[.08167,.58167,0,0,.77778],8907:[0,.69224,0,0,.77778],8908:[0,.69224,0,0,.77778],8909:[-.03598,.46402,0,0,.77778],8910:[0,.54986,0,0,.76042],8911:[0,.54986,0,0,.76042],8912:[.03517,.54986,0,0,.77778],8913:[.03517,.54986,0,0,.77778],8914:[0,.54986,0,0,.66667],8915:[0,.54986,0,0,.66667],8916:[0,.69224,0,0,.66667],8918:[.0391,.5391,0,0,.77778],8919:[.0391,.5391,0,0,.77778],8920:[.03517,.54986,0,0,1.33334],8921:[.03517,.54986,0,0,1.33334],8922:[.38569,.88569,0,0,.77778],8923:[.38569,.88569,0,0,.77778],8926:[.13667,.63667,0,0,.77778],8927:[.13667,.63667,0,0,.77778],8928:[.30274,.79383,0,0,.77778],8929:[.30274,.79383,0,0,.77778],8934:[.23222,.74111,0,0,.77778],8935:[.23222,.74111,0,0,.77778],8936:[.23222,.74111,0,0,.77778],8937:[.23222,.74111,0,0,.77778],8938:[.20576,.70576,0,0,.77778],8939:[.20576,.70576,0,0,.77778],8940:[.30274,.79383,0,0,.77778],8941:[.30274,.79383,0,0,.77778],8994:[.19444,.69224,0,0,.77778],8995:[.19444,.69224,0,0,.77778],9416:[.15559,.69224,0,0,.90222],9484:[0,.69224,0,0,.5],9488:[0,.69224,0,0,.5],9492:[0,.37788,0,0,.5],9496:[0,.37788,0,0,.5],9585:[.19444,.68889,0,0,.88889],9586:[.19444,.74111,0,0,.88889],9632:[0,.675,0,0,.77778],9633:[0,.675,0,0,.77778],9650:[0,.54986,0,0,.72222],9651:[0,.54986,0,0,.72222],9654:[.03517,.54986,0,0,.77778],9660:[0,.54986,0,0,.72222],9661:[0,.54986,0,0,.72222],9664:[.03517,.54986,0,0,.77778],9674:[.11111,.69224,0,0,.66667],9733:[.19444,.69224,0,0,.94445],10003:[0,.69224,0,0,.83334],10016:[0,.69224,0,0,.83334],10731:[.11111,.69224,0,0,.66667],10846:[.19444,.75583,0,0,.61111],10877:[.13667,.63667,0,0,.77778],10878:[.13667,.63667,0,0,.77778],10885:[.25583,.75583,0,0,.77778],10886:[.25583,.75583,0,0,.77778],10887:[.13597,.63597,0,0,.77778],10888:[.13597,.63597,0,0,.77778],10889:[.26167,.75726,0,0,.77778],10890:[.26167,.75726,0,0,.77778],10891:[.48256,.98256,0,0,.77778],10892:[.48256,.98256,0,0,.77778],10901:[.13667,.63667,0,0,.77778],10902:[.13667,.63667,0,0,.77778],10933:[.25142,.75726,0,0,.77778],10934:[.25142,.75726,0,0,.77778],10935:[.26167,.75726,0,0,.77778],10936:[.26167,.75726,0,0,.77778],10937:[.26167,.75726,0,0,.77778],10938:[.26167,.75726,0,0,.77778],10949:[.25583,.75583,0,0,.77778],10950:[.25583,.75583,0,0,.77778],10955:[.28481,.79383,0,0,.77778],10956:[.28481,.79383,0,0,.77778],57350:[.08167,.58167,0,0,.22222],57351:[.08167,.58167,0,0,.38889],57352:[.08167,.58167,0,0,.77778],57353:[0,.43056,.04028,0,.66667],57356:[.25142,.75726,0,0,.77778],57357:[.25142,.75726,0,0,.77778],57358:[.41951,.91951,0,0,.77778],57359:[.30274,.79383,0,0,.77778],57360:[.30274,.79383,0,0,.77778],57361:[.41951,.91951,0,0,.77778],57366:[.25142,.75726,0,0,.77778],57367:[.25142,.75726,0,0,.77778],57368:[.25142,.75726,0,0,.77778],57369:[.25142,.75726,0,0,.77778],57370:[.13597,.63597,0,0,.77778],57371:[.13597,.63597,0,0,.77778]},"Caligraphic-Regular":{32:[0,0,0,0,.25],65:[0,.68333,0,.19445,.79847],66:[0,.68333,.03041,.13889,.65681],67:[0,.68333,.05834,.13889,.52653],68:[0,.68333,.02778,.08334,.77139],69:[0,.68333,.08944,.11111,.52778],70:[0,.68333,.09931,.11111,.71875],71:[.09722,.68333,.0593,.11111,.59487],72:[0,.68333,.00965,.11111,.84452],73:[0,.68333,.07382,0,.54452],74:[.09722,.68333,.18472,.16667,.67778],75:[0,.68333,.01445,.05556,.76195],76:[0,.68333,0,.13889,.68972],77:[0,.68333,0,.13889,1.2009],78:[0,.68333,.14736,.08334,.82049],79:[0,.68333,.02778,.11111,.79611],80:[0,.68333,.08222,.08334,.69556],81:[.09722,.68333,0,.11111,.81667],82:[0,.68333,0,.08334,.8475],83:[0,.68333,.075,.13889,.60556],84:[0,.68333,.25417,0,.54464],85:[0,.68333,.09931,.08334,.62583],86:[0,.68333,.08222,0,.61278],87:[0,.68333,.08222,.08334,.98778],88:[0,.68333,.14643,.13889,.7133],89:[.09722,.68333,.08222,.08334,.66834],90:[0,.68333,.07944,.13889,.72473],160:[0,0,0,0,.25]},"Fraktur-Regular":{32:[0,0,0,0,.25],33:[0,.69141,0,0,.29574],34:[0,.69141,0,0,.21471],38:[0,.69141,0,0,.73786],39:[0,.69141,0,0,.21201],40:[.24982,.74947,0,0,.38865],41:[.24982,.74947,0,0,.38865],42:[0,.62119,0,0,.27764],43:[.08319,.58283,0,0,.75623],44:[0,.10803,0,0,.27764],45:[.08319,.58283,0,0,.75623],46:[0,.10803,0,0,.27764],47:[.24982,.74947,0,0,.50181],48:[0,.47534,0,0,.50181],49:[0,.47534,0,0,.50181],50:[0,.47534,0,0,.50181],51:[.18906,.47534,0,0,.50181],52:[.18906,.47534,0,0,.50181],53:[.18906,.47534,0,0,.50181],54:[0,.69141,0,0,.50181],55:[.18906,.47534,0,0,.50181],56:[0,.69141,0,0,.50181],57:[.18906,.47534,0,0,.50181],58:[0,.47534,0,0,.21606],59:[.12604,.47534,0,0,.21606],61:[-.13099,.36866,0,0,.75623],63:[0,.69141,0,0,.36245],65:[0,.69141,0,0,.7176],66:[0,.69141,0,0,.88397],67:[0,.69141,0,0,.61254],68:[0,.69141,0,0,.83158],69:[0,.69141,0,0,.66278],70:[.12604,.69141,0,0,.61119],71:[0,.69141,0,0,.78539],72:[.06302,.69141,0,0,.7203],73:[0,.69141,0,0,.55448],74:[.12604,.69141,0,0,.55231],75:[0,.69141,0,0,.66845],76:[0,.69141,0,0,.66602],77:[0,.69141,0,0,1.04953],78:[0,.69141,0,0,.83212],79:[0,.69141,0,0,.82699],80:[.18906,.69141,0,0,.82753],81:[.03781,.69141,0,0,.82699],82:[0,.69141,0,0,.82807],83:[0,.69141,0,0,.82861],84:[0,.69141,0,0,.66899],85:[0,.69141,0,0,.64576],86:[0,.69141,0,0,.83131],87:[0,.69141,0,0,1.04602],88:[0,.69141,0,0,.71922],89:[.18906,.69141,0,0,.83293],90:[.12604,.69141,0,0,.60201],91:[.24982,.74947,0,0,.27764],93:[.24982,.74947,0,0,.27764],94:[0,.69141,0,0,.49965],97:[0,.47534,0,0,.50046],98:[0,.69141,0,0,.51315],99:[0,.47534,0,0,.38946],100:[0,.62119,0,0,.49857],101:[0,.47534,0,0,.40053],102:[.18906,.69141,0,0,.32626],103:[.18906,.47534,0,0,.5037],104:[.18906,.69141,0,0,.52126],105:[0,.69141,0,0,.27899],106:[0,.69141,0,0,.28088],107:[0,.69141,0,0,.38946],108:[0,.69141,0,0,.27953],109:[0,.47534,0,0,.76676],110:[0,.47534,0,0,.52666],111:[0,.47534,0,0,.48885],112:[.18906,.52396,0,0,.50046],113:[.18906,.47534,0,0,.48912],114:[0,.47534,0,0,.38919],115:[0,.47534,0,0,.44266],116:[0,.62119,0,0,.33301],117:[0,.47534,0,0,.5172],118:[0,.52396,0,0,.5118],119:[0,.52396,0,0,.77351],120:[.18906,.47534,0,0,.38865],121:[.18906,.47534,0,0,.49884],122:[.18906,.47534,0,0,.39054],160:[0,0,0,0,.25],8216:[0,.69141,0,0,.21471],8217:[0,.69141,0,0,.21471],58112:[0,.62119,0,0,.49749],58113:[0,.62119,0,0,.4983],58114:[.18906,.69141,0,0,.33328],58115:[.18906,.69141,0,0,.32923],58116:[.18906,.47534,0,0,.50343],58117:[0,.69141,0,0,.33301],58118:[0,.62119,0,0,.33409],58119:[0,.47534,0,0,.50073]},"Main-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.35],34:[0,.69444,0,0,.60278],35:[.19444,.69444,0,0,.95833],36:[.05556,.75,0,0,.575],37:[.05556,.75,0,0,.95833],38:[0,.69444,0,0,.89444],39:[0,.69444,0,0,.31944],40:[.25,.75,0,0,.44722],41:[.25,.75,0,0,.44722],42:[0,.75,0,0,.575],43:[.13333,.63333,0,0,.89444],44:[.19444,.15556,0,0,.31944],45:[0,.44444,0,0,.38333],46:[0,.15556,0,0,.31944],47:[.25,.75,0,0,.575],48:[0,.64444,0,0,.575],49:[0,.64444,0,0,.575],50:[0,.64444,0,0,.575],51:[0,.64444,0,0,.575],52:[0,.64444,0,0,.575],53:[0,.64444,0,0,.575],54:[0,.64444,0,0,.575],55:[0,.64444,0,0,.575],56:[0,.64444,0,0,.575],57:[0,.64444,0,0,.575],58:[0,.44444,0,0,.31944],59:[.19444,.44444,0,0,.31944],60:[.08556,.58556,0,0,.89444],61:[-.10889,.39111,0,0,.89444],62:[.08556,.58556,0,0,.89444],63:[0,.69444,0,0,.54305],64:[0,.69444,0,0,.89444],65:[0,.68611,0,0,.86944],66:[0,.68611,0,0,.81805],67:[0,.68611,0,0,.83055],68:[0,.68611,0,0,.88194],69:[0,.68611,0,0,.75555],70:[0,.68611,0,0,.72361],71:[0,.68611,0,0,.90416],72:[0,.68611,0,0,.9],73:[0,.68611,0,0,.43611],74:[0,.68611,0,0,.59444],75:[0,.68611,0,0,.90138],76:[0,.68611,0,0,.69166],77:[0,.68611,0,0,1.09166],78:[0,.68611,0,0,.9],79:[0,.68611,0,0,.86388],80:[0,.68611,0,0,.78611],81:[.19444,.68611,0,0,.86388],82:[0,.68611,0,0,.8625],83:[0,.68611,0,0,.63889],84:[0,.68611,0,0,.8],85:[0,.68611,0,0,.88472],86:[0,.68611,.01597,0,.86944],87:[0,.68611,.01597,0,1.18888],88:[0,.68611,0,0,.86944],89:[0,.68611,.02875,0,.86944],90:[0,.68611,0,0,.70277],91:[.25,.75,0,0,.31944],92:[.25,.75,0,0,.575],93:[.25,.75,0,0,.31944],94:[0,.69444,0,0,.575],95:[.31,.13444,.03194,0,.575],97:[0,.44444,0,0,.55902],98:[0,.69444,0,0,.63889],99:[0,.44444,0,0,.51111],100:[0,.69444,0,0,.63889],101:[0,.44444,0,0,.52708],102:[0,.69444,.10903,0,.35139],103:[.19444,.44444,.01597,0,.575],104:[0,.69444,0,0,.63889],105:[0,.69444,0,0,.31944],106:[.19444,.69444,0,0,.35139],107:[0,.69444,0,0,.60694],108:[0,.69444,0,0,.31944],109:[0,.44444,0,0,.95833],110:[0,.44444,0,0,.63889],111:[0,.44444,0,0,.575],112:[.19444,.44444,0,0,.63889],113:[.19444,.44444,0,0,.60694],114:[0,.44444,0,0,.47361],115:[0,.44444,0,0,.45361],116:[0,.63492,0,0,.44722],117:[0,.44444,0,0,.63889],118:[0,.44444,.01597,0,.60694],119:[0,.44444,.01597,0,.83055],120:[0,.44444,0,0,.60694],121:[.19444,.44444,.01597,0,.60694],122:[0,.44444,0,0,.51111],123:[.25,.75,0,0,.575],124:[.25,.75,0,0,.31944],125:[.25,.75,0,0,.575],126:[.35,.34444,0,0,.575],160:[0,0,0,0,.25],163:[0,.69444,0,0,.86853],168:[0,.69444,0,0,.575],172:[0,.44444,0,0,.76666],176:[0,.69444,0,0,.86944],177:[.13333,.63333,0,0,.89444],184:[.17014,0,0,0,.51111],198:[0,.68611,0,0,1.04166],215:[.13333,.63333,0,0,.89444],216:[.04861,.73472,0,0,.89444],223:[0,.69444,0,0,.59722],230:[0,.44444,0,0,.83055],247:[.13333,.63333,0,0,.89444],248:[.09722,.54167,0,0,.575],305:[0,.44444,0,0,.31944],338:[0,.68611,0,0,1.16944],339:[0,.44444,0,0,.89444],567:[.19444,.44444,0,0,.35139],710:[0,.69444,0,0,.575],711:[0,.63194,0,0,.575],713:[0,.59611,0,0,.575],714:[0,.69444,0,0,.575],715:[0,.69444,0,0,.575],728:[0,.69444,0,0,.575],729:[0,.69444,0,0,.31944],730:[0,.69444,0,0,.86944],732:[0,.69444,0,0,.575],733:[0,.69444,0,0,.575],915:[0,.68611,0,0,.69166],916:[0,.68611,0,0,.95833],920:[0,.68611,0,0,.89444],923:[0,.68611,0,0,.80555],926:[0,.68611,0,0,.76666],928:[0,.68611,0,0,.9],931:[0,.68611,0,0,.83055],933:[0,.68611,0,0,.89444],934:[0,.68611,0,0,.83055],936:[0,.68611,0,0,.89444],937:[0,.68611,0,0,.83055],8211:[0,.44444,.03194,0,.575],8212:[0,.44444,.03194,0,1.14999],8216:[0,.69444,0,0,.31944],8217:[0,.69444,0,0,.31944],8220:[0,.69444,0,0,.60278],8221:[0,.69444,0,0,.60278],8224:[.19444,.69444,0,0,.51111],8225:[.19444,.69444,0,0,.51111],8242:[0,.55556,0,0,.34444],8407:[0,.72444,.15486,0,.575],8463:[0,.69444,0,0,.66759],8465:[0,.69444,0,0,.83055],8467:[0,.69444,0,0,.47361],8472:[.19444,.44444,0,0,.74027],8476:[0,.69444,0,0,.83055],8501:[0,.69444,0,0,.70277],8592:[-.10889,.39111,0,0,1.14999],8593:[.19444,.69444,0,0,.575],8594:[-.10889,.39111,0,0,1.14999],8595:[.19444,.69444,0,0,.575],8596:[-.10889,.39111,0,0,1.14999],8597:[.25,.75,0,0,.575],8598:[.19444,.69444,0,0,1.14999],8599:[.19444,.69444,0,0,1.14999],8600:[.19444,.69444,0,0,1.14999],8601:[.19444,.69444,0,0,1.14999],8636:[-.10889,.39111,0,0,1.14999],8637:[-.10889,.39111,0,0,1.14999],8640:[-.10889,.39111,0,0,1.14999],8641:[-.10889,.39111,0,0,1.14999],8656:[-.10889,.39111,0,0,1.14999],8657:[.19444,.69444,0,0,.70277],8658:[-.10889,.39111,0,0,1.14999],8659:[.19444,.69444,0,0,.70277],8660:[-.10889,.39111,0,0,1.14999],8661:[.25,.75,0,0,.70277],8704:[0,.69444,0,0,.63889],8706:[0,.69444,.06389,0,.62847],8707:[0,.69444,0,0,.63889],8709:[.05556,.75,0,0,.575],8711:[0,.68611,0,0,.95833],8712:[.08556,.58556,0,0,.76666],8715:[.08556,.58556,0,0,.76666],8722:[.13333,.63333,0,0,.89444],8723:[.13333,.63333,0,0,.89444],8725:[.25,.75,0,0,.575],8726:[.25,.75,0,0,.575],8727:[-.02778,.47222,0,0,.575],8728:[-.02639,.47361,0,0,.575],8729:[-.02639,.47361,0,0,.575],8730:[.18,.82,0,0,.95833],8733:[0,.44444,0,0,.89444],8734:[0,.44444,0,0,1.14999],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.31944],8741:[.25,.75,0,0,.575],8743:[0,.55556,0,0,.76666],8744:[0,.55556,0,0,.76666],8745:[0,.55556,0,0,.76666],8746:[0,.55556,0,0,.76666],8747:[.19444,.69444,.12778,0,.56875],8764:[-.10889,.39111,0,0,.89444],8768:[.19444,.69444,0,0,.31944],8771:[.00222,.50222,0,0,.89444],8773:[.027,.638,0,0,.894],8776:[.02444,.52444,0,0,.89444],8781:[.00222,.50222,0,0,.89444],8801:[.00222,.50222,0,0,.89444],8804:[.19667,.69667,0,0,.89444],8805:[.19667,.69667,0,0,.89444],8810:[.08556,.58556,0,0,1.14999],8811:[.08556,.58556,0,0,1.14999],8826:[.08556,.58556,0,0,.89444],8827:[.08556,.58556,0,0,.89444],8834:[.08556,.58556,0,0,.89444],8835:[.08556,.58556,0,0,.89444],8838:[.19667,.69667,0,0,.89444],8839:[.19667,.69667,0,0,.89444],8846:[0,.55556,0,0,.76666],8849:[.19667,.69667,0,0,.89444],8850:[.19667,.69667,0,0,.89444],8851:[0,.55556,0,0,.76666],8852:[0,.55556,0,0,.76666],8853:[.13333,.63333,0,0,.89444],8854:[.13333,.63333,0,0,.89444],8855:[.13333,.63333,0,0,.89444],8856:[.13333,.63333,0,0,.89444],8857:[.13333,.63333,0,0,.89444],8866:[0,.69444,0,0,.70277],8867:[0,.69444,0,0,.70277],8868:[0,.69444,0,0,.89444],8869:[0,.69444,0,0,.89444],8900:[-.02639,.47361,0,0,.575],8901:[-.02639,.47361,0,0,.31944],8902:[-.02778,.47222,0,0,.575],8968:[.25,.75,0,0,.51111],8969:[.25,.75,0,0,.51111],8970:[.25,.75,0,0,.51111],8971:[.25,.75,0,0,.51111],8994:[-.13889,.36111,0,0,1.14999],8995:[-.13889,.36111,0,0,1.14999],9651:[.19444,.69444,0,0,1.02222],9657:[-.02778,.47222,0,0,.575],9661:[.19444,.69444,0,0,1.02222],9667:[-.02778,.47222,0,0,.575],9711:[.19444,.69444,0,0,1.14999],9824:[.12963,.69444,0,0,.89444],9825:[.12963,.69444,0,0,.89444],9826:[.12963,.69444,0,0,.89444],9827:[.12963,.69444,0,0,.89444],9837:[0,.75,0,0,.44722],9838:[.19444,.69444,0,0,.44722],9839:[.19444,.69444,0,0,.44722],10216:[.25,.75,0,0,.44722],10217:[.25,.75,0,0,.44722],10815:[0,.68611,0,0,.9],10927:[.19667,.69667,0,0,.89444],10928:[.19667,.69667,0,0,.89444],57376:[.19444,.69444,0,0,0]},"Main-BoldItalic":{32:[0,0,0,0,.25],33:[0,.69444,.11417,0,.38611],34:[0,.69444,.07939,0,.62055],35:[.19444,.69444,.06833,0,.94444],37:[.05556,.75,.12861,0,.94444],38:[0,.69444,.08528,0,.88555],39:[0,.69444,.12945,0,.35555],40:[.25,.75,.15806,0,.47333],41:[.25,.75,.03306,0,.47333],42:[0,.75,.14333,0,.59111],43:[.10333,.60333,.03306,0,.88555],44:[.19444,.14722,0,0,.35555],45:[0,.44444,.02611,0,.41444],46:[0,.14722,0,0,.35555],47:[.25,.75,.15806,0,.59111],48:[0,.64444,.13167,0,.59111],49:[0,.64444,.13167,0,.59111],50:[0,.64444,.13167,0,.59111],51:[0,.64444,.13167,0,.59111],52:[.19444,.64444,.13167,0,.59111],53:[0,.64444,.13167,0,.59111],54:[0,.64444,.13167,0,.59111],55:[.19444,.64444,.13167,0,.59111],56:[0,.64444,.13167,0,.59111],57:[0,.64444,.13167,0,.59111],58:[0,.44444,.06695,0,.35555],59:[.19444,.44444,.06695,0,.35555],61:[-.10889,.39111,.06833,0,.88555],63:[0,.69444,.11472,0,.59111],64:[0,.69444,.09208,0,.88555],65:[0,.68611,0,0,.86555],66:[0,.68611,.0992,0,.81666],67:[0,.68611,.14208,0,.82666],68:[0,.68611,.09062,0,.87555],69:[0,.68611,.11431,0,.75666],70:[0,.68611,.12903,0,.72722],71:[0,.68611,.07347,0,.89527],72:[0,.68611,.17208,0,.8961],73:[0,.68611,.15681,0,.47166],74:[0,.68611,.145,0,.61055],75:[0,.68611,.14208,0,.89499],76:[0,.68611,0,0,.69777],77:[0,.68611,.17208,0,1.07277],78:[0,.68611,.17208,0,.8961],79:[0,.68611,.09062,0,.85499],80:[0,.68611,.0992,0,.78721],81:[.19444,.68611,.09062,0,.85499],82:[0,.68611,.02559,0,.85944],83:[0,.68611,.11264,0,.64999],84:[0,.68611,.12903,0,.7961],85:[0,.68611,.17208,0,.88083],86:[0,.68611,.18625,0,.86555],87:[0,.68611,.18625,0,1.15999],88:[0,.68611,.15681,0,.86555],89:[0,.68611,.19803,0,.86555],90:[0,.68611,.14208,0,.70888],91:[.25,.75,.1875,0,.35611],93:[.25,.75,.09972,0,.35611],94:[0,.69444,.06709,0,.59111],95:[.31,.13444,.09811,0,.59111],97:[0,.44444,.09426,0,.59111],98:[0,.69444,.07861,0,.53222],99:[0,.44444,.05222,0,.53222],100:[0,.69444,.10861,0,.59111],101:[0,.44444,.085,0,.53222],102:[.19444,.69444,.21778,0,.4],103:[.19444,.44444,.105,0,.53222],104:[0,.69444,.09426,0,.59111],105:[0,.69326,.11387,0,.35555],106:[.19444,.69326,.1672,0,.35555],107:[0,.69444,.11111,0,.53222],108:[0,.69444,.10861,0,.29666],109:[0,.44444,.09426,0,.94444],110:[0,.44444,.09426,0,.64999],111:[0,.44444,.07861,0,.59111],112:[.19444,.44444,.07861,0,.59111],113:[.19444,.44444,.105,0,.53222],114:[0,.44444,.11111,0,.50167],115:[0,.44444,.08167,0,.48694],116:[0,.63492,.09639,0,.385],117:[0,.44444,.09426,0,.62055],118:[0,.44444,.11111,0,.53222],119:[0,.44444,.11111,0,.76777],120:[0,.44444,.12583,0,.56055],121:[.19444,.44444,.105,0,.56166],122:[0,.44444,.13889,0,.49055],126:[.35,.34444,.11472,0,.59111],160:[0,0,0,0,.25],168:[0,.69444,.11473,0,.59111],176:[0,.69444,0,0,.94888],184:[.17014,0,0,0,.53222],198:[0,.68611,.11431,0,1.02277],216:[.04861,.73472,.09062,0,.88555],223:[.19444,.69444,.09736,0,.665],230:[0,.44444,.085,0,.82666],248:[.09722,.54167,.09458,0,.59111],305:[0,.44444,.09426,0,.35555],338:[0,.68611,.11431,0,1.14054],339:[0,.44444,.085,0,.82666],567:[.19444,.44444,.04611,0,.385],710:[0,.69444,.06709,0,.59111],711:[0,.63194,.08271,0,.59111],713:[0,.59444,.10444,0,.59111],714:[0,.69444,.08528,0,.59111],715:[0,.69444,0,0,.59111],728:[0,.69444,.10333,0,.59111],729:[0,.69444,.12945,0,.35555],730:[0,.69444,0,0,.94888],732:[0,.69444,.11472,0,.59111],733:[0,.69444,.11472,0,.59111],915:[0,.68611,.12903,0,.69777],916:[0,.68611,0,0,.94444],920:[0,.68611,.09062,0,.88555],923:[0,.68611,0,0,.80666],926:[0,.68611,.15092,0,.76777],928:[0,.68611,.17208,0,.8961],931:[0,.68611,.11431,0,.82666],933:[0,.68611,.10778,0,.88555],934:[0,.68611,.05632,0,.82666],936:[0,.68611,.10778,0,.88555],937:[0,.68611,.0992,0,.82666],8211:[0,.44444,.09811,0,.59111],8212:[0,.44444,.09811,0,1.18221],8216:[0,.69444,.12945,0,.35555],8217:[0,.69444,.12945,0,.35555],8220:[0,.69444,.16772,0,.62055],8221:[0,.69444,.07939,0,.62055]},"Main-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.12417,0,.30667],34:[0,.69444,.06961,0,.51444],35:[.19444,.69444,.06616,0,.81777],37:[.05556,.75,.13639,0,.81777],38:[0,.69444,.09694,0,.76666],39:[0,.69444,.12417,0,.30667],40:[.25,.75,.16194,0,.40889],41:[.25,.75,.03694,0,.40889],42:[0,.75,.14917,0,.51111],43:[.05667,.56167,.03694,0,.76666],44:[.19444,.10556,0,0,.30667],45:[0,.43056,.02826,0,.35778],46:[0,.10556,0,0,.30667],47:[.25,.75,.16194,0,.51111],48:[0,.64444,.13556,0,.51111],49:[0,.64444,.13556,0,.51111],50:[0,.64444,.13556,0,.51111],51:[0,.64444,.13556,0,.51111],52:[.19444,.64444,.13556,0,.51111],53:[0,.64444,.13556,0,.51111],54:[0,.64444,.13556,0,.51111],55:[.19444,.64444,.13556,0,.51111],56:[0,.64444,.13556,0,.51111],57:[0,.64444,.13556,0,.51111],58:[0,.43056,.0582,0,.30667],59:[.19444,.43056,.0582,0,.30667],61:[-.13313,.36687,.06616,0,.76666],63:[0,.69444,.1225,0,.51111],64:[0,.69444,.09597,0,.76666],65:[0,.68333,0,0,.74333],66:[0,.68333,.10257,0,.70389],67:[0,.68333,.14528,0,.71555],68:[0,.68333,.09403,0,.755],69:[0,.68333,.12028,0,.67833],70:[0,.68333,.13305,0,.65277],71:[0,.68333,.08722,0,.77361],72:[0,.68333,.16389,0,.74333],73:[0,.68333,.15806,0,.38555],74:[0,.68333,.14028,0,.525],75:[0,.68333,.14528,0,.76888],76:[0,.68333,0,0,.62722],77:[0,.68333,.16389,0,.89666],78:[0,.68333,.16389,0,.74333],79:[0,.68333,.09403,0,.76666],80:[0,.68333,.10257,0,.67833],81:[.19444,.68333,.09403,0,.76666],82:[0,.68333,.03868,0,.72944],83:[0,.68333,.11972,0,.56222],84:[0,.68333,.13305,0,.71555],85:[0,.68333,.16389,0,.74333],86:[0,.68333,.18361,0,.74333],87:[0,.68333,.18361,0,.99888],88:[0,.68333,.15806,0,.74333],89:[0,.68333,.19383,0,.74333],90:[0,.68333,.14528,0,.61333],91:[.25,.75,.1875,0,.30667],93:[.25,.75,.10528,0,.30667],94:[0,.69444,.06646,0,.51111],95:[.31,.12056,.09208,0,.51111],97:[0,.43056,.07671,0,.51111],98:[0,.69444,.06312,0,.46],99:[0,.43056,.05653,0,.46],100:[0,.69444,.10333,0,.51111],101:[0,.43056,.07514,0,.46],102:[.19444,.69444,.21194,0,.30667],103:[.19444,.43056,.08847,0,.46],104:[0,.69444,.07671,0,.51111],105:[0,.65536,.1019,0,.30667],106:[.19444,.65536,.14467,0,.30667],107:[0,.69444,.10764,0,.46],108:[0,.69444,.10333,0,.25555],109:[0,.43056,.07671,0,.81777],110:[0,.43056,.07671,0,.56222],111:[0,.43056,.06312,0,.51111],112:[.19444,.43056,.06312,0,.51111],113:[.19444,.43056,.08847,0,.46],114:[0,.43056,.10764,0,.42166],115:[0,.43056,.08208,0,.40889],116:[0,.61508,.09486,0,.33222],117:[0,.43056,.07671,0,.53666],118:[0,.43056,.10764,0,.46],119:[0,.43056,.10764,0,.66444],120:[0,.43056,.12042,0,.46389],121:[.19444,.43056,.08847,0,.48555],122:[0,.43056,.12292,0,.40889],126:[.35,.31786,.11585,0,.51111],160:[0,0,0,0,.25],168:[0,.66786,.10474,0,.51111],176:[0,.69444,0,0,.83129],184:[.17014,0,0,0,.46],198:[0,.68333,.12028,0,.88277],216:[.04861,.73194,.09403,0,.76666],223:[.19444,.69444,.10514,0,.53666],230:[0,.43056,.07514,0,.71555],248:[.09722,.52778,.09194,0,.51111],338:[0,.68333,.12028,0,.98499],339:[0,.43056,.07514,0,.71555],710:[0,.69444,.06646,0,.51111],711:[0,.62847,.08295,0,.51111],713:[0,.56167,.10333,0,.51111],714:[0,.69444,.09694,0,.51111],715:[0,.69444,0,0,.51111],728:[0,.69444,.10806,0,.51111],729:[0,.66786,.11752,0,.30667],730:[0,.69444,0,0,.83129],732:[0,.66786,.11585,0,.51111],733:[0,.69444,.1225,0,.51111],915:[0,.68333,.13305,0,.62722],916:[0,.68333,0,0,.81777],920:[0,.68333,.09403,0,.76666],923:[0,.68333,0,0,.69222],926:[0,.68333,.15294,0,.66444],928:[0,.68333,.16389,0,.74333],931:[0,.68333,.12028,0,.71555],933:[0,.68333,.11111,0,.76666],934:[0,.68333,.05986,0,.71555],936:[0,.68333,.11111,0,.76666],937:[0,.68333,.10257,0,.71555],8211:[0,.43056,.09208,0,.51111],8212:[0,.43056,.09208,0,1.02222],8216:[0,.69444,.12417,0,.30667],8217:[0,.69444,.12417,0,.30667],8220:[0,.69444,.1685,0,.51444],8221:[0,.69444,.06961,0,.51444],8463:[0,.68889,0,0,.54028]},"Main-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.27778],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.77778],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.19444,.10556,0,0,.27778],45:[0,.43056,0,0,.33333],46:[0,.10556,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.64444,0,0,.5],49:[0,.64444,0,0,.5],50:[0,.64444,0,0,.5],51:[0,.64444,0,0,.5],52:[0,.64444,0,0,.5],53:[0,.64444,0,0,.5],54:[0,.64444,0,0,.5],55:[0,.64444,0,0,.5],56:[0,.64444,0,0,.5],57:[0,.64444,0,0,.5],58:[0,.43056,0,0,.27778],59:[.19444,.43056,0,0,.27778],60:[.0391,.5391,0,0,.77778],61:[-.13313,.36687,0,0,.77778],62:[.0391,.5391,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.77778],65:[0,.68333,0,0,.75],66:[0,.68333,0,0,.70834],67:[0,.68333,0,0,.72222],68:[0,.68333,0,0,.76389],69:[0,.68333,0,0,.68056],70:[0,.68333,0,0,.65278],71:[0,.68333,0,0,.78472],72:[0,.68333,0,0,.75],73:[0,.68333,0,0,.36111],74:[0,.68333,0,0,.51389],75:[0,.68333,0,0,.77778],76:[0,.68333,0,0,.625],77:[0,.68333,0,0,.91667],78:[0,.68333,0,0,.75],79:[0,.68333,0,0,.77778],80:[0,.68333,0,0,.68056],81:[.19444,.68333,0,0,.77778],82:[0,.68333,0,0,.73611],83:[0,.68333,0,0,.55556],84:[0,.68333,0,0,.72222],85:[0,.68333,0,0,.75],86:[0,.68333,.01389,0,.75],87:[0,.68333,.01389,0,1.02778],88:[0,.68333,0,0,.75],89:[0,.68333,.025,0,.75],90:[0,.68333,0,0,.61111],91:[.25,.75,0,0,.27778],92:[.25,.75,0,0,.5],93:[.25,.75,0,0,.27778],94:[0,.69444,0,0,.5],95:[.31,.12056,.02778,0,.5],97:[0,.43056,0,0,.5],98:[0,.69444,0,0,.55556],99:[0,.43056,0,0,.44445],100:[0,.69444,0,0,.55556],101:[0,.43056,0,0,.44445],102:[0,.69444,.07778,0,.30556],103:[.19444,.43056,.01389,0,.5],104:[0,.69444,0,0,.55556],105:[0,.66786,0,0,.27778],106:[.19444,.66786,0,0,.30556],107:[0,.69444,0,0,.52778],108:[0,.69444,0,0,.27778],109:[0,.43056,0,0,.83334],110:[0,.43056,0,0,.55556],111:[0,.43056,0,0,.5],112:[.19444,.43056,0,0,.55556],113:[.19444,.43056,0,0,.52778],114:[0,.43056,0,0,.39167],115:[0,.43056,0,0,.39445],116:[0,.61508,0,0,.38889],117:[0,.43056,0,0,.55556],118:[0,.43056,.01389,0,.52778],119:[0,.43056,.01389,0,.72222],120:[0,.43056,0,0,.52778],121:[.19444,.43056,.01389,0,.52778],122:[0,.43056,0,0,.44445],123:[.25,.75,0,0,.5],124:[.25,.75,0,0,.27778],125:[.25,.75,0,0,.5],126:[.35,.31786,0,0,.5],160:[0,0,0,0,.25],163:[0,.69444,0,0,.76909],167:[.19444,.69444,0,0,.44445],168:[0,.66786,0,0,.5],172:[0,.43056,0,0,.66667],176:[0,.69444,0,0,.75],177:[.08333,.58333,0,0,.77778],182:[.19444,.69444,0,0,.61111],184:[.17014,0,0,0,.44445],198:[0,.68333,0,0,.90278],215:[.08333,.58333,0,0,.77778],216:[.04861,.73194,0,0,.77778],223:[0,.69444,0,0,.5],230:[0,.43056,0,0,.72222],247:[.08333,.58333,0,0,.77778],248:[.09722,.52778,0,0,.5],305:[0,.43056,0,0,.27778],338:[0,.68333,0,0,1.01389],339:[0,.43056,0,0,.77778],567:[.19444,.43056,0,0,.30556],710:[0,.69444,0,0,.5],711:[0,.62847,0,0,.5],713:[0,.56778,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.66786,0,0,.27778],730:[0,.69444,0,0,.75],732:[0,.66786,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.68333,0,0,.625],916:[0,.68333,0,0,.83334],920:[0,.68333,0,0,.77778],923:[0,.68333,0,0,.69445],926:[0,.68333,0,0,.66667],928:[0,.68333,0,0,.75],931:[0,.68333,0,0,.72222],933:[0,.68333,0,0,.77778],934:[0,.68333,0,0,.72222],936:[0,.68333,0,0,.77778],937:[0,.68333,0,0,.72222],8211:[0,.43056,.02778,0,.5],8212:[0,.43056,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5],8224:[.19444,.69444,0,0,.44445],8225:[.19444,.69444,0,0,.44445],8230:[0,.123,0,0,1.172],8242:[0,.55556,0,0,.275],8407:[0,.71444,.15382,0,.5],8463:[0,.68889,0,0,.54028],8465:[0,.69444,0,0,.72222],8467:[0,.69444,0,.11111,.41667],8472:[.19444,.43056,0,.11111,.63646],8476:[0,.69444,0,0,.72222],8501:[0,.69444,0,0,.61111],8592:[-.13313,.36687,0,0,1],8593:[.19444,.69444,0,0,.5],8594:[-.13313,.36687,0,0,1],8595:[.19444,.69444,0,0,.5],8596:[-.13313,.36687,0,0,1],8597:[.25,.75,0,0,.5],8598:[.19444,.69444,0,0,1],8599:[.19444,.69444,0,0,1],8600:[.19444,.69444,0,0,1],8601:[.19444,.69444,0,0,1],8614:[.011,.511,0,0,1],8617:[.011,.511,0,0,1.126],8618:[.011,.511,0,0,1.126],8636:[-.13313,.36687,0,0,1],8637:[-.13313,.36687,0,0,1],8640:[-.13313,.36687,0,0,1],8641:[-.13313,.36687,0,0,1],8652:[.011,.671,0,0,1],8656:[-.13313,.36687,0,0,1],8657:[.19444,.69444,0,0,.61111],8658:[-.13313,.36687,0,0,1],8659:[.19444,.69444,0,0,.61111],8660:[-.13313,.36687,0,0,1],8661:[.25,.75,0,0,.61111],8704:[0,.69444,0,0,.55556],8706:[0,.69444,.05556,.08334,.5309],8707:[0,.69444,0,0,.55556],8709:[.05556,.75,0,0,.5],8711:[0,.68333,0,0,.83334],8712:[.0391,.5391,0,0,.66667],8715:[.0391,.5391,0,0,.66667],8722:[.08333,.58333,0,0,.77778],8723:[.08333,.58333,0,0,.77778],8725:[.25,.75,0,0,.5],8726:[.25,.75,0,0,.5],8727:[-.03472,.46528,0,0,.5],8728:[-.05555,.44445,0,0,.5],8729:[-.05555,.44445,0,0,.5],8730:[.2,.8,0,0,.83334],8733:[0,.43056,0,0,.77778],8734:[0,.43056,0,0,1],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.27778],8741:[.25,.75,0,0,.5],8743:[0,.55556,0,0,.66667],8744:[0,.55556,0,0,.66667],8745:[0,.55556,0,0,.66667],8746:[0,.55556,0,0,.66667],8747:[.19444,.69444,.11111,0,.41667],8764:[-.13313,.36687,0,0,.77778],8768:[.19444,.69444,0,0,.27778],8771:[-.03625,.46375,0,0,.77778],8773:[-.022,.589,0,0,.778],8776:[-.01688,.48312,0,0,.77778],8781:[-.03625,.46375,0,0,.77778],8784:[-.133,.673,0,0,.778],8801:[-.03625,.46375,0,0,.77778],8804:[.13597,.63597,0,0,.77778],8805:[.13597,.63597,0,0,.77778],8810:[.0391,.5391,0,0,1],8811:[.0391,.5391,0,0,1],8826:[.0391,.5391,0,0,.77778],8827:[.0391,.5391,0,0,.77778],8834:[.0391,.5391,0,0,.77778],8835:[.0391,.5391,0,0,.77778],8838:[.13597,.63597,0,0,.77778],8839:[.13597,.63597,0,0,.77778],8846:[0,.55556,0,0,.66667],8849:[.13597,.63597,0,0,.77778],8850:[.13597,.63597,0,0,.77778],8851:[0,.55556,0,0,.66667],8852:[0,.55556,0,0,.66667],8853:[.08333,.58333,0,0,.77778],8854:[.08333,.58333,0,0,.77778],8855:[.08333,.58333,0,0,.77778],8856:[.08333,.58333,0,0,.77778],8857:[.08333,.58333,0,0,.77778],8866:[0,.69444,0,0,.61111],8867:[0,.69444,0,0,.61111],8868:[0,.69444,0,0,.77778],8869:[0,.69444,0,0,.77778],8872:[.249,.75,0,0,.867],8900:[-.05555,.44445,0,0,.5],8901:[-.05555,.44445,0,0,.27778],8902:[-.03472,.46528,0,0,.5],8904:[.005,.505,0,0,.9],8942:[.03,.903,0,0,.278],8943:[-.19,.313,0,0,1.172],8945:[-.1,.823,0,0,1.282],8968:[.25,.75,0,0,.44445],8969:[.25,.75,0,0,.44445],8970:[.25,.75,0,0,.44445],8971:[.25,.75,0,0,.44445],8994:[-.14236,.35764,0,0,1],8995:[-.14236,.35764,0,0,1],9136:[.244,.744,0,0,.412],9137:[.244,.745,0,0,.412],9651:[.19444,.69444,0,0,.88889],9657:[-.03472,.46528,0,0,.5],9661:[.19444,.69444,0,0,.88889],9667:[-.03472,.46528,0,0,.5],9711:[.19444,.69444,0,0,1],9824:[.12963,.69444,0,0,.77778],9825:[.12963,.69444,0,0,.77778],9826:[.12963,.69444,0,0,.77778],9827:[.12963,.69444,0,0,.77778],9837:[0,.75,0,0,.38889],9838:[.19444,.69444,0,0,.38889],9839:[.19444,.69444,0,0,.38889],10216:[.25,.75,0,0,.38889],10217:[.25,.75,0,0,.38889],10222:[.244,.744,0,0,.412],10223:[.244,.745,0,0,.412],10229:[.011,.511,0,0,1.609],10230:[.011,.511,0,0,1.638],10231:[.011,.511,0,0,1.859],10232:[.024,.525,0,0,1.609],10233:[.024,.525,0,0,1.638],10234:[.024,.525,0,0,1.858],10236:[.011,.511,0,0,1.638],10815:[0,.68333,0,0,.75],10927:[.13597,.63597,0,0,.77778],10928:[.13597,.63597,0,0,.77778],57376:[.19444,.69444,0,0,0]},"Math-BoldItalic":{32:[0,0,0,0,.25],48:[0,.44444,0,0,.575],49:[0,.44444,0,0,.575],50:[0,.44444,0,0,.575],51:[.19444,.44444,0,0,.575],52:[.19444,.44444,0,0,.575],53:[.19444,.44444,0,0,.575],54:[0,.64444,0,0,.575],55:[.19444,.44444,0,0,.575],56:[0,.64444,0,0,.575],57:[.19444,.44444,0,0,.575],65:[0,.68611,0,0,.86944],66:[0,.68611,.04835,0,.8664],67:[0,.68611,.06979,0,.81694],68:[0,.68611,.03194,0,.93812],69:[0,.68611,.05451,0,.81007],70:[0,.68611,.15972,0,.68889],71:[0,.68611,0,0,.88673],72:[0,.68611,.08229,0,.98229],73:[0,.68611,.07778,0,.51111],74:[0,.68611,.10069,0,.63125],75:[0,.68611,.06979,0,.97118],76:[0,.68611,0,0,.75555],77:[0,.68611,.11424,0,1.14201],78:[0,.68611,.11424,0,.95034],79:[0,.68611,.03194,0,.83666],80:[0,.68611,.15972,0,.72309],81:[.19444,.68611,0,0,.86861],82:[0,.68611,.00421,0,.87235],83:[0,.68611,.05382,0,.69271],84:[0,.68611,.15972,0,.63663],85:[0,.68611,.11424,0,.80027],86:[0,.68611,.25555,0,.67778],87:[0,.68611,.15972,0,1.09305],88:[0,.68611,.07778,0,.94722],89:[0,.68611,.25555,0,.67458],90:[0,.68611,.06979,0,.77257],97:[0,.44444,0,0,.63287],98:[0,.69444,0,0,.52083],99:[0,.44444,0,0,.51342],100:[0,.69444,0,0,.60972],101:[0,.44444,0,0,.55361],102:[.19444,.69444,.11042,0,.56806],103:[.19444,.44444,.03704,0,.5449],104:[0,.69444,0,0,.66759],105:[0,.69326,0,0,.4048],106:[.19444,.69326,.0622,0,.47083],107:[0,.69444,.01852,0,.6037],108:[0,.69444,.0088,0,.34815],109:[0,.44444,0,0,1.0324],110:[0,.44444,0,0,.71296],111:[0,.44444,0,0,.58472],112:[.19444,.44444,0,0,.60092],113:[.19444,.44444,.03704,0,.54213],114:[0,.44444,.03194,0,.5287],115:[0,.44444,0,0,.53125],116:[0,.63492,0,0,.41528],117:[0,.44444,0,0,.68102],118:[0,.44444,.03704,0,.56666],119:[0,.44444,.02778,0,.83148],120:[0,.44444,0,0,.65903],121:[.19444,.44444,.03704,0,.59028],122:[0,.44444,.04213,0,.55509],160:[0,0,0,0,.25],915:[0,.68611,.15972,0,.65694],916:[0,.68611,0,0,.95833],920:[0,.68611,.03194,0,.86722],923:[0,.68611,0,0,.80555],926:[0,.68611,.07458,0,.84125],928:[0,.68611,.08229,0,.98229],931:[0,.68611,.05451,0,.88507],933:[0,.68611,.15972,0,.67083],934:[0,.68611,0,0,.76666],936:[0,.68611,.11653,0,.71402],937:[0,.68611,.04835,0,.8789],945:[0,.44444,0,0,.76064],946:[.19444,.69444,.03403,0,.65972],947:[.19444,.44444,.06389,0,.59003],948:[0,.69444,.03819,0,.52222],949:[0,.44444,0,0,.52882],950:[.19444,.69444,.06215,0,.50833],951:[.19444,.44444,.03704,0,.6],952:[0,.69444,.03194,0,.5618],953:[0,.44444,0,0,.41204],954:[0,.44444,0,0,.66759],955:[0,.69444,0,0,.67083],956:[.19444,.44444,0,0,.70787],957:[0,.44444,.06898,0,.57685],958:[.19444,.69444,.03021,0,.50833],959:[0,.44444,0,0,.58472],960:[0,.44444,.03704,0,.68241],961:[.19444,.44444,0,0,.6118],962:[.09722,.44444,.07917,0,.42361],963:[0,.44444,.03704,0,.68588],964:[0,.44444,.13472,0,.52083],965:[0,.44444,.03704,0,.63055],966:[.19444,.44444,0,0,.74722],967:[.19444,.44444,0,0,.71805],968:[.19444,.69444,.03704,0,.75833],969:[0,.44444,.03704,0,.71782],977:[0,.69444,0,0,.69155],981:[.19444,.69444,0,0,.7125],982:[0,.44444,.03194,0,.975],1009:[.19444,.44444,0,0,.6118],1013:[0,.44444,0,0,.48333],57649:[0,.44444,0,0,.39352],57911:[.19444,.44444,0,0,.43889]},"Math-Italic":{32:[0,0,0,0,.25],48:[0,.43056,0,0,.5],49:[0,.43056,0,0,.5],50:[0,.43056,0,0,.5],51:[.19444,.43056,0,0,.5],52:[.19444,.43056,0,0,.5],53:[.19444,.43056,0,0,.5],54:[0,.64444,0,0,.5],55:[.19444,.43056,0,0,.5],56:[0,.64444,0,0,.5],57:[.19444,.43056,0,0,.5],65:[0,.68333,0,.13889,.75],66:[0,.68333,.05017,.08334,.75851],67:[0,.68333,.07153,.08334,.71472],68:[0,.68333,.02778,.05556,.82792],69:[0,.68333,.05764,.08334,.7382],70:[0,.68333,.13889,.08334,.64306],71:[0,.68333,0,.08334,.78625],72:[0,.68333,.08125,.05556,.83125],73:[0,.68333,.07847,.11111,.43958],74:[0,.68333,.09618,.16667,.55451],75:[0,.68333,.07153,.05556,.84931],76:[0,.68333,0,.02778,.68056],77:[0,.68333,.10903,.08334,.97014],78:[0,.68333,.10903,.08334,.80347],79:[0,.68333,.02778,.08334,.76278],80:[0,.68333,.13889,.08334,.64201],81:[.19444,.68333,0,.08334,.79056],82:[0,.68333,.00773,.08334,.75929],83:[0,.68333,.05764,.08334,.6132],84:[0,.68333,.13889,.08334,.58438],85:[0,.68333,.10903,.02778,.68278],86:[0,.68333,.22222,0,.58333],87:[0,.68333,.13889,0,.94445],88:[0,.68333,.07847,.08334,.82847],89:[0,.68333,.22222,0,.58056],90:[0,.68333,.07153,.08334,.68264],97:[0,.43056,0,0,.52859],98:[0,.69444,0,0,.42917],99:[0,.43056,0,.05556,.43276],100:[0,.69444,0,.16667,.52049],101:[0,.43056,0,.05556,.46563],102:[.19444,.69444,.10764,.16667,.48959],103:[.19444,.43056,.03588,.02778,.47697],104:[0,.69444,0,0,.57616],105:[0,.65952,0,0,.34451],106:[.19444,.65952,.05724,0,.41181],107:[0,.69444,.03148,0,.5206],108:[0,.69444,.01968,.08334,.29838],109:[0,.43056,0,0,.87801],110:[0,.43056,0,0,.60023],111:[0,.43056,0,.05556,.48472],112:[.19444,.43056,0,.08334,.50313],113:[.19444,.43056,.03588,.08334,.44641],114:[0,.43056,.02778,.05556,.45116],115:[0,.43056,0,.05556,.46875],116:[0,.61508,0,.08334,.36111],117:[0,.43056,0,.02778,.57246],118:[0,.43056,.03588,.02778,.48472],119:[0,.43056,.02691,.08334,.71592],120:[0,.43056,0,.02778,.57153],121:[.19444,.43056,.03588,.05556,.49028],122:[0,.43056,.04398,.05556,.46505],160:[0,0,0,0,.25],915:[0,.68333,.13889,.08334,.61528],916:[0,.68333,0,.16667,.83334],920:[0,.68333,.02778,.08334,.76278],923:[0,.68333,0,.16667,.69445],926:[0,.68333,.07569,.08334,.74236],928:[0,.68333,.08125,.05556,.83125],931:[0,.68333,.05764,.08334,.77986],933:[0,.68333,.13889,.05556,.58333],934:[0,.68333,0,.08334,.66667],936:[0,.68333,.11,.05556,.61222],937:[0,.68333,.05017,.08334,.7724],945:[0,.43056,.0037,.02778,.6397],946:[.19444,.69444,.05278,.08334,.56563],947:[.19444,.43056,.05556,0,.51773],948:[0,.69444,.03785,.05556,.44444],949:[0,.43056,0,.08334,.46632],950:[.19444,.69444,.07378,.08334,.4375],951:[.19444,.43056,.03588,.05556,.49653],952:[0,.69444,.02778,.08334,.46944],953:[0,.43056,0,.05556,.35394],954:[0,.43056,0,0,.57616],955:[0,.69444,0,0,.58334],956:[.19444,.43056,0,.02778,.60255],957:[0,.43056,.06366,.02778,.49398],958:[.19444,.69444,.04601,.11111,.4375],959:[0,.43056,0,.05556,.48472],960:[0,.43056,.03588,0,.57003],961:[.19444,.43056,0,.08334,.51702],962:[.09722,.43056,.07986,.08334,.36285],963:[0,.43056,.03588,0,.57141],964:[0,.43056,.1132,.02778,.43715],965:[0,.43056,.03588,.02778,.54028],966:[.19444,.43056,0,.08334,.65417],967:[.19444,.43056,0,.05556,.62569],968:[.19444,.69444,.03588,.11111,.65139],969:[0,.43056,.03588,0,.62245],977:[0,.69444,0,.08334,.59144],981:[.19444,.69444,0,.08334,.59583],982:[0,.43056,.02778,0,.82813],1009:[.19444,.43056,0,.08334,.51702],1013:[0,.43056,0,.05556,.4059],57649:[0,.43056,0,.02778,.32246],57911:[.19444,.43056,0,.08334,.38403]},"SansSerif-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.36667],34:[0,.69444,0,0,.55834],35:[.19444,.69444,0,0,.91667],36:[.05556,.75,0,0,.55],37:[.05556,.75,0,0,1.02912],38:[0,.69444,0,0,.83056],39:[0,.69444,0,0,.30556],40:[.25,.75,0,0,.42778],41:[.25,.75,0,0,.42778],42:[0,.75,0,0,.55],43:[.11667,.61667,0,0,.85556],44:[.10556,.13056,0,0,.30556],45:[0,.45833,0,0,.36667],46:[0,.13056,0,0,.30556],47:[.25,.75,0,0,.55],48:[0,.69444,0,0,.55],49:[0,.69444,0,0,.55],50:[0,.69444,0,0,.55],51:[0,.69444,0,0,.55],52:[0,.69444,0,0,.55],53:[0,.69444,0,0,.55],54:[0,.69444,0,0,.55],55:[0,.69444,0,0,.55],56:[0,.69444,0,0,.55],57:[0,.69444,0,0,.55],58:[0,.45833,0,0,.30556],59:[.10556,.45833,0,0,.30556],61:[-.09375,.40625,0,0,.85556],63:[0,.69444,0,0,.51945],64:[0,.69444,0,0,.73334],65:[0,.69444,0,0,.73334],66:[0,.69444,0,0,.73334],67:[0,.69444,0,0,.70278],68:[0,.69444,0,0,.79445],69:[0,.69444,0,0,.64167],70:[0,.69444,0,0,.61111],71:[0,.69444,0,0,.73334],72:[0,.69444,0,0,.79445],73:[0,.69444,0,0,.33056],74:[0,.69444,0,0,.51945],75:[0,.69444,0,0,.76389],76:[0,.69444,0,0,.58056],77:[0,.69444,0,0,.97778],78:[0,.69444,0,0,.79445],79:[0,.69444,0,0,.79445],80:[0,.69444,0,0,.70278],81:[.10556,.69444,0,0,.79445],82:[0,.69444,0,0,.70278],83:[0,.69444,0,0,.61111],84:[0,.69444,0,0,.73334],85:[0,.69444,0,0,.76389],86:[0,.69444,.01528,0,.73334],87:[0,.69444,.01528,0,1.03889],88:[0,.69444,0,0,.73334],89:[0,.69444,.0275,0,.73334],90:[0,.69444,0,0,.67223],91:[.25,.75,0,0,.34306],93:[.25,.75,0,0,.34306],94:[0,.69444,0,0,.55],95:[.35,.10833,.03056,0,.55],97:[0,.45833,0,0,.525],98:[0,.69444,0,0,.56111],99:[0,.45833,0,0,.48889],100:[0,.69444,0,0,.56111],101:[0,.45833,0,0,.51111],102:[0,.69444,.07639,0,.33611],103:[.19444,.45833,.01528,0,.55],104:[0,.69444,0,0,.56111],105:[0,.69444,0,0,.25556],106:[.19444,.69444,0,0,.28611],107:[0,.69444,0,0,.53056],108:[0,.69444,0,0,.25556],109:[0,.45833,0,0,.86667],110:[0,.45833,0,0,.56111],111:[0,.45833,0,0,.55],112:[.19444,.45833,0,0,.56111],113:[.19444,.45833,0,0,.56111],114:[0,.45833,.01528,0,.37222],115:[0,.45833,0,0,.42167],116:[0,.58929,0,0,.40417],117:[0,.45833,0,0,.56111],118:[0,.45833,.01528,0,.5],119:[0,.45833,.01528,0,.74445],120:[0,.45833,0,0,.5],121:[.19444,.45833,.01528,0,.5],122:[0,.45833,0,0,.47639],126:[.35,.34444,0,0,.55],160:[0,0,0,0,.25],168:[0,.69444,0,0,.55],176:[0,.69444,0,0,.73334],180:[0,.69444,0,0,.55],184:[.17014,0,0,0,.48889],305:[0,.45833,0,0,.25556],567:[.19444,.45833,0,0,.28611],710:[0,.69444,0,0,.55],711:[0,.63542,0,0,.55],713:[0,.63778,0,0,.55],728:[0,.69444,0,0,.55],729:[0,.69444,0,0,.30556],730:[0,.69444,0,0,.73334],732:[0,.69444,0,0,.55],733:[0,.69444,0,0,.55],915:[0,.69444,0,0,.58056],916:[0,.69444,0,0,.91667],920:[0,.69444,0,0,.85556],923:[0,.69444,0,0,.67223],926:[0,.69444,0,0,.73334],928:[0,.69444,0,0,.79445],931:[0,.69444,0,0,.79445],933:[0,.69444,0,0,.85556],934:[0,.69444,0,0,.79445],936:[0,.69444,0,0,.85556],937:[0,.69444,0,0,.79445],8211:[0,.45833,.03056,0,.55],8212:[0,.45833,.03056,0,1.10001],8216:[0,.69444,0,0,.30556],8217:[0,.69444,0,0,.30556],8220:[0,.69444,0,0,.55834],8221:[0,.69444,0,0,.55834]},"SansSerif-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.05733,0,.31945],34:[0,.69444,.00316,0,.5],35:[.19444,.69444,.05087,0,.83334],36:[.05556,.75,.11156,0,.5],37:[.05556,.75,.03126,0,.83334],38:[0,.69444,.03058,0,.75834],39:[0,.69444,.07816,0,.27778],40:[.25,.75,.13164,0,.38889],41:[.25,.75,.02536,0,.38889],42:[0,.75,.11775,0,.5],43:[.08333,.58333,.02536,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,.01946,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,.13164,0,.5],48:[0,.65556,.11156,0,.5],49:[0,.65556,.11156,0,.5],50:[0,.65556,.11156,0,.5],51:[0,.65556,.11156,0,.5],52:[0,.65556,.11156,0,.5],53:[0,.65556,.11156,0,.5],54:[0,.65556,.11156,0,.5],55:[0,.65556,.11156,0,.5],56:[0,.65556,.11156,0,.5],57:[0,.65556,.11156,0,.5],58:[0,.44444,.02502,0,.27778],59:[.125,.44444,.02502,0,.27778],61:[-.13,.37,.05087,0,.77778],63:[0,.69444,.11809,0,.47222],64:[0,.69444,.07555,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,.08293,0,.66667],67:[0,.69444,.11983,0,.63889],68:[0,.69444,.07555,0,.72223],69:[0,.69444,.11983,0,.59722],70:[0,.69444,.13372,0,.56945],71:[0,.69444,.11983,0,.66667],72:[0,.69444,.08094,0,.70834],73:[0,.69444,.13372,0,.27778],74:[0,.69444,.08094,0,.47222],75:[0,.69444,.11983,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,.08094,0,.875],78:[0,.69444,.08094,0,.70834],79:[0,.69444,.07555,0,.73611],80:[0,.69444,.08293,0,.63889],81:[.125,.69444,.07555,0,.73611],82:[0,.69444,.08293,0,.64584],83:[0,.69444,.09205,0,.55556],84:[0,.69444,.13372,0,.68056],85:[0,.69444,.08094,0,.6875],86:[0,.69444,.1615,0,.66667],87:[0,.69444,.1615,0,.94445],88:[0,.69444,.13372,0,.66667],89:[0,.69444,.17261,0,.66667],90:[0,.69444,.11983,0,.61111],91:[.25,.75,.15942,0,.28889],93:[.25,.75,.08719,0,.28889],94:[0,.69444,.0799,0,.5],95:[.35,.09444,.08616,0,.5],97:[0,.44444,.00981,0,.48056],98:[0,.69444,.03057,0,.51667],99:[0,.44444,.08336,0,.44445],100:[0,.69444,.09483,0,.51667],101:[0,.44444,.06778,0,.44445],102:[0,.69444,.21705,0,.30556],103:[.19444,.44444,.10836,0,.5],104:[0,.69444,.01778,0,.51667],105:[0,.67937,.09718,0,.23889],106:[.19444,.67937,.09162,0,.26667],107:[0,.69444,.08336,0,.48889],108:[0,.69444,.09483,0,.23889],109:[0,.44444,.01778,0,.79445],110:[0,.44444,.01778,0,.51667],111:[0,.44444,.06613,0,.5],112:[.19444,.44444,.0389,0,.51667],113:[.19444,.44444,.04169,0,.51667],114:[0,.44444,.10836,0,.34167],115:[0,.44444,.0778,0,.38333],116:[0,.57143,.07225,0,.36111],117:[0,.44444,.04169,0,.51667],118:[0,.44444,.10836,0,.46111],119:[0,.44444,.10836,0,.68334],120:[0,.44444,.09169,0,.46111],121:[.19444,.44444,.10836,0,.46111],122:[0,.44444,.08752,0,.43472],126:[.35,.32659,.08826,0,.5],160:[0,0,0,0,.25],168:[0,.67937,.06385,0,.5],176:[0,.69444,0,0,.73752],184:[.17014,0,0,0,.44445],305:[0,.44444,.04169,0,.23889],567:[.19444,.44444,.04169,0,.26667],710:[0,.69444,.0799,0,.5],711:[0,.63194,.08432,0,.5],713:[0,.60889,.08776,0,.5],714:[0,.69444,.09205,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,.09483,0,.5],729:[0,.67937,.07774,0,.27778],730:[0,.69444,0,0,.73752],732:[0,.67659,.08826,0,.5],733:[0,.69444,.09205,0,.5],915:[0,.69444,.13372,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,.07555,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,.12816,0,.66667],928:[0,.69444,.08094,0,.70834],931:[0,.69444,.11983,0,.72222],933:[0,.69444,.09031,0,.77778],934:[0,.69444,.04603,0,.72222],936:[0,.69444,.09031,0,.77778],937:[0,.69444,.08293,0,.72222],8211:[0,.44444,.08616,0,.5],8212:[0,.44444,.08616,0,1],8216:[0,.69444,.07816,0,.27778],8217:[0,.69444,.07816,0,.27778],8220:[0,.69444,.14205,0,.5],8221:[0,.69444,.00316,0,.5]},"SansSerif-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.31945],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.75834],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,0,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.65556,0,0,.5],49:[0,.65556,0,0,.5],50:[0,.65556,0,0,.5],51:[0,.65556,0,0,.5],52:[0,.65556,0,0,.5],53:[0,.65556,0,0,.5],54:[0,.65556,0,0,.5],55:[0,.65556,0,0,.5],56:[0,.65556,0,0,.5],57:[0,.65556,0,0,.5],58:[0,.44444,0,0,.27778],59:[.125,.44444,0,0,.27778],61:[-.13,.37,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,0,0,.66667],67:[0,.69444,0,0,.63889],68:[0,.69444,0,0,.72223],69:[0,.69444,0,0,.59722],70:[0,.69444,0,0,.56945],71:[0,.69444,0,0,.66667],72:[0,.69444,0,0,.70834],73:[0,.69444,0,0,.27778],74:[0,.69444,0,0,.47222],75:[0,.69444,0,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,0,0,.875],78:[0,.69444,0,0,.70834],79:[0,.69444,0,0,.73611],80:[0,.69444,0,0,.63889],81:[.125,.69444,0,0,.73611],82:[0,.69444,0,0,.64584],83:[0,.69444,0,0,.55556],84:[0,.69444,0,0,.68056],85:[0,.69444,0,0,.6875],86:[0,.69444,.01389,0,.66667],87:[0,.69444,.01389,0,.94445],88:[0,.69444,0,0,.66667],89:[0,.69444,.025,0,.66667],90:[0,.69444,0,0,.61111],91:[.25,.75,0,0,.28889],93:[.25,.75,0,0,.28889],94:[0,.69444,0,0,.5],95:[.35,.09444,.02778,0,.5],97:[0,.44444,0,0,.48056],98:[0,.69444,0,0,.51667],99:[0,.44444,0,0,.44445],100:[0,.69444,0,0,.51667],101:[0,.44444,0,0,.44445],102:[0,.69444,.06944,0,.30556],103:[.19444,.44444,.01389,0,.5],104:[0,.69444,0,0,.51667],105:[0,.67937,0,0,.23889],106:[.19444,.67937,0,0,.26667],107:[0,.69444,0,0,.48889],108:[0,.69444,0,0,.23889],109:[0,.44444,0,0,.79445],110:[0,.44444,0,0,.51667],111:[0,.44444,0,0,.5],112:[.19444,.44444,0,0,.51667],113:[.19444,.44444,0,0,.51667],114:[0,.44444,.01389,0,.34167],115:[0,.44444,0,0,.38333],116:[0,.57143,0,0,.36111],117:[0,.44444,0,0,.51667],118:[0,.44444,.01389,0,.46111],119:[0,.44444,.01389,0,.68334],120:[0,.44444,0,0,.46111],121:[.19444,.44444,.01389,0,.46111],122:[0,.44444,0,0,.43472],126:[.35,.32659,0,0,.5],160:[0,0,0,0,.25],168:[0,.67937,0,0,.5],176:[0,.69444,0,0,.66667],184:[.17014,0,0,0,.44445],305:[0,.44444,0,0,.23889],567:[.19444,.44444,0,0,.26667],710:[0,.69444,0,0,.5],711:[0,.63194,0,0,.5],713:[0,.60889,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.67937,0,0,.27778],730:[0,.69444,0,0,.66667],732:[0,.67659,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.69444,0,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,0,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,0,0,.66667],928:[0,.69444,0,0,.70834],931:[0,.69444,0,0,.72222],933:[0,.69444,0,0,.77778],934:[0,.69444,0,0,.72222],936:[0,.69444,0,0,.77778],937:[0,.69444,0,0,.72222],8211:[0,.44444,.02778,0,.5],8212:[0,.44444,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5]},"Script-Regular":{32:[0,0,0,0,.25],65:[0,.7,.22925,0,.80253],66:[0,.7,.04087,0,.90757],67:[0,.7,.1689,0,.66619],68:[0,.7,.09371,0,.77443],69:[0,.7,.18583,0,.56162],70:[0,.7,.13634,0,.89544],71:[0,.7,.17322,0,.60961],72:[0,.7,.29694,0,.96919],73:[0,.7,.19189,0,.80907],74:[.27778,.7,.19189,0,1.05159],75:[0,.7,.31259,0,.91364],76:[0,.7,.19189,0,.87373],77:[0,.7,.15981,0,1.08031],78:[0,.7,.3525,0,.9015],79:[0,.7,.08078,0,.73787],80:[0,.7,.08078,0,1.01262],81:[0,.7,.03305,0,.88282],82:[0,.7,.06259,0,.85],83:[0,.7,.19189,0,.86767],84:[0,.7,.29087,0,.74697],85:[0,.7,.25815,0,.79996],86:[0,.7,.27523,0,.62204],87:[0,.7,.27523,0,.80532],88:[0,.7,.26006,0,.94445],89:[0,.7,.2939,0,.70961],90:[0,.7,.24037,0,.8212],160:[0,0,0,0,.25]},"Size1-Regular":{32:[0,0,0,0,.25],40:[.35001,.85,0,0,.45834],41:[.35001,.85,0,0,.45834],47:[.35001,.85,0,0,.57778],91:[.35001,.85,0,0,.41667],92:[.35001,.85,0,0,.57778],93:[.35001,.85,0,0,.41667],123:[.35001,.85,0,0,.58334],125:[.35001,.85,0,0,.58334],160:[0,0,0,0,.25],710:[0,.72222,0,0,.55556],732:[0,.72222,0,0,.55556],770:[0,.72222,0,0,.55556],771:[0,.72222,0,0,.55556],8214:[-99e-5,.601,0,0,.77778],8593:[1e-5,.6,0,0,.66667],8595:[1e-5,.6,0,0,.66667],8657:[1e-5,.6,0,0,.77778],8659:[1e-5,.6,0,0,.77778],8719:[.25001,.75,0,0,.94445],8720:[.25001,.75,0,0,.94445],8721:[.25001,.75,0,0,1.05556],8730:[.35001,.85,0,0,1],8739:[-.00599,.606,0,0,.33333],8741:[-.00599,.606,0,0,.55556],8747:[.30612,.805,.19445,0,.47222],8748:[.306,.805,.19445,0,.47222],8749:[.306,.805,.19445,0,.47222],8750:[.30612,.805,.19445,0,.47222],8896:[.25001,.75,0,0,.83334],8897:[.25001,.75,0,0,.83334],8898:[.25001,.75,0,0,.83334],8899:[.25001,.75,0,0,.83334],8968:[.35001,.85,0,0,.47222],8969:[.35001,.85,0,0,.47222],8970:[.35001,.85,0,0,.47222],8971:[.35001,.85,0,0,.47222],9168:[-99e-5,.601,0,0,.66667],10216:[.35001,.85,0,0,.47222],10217:[.35001,.85,0,0,.47222],10752:[.25001,.75,0,0,1.11111],10753:[.25001,.75,0,0,1.11111],10754:[.25001,.75,0,0,1.11111],10756:[.25001,.75,0,0,.83334],10758:[.25001,.75,0,0,.83334]},"Size2-Regular":{32:[0,0,0,0,.25],40:[.65002,1.15,0,0,.59722],41:[.65002,1.15,0,0,.59722],47:[.65002,1.15,0,0,.81111],91:[.65002,1.15,0,0,.47222],92:[.65002,1.15,0,0,.81111],93:[.65002,1.15,0,0,.47222],123:[.65002,1.15,0,0,.66667],125:[.65002,1.15,0,0,.66667],160:[0,0,0,0,.25],710:[0,.75,0,0,1],732:[0,.75,0,0,1],770:[0,.75,0,0,1],771:[0,.75,0,0,1],8719:[.55001,1.05,0,0,1.27778],8720:[.55001,1.05,0,0,1.27778],8721:[.55001,1.05,0,0,1.44445],8730:[.65002,1.15,0,0,1],8747:[.86225,1.36,.44445,0,.55556],8748:[.862,1.36,.44445,0,.55556],8749:[.862,1.36,.44445,0,.55556],8750:[.86225,1.36,.44445,0,.55556],8896:[.55001,1.05,0,0,1.11111],8897:[.55001,1.05,0,0,1.11111],8898:[.55001,1.05,0,0,1.11111],8899:[.55001,1.05,0,0,1.11111],8968:[.65002,1.15,0,0,.52778],8969:[.65002,1.15,0,0,.52778],8970:[.65002,1.15,0,0,.52778],8971:[.65002,1.15,0,0,.52778],10216:[.65002,1.15,0,0,.61111],10217:[.65002,1.15,0,0,.61111],10752:[.55001,1.05,0,0,1.51112],10753:[.55001,1.05,0,0,1.51112],10754:[.55001,1.05,0,0,1.51112],10756:[.55001,1.05,0,0,1.11111],10758:[.55001,1.05,0,0,1.11111]},"Size3-Regular":{32:[0,0,0,0,.25],40:[.95003,1.45,0,0,.73611],41:[.95003,1.45,0,0,.73611],47:[.95003,1.45,0,0,1.04445],91:[.95003,1.45,0,0,.52778],92:[.95003,1.45,0,0,1.04445],93:[.95003,1.45,0,0,.52778],123:[.95003,1.45,0,0,.75],125:[.95003,1.45,0,0,.75],160:[0,0,0,0,.25],710:[0,.75,0,0,1.44445],732:[0,.75,0,0,1.44445],770:[0,.75,0,0,1.44445],771:[0,.75,0,0,1.44445],8730:[.95003,1.45,0,0,1],8968:[.95003,1.45,0,0,.58334],8969:[.95003,1.45,0,0,.58334],8970:[.95003,1.45,0,0,.58334],8971:[.95003,1.45,0,0,.58334],10216:[.95003,1.45,0,0,.75],10217:[.95003,1.45,0,0,.75]},"Size4-Regular":{32:[0,0,0,0,.25],40:[1.25003,1.75,0,0,.79167],41:[1.25003,1.75,0,0,.79167],47:[1.25003,1.75,0,0,1.27778],91:[1.25003,1.75,0,0,.58334],92:[1.25003,1.75,0,0,1.27778],93:[1.25003,1.75,0,0,.58334],123:[1.25003,1.75,0,0,.80556],125:[1.25003,1.75,0,0,.80556],160:[0,0,0,0,.25],710:[0,.825,0,0,1.8889],732:[0,.825,0,0,1.8889],770:[0,.825,0,0,1.8889],771:[0,.825,0,0,1.8889],8730:[1.25003,1.75,0,0,1],8968:[1.25003,1.75,0,0,.63889],8969:[1.25003,1.75,0,0,.63889],8970:[1.25003,1.75,0,0,.63889],8971:[1.25003,1.75,0,0,.63889],9115:[.64502,1.155,0,0,.875],9116:[1e-5,.6,0,0,.875],9117:[.64502,1.155,0,0,.875],9118:[.64502,1.155,0,0,.875],9119:[1e-5,.6,0,0,.875],9120:[.64502,1.155,0,0,.875],9121:[.64502,1.155,0,0,.66667],9122:[-99e-5,.601,0,0,.66667],9123:[.64502,1.155,0,0,.66667],9124:[.64502,1.155,0,0,.66667],9125:[-99e-5,.601,0,0,.66667],9126:[.64502,1.155,0,0,.66667],9127:[1e-5,.9,0,0,.88889],9128:[.65002,1.15,0,0,.88889],9129:[.90001,0,0,0,.88889],9130:[0,.3,0,0,.88889],9131:[1e-5,.9,0,0,.88889],9132:[.65002,1.15,0,0,.88889],9133:[.90001,0,0,0,.88889],9143:[.88502,.915,0,0,1.05556],10216:[1.25003,1.75,0,0,.80556],10217:[1.25003,1.75,0,0,.80556],57344:[-.00499,.605,0,0,1.05556],57345:[-.00499,.605,0,0,1.05556],57680:[0,.12,0,0,.45],57681:[0,.12,0,0,.45],57682:[0,.12,0,0,.45],57683:[0,.12,0,0,.45]},"Typewriter-Regular":{32:[0,0,0,0,.525],33:[0,.61111,0,0,.525],34:[0,.61111,0,0,.525],35:[0,.61111,0,0,.525],36:[.08333,.69444,0,0,.525],37:[.08333,.69444,0,0,.525],38:[0,.61111,0,0,.525],39:[0,.61111,0,0,.525],40:[.08333,.69444,0,0,.525],41:[.08333,.69444,0,0,.525],42:[0,.52083,0,0,.525],43:[-.08056,.53055,0,0,.525],44:[.13889,.125,0,0,.525],45:[-.08056,.53055,0,0,.525],46:[0,.125,0,0,.525],47:[.08333,.69444,0,0,.525],48:[0,.61111,0,0,.525],49:[0,.61111,0,0,.525],50:[0,.61111,0,0,.525],51:[0,.61111,0,0,.525],52:[0,.61111,0,0,.525],53:[0,.61111,0,0,.525],54:[0,.61111,0,0,.525],55:[0,.61111,0,0,.525],56:[0,.61111,0,0,.525],57:[0,.61111,0,0,.525],58:[0,.43056,0,0,.525],59:[.13889,.43056,0,0,.525],60:[-.05556,.55556,0,0,.525],61:[-.19549,.41562,0,0,.525],62:[-.05556,.55556,0,0,.525],63:[0,.61111,0,0,.525],64:[0,.61111,0,0,.525],65:[0,.61111,0,0,.525],66:[0,.61111,0,0,.525],67:[0,.61111,0,0,.525],68:[0,.61111,0,0,.525],69:[0,.61111,0,0,.525],70:[0,.61111,0,0,.525],71:[0,.61111,0,0,.525],72:[0,.61111,0,0,.525],73:[0,.61111,0,0,.525],74:[0,.61111,0,0,.525],75:[0,.61111,0,0,.525],76:[0,.61111,0,0,.525],77:[0,.61111,0,0,.525],78:[0,.61111,0,0,.525],79:[0,.61111,0,0,.525],80:[0,.61111,0,0,.525],81:[.13889,.61111,0,0,.525],82:[0,.61111,0,0,.525],83:[0,.61111,0,0,.525],84:[0,.61111,0,0,.525],85:[0,.61111,0,0,.525],86:[0,.61111,0,0,.525],87:[0,.61111,0,0,.525],88:[0,.61111,0,0,.525],89:[0,.61111,0,0,.525],90:[0,.61111,0,0,.525],91:[.08333,.69444,0,0,.525],92:[.08333,.69444,0,0,.525],93:[.08333,.69444,0,0,.525],94:[0,.61111,0,0,.525],95:[.09514,0,0,0,.525],96:[0,.61111,0,0,.525],97:[0,.43056,0,0,.525],98:[0,.61111,0,0,.525],99:[0,.43056,0,0,.525],100:[0,.61111,0,0,.525],101:[0,.43056,0,0,.525],102:[0,.61111,0,0,.525],103:[.22222,.43056,0,0,.525],104:[0,.61111,0,0,.525],105:[0,.61111,0,0,.525],106:[.22222,.61111,0,0,.525],107:[0,.61111,0,0,.525],108:[0,.61111,0,0,.525],109:[0,.43056,0,0,.525],110:[0,.43056,0,0,.525],111:[0,.43056,0,0,.525],112:[.22222,.43056,0,0,.525],113:[.22222,.43056,0,0,.525],114:[0,.43056,0,0,.525],115:[0,.43056,0,0,.525],116:[0,.55358,0,0,.525],117:[0,.43056,0,0,.525],118:[0,.43056,0,0,.525],119:[0,.43056,0,0,.525],120:[0,.43056,0,0,.525],121:[.22222,.43056,0,0,.525],122:[0,.43056,0,0,.525],123:[.08333,.69444,0,0,.525],124:[.08333,.69444,0,0,.525],125:[.08333,.69444,0,0,.525],126:[0,.61111,0,0,.525],127:[0,.61111,0,0,.525],160:[0,0,0,0,.525],176:[0,.61111,0,0,.525],184:[.19445,0,0,0,.525],305:[0,.43056,0,0,.525],567:[.22222,.43056,0,0,.525],711:[0,.56597,0,0,.525],713:[0,.56555,0,0,.525],714:[0,.61111,0,0,.525],715:[0,.61111,0,0,.525],728:[0,.61111,0,0,.525],730:[0,.61111,0,0,.525],770:[0,.61111,0,0,.525],771:[0,.61111,0,0,.525],776:[0,.61111,0,0,.525],915:[0,.61111,0,0,.525],916:[0,.61111,0,0,.525],920:[0,.61111,0,0,.525],923:[0,.61111,0,0,.525],926:[0,.61111,0,0,.525],928:[0,.61111,0,0,.525],931:[0,.61111,0,0,.525],933:[0,.61111,0,0,.525],934:[0,.61111,0,0,.525],936:[0,.61111,0,0,.525],937:[0,.61111,0,0,.525],8216:[0,.61111,0,0,.525],8217:[0,.61111,0,0,.525],8242:[0,.61111,0,0,.525],9251:[.11111,.21944,0,0,.525]}};let yn={slant:[.25,.25,.25],space:[0,0,0],stretch:[0,0,0],shrink:[0,0,0],xHeight:[.431,.431,.431],quad:[1,1.171,1.472],extraSpace:[0,0,0],num1:[.677,.732,.925],num2:[.394,.384,.387],num3:[.444,.471,.504],denom1:[.686,.752,1.025],denom2:[.345,.344,.532],sup1:[.413,.503,.504],sup2:[.363,.431,.404],sup3:[.289,.286,.294],sub1:[.15,.143,.2],sub2:[.247,.286,.4],supDrop:[.386,.353,.494],subDrop:[.05,.071,.1],delim1:[2.39,1.7,1.98],delim2:[1.01,1.157,1.42],axisHeight:[.25,.25,.25],defaultRuleThickness:[.04,.049,.049],bigOpSpacing1:[.111,.111,.111],bigOpSpacing2:[.166,.166,.166],bigOpSpacing3:[.2,.2,.2],bigOpSpacing4:[.6,.611,.611],bigOpSpacing5:[.1,.143,.143],sqrtRuleThickness:[.04,.04,.04],ptPerEm:[10,10,10],doubleRuleSep:[.2,.2,.2],arrayRuleWidth:[.04,.04,.04],fboxsep:[.3,.3,.3],fboxrule:[.04,.04,.04]},Bo={\u00C5:"A",\u00D0:"D",\u00DE:"o",\u00E5:"a",\u00F0:"d",\u00FE:"o",\u0410:"A",\u0411:"B",\u0412:"B",\u0413:"F",\u0414:"A",\u0415:"E",\u0416:"K",\u0417:"3",\u0418:"N",\u0419:"N",\u041A:"K",\u041B:"N",\u041C:"M",\u041D:"H",\u041E:"O",\u041F:"N",\u0420:"P",\u0421:"C",\u0422:"T",\u0423:"y",\u0424:"O",\u0425:"X",\u0426:"U",\u0427:"h",\u0428:"W",\u0429:"W",\u042A:"B",\u042B:"X",\u042C:"B",\u042D:"3",\u042E:"X",\u042F:"R",\u0430:"a",\u0431:"b",\u0432:"a",\u0433:"r",\u0434:"y",\u0435:"e",\u0436:"m",\u0437:"e",\u0438:"n",\u0439:"n",\u043A:"n",\u043B:"n",\u043C:"m",\u043D:"n",\u043E:"o",\u043F:"n",\u0440:"p",\u0441:"c",\u0442:"o",\u0443:"y",\u0444:"b",\u0445:"x",\u0446:"n",\u0447:"n",\u0448:"w",\u0449:"w",\u044A:"a",\u044B:"m",\u044C:"a",\u044D:"e",\u044E:"m",\u044F:"r"};function hh(i,n){St[i]=n}function B0(i,n,l){if(!St[n])throw new Error("Font metrics not found for font: "+n+".");let a=i.charCodeAt(0),f=St[n][a];if(!f&&i[0]in Bo&&(a=Bo[i[0]].charCodeAt(0),f=St[n][a]),!f&&l==="text"&&it(a)&&(f=St[n][77]),f)return{depth:f[0],height:f[1],italic:f[2],skew:f[3],width:f[4]}}let O0={};function fh(i){let n;if(i>=5?n=0:i>=3?n=1:n=2,!O0[n]){let l=O0[n]={cssEmPerMu:yn.quad[n]/18};for(let a in yn)yn.hasOwnProperty(a)&&(l[a]=yn[a][n])}return O0[n]}let ph=[[1,1,1],[2,1,1],[3,1,1],[4,2,1],[5,2,1],[6,3,1],[7,4,2],[8,6,3],[9,7,6],[10,8,7],[11,10,9]],Oo=[.5,.6,.7,.8,.9,1,1.2,1.44,1.728,2.074,2.488],Ro=function(i,n){return n.size<2?i:ph[i-1][n.size-1]};class Et{constructor(n){this.style=void 0,this.color=void 0,this.size=void 0,this.textSize=void 0,this.phantom=void 0,this.font=void 0,this.fontFamily=void 0,this.fontWeight=void 0,this.fontShape=void 0,this.sizeMultiplier=void 0,this.maxSize=void 0,this.minRuleThickness=void 0,this._fontMetrics=void 0,this.style=n.style,this.color=n.color,this.size=n.size||Et.BASESIZE,this.textSize=n.textSize||this.size,this.phantom=!!n.phantom,this.font=n.font||"",this.fontFamily=n.fontFamily||"",this.fontWeight=n.fontWeight||"",this.fontShape=n.fontShape||"",this.sizeMultiplier=Oo[this.size-1],this.maxSize=n.maxSize,this.minRuleThickness=n.minRuleThickness,this._fontMetrics=void 0}extend(n){let l={style:this.style,size:this.size,textSize:this.textSize,color:this.color,phantom:this.phantom,font:this.font,fontFamily:this.fontFamily,fontWeight:this.fontWeight,fontShape:this.fontShape,maxSize:this.maxSize,minRuleThickness:this.minRuleThickness};for(let a in n)n.hasOwnProperty(a)&&(l[a]=n[a]);return new Et(l)}havingStyle(n){return this.style===n?this:this.extend({style:n,size:Ro(this.textSize,n)})}havingCrampedStyle(){return this.havingStyle(this.style.cramp())}havingSize(n){return this.size===n&&this.textSize===n?this:this.extend({style:this.style.text(),size:n,textSize:n,sizeMultiplier:Oo[n-1]})}havingBaseStyle(n){n=n||this.style.text();let l=Ro(Et.BASESIZE,n);return this.size===l&&this.textSize===Et.BASESIZE&&this.style===n?this:this.extend({style:n,size:l})}havingBaseSizing(){let n;switch(this.style.id){case 4:case 5:n=3;break;case 6:case 7:n=1;break;default:n=6}return this.extend({style:this.style.text(),size:n})}withColor(n){return this.extend({color:n})}withPhantom(){return this.extend({phantom:!0})}withFont(n){return this.extend({font:n})}withTextFontFamily(n){return this.extend({fontFamily:n,font:""})}withTextFontWeight(n){return this.extend({fontWeight:n,font:""})}withTextFontShape(n){return this.extend({fontShape:n,font:""})}sizingClasses(n){return n.size!==this.size?["sizing","reset-size"+n.size,"size"+this.size]:[]}baseSizingClasses(){return this.size!==Et.BASESIZE?["sizing","reset-size"+this.size,"size"+Et.BASESIZE]:[]}fontMetrics(){return this._fontMetrics||(this._fontMetrics=fh(this.size)),this._fontMetrics}getColor(){return this.phantom?"transparent":this.color}}Et.BASESIZE=6;var dh=Et;let R0={pt:1,mm:7227/2540,cm:7227/254,in:72.27,bp:803/800,pc:12,dd:1238/1157,cc:14856/1157,nd:685/642,nc:1370/107,sp:1/65536,px:803/800},mh={ex:!0,em:!0,mu:!0},Fo=function(i){return typeof i!="string"&&(i=i.unit),i in R0||i in mh||i==="ex"},Ce=function(i,n){let l;if(i.unit in R0)l=R0[i.unit]/n.fontMetrics().ptPerEm/n.sizeMultiplier;else if(i.unit==="mu")l=n.fontMetrics().cssEmPerMu;else{let a;if(n.style.isTight()?a=n.havingStyle(n.style.text()):a=n,i.unit==="ex")l=a.fontMetrics().xHeight;else if(i.unit==="em")l=a.fontMetrics().quad;else throw new o("Invalid unit: '"+i.unit+"'");a!==n&&(l*=a.sizeMultiplier/n.sizeMultiplier)}return Math.min(i.number*l,n.maxSize)},U=function(i){return+i.toFixed(4)+"em"},Vt=function(i){return i.filter(n=>n).join(" ")},Lo=function(i,n,l){if(this.classes=i||[],this.attributes={},this.height=0,this.depth=0,this.maxFontSize=0,this.style=l||{},n){n.style.isTight()&&this.classes.push("mtight");let a=n.getColor();a&&(this.style.color=a)}},qo=function(i){let n=document.createElement(i);n.className=Vt(this.classes);for(let l in this.style)this.style.hasOwnProperty(l)&&(n.style[l]=this.style[l]);for(let l in this.attributes)this.attributes.hasOwnProperty(l)&&n.setAttribute(l,this.attributes[l]);for(let l=0;l<this.children.length;l++)n.appendChild(this.children[l].toNode());return n},gh=/[\s"'>/=\x00-\x1f]/,Po=function(i){let n="<"+i;this.classes.length&&(n+=' class="'+z.escape(Vt(this.classes))+'"');let l="";for(let a in this.style)this.style.hasOwnProperty(a)&&(l+=z.hyphenate(a)+":"+this.style[a]+";");l&&(n+=' style="'+z.escape(l)+'"');for(let a in this.attributes)if(this.attributes.hasOwnProperty(a)){if(gh.test(a))throw new o("Invalid attribute name '"+a+"'");n+=" "+a+'="'+z.escape(this.attributes[a])+'"'}n+=">";for(let a=0;a<this.children.length;a++)n+=this.children[a].toMarkup();return n+="</"+i+">",n};class Rr{constructor(n,l,a,f){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.width=void 0,this.maxFontSize=void 0,this.style=void 0,Lo.call(this,n,a,f),this.children=l||[]}setAttribute(n,l){this.attributes[n]=l}hasClass(n){return z.contains(this.classes,n)}toNode(){return qo.call(this,"span")}toMarkup(){return Po.call(this,"span")}}class F0{constructor(n,l,a,f){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,Lo.call(this,l,f),this.children=a||[],this.setAttribute("href",n)}setAttribute(n,l){this.attributes[n]=l}hasClass(n){return z.contains(this.classes,n)}toNode(){return qo.call(this,"a")}toMarkup(){return Po.call(this,"a")}}class xh{constructor(n,l,a){this.src=void 0,this.alt=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.alt=l,this.src=n,this.classes=["mord"],this.style=a}hasClass(n){return z.contains(this.classes,n)}toNode(){let n=document.createElement("img");n.src=this.src,n.alt=this.alt,n.className="mord";for(let l in this.style)this.style.hasOwnProperty(l)&&(n.style[l]=this.style[l]);return n}toMarkup(){let n='<img src="'+z.escape(this.src)+'"'+(' alt="'+z.escape(this.alt)+'"'),l="";for(let a in this.style)this.style.hasOwnProperty(a)&&(l+=z.hyphenate(a)+":"+this.style[a]+";");return l&&(n+=' style="'+z.escape(l)+'"'),n+="'/>",n}}let yh={\u00EE:"\u0131\u0302",\u00EF:"\u0131\u0308",\u00ED:"\u0131\u0301",\u00EC:"\u0131\u0300"};class ht{constructor(n,l,a,f,d,x,w,A){this.text=void 0,this.height=void 0,this.depth=void 0,this.italic=void 0,this.skew=void 0,this.width=void 0,this.maxFontSize=void 0,this.classes=void 0,this.style=void 0,this.text=n,this.height=l||0,this.depth=a||0,this.italic=f||0,this.skew=d||0,this.width=x||0,this.classes=w||[],this.style=A||{},this.maxFontSize=0;let C=xe(this.text.charCodeAt(0));C&&this.classes.push(C+"_fallback"),/[îïíì]/.test(this.text)&&(this.text=yh[this.text])}hasClass(n){return z.contains(this.classes,n)}toNode(){let n=document.createTextNode(this.text),l=null;this.italic>0&&(l=document.createElement("span"),l.style.marginRight=U(this.italic)),this.classes.length>0&&(l=l||document.createElement("span"),l.className=Vt(this.classes));for(let a in this.style)this.style.hasOwnProperty(a)&&(l=l||document.createElement("span"),l.style[a]=this.style[a]);return l?(l.appendChild(n),l):n}toMarkup(){let n=!1,l="<span";this.classes.length&&(n=!0,l+=' class="',l+=z.escape(Vt(this.classes)),l+='"');let a="";this.italic>0&&(a+="margin-right:"+this.italic+"em;");for(let d in this.style)this.style.hasOwnProperty(d)&&(a+=z.hyphenate(d)+":"+this.style[d]+";");a&&(n=!0,l+=' style="'+z.escape(a)+'"');let f=z.escape(this.text);return n?(l+=">",l+=f,l+="</span>",l):f}}class zt{constructor(n,l){this.children=void 0,this.attributes=void 0,this.children=n||[],this.attributes=l||{}}toNode(){let n="http://www.w3.org/2000/svg",l=document.createElementNS(n,"svg");for(let a in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,a)&&l.setAttribute(a,this.attributes[a]);for(let a=0;a<this.children.length;a++)l.appendChild(this.children[a].toNode());return l}toMarkup(){let n='<svg xmlns="http://www.w3.org/2000/svg"';for(let l in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,l)&&(n+=" "+l+'="'+z.escape(this.attributes[l])+'"');n+=">";for(let l=0;l<this.children.length;l++)n+=this.children[l].toMarkup();return n+="</svg>",n}}class Gt{constructor(n,l){this.pathName=void 0,this.alternate=void 0,this.pathName=n,this.alternate=l}toNode(){let n="http://www.w3.org/2000/svg",l=document.createElementNS(n,"path");return this.alternate?l.setAttribute("d",this.alternate):l.setAttribute("d",Do[this.pathName]),l}toMarkup(){return this.alternate?'<path d="'+z.escape(this.alternate)+'"/>':'<path d="'+z.escape(Do[this.pathName])+'"/>'}}class L0{constructor(n){this.attributes=void 0,this.attributes=n||{}}toNode(){let n="http://www.w3.org/2000/svg",l=document.createElementNS(n,"line");for(let a in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,a)&&l.setAttribute(a,this.attributes[a]);return l}toMarkup(){let n="<line";for(let l in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,l)&&(n+=" "+l+'="'+z.escape(this.attributes[l])+'"');return n+="/>",n}}function Ho(i){if(i instanceof ht)return i;throw new Error("Expected symbolNode but got "+String(i)+".")}function bh(i){if(i instanceof Rr)return i;throw new Error("Expected span<HtmlDomNode> but got "+String(i)+".")}let vh={bin:1,close:1,inner:1,open:1,punct:1,rel:1},wh={"accent-token":1,mathord:1,"op-token":1,spacing:1,textord:1},bn={math:{},text:{}};var Te=bn;function p(i,n,l,a,f,d){bn[i][f]={font:n,group:l,replace:a},d&&a&&(bn[i][a]=bn[i][f])}let m="math",$="text",v="main",_="ams",_e="accent-token",K="bin",je="close",br="inner",re="mathord",Re="op-token",st="open",vn="punct",M="rel",It="spacing",E="textord";p(m,v,M,"\u2261","\\equiv",!0),p(m,v,M,"\u227A","\\prec",!0),p(m,v,M,"\u227B","\\succ",!0),p(m,v,M,"\u223C","\\sim",!0),p(m,v,M,"\u22A5","\\perp"),p(m,v,M,"\u2AAF","\\preceq",!0),p(m,v,M,"\u2AB0","\\succeq",!0),p(m,v,M,"\u2243","\\simeq",!0),p(m,v,M,"\u2223","\\mid",!0),p(m,v,M,"\u226A","\\ll",!0),p(m,v,M,"\u226B","\\gg",!0),p(m,v,M,"\u224D","\\asymp",!0),p(m,v,M,"\u2225","\\parallel"),p(m,v,M,"\u22C8","\\bowtie",!0),p(m,v,M,"\u2323","\\smile",!0),p(m,v,M,"\u2291","\\sqsubseteq",!0),p(m,v,M,"\u2292","\\sqsupseteq",!0),p(m,v,M,"\u2250","\\doteq",!0),p(m,v,M,"\u2322","\\frown",!0),p(m,v,M,"\u220B","\\ni",!0),p(m,v,M,"\u221D","\\propto",!0),p(m,v,M,"\u22A2","\\vdash",!0),p(m,v,M,"\u22A3","\\dashv",!0),p(m,v,M,"\u220B","\\owns"),p(m,v,vn,".","\\ldotp"),p(m,v,vn,"\u22C5","\\cdotp"),p(m,v,E,"#","\\#"),p($,v,E,"#","\\#"),p(m,v,E,"&","\\&"),p($,v,E,"&","\\&"),p(m,v,E,"\u2135","\\aleph",!0),p(m,v,E,"\u2200","\\forall",!0),p(m,v,E,"\u210F","\\hbar",!0),p(m,v,E,"\u2203","\\exists",!0),p(m,v,E,"\u2207","\\nabla",!0),p(m,v,E,"\u266D","\\flat",!0),p(m,v,E,"\u2113","\\ell",!0),p(m,v,E,"\u266E","\\natural",!0),p(m,v,E,"\u2663","\\clubsuit",!0),p(m,v,E,"\u2118","\\wp",!0),p(m,v,E,"\u266F","\\sharp",!0),p(m,v,E,"\u2662","\\diamondsuit",!0),p(m,v,E,"\u211C","\\Re",!0),p(m,v,E,"\u2661","\\heartsuit",!0),p(m,v,E,"\u2111","\\Im",!0),p(m,v,E,"\u2660","\\spadesuit",!0),p(m,v,E,"\xA7","\\S",!0),p($,v,E,"\xA7","\\S"),p(m,v,E,"\xB6","\\P",!0),p($,v,E,"\xB6","\\P"),p(m,v,E,"\u2020","\\dag"),p($,v,E,"\u2020","\\dag"),p($,v,E,"\u2020","\\textdagger"),p(m,v,E,"\u2021","\\ddag"),p($,v,E,"\u2021","\\ddag"),p($,v,E,"\u2021","\\textdaggerdbl"),p(m,v,je,"\u23B1","\\rmoustache",!0),p(m,v,st,"\u23B0","\\lmoustache",!0),p(m,v,je,"\u27EF","\\rgroup",!0),p(m,v,st,"\u27EE","\\lgroup",!0),p(m,v,K,"\u2213","\\mp",!0),p(m,v,K,"\u2296","\\ominus",!0),p(m,v,K,"\u228E","\\uplus",!0),p(m,v,K,"\u2293","\\sqcap",!0),p(m,v,K,"\u2217","\\ast"),p(m,v,K,"\u2294","\\sqcup",!0),p(m,v,K,"\u25EF","\\bigcirc",!0),p(m,v,K,"\u2219","\\bullet",!0),p(m,v,K,"\u2021","\\ddagger"),p(m,v,K,"\u2240","\\wr",!0),p(m,v,K,"\u2A3F","\\amalg"),p(m,v,K,"&","\\And"),p(m,v,M,"\u27F5","\\longleftarrow",!0),p(m,v,M,"\u21D0","\\Leftarrow",!0),p(m,v,M,"\u27F8","\\Longleftarrow",!0),p(m,v,M,"\u27F6","\\longrightarrow",!0),p(m,v,M,"\u21D2","\\Rightarrow",!0),p(m,v,M,"\u27F9","\\Longrightarrow",!0),p(m,v,M,"\u2194","\\leftrightarrow",!0),p(m,v,M,"\u27F7","\\longleftrightarrow",!0),p(m,v,M,"\u21D4","\\Leftrightarrow",!0),p(m,v,M,"\u27FA","\\Longleftrightarrow",!0),p(m,v,M,"\u21A6","\\mapsto",!0),p(m,v,M,"\u27FC","\\longmapsto",!0),p(m,v,M,"\u2197","\\nearrow",!0),p(m,v,M,"\u21A9","\\hookleftarrow",!0),p(m,v,M,"\u21AA","\\hookrightarrow",!0),p(m,v,M,"\u2198","\\searrow",!0),p(m,v,M,"\u21BC","\\leftharpoonup",!0),p(m,v,M,"\u21C0","\\rightharpoonup",!0),p(m,v,M,"\u2199","\\swarrow",!0),p(m,v,M,"\u21BD","\\leftharpoondown",!0),p(m,v,M,"\u21C1","\\rightharpoondown",!0),p(m,v,M,"\u2196","\\nwarrow",!0),p(m,v,M,"\u21CC","\\rightleftharpoons",!0),p(m,_,M,"\u226E","\\nless",!0),p(m,_,M,"\uE010","\\@nleqslant"),p(m,_,M,"\uE011","\\@nleqq"),p(m,_,M,"\u2A87","\\lneq",!0),p(m,_,M,"\u2268","\\lneqq",!0),p(m,_,M,"\uE00C","\\@lvertneqq"),p(m,_,M,"\u22E6","\\lnsim",!0),p(m,_,M,"\u2A89","\\lnapprox",!0),p(m,_,M,"\u2280","\\nprec",!0),p(m,_,M,"\u22E0","\\npreceq",!0),p(m,_,M,"\u22E8","\\precnsim",!0),p(m,_,M,"\u2AB9","\\precnapprox",!0),p(m,_,M,"\u2241","\\nsim",!0),p(m,_,M,"\uE006","\\@nshortmid"),p(m,_,M,"\u2224","\\nmid",!0),p(m,_,M,"\u22AC","\\nvdash",!0),p(m,_,M,"\u22AD","\\nvDash",!0),p(m,_,M,"\u22EA","\\ntriangleleft"),p(m,_,M,"\u22EC","\\ntrianglelefteq",!0),p(m,_,M,"\u228A","\\subsetneq",!0),p(m,_,M,"\uE01A","\\@varsubsetneq"),p(m,_,M,"\u2ACB","\\subsetneqq",!0),p(m,_,M,"\uE017","\\@varsubsetneqq"),p(m,_,M,"\u226F","\\ngtr",!0),p(m,_,M,"\uE00F","\\@ngeqslant"),p(m,_,M,"\uE00E","\\@ngeqq"),p(m,_,M,"\u2A88","\\gneq",!0),p(m,_,M,"\u2269","\\gneqq",!0),p(m,_,M,"\uE00D","\\@gvertneqq"),p(m,_,M,"\u22E7","\\gnsim",!0),p(m,_,M,"\u2A8A","\\gnapprox",!0),p(m,_,M,"\u2281","\\nsucc",!0),p(m,_,M,"\u22E1","\\nsucceq",!0),p(m,_,M,"\u22E9","\\succnsim",!0),p(m,_,M,"\u2ABA","\\succnapprox",!0),p(m,_,M,"\u2246","\\ncong",!0),p(m,_,M,"\uE007","\\@nshortparallel"),p(m,_,M,"\u2226","\\nparallel",!0),p(m,_,M,"\u22AF","\\nVDash",!0),p(m,_,M,"\u22EB","\\ntriangleright"),p(m,_,M,"\u22ED","\\ntrianglerighteq",!0),p(m,_,M,"\uE018","\\@nsupseteqq"),p(m,_,M,"\u228B","\\supsetneq",!0),p(m,_,M,"\uE01B","\\@varsupsetneq"),p(m,_,M,"\u2ACC","\\supsetneqq",!0),p(m,_,M,"\uE019","\\@varsupsetneqq"),p(m,_,M,"\u22AE","\\nVdash",!0),p(m,_,M,"\u2AB5","\\precneqq",!0),p(m,_,M,"\u2AB6","\\succneqq",!0),p(m,_,M,"\uE016","\\@nsubseteqq"),p(m,_,K,"\u22B4","\\unlhd"),p(m,_,K,"\u22B5","\\unrhd"),p(m,_,M,"\u219A","\\nleftarrow",!0),p(m,_,M,"\u219B","\\nrightarrow",!0),p(m,_,M,"\u21CD","\\nLeftarrow",!0),p(m,_,M,"\u21CF","\\nRightarrow",!0),p(m,_,M,"\u21AE","\\nleftrightarrow",!0),p(m,_,M,"\u21CE","\\nLeftrightarrow",!0),p(m,_,M,"\u25B3","\\vartriangle"),p(m,_,E,"\u210F","\\hslash"),p(m,_,E,"\u25BD","\\triangledown"),p(m,_,E,"\u25CA","\\lozenge"),p(m,_,E,"\u24C8","\\circledS"),p(m,_,E,"\xAE","\\circledR"),p($,_,E,"\xAE","\\circledR"),p(m,_,E,"\u2221","\\measuredangle",!0),p(m,_,E,"\u2204","\\nexists"),p(m,_,E,"\u2127","\\mho"),p(m,_,E,"\u2132","\\Finv",!0),p(m,_,E,"\u2141","\\Game",!0),p(m,_,E,"\u2035","\\backprime"),p(m,_,E,"\u25B2","\\blacktriangle"),p(m,_,E,"\u25BC","\\blacktriangledown"),p(m,_,E,"\u25A0","\\blacksquare"),p(m,_,E,"\u29EB","\\blacklozenge"),p(m,_,E,"\u2605","\\bigstar"),p(m,_,E,"\u2222","\\sphericalangle",!0),p(m,_,E,"\u2201","\\complement",!0),p(m,_,E,"\xF0","\\eth",!0),p($,v,E,"\xF0","\xF0"),p(m,_,E,"\u2571","\\diagup"),p(m,_,E,"\u2572","\\diagdown"),p(m,_,E,"\u25A1","\\square"),p(m,_,E,"\u25A1","\\Box"),p(m,_,E,"\u25CA","\\Diamond"),p(m,_,E,"\xA5","\\yen",!0),p($,_,E,"\xA5","\\yen",!0),p(m,_,E,"\u2713","\\checkmark",!0),p($,_,E,"\u2713","\\checkmark"),p(m,_,E,"\u2136","\\beth",!0),p(m,_,E,"\u2138","\\daleth",!0),p(m,_,E,"\u2137","\\gimel",!0),p(m,_,E,"\u03DD","\\digamma",!0),p(m,_,E,"\u03F0","\\varkappa"),p(m,_,st,"\u250C","\\@ulcorner",!0),p(m,_,je,"\u2510","\\@urcorner",!0),p(m,_,st,"\u2514","\\@llcorner",!0),p(m,_,je,"\u2518","\\@lrcorner",!0),p(m,_,M,"\u2266","\\leqq",!0),p(m,_,M,"\u2A7D","\\leqslant",!0),p(m,_,M,"\u2A95","\\eqslantless",!0),p(m,_,M,"\u2272","\\lesssim",!0),p(m,_,M,"\u2A85","\\lessapprox",!0),p(m,_,M,"\u224A","\\approxeq",!0),p(m,_,K,"\u22D6","\\lessdot"),p(m,_,M,"\u22D8","\\lll",!0),p(m,_,M,"\u2276","\\lessgtr",!0),p(m,_,M,"\u22DA","\\lesseqgtr",!0),p(m,_,M,"\u2A8B","\\lesseqqgtr",!0),p(m,_,M,"\u2251","\\doteqdot"),p(m,_,M,"\u2253","\\risingdotseq",!0),p(m,_,M,"\u2252","\\fallingdotseq",!0),p(m,_,M,"\u223D","\\backsim",!0),p(m,_,M,"\u22CD","\\backsimeq",!0),p(m,_,M,"\u2AC5","\\subseteqq",!0),p(m,_,M,"\u22D0","\\Subset",!0),p(m,_,M,"\u228F","\\sqsubset",!0),p(m,_,M,"\u227C","\\preccurlyeq",!0),p(m,_,M,"\u22DE","\\curlyeqprec",!0),p(m,_,M,"\u227E","\\precsim",!0),p(m,_,M,"\u2AB7","\\precapprox",!0),p(m,_,M,"\u22B2","\\vartriangleleft"),p(m,_,M,"\u22B4","\\trianglelefteq"),p(m,_,M,"\u22A8","\\vDash",!0),p(m,_,M,"\u22AA","\\Vvdash",!0),p(m,_,M,"\u2323","\\smallsmile"),p(m,_,M,"\u2322","\\smallfrown"),p(m,_,M,"\u224F","\\bumpeq",!0),p(m,_,M,"\u224E","\\Bumpeq",!0),p(m,_,M,"\u2267","\\geqq",!0),p(m,_,M,"\u2A7E","\\geqslant",!0),p(m,_,M,"\u2A96","\\eqslantgtr",!0),p(m,_,M,"\u2273","\\gtrsim",!0),p(m,_,M,"\u2A86","\\gtrapprox",!0),p(m,_,K,"\u22D7","\\gtrdot"),p(m,_,M,"\u22D9","\\ggg",!0),p(m,_,M,"\u2277","\\gtrless",!0),p(m,_,M,"\u22DB","\\gtreqless",!0),p(m,_,M,"\u2A8C","\\gtreqqless",!0),p(m,_,M,"\u2256","\\eqcirc",!0),p(m,_,M,"\u2257","\\circeq",!0),p(m,_,M,"\u225C","\\triangleq",!0),p(m,_,M,"\u223C","\\thicksim"),p(m,_,M,"\u2248","\\thickapprox"),p(m,_,M,"\u2AC6","\\supseteqq",!0),p(m,_,M,"\u22D1","\\Supset",!0),p(m,_,M,"\u2290","\\sqsupset",!0),p(m,_,M,"\u227D","\\succcurlyeq",!0),p(m,_,M,"\u22DF","\\curlyeqsucc",!0),p(m,_,M,"\u227F","\\succsim",!0),p(m,_,M,"\u2AB8","\\succapprox",!0),p(m,_,M,"\u22B3","\\vartriangleright"),p(m,_,M,"\u22B5","\\trianglerighteq"),p(m,_,M,"\u22A9","\\Vdash",!0),p(m,_,M,"\u2223","\\shortmid"),p(m,_,M,"\u2225","\\shortparallel"),p(m,_,M,"\u226C","\\between",!0),p(m,_,M,"\u22D4","\\pitchfork",!0),p(m,_,M,"\u221D","\\varpropto"),p(m,_,M,"\u25C0","\\blacktriangleleft"),p(m,_,M,"\u2234","\\therefore",!0),p(m,_,M,"\u220D","\\backepsilon"),p(m,_,M,"\u25B6","\\blacktriangleright"),p(m,_,M,"\u2235","\\because",!0),p(m,_,M,"\u22D8","\\llless"),p(m,_,M,"\u22D9","\\gggtr"),p(m,_,K,"\u22B2","\\lhd"),p(m,_,K,"\u22B3","\\rhd"),p(m,_,M,"\u2242","\\eqsim",!0),p(m,v,M,"\u22C8","\\Join"),p(m,_,M,"\u2251","\\Doteq",!0),p(m,_,K,"\u2214","\\dotplus",!0),p(m,_,K,"\u2216","\\smallsetminus"),p(m,_,K,"\u22D2","\\Cap",!0),p(m,_,K,"\u22D3","\\Cup",!0),p(m,_,K,"\u2A5E","\\doublebarwedge",!0),p(m,_,K,"\u229F","\\boxminus",!0),p(m,_,K,"\u229E","\\boxplus",!0),p(m,_,K,"\u22C7","\\divideontimes",!0),p(m,_,K,"\u22C9","\\ltimes",!0),p(m,_,K,"\u22CA","\\rtimes",!0),p(m,_,K,"\u22CB","\\leftthreetimes",!0),p(m,_,K,"\u22CC","\\rightthreetimes",!0),p(m,_,K,"\u22CF","\\curlywedge",!0),p(m,_,K,"\u22CE","\\curlyvee",!0),p(m,_,K,"\u229D","\\circleddash",!0),p(m,_,K,"\u229B","\\circledast",!0),p(m,_,K,"\u22C5","\\centerdot"),p(m,_,K,"\u22BA","\\intercal",!0),p(m,_,K,"\u22D2","\\doublecap"),p(m,_,K,"\u22D3","\\doublecup"),p(m,_,K,"\u22A0","\\boxtimes",!0),p(m,_,M,"\u21E2","\\dashrightarrow",!0),p(m,_,M,"\u21E0","\\dashleftarrow",!0),p(m,_,M,"\u21C7","\\leftleftarrows",!0),p(m,_,M,"\u21C6","\\leftrightarrows",!0),p(m,_,M,"\u21DA","\\Lleftarrow",!0),p(m,_,M,"\u219E","\\twoheadleftarrow",!0),p(m,_,M,"\u21A2","\\leftarrowtail",!0),p(m,_,M,"\u21AB","\\looparrowleft",!0),p(m,_,M,"\u21CB","\\leftrightharpoons",!0),p(m,_,M,"\u21B6","\\curvearrowleft",!0),p(m,_,M,"\u21BA","\\circlearrowleft",!0),p(m,_,M,"\u21B0","\\Lsh",!0),p(m,_,M,"\u21C8","\\upuparrows",!0),p(m,_,M,"\u21BF","\\upharpoonleft",!0),p(m,_,M,"\u21C3","\\downharpoonleft",!0),p(m,v,M,"\u22B6","\\origof",!0),p(m,v,M,"\u22B7","\\imageof",!0),p(m,_,M,"\u22B8","\\multimap",!0),p(m,_,M,"\u21AD","\\leftrightsquigarrow",!0),p(m,_,M,"\u21C9","\\rightrightarrows",!0),p(m,_,M,"\u21C4","\\rightleftarrows",!0),p(m,_,M,"\u21A0","\\twoheadrightarrow",!0),p(m,_,M,"\u21A3","\\rightarrowtail",!0),p(m,_,M,"\u21AC","\\looparrowright",!0),p(m,_,M,"\u21B7","\\curvearrowright",!0),p(m,_,M,"\u21BB","\\circlearrowright",!0),p(m,_,M,"\u21B1","\\Rsh",!0),p(m,_,M,"\u21CA","\\downdownarrows",!0),p(m,_,M,"\u21BE","\\upharpoonright",!0),p(m,_,M,"\u21C2","\\downharpoonright",!0),p(m,_,M,"\u21DD","\\rightsquigarrow",!0),p(m,_,M,"\u21DD","\\leadsto"),p(m,_,M,"\u21DB","\\Rrightarrow",!0),p(m,_,M,"\u21BE","\\restriction"),p(m,v,E,"\u2018","`"),p(m,v,E,"$","\\$"),p($,v,E,"$","\\$"),p($,v,E,"$","\\textdollar"),p(m,v,E,"%","\\%"),p($,v,E,"%","\\%"),p(m,v,E,"_","\\_"),p($,v,E,"_","\\_"),p($,v,E,"_","\\textunderscore"),p(m,v,E,"\u2220","\\angle",!0),p(m,v,E,"\u221E","\\infty",!0),p(m,v,E,"\u2032","\\prime"),p(m,v,E,"\u25B3","\\triangle"),p(m,v,E,"\u0393","\\Gamma",!0),p(m,v,E,"\u0394","\\Delta",!0),p(m,v,E,"\u0398","\\Theta",!0),p(m,v,E,"\u039B","\\Lambda",!0),p(m,v,E,"\u039E","\\Xi",!0),p(m,v,E,"\u03A0","\\Pi",!0),p(m,v,E,"\u03A3","\\Sigma",!0),p(m,v,E,"\u03A5","\\Upsilon",!0),p(m,v,E,"\u03A6","\\Phi",!0),p(m,v,E,"\u03A8","\\Psi",!0),p(m,v,E,"\u03A9","\\Omega",!0),p(m,v,E,"A","\u0391"),p(m,v,E,"B","\u0392"),p(m,v,E,"E","\u0395"),p(m,v,E,"Z","\u0396"),p(m,v,E,"H","\u0397"),p(m,v,E,"I","\u0399"),p(m,v,E,"K","\u039A"),p(m,v,E,"M","\u039C"),p(m,v,E,"N","\u039D"),p(m,v,E,"O","\u039F"),p(m,v,E,"P","\u03A1"),p(m,v,E,"T","\u03A4"),p(m,v,E,"X","\u03A7"),p(m,v,E,"\xAC","\\neg",!0),p(m,v,E,"\xAC","\\lnot"),p(m,v,E,"\u22A4","\\top"),p(m,v,E,"\u22A5","\\bot"),p(m,v,E,"\u2205","\\emptyset"),p(m,_,E,"\u2205","\\varnothing"),p(m,v,re,"\u03B1","\\alpha",!0),p(m,v,re,"\u03B2","\\beta",!0),p(m,v,re,"\u03B3","\\gamma",!0),p(m,v,re,"\u03B4","\\delta",!0),p(m,v,re,"\u03F5","\\epsilon",!0),p(m,v,re,"\u03B6","\\zeta",!0),p(m,v,re,"\u03B7","\\eta",!0),p(m,v,re,"\u03B8","\\theta",!0),p(m,v,re,"\u03B9","\\iota",!0),p(m,v,re,"\u03BA","\\kappa",!0),p(m,v,re,"\u03BB","\\lambda",!0),p(m,v,re,"\u03BC","\\mu",!0),p(m,v,re,"\u03BD","\\nu",!0),p(m,v,re,"\u03BE","\\xi",!0),p(m,v,re,"\u03BF","\\omicron",!0),p(m,v,re,"\u03C0","\\pi",!0),p(m,v,re,"\u03C1","\\rho",!0),p(m,v,re,"\u03C3","\\sigma",!0),p(m,v,re,"\u03C4","\\tau",!0),p(m,v,re,"\u03C5","\\upsilon",!0),p(m,v,re,"\u03D5","\\phi",!0),p(m,v,re,"\u03C7","\\chi",!0),p(m,v,re,"\u03C8","\\psi",!0),p(m,v,re,"\u03C9","\\omega",!0),p(m,v,re,"\u03B5","\\varepsilon",!0),p(m,v,re,"\u03D1","\\vartheta",!0),p(m,v,re,"\u03D6","\\varpi",!0),p(m,v,re,"\u03F1","\\varrho",!0),p(m,v,re,"\u03C2","\\varsigma",!0),p(m,v,re,"\u03C6","\\varphi",!0),p(m,v,K,"\u2217","*",!0),p(m,v,K,"+","+"),p(m,v,K,"\u2212","-",!0),p(m,v,K,"\u22C5","\\cdot",!0),p(m,v,K,"\u2218","\\circ",!0),p(m,v,K,"\xF7","\\div",!0),p(m,v,K,"\xB1","\\pm",!0),p(m,v,K,"\xD7","\\times",!0),p(m,v,K,"\u2229","\\cap",!0),p(m,v,K,"\u222A","\\cup",!0),p(m,v,K,"\u2216","\\setminus",!0),p(m,v,K,"\u2227","\\land"),p(m,v,K,"\u2228","\\lor"),p(m,v,K,"\u2227","\\wedge",!0),p(m,v,K,"\u2228","\\vee",!0),p(m,v,E,"\u221A","\\surd"),p(m,v,st,"\u27E8","\\langle",!0),p(m,v,st,"\u2223","\\lvert"),p(m,v,st,"\u2225","\\lVert"),p(m,v,je,"?","?"),p(m,v,je,"!","!"),p(m,v,je,"\u27E9","\\rangle",!0),p(m,v,je,"\u2223","\\rvert"),p(m,v,je,"\u2225","\\rVert"),p(m,v,M,"=","="),p(m,v,M,":",":"),p(m,v,M,"\u2248","\\approx",!0),p(m,v,M,"\u2245","\\cong",!0),p(m,v,M,"\u2265","\\ge"),p(m,v,M,"\u2265","\\geq",!0),p(m,v,M,"\u2190","\\gets"),p(m,v,M,">","\\gt",!0),p(m,v,M,"\u2208","\\in",!0),p(m,v,M,"\uE020","\\@not"),p(m,v,M,"\u2282","\\subset",!0),p(m,v,M,"\u2283","\\supset",!0),p(m,v,M,"\u2286","\\subseteq",!0),p(m,v,M,"\u2287","\\supseteq",!0),p(m,_,M,"\u2288","\\nsubseteq",!0),p(m,_,M,"\u2289","\\nsupseteq",!0),p(m,v,M,"\u22A8","\\models"),p(m,v,M,"\u2190","\\leftarrow",!0),p(m,v,M,"\u2264","\\le"),p(m,v,M,"\u2264","\\leq",!0),p(m,v,M,"<","\\lt",!0),p(m,v,M,"\u2192","\\rightarrow",!0),p(m,v,M,"\u2192","\\to"),p(m,_,M,"\u2271","\\ngeq",!0),p(m,_,M,"\u2270","\\nleq",!0),p(m,v,It,"\xA0","\\ "),p(m,v,It,"\xA0","\\space"),p(m,v,It,"\xA0","\\nobreakspace"),p($,v,It,"\xA0","\\ "),p($,v,It,"\xA0"," "),p($,v,It,"\xA0","\\space"),p($,v,It,"\xA0","\\nobreakspace"),p(m,v,It,null,"\\nobreak"),p(m,v,It,null,"\\allowbreak"),p(m,v,vn,",",","),p(m,v,vn,";",";"),p(m,_,K,"\u22BC","\\barwedge",!0),p(m,_,K,"\u22BB","\\veebar",!0),p(m,v,K,"\u2299","\\odot",!0),p(m,v,K,"\u2295","\\oplus",!0),p(m,v,K,"\u2297","\\otimes",!0),p(m,v,E,"\u2202","\\partial",!0),p(m,v,K,"\u2298","\\oslash",!0),p(m,_,K,"\u229A","\\circledcirc",!0),p(m,_,K,"\u22A1","\\boxdot",!0),p(m,v,K,"\u25B3","\\bigtriangleup"),p(m,v,K,"\u25BD","\\bigtriangledown"),p(m,v,K,"\u2020","\\dagger"),p(m,v,K,"\u22C4","\\diamond"),p(m,v,K,"\u22C6","\\star"),p(m,v,K,"\u25C3","\\triangleleft"),p(m,v,K,"\u25B9","\\triangleright"),p(m,v,st,"{","\\{"),p($,v,E,"{","\\{"),p($,v,E,"{","\\textbraceleft"),p(m,v,je,"}","\\}"),p($,v,E,"}","\\}"),p($,v,E,"}","\\textbraceright"),p(m,v,st,"{","\\lbrace"),p(m,v,je,"}","\\rbrace"),p(m,v,st,"[","\\lbrack",!0),p($,v,E,"[","\\lbrack",!0),p(m,v,je,"]","\\rbrack",!0),p($,v,E,"]","\\rbrack",!0),p(m,v,st,"(","\\lparen",!0),p(m,v,je,")","\\rparen",!0),p($,v,E,"<","\\textless",!0),p($,v,E,">","\\textgreater",!0),p(m,v,st,"\u230A","\\lfloor",!0),p(m,v,je,"\u230B","\\rfloor",!0),p(m,v,st,"\u2308","\\lceil",!0),p(m,v,je,"\u2309","\\rceil",!0),p(m,v,E,"\\","\\backslash"),p(m,v,E,"\u2223","|"),p(m,v,E,"\u2223","\\vert"),p($,v,E,"|","\\textbar",!0),p(m,v,E,"\u2225","\\|"),p(m,v,E,"\u2225","\\Vert"),p($,v,E,"\u2225","\\textbardbl"),p($,v,E,"~","\\textasciitilde"),p($,v,E,"\\","\\textbackslash"),p($,v,E,"^","\\textasciicircum"),p(m,v,M,"\u2191","\\uparrow",!0),p(m,v,M,"\u21D1","\\Uparrow",!0),p(m,v,M,"\u2193","\\downarrow",!0),p(m,v,M,"\u21D3","\\Downarrow",!0),p(m,v,M,"\u2195","\\updownarrow",!0),p(m,v,M,"\u21D5","\\Updownarrow",!0),p(m,v,Re,"\u2210","\\coprod"),p(m,v,Re,"\u22C1","\\bigvee"),p(m,v,Re,"\u22C0","\\bigwedge"),p(m,v,Re,"\u2A04","\\biguplus"),p(m,v,Re,"\u22C2","\\bigcap"),p(m,v,Re,"\u22C3","\\bigcup"),p(m,v,Re,"\u222B","\\int"),p(m,v,Re,"\u222B","\\intop"),p(m,v,Re,"\u222C","\\iint"),p(m,v,Re,"\u222D","\\iiint"),p(m,v,Re,"\u220F","\\prod"),p(m,v,Re,"\u2211","\\sum"),p(m,v,Re,"\u2A02","\\bigotimes"),p(m,v,Re,"\u2A01","\\bigoplus"),p(m,v,Re,"\u2A00","\\bigodot"),p(m,v,Re,"\u222E","\\oint"),p(m,v,Re,"\u222F","\\oiint"),p(m,v,Re,"\u2230","\\oiiint"),p(m,v,Re,"\u2A06","\\bigsqcup"),p(m,v,Re,"\u222B","\\smallint"),p($,v,br,"\u2026","\\textellipsis"),p(m,v,br,"\u2026","\\mathellipsis"),p($,v,br,"\u2026","\\ldots",!0),p(m,v,br,"\u2026","\\ldots",!0),p(m,v,br,"\u22EF","\\@cdots",!0),p(m,v,br,"\u22F1","\\ddots",!0),p(m,v,E,"\u22EE","\\varvdots"),p($,v,E,"\u22EE","\\varvdots"),p(m,v,_e,"\u02CA","\\acute"),p(m,v,_e,"\u02CB","\\grave"),p(m,v,_e,"\xA8","\\ddot"),p(m,v,_e,"~","\\tilde"),p(m,v,_e,"\u02C9","\\bar"),p(m,v,_e,"\u02D8","\\breve"),p(m,v,_e,"\u02C7","\\check"),p(m,v,_e,"^","\\hat"),p(m,v,_e,"\u20D7","\\vec"),p(m,v,_e,"\u02D9","\\dot"),p(m,v,_e,"\u02DA","\\mathring"),p(m,v,re,"\uE131","\\@imath"),p(m,v,re,"\uE237","\\@jmath"),p(m,v,E,"\u0131","\u0131"),p(m,v,E,"\u0237","\u0237"),p($,v,E,"\u0131","\\i",!0),p($,v,E,"\u0237","\\j",!0),p($,v,E,"\xDF","\\ss",!0),p($,v,E,"\xE6","\\ae",!0),p($,v,E,"\u0153","\\oe",!0),p($,v,E,"\xF8","\\o",!0),p($,v,E,"\xC6","\\AE",!0),p($,v,E,"\u0152","\\OE",!0),p($,v,E,"\xD8","\\O",!0),p($,v,_e,"\u02CA","\\'"),p($,v,_e,"\u02CB","\\`"),p($,v,_e,"\u02C6","\\^"),p($,v,_e,"\u02DC","\\~"),p($,v,_e,"\u02C9","\\="),p($,v,_e,"\u02D8","\\u"),p($,v,_e,"\u02D9","\\."),p($,v,_e,"\xB8","\\c"),p($,v,_e,"\u02DA","\\r"),p($,v,_e,"\u02C7","\\v"),p($,v,_e,"\xA8",'\\"'),p($,v,_e,"\u02DD","\\H"),p($,v,_e,"\u25EF","\\textcircled");let $o={"--":!0,"---":!0,"``":!0,"''":!0};p($,v,E,"\u2013","--",!0),p($,v,E,"\u2013","\\textendash"),p($,v,E,"\u2014","---",!0),p($,v,E,"\u2014","\\textemdash"),p($,v,E,"\u2018","`",!0),p($,v,E,"\u2018","\\textquoteleft"),p($,v,E,"\u2019","'",!0),p($,v,E,"\u2019","\\textquoteright"),p($,v,E,"\u201C","``",!0),p($,v,E,"\u201C","\\textquotedblleft"),p($,v,E,"\u201D","''",!0),p($,v,E,"\u201D","\\textquotedblright"),p(m,v,E,"\xB0","\\degree",!0),p($,v,E,"\xB0","\\degree"),p($,v,E,"\xB0","\\textdegree",!0),p(m,v,E,"\xA3","\\pounds"),p(m,v,E,"\xA3","\\mathsterling",!0),p($,v,E,"\xA3","\\pounds"),p($,v,E,"\xA3","\\textsterling",!0),p(m,_,E,"\u2720","\\maltese"),p($,_,E,"\u2720","\\maltese");let Vo='0123456789/@."';for(let i=0;i<Vo.length;i++){let n=Vo.charAt(i);p(m,v,E,n,n)}let Go='0123456789!@*()-=+";:?/.,';for(let i=0;i<Go.length;i++){let n=Go.charAt(i);p($,v,E,n,n)}let wn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";for(let i=0;i<wn.length;i++){let n=wn.charAt(i);p(m,v,re,n,n),p($,v,E,n,n)}p(m,_,E,"C","\u2102"),p($,_,E,"C","\u2102"),p(m,_,E,"H","\u210D"),p($,_,E,"H","\u210D"),p(m,_,E,"N","\u2115"),p($,_,E,"N","\u2115"),p(m,_,E,"P","\u2119"),p($,_,E,"P","\u2119"),p(m,_,E,"Q","\u211A"),p($,_,E,"Q","\u211A"),p(m,_,E,"R","\u211D"),p($,_,E,"R","\u211D"),p(m,_,E,"Z","\u2124"),p($,_,E,"Z","\u2124"),p(m,v,re,"h","\u210E"),p($,v,re,"h","\u210E");let ie="";for(let i=0;i<wn.length;i++){let n=wn.charAt(i);ie=String.fromCharCode(55349,56320+i),p(m,v,re,n,ie),p($,v,E,n,ie),ie=String.fromCharCode(55349,56372+i),p(m,v,re,n,ie),p($,v,E,n,ie),ie=String.fromCharCode(55349,56424+i),p(m,v,re,n,ie),p($,v,E,n,ie),ie=String.fromCharCode(55349,56580+i),p(m,v,re,n,ie),p($,v,E,n,ie),ie=String.fromCharCode(55349,56684+i),p(m,v,re,n,ie),p($,v,E,n,ie),ie=String.fromCharCode(55349,56736+i),p(m,v,re,n,ie),p($,v,E,n,ie),ie=String.fromCharCode(55349,56788+i),p(m,v,re,n,ie),p($,v,E,n,ie),ie=String.fromCharCode(55349,56840+i),p(m,v,re,n,ie),p($,v,E,n,ie),ie=String.fromCharCode(55349,56944+i),p(m,v,re,n,ie),p($,v,E,n,ie),i<26&&(ie=String.fromCharCode(55349,56632+i),p(m,v,re,n,ie),p($,v,E,n,ie),ie=String.fromCharCode(55349,56476+i),p(m,v,re,n,ie),p($,v,E,n,ie))}ie=String.fromCharCode(55349,56668),p(m,v,re,"k",ie),p($,v,E,"k",ie);for(let i=0;i<10;i++){let n=i.toString();ie=String.fromCharCode(55349,57294+i),p(m,v,re,n,ie),p($,v,E,n,ie),ie=String.fromCharCode(55349,57314+i),p(m,v,re,n,ie),p($,v,E,n,ie),ie=String.fromCharCode(55349,57324+i),p(m,v,re,n,ie),p($,v,E,n,ie),ie=String.fromCharCode(55349,57334+i),p(m,v,re,n,ie),p($,v,E,n,ie)}let q0="\xD0\xDE\xFE";for(let i=0;i<q0.length;i++){let n=q0.charAt(i);p(m,v,re,n,n),p($,v,E,n,n)}let kn=[["mathbf","textbf","Main-Bold"],["mathbf","textbf","Main-Bold"],["mathnormal","textit","Math-Italic"],["mathnormal","textit","Math-Italic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["mathscr","textscr","Script-Regular"],["","",""],["","",""],["","",""],["mathfrak","textfrak","Fraktur-Regular"],["mathfrak","textfrak","Fraktur-Regular"],["mathbb","textbb","AMS-Regular"],["mathbb","textbb","AMS-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathitsf","textitsf","SansSerif-Italic"],["mathitsf","textitsf","SansSerif-Italic"],["","",""],["","",""],["mathtt","texttt","Typewriter-Regular"],["mathtt","texttt","Typewriter-Regular"]],Yo=[["mathbf","textbf","Main-Bold"],["","",""],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathtt","texttt","Typewriter-Regular"]],kh=function(i,n){let l=i.charCodeAt(0),a=i.charCodeAt(1),f=(l-55296)*1024+(a-56320)+65536,d=n==="math"?0:1;if(119808<=f&&f<120484){let x=Math.floor((f-119808)/26);return[kn[x][2],kn[x][d]]}else if(120782<=f&&f<=120831){let x=Math.floor((f-120782)/10);return[Yo[x][2],Yo[x][d]]}else{if(f===120485||f===120486)return[kn[0][2],kn[0][d]];if(120486<f&&f<120782)return["",""];throw new o("Unsupported character: "+i)}},Sn=function(i,n,l){return Te[l][i]&&Te[l][i].replace&&(i=Te[l][i].replace),{value:i,metrics:B0(i,n,l)}},bt=function(i,n,l,a,f){let d=Sn(i,n,l),x=d.metrics;i=d.value;let w;if(x){let A=x.italic;(l==="text"||a&&a.font==="mathit")&&(A=0),w=new ht(i,x.height,x.depth,A,x.skew,x.width,f)}else typeof console!="undefined"&&console.warn("No character metrics "+("for '"+i+"' in style '"+n+"' and mode '"+l+"'")),w=new ht(i,0,0,0,0,0,f);if(a){w.maxFontSize=a.sizeMultiplier,a.style.isTight()&&w.classes.push("mtight");let A=a.getColor();A&&(w.style.color=A)}return w},Sh=function(i,n,l,a){return a===void 0&&(a=[]),l.font==="boldsymbol"&&Sn(i,"Main-Bold",n).metrics?bt(i,"Main-Bold",n,l,a.concat(["mathbf"])):i==="\\"||Te[n][i].font==="main"?bt(i,"Main-Regular",n,l,a):bt(i,"AMS-Regular",n,l,a.concat(["amsrm"]))},Ah=function(i,n,l,a,f){return f!=="textord"&&Sn(i,"Math-BoldItalic",n).metrics?{fontName:"Math-BoldItalic",fontClass:"boldsymbol"}:{fontName:"Main-Bold",fontClass:"mathbf"}},_h=function(i,n,l){let a=i.mode,f=i.text,d=["mord"],x=a==="math"||a==="text"&&n.font,w=x?n.font:n.fontFamily,A="",C="";if(f.charCodeAt(0)===55349&&([A,C]=kh(f,a)),A.length>0)return bt(f,A,a,n,d.concat(C));if(w){let I,B;if(w==="boldsymbol"){let F=Ah(f,a,n,d,l);I=F.fontName,B=[F.fontClass]}else x?(I=jo[w].fontName,B=[w]):(I=An(w,n.fontWeight,n.fontShape),B=[w,n.fontWeight,n.fontShape]);if(Sn(f,I,a).metrics)return bt(f,I,a,n,d.concat(B));if($o.hasOwnProperty(f)&&I.slice(0,10)==="Typewriter"){let F=[];for(let H=0;H<f.length;H++)F.push(bt(f[H],I,a,n,d.concat(B)));return Xo(F)}}if(l==="mathord")return bt(f,"Math-Italic",a,n,d.concat(["mathnormal"]));if(l==="textord"){let I=Te[a][f]&&Te[a][f].font;if(I==="ams"){let B=An("amsrm",n.fontWeight,n.fontShape);return bt(f,B,a,n,d.concat("amsrm",n.fontWeight,n.fontShape))}else if(I==="main"||!I){let B=An("textrm",n.fontWeight,n.fontShape);return bt(f,B,a,n,d.concat(n.fontWeight,n.fontShape))}else{let B=An(I,n.fontWeight,n.fontShape);return bt(f,B,a,n,d.concat(B,n.fontWeight,n.fontShape))}}else throw new Error("unexpected type: "+l+" in makeOrd")},Mh=(i,n)=>{if(Vt(i.classes)!==Vt(n.classes)||i.skew!==n.skew||i.maxFontSize!==n.maxFontSize)return!1;if(i.classes.length===1){let l=i.classes[0];if(l==="mbin"||l==="mord")return!1}for(let l in i.style)if(i.style.hasOwnProperty(l)&&i.style[l]!==n.style[l])return!1;for(let l in n.style)if(n.style.hasOwnProperty(l)&&i.style[l]!==n.style[l])return!1;return!0},Ch=i=>{for(let n=0;n<i.length-1;n++){let l=i[n],a=i[n+1];l instanceof ht&&a instanceof ht&&Mh(l,a)&&(l.text+=a.text,l.height=Math.max(l.height,a.height),l.depth=Math.max(l.depth,a.depth),l.italic=a.italic,i.splice(n+1,1),n--)}return i},P0=function(i){let n=0,l=0,a=0;for(let f=0;f<i.children.length;f++){let d=i.children[f];d.height>n&&(n=d.height),d.depth>l&&(l=d.depth),d.maxFontSize>a&&(a=d.maxFontSize)}i.height=n,i.depth=l,i.maxFontSize=a},Je=function(i,n,l,a){let f=new Rr(i,n,l,a);return P0(f),f},Uo=(i,n,l,a)=>new Rr(i,n,l,a),Th=function(i,n,l){let a=Je([i],[],n);return a.height=Math.max(l||n.fontMetrics().defaultRuleThickness,n.minRuleThickness),a.style.borderBottomWidth=U(a.height),a.maxFontSize=1,a},Nh=function(i,n,l,a){let f=new F0(i,n,l,a);return P0(f),f},Xo=function(i){let n=new Or(i);return P0(n),n},Eh=function(i,n){return i instanceof Or?Je([],[i],n):i},zh=function(i){if(i.positionType==="individualShift"){let l=i.children,a=[l[0]],f=-l[0].shift-l[0].elem.depth,d=f;for(let x=1;x<l.length;x++){let w=-l[x].shift-d-l[x].elem.depth,A=w-(l[x-1].elem.height+l[x-1].elem.depth);d=d+w,a.push({type:"kern",size:A}),a.push(l[x])}return{children:a,depth:f}}let n;if(i.positionType==="top"){let l=i.positionData;for(let a=0;a<i.children.length;a++){let f=i.children[a];l-=f.type==="kern"?f.size:f.elem.height+f.elem.depth}n=l}else if(i.positionType==="bottom")n=-i.positionData;else{let l=i.children[0];if(l.type!=="elem")throw new Error('First child must have type "elem".');if(i.positionType==="shift")n=-l.elem.depth-i.positionData;else if(i.positionType==="firstBaseline")n=-l.elem.depth;else throw new Error("Invalid positionType "+i.positionType+".")}return{children:i.children,depth:n}},Ih=function(i,n){let{children:l,depth:a}=zh(i),f=0;for(let H=0;H<l.length;H++){let Z=l[H];if(Z.type==="elem"){let Q=Z.elem;f=Math.max(f,Q.maxFontSize,Q.height)}}f+=2;let d=Je(["pstrut"],[]);d.style.height=U(f);let x=[],w=a,A=a,C=a;for(let H=0;H<l.length;H++){let Z=l[H];if(Z.type==="kern")C+=Z.size;else{let Q=Z.elem,ce=Z.wrapperClasses||[],ae=Z.wrapperStyle||{},he=Je(ce,[d,Q],void 0,ae);he.style.top=U(-f-C-Q.depth),Z.marginLeft&&(he.style.marginLeft=Z.marginLeft),Z.marginRight&&(he.style.marginRight=Z.marginRight),x.push(he),C+=Q.height+Q.depth}w=Math.min(w,C),A=Math.max(A,C)}let I=Je(["vlist"],x);I.style.height=U(A);let B;if(w<0){let H=Je([],[]),Z=Je(["vlist"],[H]);Z.style.height=U(-w);let Q=Je(["vlist-s"],[new ht("\u200B")]);B=[Je(["vlist-r"],[I,Q]),Je(["vlist-r"],[Z])]}else B=[Je(["vlist-r"],[I])];let F=Je(["vlist-t"],B);return B.length===2&&F.classes.push("vlist-t2"),F.height=A,F.depth=-w,F},Dh=(i,n)=>{let l=Je(["mspace"],[],n),a=Ce(i,n);return l.style.marginRight=U(a),l},An=function(i,n,l){let a="";switch(i){case"amsrm":a="AMS";break;case"textrm":a="Main";break;case"textsf":a="SansSerif";break;case"texttt":a="Typewriter";break;default:a=i}let f;return n==="textbf"&&l==="textit"?f="BoldItalic":n==="textbf"?f="Bold":n==="textit"?f="Italic":f="Regular",a+"-"+f},jo={mathbf:{variant:"bold",fontName:"Main-Bold"},mathrm:{variant:"normal",fontName:"Main-Regular"},textit:{variant:"italic",fontName:"Main-Italic"},mathit:{variant:"italic",fontName:"Main-Italic"},mathnormal:{variant:"italic",fontName:"Math-Italic"},mathsfit:{variant:"sans-serif-italic",fontName:"SansSerif-Italic"},mathbb:{variant:"double-struck",fontName:"AMS-Regular"},mathcal:{variant:"script",fontName:"Caligraphic-Regular"},mathfrak:{variant:"fraktur",fontName:"Fraktur-Regular"},mathscr:{variant:"script",fontName:"Script-Regular"},mathsf:{variant:"sans-serif",fontName:"SansSerif-Regular"},mathtt:{variant:"monospace",fontName:"Typewriter-Regular"}},Wo={vec:["vec",.471,.714],oiintSize1:["oiintSize1",.957,.499],oiintSize2:["oiintSize2",1.472,.659],oiiintSize1:["oiiintSize1",1.304,.499],oiiintSize2:["oiiintSize2",1.98,.659]};var O={fontMap:jo,makeSymbol:bt,mathsym:Sh,makeSpan:Je,makeSvgSpan:Uo,makeLineSpan:Th,makeAnchor:Nh,makeFragment:Xo,wrapFragment:Eh,makeVList:Ih,makeOrd:_h,makeGlue:Dh,staticSvg:function(i,n){let[l,a,f]=Wo[i],d=new Gt(l),x=new zt([d],{width:U(a),height:U(f),style:"width:"+U(a),viewBox:"0 0 "+1e3*a+" "+1e3*f,preserveAspectRatio:"xMinYMin"}),w=Uo(["overlay"],[x],n);return w.height=f,w.style.height=U(f),w.style.width=U(a),w},svgData:Wo,tryCombineChars:Ch};let Ne={number:3,unit:"mu"},tr={number:4,unit:"mu"},Dt={number:5,unit:"mu"},Bh={mord:{mop:Ne,mbin:tr,mrel:Dt,minner:Ne},mop:{mord:Ne,mop:Ne,mrel:Dt,minner:Ne},mbin:{mord:tr,mop:tr,mopen:tr,minner:tr},mrel:{mord:Dt,mop:Dt,mopen:Dt,minner:Dt},mopen:{},mclose:{mop:Ne,mbin:tr,mrel:Dt,minner:Ne},mpunct:{mord:Ne,mop:Ne,mrel:Dt,mopen:Ne,mclose:Ne,mpunct:Ne,minner:Ne},minner:{mord:Ne,mop:Ne,mbin:tr,mrel:Dt,mopen:Ne,mpunct:Ne,minner:Ne}},Oh={mord:{mop:Ne},mop:{mord:Ne,mop:Ne},mbin:{},mrel:{},mopen:{},mclose:{mop:Ne},mpunct:{},minner:{mop:Ne}},Ko={},_n={},Mn={};function j(i){let{type:n,names:l,props:a,handler:f,htmlBuilder:d,mathmlBuilder:x}=i,w={type:n,numArgs:a.numArgs,argTypes:a.argTypes,allowedInArgument:!!a.allowedInArgument,allowedInText:!!a.allowedInText,allowedInMath:a.allowedInMath===void 0?!0:a.allowedInMath,numOptionalArgs:a.numOptionalArgs||0,infix:!!a.infix,primitive:!!a.primitive,handler:f};for(let A=0;A<l.length;++A)Ko[l[A]]=w;n&&(d&&(_n[n]=d),x&&(Mn[n]=x))}function rr(i){let{type:n,htmlBuilder:l,mathmlBuilder:a}=i;j({type:n,names:[],props:{numArgs:0},handler(){throw new Error("Should never be called.")},htmlBuilder:l,mathmlBuilder:a})}let Cn=function(i){return i.type==="ordgroup"&&i.body.length===1?i.body[0]:i},Be=function(i){return i.type==="ordgroup"?i.body:[i]},Bt=O.makeSpan,Rh=["leftmost","mbin","mopen","mrel","mop","mpunct"],Fh=["rightmost","mrel","mclose","mpunct"],Lh={display:Y.DISPLAY,text:Y.TEXT,script:Y.SCRIPT,scriptscript:Y.SCRIPTSCRIPT},qh={mord:"mord",mop:"mop",mbin:"mbin",mrel:"mrel",mopen:"mopen",mclose:"mclose",mpunct:"mpunct",minner:"minner"},Le=function(i,n,l,a){a===void 0&&(a=[null,null]);let f=[];for(let C=0;C<i.length;C++){let I=fe(i[C],n);if(I instanceof Or){let B=I.children;f.push(...B)}else f.push(I)}if(O.tryCombineChars(f),!l)return f;let d=n;if(i.length===1){let C=i[0];C.type==="sizing"?d=n.havingSize(C.size):C.type==="styling"&&(d=n.havingStyle(Lh[C.style]))}let x=Bt([a[0]||"leftmost"],[],n),w=Bt([a[1]||"rightmost"],[],n),A=l==="root";return H0(f,(C,I)=>{let B=I.classes[0],F=C.classes[0];B==="mbin"&&z.contains(Fh,F)?I.classes[0]="mord":F==="mbin"&&z.contains(Rh,B)&&(C.classes[0]="mord")},{node:x},w,A),H0(f,(C,I)=>{let B=V0(I),F=V0(C),H=B&&F?C.hasClass("mtight")?Oh[B][F]:Bh[B][F]:null;if(H)return O.makeGlue(H,d)},{node:x},w,A),f},H0=function(i,n,l,a,f){a&&i.push(a);let d=0;for(;d<i.length;d++){let x=i[d],w=Zo(x);if(w){H0(w.children,n,l,null,f);continue}let A=!x.hasClass("mspace");if(A){let C=n(x,l.node);C&&(l.insertAfter?l.insertAfter(C):(i.unshift(C),d++))}A?l.node=x:f&&x.hasClass("newline")&&(l.node=Bt(["leftmost"])),l.insertAfter=(C=>I=>{i.splice(C+1,0,I),d++})(d)}a&&i.pop()},Zo=function(i){return i instanceof Or||i instanceof F0||i instanceof Rr&&i.hasClass("enclosing")?i:null},$0=function(i,n){let l=Zo(i);if(l){let a=l.children;if(a.length){if(n==="right")return $0(a[a.length-1],"right");if(n==="left")return $0(a[0],"left")}}return i},V0=function(i,n){return i?(n&&(i=$0(i,n)),qh[i.classes[0]]||null):null},Fr=function(i,n){let l=["nulldelimiter"].concat(i.baseSizingClasses());return Bt(n.concat(l))},fe=function(i,n,l){if(!i)return Bt();if(_n[i.type]){let a=_n[i.type](i,n);if(l&&n.size!==l.size){a=Bt(n.sizingClasses(l),[a],n);let f=n.sizeMultiplier/l.sizeMultiplier;a.height*=f,a.depth*=f}return a}else throw new o("Got group of unknown type: '"+i.type+"'")};function Tn(i,n){let l=Bt(["base"],i,n),a=Bt(["strut"]);return a.style.height=U(l.height+l.depth),l.depth&&(a.style.verticalAlign=U(-l.depth)),l.children.unshift(a),l}function G0(i,n){let l=null;i.length===1&&i[0].type==="tag"&&(l=i[0].tag,i=i[0].body);let a=Le(i,n,"root"),f;a.length===2&&a[1].hasClass("tag")&&(f=a.pop());let d=[],x=[];for(let C=0;C<a.length;C++)if(x.push(a[C]),a[C].hasClass("mbin")||a[C].hasClass("mrel")||a[C].hasClass("allowbreak")){let I=!1;for(;C<a.length-1&&a[C+1].hasClass("mspace")&&!a[C+1].hasClass("newline");)C++,x.push(a[C]),a[C].hasClass("nobreak")&&(I=!0);I||(d.push(Tn(x,n)),x=[])}else a[C].hasClass("newline")&&(x.pop(),x.length>0&&(d.push(Tn(x,n)),x=[]),d.push(a[C]));x.length>0&&d.push(Tn(x,n));let w;l?(w=Tn(Le(l,n,!0)),w.classes=["tag"],d.push(w)):f&&d.push(f);let A=Bt(["katex-html"],d);if(A.setAttribute("aria-hidden","true"),w){let C=w.children[0];C.style.height=U(A.height+A.depth),A.depth&&(C.style.verticalAlign=U(-A.depth))}return A}function Jo(i){return new Or(i)}class at{constructor(n,l,a){this.type=void 0,this.attributes=void 0,this.children=void 0,this.classes=void 0,this.type=n,this.attributes={},this.children=l||[],this.classes=a||[]}setAttribute(n,l){this.attributes[n]=l}getAttribute(n){return this.attributes[n]}toNode(){let n=document.createElementNS("http://www.w3.org/1998/Math/MathML",this.type);for(let l in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,l)&&n.setAttribute(l,this.attributes[l]);this.classes.length>0&&(n.className=Vt(this.classes));for(let l=0;l<this.children.length;l++)if(this.children[l]instanceof At&&this.children[l+1]instanceof At){let a=this.children[l].toText()+this.children[++l].toText();for(;this.children[l+1]instanceof At;)a+=this.children[++l].toText();n.appendChild(new At(a).toNode())}else n.appendChild(this.children[l].toNode());return n}toMarkup(){let n="<"+this.type;for(let l in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,l)&&(n+=" "+l+'="',n+=z.escape(this.attributes[l]),n+='"');this.classes.length>0&&(n+=' class ="'+z.escape(Vt(this.classes))+'"'),n+=">";for(let l=0;l<this.children.length;l++)n+=this.children[l].toMarkup();return n+="</"+this.type+">",n}toText(){return this.children.map(n=>n.toText()).join("")}}class At{constructor(n){this.text=void 0,this.text=n}toNode(){return document.createTextNode(this.text)}toMarkup(){return z.escape(this.toText())}toText(){return this.text}}class Ph{constructor(n){this.width=void 0,this.character=void 0,this.width=n,n>=.05555&&n<=.05556?this.character="\u200A":n>=.1666&&n<=.1667?this.character="\u2009":n>=.2222&&n<=.2223?this.character="\u2005":n>=.2777&&n<=.2778?this.character="\u2005\u200A":n>=-.05556&&n<=-.05555?this.character="\u200A\u2063":n>=-.1667&&n<=-.1666?this.character="\u2009\u2063":n>=-.2223&&n<=-.2222?this.character="\u205F\u2063":n>=-.2778&&n<=-.2777?this.character="\u2005\u2063":this.character=null}toNode(){if(this.character)return document.createTextNode(this.character);{let n=document.createElementNS("http://www.w3.org/1998/Math/MathML","mspace");return n.setAttribute("width",U(this.width)),n}}toMarkup(){return this.character?"<mtext>"+this.character+"</mtext>":'<mspace width="'+U(this.width)+'"/>'}toText(){return this.character?this.character:" "}}var V={MathNode:at,TextNode:At,SpaceNode:Ph,newDocumentFragment:Jo};let ft=function(i,n,l){return Te[n][i]&&Te[n][i].replace&&i.charCodeAt(0)!==55349&&!($o.hasOwnProperty(i)&&l&&(l.fontFamily&&l.fontFamily.slice(4,6)==="tt"||l.font&&l.font.slice(4,6)==="tt"))&&(i=Te[n][i].replace),new V.TextNode(i)},Y0=function(i){return i.length===1?i[0]:new V.MathNode("mrow",i)},U0=function(i,n){if(n.fontFamily==="texttt")return"monospace";if(n.fontFamily==="textsf")return n.fontShape==="textit"&&n.fontWeight==="textbf"?"sans-serif-bold-italic":n.fontShape==="textit"?"sans-serif-italic":n.fontWeight==="textbf"?"bold-sans-serif":"sans-serif";if(n.fontShape==="textit"&&n.fontWeight==="textbf")return"bold-italic";if(n.fontShape==="textit")return"italic";if(n.fontWeight==="textbf")return"bold";let l=n.font;if(!l||l==="mathnormal")return null;let a=i.mode;if(l==="mathit")return"italic";if(l==="boldsymbol")return i.type==="textord"?"bold":"bold-italic";if(l==="mathbf")return"bold";if(l==="mathbb")return"double-struck";if(l==="mathsfit")return"sans-serif-italic";if(l==="mathfrak")return"fraktur";if(l==="mathscr"||l==="mathcal")return"script";if(l==="mathsf")return"sans-serif";if(l==="mathtt")return"monospace";let f=i.text;if(z.contains(["\\imath","\\jmath"],f))return null;Te[a][f]&&Te[a][f].replace&&(f=Te[a][f].replace);let d=O.fontMap[l].fontName;return B0(f,d,a)?O.fontMap[l].variant:null};function X0(i){if(!i)return!1;if(i.type==="mi"&&i.children.length===1){let n=i.children[0];return n instanceof At&&n.text==="."}else if(i.type==="mo"&&i.children.length===1&&i.getAttribute("separator")==="true"&&i.getAttribute("lspace")==="0em"&&i.getAttribute("rspace")==="0em"){let n=i.children[0];return n instanceof At&&n.text===","}else return!1}let Qe=function(i,n,l){if(i.length===1){let d=ye(i[0],n);return l&&d instanceof at&&d.type==="mo"&&(d.setAttribute("lspace","0em"),d.setAttribute("rspace","0em")),[d]}let a=[],f;for(let d=0;d<i.length;d++){let x=ye(i[d],n);if(x instanceof at&&f instanceof at){if(x.type==="mtext"&&f.type==="mtext"&&x.getAttribute("mathvariant")===f.getAttribute("mathvariant")){f.children.push(...x.children);continue}else if(x.type==="mn"&&f.type==="mn"){f.children.push(...x.children);continue}else if(X0(x)&&f.type==="mn"){f.children.push(...x.children);continue}else if(x.type==="mn"&&X0(f))x.children=[...f.children,...x.children],a.pop();else if((x.type==="msup"||x.type==="msub")&&x.children.length>=1&&(f.type==="mn"||X0(f))){let w=x.children[0];w instanceof at&&w.type==="mn"&&(w.children=[...f.children,...w.children],a.pop())}else if(f.type==="mi"&&f.children.length===1){let w=f.children[0];if(w instanceof At&&w.text==="\u0338"&&(x.type==="mo"||x.type==="mi"||x.type==="mn")){let A=x.children[0];A instanceof At&&A.text.length>0&&(A.text=A.text.slice(0,1)+"\u0338"+A.text.slice(1),a.pop())}}}a.push(x),f=x}return a},Yt=function(i,n,l){return Y0(Qe(i,n,l))},ye=function(i,n){if(!i)return new V.MathNode("mrow");if(Mn[i.type])return Mn[i.type](i,n);throw new o("Got group of unknown type: '"+i.type+"'")};function Qo(i,n,l,a,f){let d=Qe(i,l),x;d.length===1&&d[0]instanceof at&&z.contains(["mrow","mtable"],d[0].type)?x=d[0]:x=new V.MathNode("mrow",d);let w=new V.MathNode("annotation",[new V.TextNode(n)]);w.setAttribute("encoding","application/x-tex");let A=new V.MathNode("semantics",[x,w]),C=new V.MathNode("math",[A]);C.setAttribute("xmlns","http://www.w3.org/1998/Math/MathML"),a&&C.setAttribute("display","block");let I=f?"katex":"katex-mathml";return O.makeSpan([I],[C])}let el=function(i){return new dh({style:i.displayMode?Y.DISPLAY:Y.TEXT,maxSize:i.maxSize,minRuleThickness:i.minRuleThickness})},tl=function(i,n){if(n.displayMode){let l=["katex-display"];n.leqno&&l.push("leqno"),n.fleqn&&l.push("fleqn"),i=O.makeSpan(l,[i])}return i},Hh=function(i,n,l){let a=el(l),f;if(l.output==="mathml")return Qo(i,n,a,l.displayMode,!0);if(l.output==="html"){let d=G0(i,a);f=O.makeSpan(["katex"],[d])}else{let d=Qo(i,n,a,l.displayMode,!1),x=G0(i,a);f=O.makeSpan(["katex"],[d,x])}return tl(f,l)},$h=function(i,n,l){let a=el(l),f=G0(i,a),d=O.makeSpan(["katex"],[f]);return tl(d,l)};var l6=null;let Vh={widehat:"^",widecheck:"\u02C7",widetilde:"~",utilde:"~",overleftarrow:"\u2190",underleftarrow:"\u2190",xleftarrow:"\u2190",overrightarrow:"\u2192",underrightarrow:"\u2192",xrightarrow:"\u2192",underbrace:"\u23DF",overbrace:"\u23DE",overgroup:"\u23E0",undergroup:"\u23E1",overleftrightarrow:"\u2194",underleftrightarrow:"\u2194",xleftrightarrow:"\u2194",Overrightarrow:"\u21D2",xRightarrow:"\u21D2",overleftharpoon:"\u21BC",xleftharpoonup:"\u21BC",overrightharpoon:"\u21C0",xrightharpoonup:"\u21C0",xLeftarrow:"\u21D0",xLeftrightarrow:"\u21D4",xhookleftarrow:"\u21A9",xhookrightarrow:"\u21AA",xmapsto:"\u21A6",xrightharpoondown:"\u21C1",xleftharpoondown:"\u21BD",xrightleftharpoons:"\u21CC",xleftrightharpoons:"\u21CB",xtwoheadleftarrow:"\u219E",xtwoheadrightarrow:"\u21A0",xlongequal:"=",xtofrom:"\u21C4",xrightleftarrows:"\u21C4",xrightequilibrium:"\u21CC",xleftequilibrium:"\u21CB","\\cdrightarrow":"\u2192","\\cdleftarrow":"\u2190","\\cdlongequal":"="},Gh=function(i){let n=new V.MathNode("mo",[new V.TextNode(Vh[i.replace(/^\\/,"")])]);return n.setAttribute("stretchy","true"),n},Yh={overrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],overleftarrow:[["leftarrow"],.888,522,"xMinYMin"],underrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],underleftarrow:[["leftarrow"],.888,522,"xMinYMin"],xrightarrow:[["rightarrow"],1.469,522,"xMaxYMin"],"\\cdrightarrow":[["rightarrow"],3,522,"xMaxYMin"],xleftarrow:[["leftarrow"],1.469,522,"xMinYMin"],"\\cdleftarrow":[["leftarrow"],3,522,"xMinYMin"],Overrightarrow:[["doublerightarrow"],.888,560,"xMaxYMin"],xRightarrow:[["doublerightarrow"],1.526,560,"xMaxYMin"],xLeftarrow:[["doubleleftarrow"],1.526,560,"xMinYMin"],overleftharpoon:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoonup:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoondown:[["leftharpoondown"],.888,522,"xMinYMin"],overrightharpoon:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoonup:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoondown:[["rightharpoondown"],.888,522,"xMaxYMin"],xlongequal:[["longequal"],.888,334,"xMinYMin"],"\\cdlongequal":[["longequal"],3,334,"xMinYMin"],xtwoheadleftarrow:[["twoheadleftarrow"],.888,334,"xMinYMin"],xtwoheadrightarrow:[["twoheadrightarrow"],.888,334,"xMaxYMin"],overleftrightarrow:[["leftarrow","rightarrow"],.888,522],overbrace:[["leftbrace","midbrace","rightbrace"],1.6,548],underbrace:[["leftbraceunder","midbraceunder","rightbraceunder"],1.6,548],underleftrightarrow:[["leftarrow","rightarrow"],.888,522],xleftrightarrow:[["leftarrow","rightarrow"],1.75,522],xLeftrightarrow:[["doubleleftarrow","doublerightarrow"],1.75,560],xrightleftharpoons:[["leftharpoondownplus","rightharpoonplus"],1.75,716],xleftrightharpoons:[["leftharpoonplus","rightharpoondownplus"],1.75,716],xhookleftarrow:[["leftarrow","righthook"],1.08,522],xhookrightarrow:[["lefthook","rightarrow"],1.08,522],overlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],underlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],overgroup:[["leftgroup","rightgroup"],.888,342],undergroup:[["leftgroupunder","rightgroupunder"],.888,342],xmapsto:[["leftmapsto","rightarrow"],1.5,522],xtofrom:[["leftToFrom","rightToFrom"],1.75,528],xrightleftarrows:[["baraboveleftarrow","rightarrowabovebar"],1.75,901],xrightequilibrium:[["baraboveshortleftharpoon","rightharpoonaboveshortbar"],1.75,716],xleftequilibrium:[["shortbaraboveleftharpoon","shortrightharpoonabovebar"],1.75,716]},Uh=function(i){return i.type==="ordgroup"?i.body.length:1};var Ot={encloseSpan:function(i,n,l,a,f){let d,x=i.height+i.depth+l+a;if(/fbox|color|angl/.test(n)){if(d=O.makeSpan(["stretchy",n],[],f),n==="fbox"){let w=f.color&&f.getColor();w&&(d.style.borderColor=w)}}else{let w=[];/^[bx]cancel$/.test(n)&&w.push(new L0({x1:"0",y1:"0",x2:"100%",y2:"100%","stroke-width":"0.046em"})),/^x?cancel$/.test(n)&&w.push(new L0({x1:"0",y1:"100%",x2:"100%",y2:"0","stroke-width":"0.046em"}));let A=new zt(w,{width:"100%",height:U(x)});d=O.makeSvgSpan([],[A],f)}return d.height=x,d.style.height=U(x),d},mathMLnode:Gh,svgSpan:function(i,n){function l(){let x=4e5,w=i.label.slice(1);if(z.contains(["widehat","widecheck","widetilde","utilde"],w)){let C=Uh(i.base),I,B,F;if(C>5)w==="widehat"||w==="widecheck"?(I=420,x=2364,F=.42,B=w+"4"):(I=312,x=2340,F=.34,B="tilde4");else{let Q=[1,1,2,2,3,3][C];w==="widehat"||w==="widecheck"?(x=[0,1062,2364,2364,2364][Q],I=[0,239,300,360,420][Q],F=[0,.24,.3,.3,.36,.42][Q],B=w+Q):(x=[0,600,1033,2339,2340][Q],I=[0,260,286,306,312][Q],F=[0,.26,.286,.3,.306,.34][Q],B="tilde"+Q)}let H=new Gt(B),Z=new zt([H],{width:"100%",height:U(F),viewBox:"0 0 "+x+" "+I,preserveAspectRatio:"none"});return{span:O.makeSvgSpan([],[Z],n),minWidth:0,height:F}}else{let A=[],C=Yh[w],[I,B,F]=C,H=F/1e3,Z=I.length,Q,ce;if(Z===1){let ae=C[3];Q=["hide-tail"],ce=[ae]}else if(Z===2)Q=["halfarrow-left","halfarrow-right"],ce=["xMinYMin","xMaxYMin"];else if(Z===3)Q=["brace-left","brace-center","brace-right"],ce=["xMinYMin","xMidYMin","xMaxYMin"];else throw new Error(`Correct katexImagesData or update code here to support
                    `+Z+" children.");for(let ae=0;ae<Z;ae++){let he=new Gt(I[ae]),de=new zt([he],{width:"400em",height:U(H),viewBox:"0 0 "+x+" "+F,preserveAspectRatio:ce[ae]+" slice"}),we=O.makeSvgSpan([Q[ae]],[de],n);if(Z===1)return{span:we,minWidth:B,height:H};we.style.height=U(H),A.push(we)}return{span:O.makeSpan(["stretchy"],A,n),minWidth:B,height:H}}}let{span:a,minWidth:f,height:d}=l();return a.height=d,a.style.height=U(d),f>0&&(a.style.minWidth=U(f)),a}};function le(i,n){if(!i||i.type!==n)throw new Error("Expected node of type "+n+", but got "+(i?"node of type "+i.type:String(i)));return i}function j0(i){let n=Nn(i);if(!n)throw new Error("Expected node of symbol group type, but got "+(i?"node of type "+i.type:String(i)));return n}function Nn(i){return i&&(i.type==="atom"||wh.hasOwnProperty(i.type))?i:null}let W0=(i,n)=>{let l,a,f;i&&i.type==="supsub"?(a=le(i.base,"accent"),l=a.base,i.base=l,f=bh(fe(i,n)),i.base=a):(a=le(i,"accent"),l=a.base);let d=fe(l,n.havingCrampedStyle()),x=a.isShifty&&z.isCharacterBox(l),w=0;if(x){let F=z.getBaseElem(l),H=fe(F,n.havingCrampedStyle());w=Ho(H).skew}let A=a.label==="\\c",C=A?d.height+d.depth:Math.min(d.height,n.fontMetrics().xHeight),I;if(a.isStretchy)I=Ot.svgSpan(a,n),I=O.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:d},{type:"elem",elem:I,wrapperClasses:["svg-align"],wrapperStyle:w>0?{width:"calc(100% - "+U(2*w)+")",marginLeft:U(2*w)}:void 0}]},n);else{let F,H;a.label==="\\vec"?(F=O.staticSvg("vec",n),H=O.svgData.vec[1]):(F=O.makeOrd({mode:a.mode,text:a.label},n,"textord"),F=Ho(F),F.italic=0,H=F.width,A&&(C+=F.depth)),I=O.makeSpan(["accent-body"],[F]);let Z=a.label==="\\textcircled";Z&&(I.classes.push("accent-full"),C=d.height);let Q=w;Z||(Q-=H/2),I.style.left=U(Q),a.label==="\\textcircled"&&(I.style.top=".2em"),I=O.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:d},{type:"kern",size:-C},{type:"elem",elem:I}]},n)}let B=O.makeSpan(["mord","accent"],[I],n);return f?(f.children[0]=B,f.height=Math.max(B.height,f.height),f.classes[0]="mord",f):B},rl=(i,n)=>{let l=i.isStretchy?Ot.mathMLnode(i.label):new V.MathNode("mo",[ft(i.label,i.mode)]),a=new V.MathNode("mover",[ye(i.base,n),l]);return a.setAttribute("accent","true"),a},Xh=new RegExp(["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring"].map(i=>"\\"+i).join("|"));j({type:"accent",names:["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring","\\widecheck","\\widehat","\\widetilde","\\overrightarrow","\\overleftarrow","\\Overrightarrow","\\overleftrightarrow","\\overgroup","\\overlinesegment","\\overleftharpoon","\\overrightharpoon"],props:{numArgs:1},handler:(i,n)=>{let l=Cn(n[0]),a=!Xh.test(i.funcName),f=!a||i.funcName==="\\widehat"||i.funcName==="\\widetilde"||i.funcName==="\\widecheck";return{type:"accent",mode:i.parser.mode,label:i.funcName,isStretchy:a,isShifty:f,base:l}},htmlBuilder:W0,mathmlBuilder:rl}),j({type:"accent",names:["\\'","\\`","\\^","\\~","\\=","\\u","\\.",'\\"',"\\c","\\r","\\H","\\v","\\textcircled"],props:{numArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["primitive"]},handler:(i,n)=>{let l=n[0],a=i.parser.mode;return a==="math"&&(i.parser.settings.reportNonstrict("mathVsTextAccents","LaTeX's accent "+i.funcName+" works only in text mode"),a="text"),{type:"accent",mode:a,label:i.funcName,isStretchy:!1,isShifty:!0,base:l}},htmlBuilder:W0,mathmlBuilder:rl}),j({type:"accentUnder",names:["\\underleftarrow","\\underrightarrow","\\underleftrightarrow","\\undergroup","\\underlinesegment","\\utilde"],props:{numArgs:1},handler:(i,n)=>{let{parser:l,funcName:a}=i,f=n[0];return{type:"accentUnder",mode:l.mode,label:a,base:f}},htmlBuilder:(i,n)=>{let l=fe(i.base,n),a=Ot.svgSpan(i,n),f=i.label==="\\utilde"?.12:0,d=O.makeVList({positionType:"top",positionData:l.height,children:[{type:"elem",elem:a,wrapperClasses:["svg-align"]},{type:"kern",size:f},{type:"elem",elem:l}]},n);return O.makeSpan(["mord","accentunder"],[d],n)},mathmlBuilder:(i,n)=>{let l=Ot.mathMLnode(i.label),a=new V.MathNode("munder",[ye(i.base,n),l]);return a.setAttribute("accentunder","true"),a}});let En=i=>{let n=new V.MathNode("mpadded",i?[i]:[]);return n.setAttribute("width","+0.6em"),n.setAttribute("lspace","0.3em"),n};j({type:"xArrow",names:["\\xleftarrow","\\xrightarrow","\\xLeftarrow","\\xRightarrow","\\xleftrightarrow","\\xLeftrightarrow","\\xhookleftarrow","\\xhookrightarrow","\\xmapsto","\\xrightharpoondown","\\xrightharpoonup","\\xleftharpoondown","\\xleftharpoonup","\\xrightleftharpoons","\\xleftrightharpoons","\\xlongequal","\\xtwoheadrightarrow","\\xtwoheadleftarrow","\\xtofrom","\\xrightleftarrows","\\xrightequilibrium","\\xleftequilibrium","\\\\cdrightarrow","\\\\cdleftarrow","\\\\cdlongequal"],props:{numArgs:1,numOptionalArgs:1},handler(i,n,l){let{parser:a,funcName:f}=i;return{type:"xArrow",mode:a.mode,label:f,body:n[0],below:l[0]}},htmlBuilder(i,n){let l=n.style,a=n.havingStyle(l.sup()),f=O.wrapFragment(fe(i.body,a,n),n),d=i.label.slice(0,2)==="\\x"?"x":"cd";f.classes.push(d+"-arrow-pad");let x;i.below&&(a=n.havingStyle(l.sub()),x=O.wrapFragment(fe(i.below,a,n),n),x.classes.push(d+"-arrow-pad"));let w=Ot.svgSpan(i,n),A=-n.fontMetrics().axisHeight+.5*w.height,C=-n.fontMetrics().axisHeight-.5*w.height-.111;(f.depth>.25||i.label==="\\xleftequilibrium")&&(C-=f.depth);let I;if(x){let B=-n.fontMetrics().axisHeight+x.height+.5*w.height+.111;I=O.makeVList({positionType:"individualShift",children:[{type:"elem",elem:f,shift:C},{type:"elem",elem:w,shift:A},{type:"elem",elem:x,shift:B}]},n)}else I=O.makeVList({positionType:"individualShift",children:[{type:"elem",elem:f,shift:C},{type:"elem",elem:w,shift:A}]},n);return I.children[0].children[0].children[1].classes.push("svg-align"),O.makeSpan(["mrel","x-arrow"],[I],n)},mathmlBuilder(i,n){let l=Ot.mathMLnode(i.label);l.setAttribute("minsize",i.label.charAt(0)==="x"?"1.75em":"3.0em");let a;if(i.body){let f=En(ye(i.body,n));if(i.below){let d=En(ye(i.below,n));a=new V.MathNode("munderover",[l,d,f])}else a=new V.MathNode("mover",[l,f])}else if(i.below){let f=En(ye(i.below,n));a=new V.MathNode("munder",[l,f])}else a=En(),a=new V.MathNode("mover",[l,a]);return a}});let jh=O.makeSpan;function nl(i,n){let l=Le(i.body,n,!0);return jh([i.mclass],l,n)}function il(i,n){let l,a=Qe(i.body,n);return i.mclass==="minner"?l=new V.MathNode("mpadded",a):i.mclass==="mord"?i.isCharacterBox?(l=a[0],l.type="mi"):l=new V.MathNode("mi",a):(i.isCharacterBox?(l=a[0],l.type="mo"):l=new V.MathNode("mo",a),i.mclass==="mbin"?(l.attributes.lspace="0.22em",l.attributes.rspace="0.22em"):i.mclass==="mpunct"?(l.attributes.lspace="0em",l.attributes.rspace="0.17em"):i.mclass==="mopen"||i.mclass==="mclose"?(l.attributes.lspace="0em",l.attributes.rspace="0em"):i.mclass==="minner"&&(l.attributes.lspace="0.0556em",l.attributes.width="+0.1111em")),l}j({type:"mclass",names:["\\mathord","\\mathbin","\\mathrel","\\mathopen","\\mathclose","\\mathpunct","\\mathinner"],props:{numArgs:1,primitive:!0},handler(i,n){let{parser:l,funcName:a}=i,f=n[0];return{type:"mclass",mode:l.mode,mclass:"m"+a.slice(5),body:Be(f),isCharacterBox:z.isCharacterBox(f)}},htmlBuilder:nl,mathmlBuilder:il});let zn=i=>{let n=i.type==="ordgroup"&&i.body.length?i.body[0]:i;return n.type==="atom"&&(n.family==="bin"||n.family==="rel")?"m"+n.family:"mord"};j({type:"mclass",names:["\\@binrel"],props:{numArgs:2},handler(i,n){let{parser:l}=i;return{type:"mclass",mode:l.mode,mclass:zn(n[0]),body:Be(n[1]),isCharacterBox:z.isCharacterBox(n[1])}}}),j({type:"mclass",names:["\\stackrel","\\overset","\\underset"],props:{numArgs:2},handler(i,n){let{parser:l,funcName:a}=i,f=n[1],d=n[0],x;a!=="\\stackrel"?x=zn(f):x="mrel";let w={type:"op",mode:f.mode,limits:!0,alwaysHandleSupSub:!0,parentIsSupSub:!1,symbol:!1,suppressBaseShift:a!=="\\stackrel",body:Be(f)},A={type:"supsub",mode:d.mode,base:w,sup:a==="\\underset"?null:d,sub:a==="\\underset"?d:null};return{type:"mclass",mode:l.mode,mclass:x,body:[A],isCharacterBox:z.isCharacterBox(A)}},htmlBuilder:nl,mathmlBuilder:il}),j({type:"pmb",names:["\\pmb"],props:{numArgs:1,allowedInText:!0},handler(i,n){let{parser:l}=i;return{type:"pmb",mode:l.mode,mclass:zn(n[0]),body:Be(n[0])}},htmlBuilder(i,n){let l=Le(i.body,n,!0),a=O.makeSpan([i.mclass],l,n);return a.style.textShadow="0.02em 0.01em 0.04px",a},mathmlBuilder(i,n){let l=Qe(i.body,n),a=new V.MathNode("mstyle",l);return a.setAttribute("style","text-shadow: 0.02em 0.01em 0.04px"),a}});let Wh={">":"\\\\cdrightarrow","<":"\\\\cdleftarrow","=":"\\\\cdlongequal",A:"\\uparrow",V:"\\downarrow","|":"\\Vert",".":"no arrow"},ol=()=>({type:"styling",body:[],mode:"math",style:"display"}),ll=i=>i.type==="textord"&&i.text==="@",Kh=(i,n)=>(i.type==="mathord"||i.type==="atom")&&i.text===n;function Zh(i,n,l){let a=Wh[i];switch(a){case"\\\\cdrightarrow":case"\\\\cdleftarrow":return l.callFunction(a,[n[0]],[n[1]]);case"\\uparrow":case"\\downarrow":{let f=l.callFunction("\\\\cdleft",[n[0]],[]),d={type:"atom",text:a,mode:"math",family:"rel"},x=l.callFunction("\\Big",[d],[]),w=l.callFunction("\\\\cdright",[n[1]],[]),A={type:"ordgroup",mode:"math",body:[f,x,w]};return l.callFunction("\\\\cdparent",[A],[])}case"\\\\cdlongequal":return l.callFunction("\\\\cdlongequal",[],[]);case"\\Vert":{let f={type:"textord",text:"\\Vert",mode:"math"};return l.callFunction("\\Big",[f],[])}default:return{type:"textord",text:" ",mode:"math"}}}function Jh(i){let n=[];for(i.gullet.beginGroup(),i.gullet.macros.set("\\cr","\\\\\\relax"),i.gullet.beginGroup();;){n.push(i.parseExpression(!1,"\\\\")),i.gullet.endGroup(),i.gullet.beginGroup();let d=i.fetch().text;if(d==="&"||d==="\\\\")i.consume();else if(d==="\\end"){n[n.length-1].length===0&&n.pop();break}else throw new o("Expected \\\\ or \\cr or \\end",i.nextToken)}let l=[],a=[l];for(let d=0;d<n.length;d++){let x=n[d],w=ol();for(let A=0;A<x.length;A++)if(!ll(x[A]))w.body.push(x[A]);else{l.push(w),A+=1;let C=j0(x[A]).text,I=new Array(2);if(I[0]={type:"ordgroup",mode:"math",body:[]},I[1]={type:"ordgroup",mode:"math",body:[]},!("=|.".indexOf(C)>-1))if("<>AV".indexOf(C)>-1)for(let H=0;H<2;H++){let Z=!0;for(let Q=A+1;Q<x.length;Q++){if(Kh(x[Q],C)){Z=!1,A=Q;break}if(ll(x[Q]))throw new o("Missing a "+C+" character to complete a CD arrow.",x[Q]);I[H].body.push(x[Q])}if(Z)throw new o("Missing a "+C+" character to complete a CD arrow.",x[A])}else throw new o('Expected one of "<>AV=|." after @',x[A]);let F={type:"styling",body:[Zh(C,I,i)],mode:"math",style:"display"};l.push(F),w=ol()}d%2===0?l.push(w):l.shift(),l=[],a.push(l)}i.gullet.endGroup(),i.gullet.endGroup();let f=new Array(a[0].length).fill({type:"align",align:"c",pregap:.25,postgap:.25});return{type:"array",mode:"math",body:a,arraystretch:1,addJot:!0,rowGaps:[null],cols:f,colSeparationType:"CD",hLinesBeforeRow:new Array(a.length+1).fill([])}}j({type:"cdlabel",names:["\\\\cdleft","\\\\cdright"],props:{numArgs:1},handler(i,n){let{parser:l,funcName:a}=i;return{type:"cdlabel",mode:l.mode,side:a.slice(4),label:n[0]}},htmlBuilder(i,n){let l=n.havingStyle(n.style.sup()),a=O.wrapFragment(fe(i.label,l,n),n);return a.classes.push("cd-label-"+i.side),a.style.bottom=U(.8-a.depth),a.height=0,a.depth=0,a},mathmlBuilder(i,n){let l=new V.MathNode("mrow",[ye(i.label,n)]);return l=new V.MathNode("mpadded",[l]),l.setAttribute("width","0"),i.side==="left"&&l.setAttribute("lspace","-1width"),l.setAttribute("voffset","0.7em"),l=new V.MathNode("mstyle",[l]),l.setAttribute("displaystyle","false"),l.setAttribute("scriptlevel","1"),l}}),j({type:"cdlabelparent",names:["\\\\cdparent"],props:{numArgs:1},handler(i,n){let{parser:l}=i;return{type:"cdlabelparent",mode:l.mode,fragment:n[0]}},htmlBuilder(i,n){let l=O.wrapFragment(fe(i.fragment,n),n);return l.classes.push("cd-vert-arrow"),l},mathmlBuilder(i,n){return new V.MathNode("mrow",[ye(i.fragment,n)])}}),j({type:"textord",names:["\\@char"],props:{numArgs:1,allowedInText:!0},handler(i,n){let{parser:l}=i,f=le(n[0],"ordgroup").body,d="";for(let A=0;A<f.length;A++){let C=le(f[A],"textord");d+=C.text}let x=parseInt(d),w;if(isNaN(x))throw new o("\\@char has non-numeric argument "+d);if(x<0||x>=1114111)throw new o("\\@char with invalid code point "+d);return x<=65535?w=String.fromCharCode(x):(x-=65536,w=String.fromCharCode((x>>10)+55296,(x&1023)+56320)),{type:"textord",mode:l.mode,text:w}}});let sl=(i,n)=>{let l=Le(i.body,n.withColor(i.color),!1);return O.makeFragment(l)},al=(i,n)=>{let l=Qe(i.body,n.withColor(i.color)),a=new V.MathNode("mstyle",l);return a.setAttribute("mathcolor",i.color),a};j({type:"color",names:["\\textcolor"],props:{numArgs:2,allowedInText:!0,argTypes:["color","original"]},handler(i,n){let{parser:l}=i,a=le(n[0],"color-token").color,f=n[1];return{type:"color",mode:l.mode,color:a,body:Be(f)}},htmlBuilder:sl,mathmlBuilder:al}),j({type:"color",names:["\\color"],props:{numArgs:1,allowedInText:!0,argTypes:["color"]},handler(i,n){let{parser:l,breakOnTokenText:a}=i,f=le(n[0],"color-token").color;l.gullet.macros.set("\\current@color",f);let d=l.parseExpression(!0,a);return{type:"color",mode:l.mode,color:f,body:d}},htmlBuilder:sl,mathmlBuilder:al}),j({type:"cr",names:["\\\\"],props:{numArgs:0,numOptionalArgs:0,allowedInText:!0},handler(i,n,l){let{parser:a}=i,f=a.gullet.future().text==="["?a.parseSizeGroup(!0):null,d=!a.settings.displayMode||!a.settings.useStrictBehavior("newLineInDisplayMode","In LaTeX, \\\\ or \\newline does nothing in display mode");return{type:"cr",mode:a.mode,newLine:d,size:f&&le(f,"size").value}},htmlBuilder(i,n){let l=O.makeSpan(["mspace"],[],n);return i.newLine&&(l.classes.push("newline"),i.size&&(l.style.marginTop=U(Ce(i.size,n)))),l},mathmlBuilder(i,n){let l=new V.MathNode("mspace");return i.newLine&&(l.setAttribute("linebreak","newline"),i.size&&l.setAttribute("height",U(Ce(i.size,n)))),l}});let K0={"\\global":"\\global","\\long":"\\\\globallong","\\\\globallong":"\\\\globallong","\\def":"\\gdef","\\gdef":"\\gdef","\\edef":"\\xdef","\\xdef":"\\xdef","\\let":"\\\\globallet","\\futurelet":"\\\\globalfuture"},ul=i=>{let n=i.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(n))throw new o("Expected a control sequence",i);return n},Qh=i=>{let n=i.gullet.popToken();return n.text==="="&&(n=i.gullet.popToken(),n.text===" "&&(n=i.gullet.popToken())),n},cl=(i,n,l,a)=>{let f=i.gullet.macros.get(l.text);f==null&&(l.noexpand=!0,f={tokens:[l],numArgs:0,unexpandable:!i.gullet.isExpandable(l.text)}),i.gullet.macros.set(n,f,a)};j({type:"internal",names:["\\global","\\long","\\\\globallong"],props:{numArgs:0,allowedInText:!0},handler(i){let{parser:n,funcName:l}=i;n.consumeSpaces();let a=n.fetch();if(K0[a.text])return(l==="\\global"||l==="\\\\globallong")&&(a.text=K0[a.text]),le(n.parseFunction(),"internal");throw new o("Invalid token after macro prefix",a)}}),j({type:"internal",names:["\\def","\\gdef","\\edef","\\xdef"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(i){let{parser:n,funcName:l}=i,a=n.gullet.popToken(),f=a.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(f))throw new o("Expected a control sequence",a);let d=0,x,w=[[]];for(;n.gullet.future().text!=="{";)if(a=n.gullet.popToken(),a.text==="#"){if(n.gullet.future().text==="{"){x=n.gullet.future(),w[d].push("{");break}if(a=n.gullet.popToken(),!/^[1-9]$/.test(a.text))throw new o('Invalid argument number "'+a.text+'"');if(parseInt(a.text)!==d+1)throw new o('Argument number "'+a.text+'" out of order');d++,w.push([])}else{if(a.text==="EOF")throw new o("Expected a macro definition");w[d].push(a.text)}let{tokens:A}=n.gullet.consumeArg();return x&&A.unshift(x),(l==="\\edef"||l==="\\xdef")&&(A=n.gullet.expandTokens(A),A.reverse()),n.gullet.macros.set(f,{tokens:A,numArgs:d,delimiters:w},l===K0[l]),{type:"internal",mode:n.mode}}}),j({type:"internal",names:["\\let","\\\\globallet"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(i){let{parser:n,funcName:l}=i,a=ul(n.gullet.popToken());n.gullet.consumeSpaces();let f=Qh(n);return cl(n,a,f,l==="\\\\globallet"),{type:"internal",mode:n.mode}}}),j({type:"internal",names:["\\futurelet","\\\\globalfuture"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(i){let{parser:n,funcName:l}=i,a=ul(n.gullet.popToken()),f=n.gullet.popToken(),d=n.gullet.popToken();return cl(n,a,d,l==="\\\\globalfuture"),n.gullet.pushToken(d),n.gullet.pushToken(f),{type:"internal",mode:n.mode}}});let Lr=function(i,n,l){let a=Te.math[i]&&Te.math[i].replace,f=B0(a||i,n,l);if(!f)throw new Error("Unsupported symbol "+i+" and font size "+n+".");return f},Z0=function(i,n,l,a){let f=l.havingBaseStyle(n),d=O.makeSpan(a.concat(f.sizingClasses(l)),[i],l),x=f.sizeMultiplier/l.sizeMultiplier;return d.height*=x,d.depth*=x,d.maxFontSize=f.sizeMultiplier,d},hl=function(i,n,l){let a=n.havingBaseStyle(l),f=(1-n.sizeMultiplier/a.sizeMultiplier)*n.fontMetrics().axisHeight;i.classes.push("delimcenter"),i.style.top=U(f),i.height-=f,i.depth+=f},e1=function(i,n,l,a,f,d){let x=O.makeSymbol(i,"Main-Regular",f,a),w=Z0(x,n,a,d);return l&&hl(w,a,n),w},t1=function(i,n,l,a){return O.makeSymbol(i,"Size"+n+"-Regular",l,a)},fl=function(i,n,l,a,f,d){let x=t1(i,n,f,a),w=Z0(O.makeSpan(["delimsizing","size"+n],[x],a),Y.TEXT,a,d);return l&&hl(w,a,Y.TEXT),w},J0=function(i,n,l){let a;return n==="Size1-Regular"?a="delim-size1":a="delim-size4",{type:"elem",elem:O.makeSpan(["delimsizinginner",a],[O.makeSpan([],[O.makeSymbol(i,n,l)])])}},Q0=function(i,n,l){let a=St["Size4-Regular"][i.charCodeAt(0)]?St["Size4-Regular"][i.charCodeAt(0)][4]:St["Size1-Regular"][i.charCodeAt(0)][4],f=new Gt("inner",uh(i,Math.round(1e3*n))),d=new zt([f],{width:U(a),height:U(n),style:"width:"+U(a),viewBox:"0 0 "+1e3*a+" "+Math.round(1e3*n),preserveAspectRatio:"xMinYMin"}),x=O.makeSvgSpan([],[d],l);return x.height=n,x.style.height=U(n),x.style.width=U(a),{type:"elem",elem:x}},ei=.008,In={type:"kern",size:-1*ei},r1=["|","\\lvert","\\rvert","\\vert"],n1=["\\|","\\lVert","\\rVert","\\Vert"],pl=function(i,n,l,a,f,d){let x,w,A,C,I="",B=0;x=A=C=i,w=null;let F="Size1-Regular";i==="\\uparrow"?A=C="\u23D0":i==="\\Uparrow"?A=C="\u2016":i==="\\downarrow"?x=A="\u23D0":i==="\\Downarrow"?x=A="\u2016":i==="\\updownarrow"?(x="\\uparrow",A="\u23D0",C="\\downarrow"):i==="\\Updownarrow"?(x="\\Uparrow",A="\u2016",C="\\Downarrow"):z.contains(r1,i)?(A="\u2223",I="vert",B=333):z.contains(n1,i)?(A="\u2225",I="doublevert",B=556):i==="["||i==="\\lbrack"?(x="\u23A1",A="\u23A2",C="\u23A3",F="Size4-Regular",I="lbrack",B=667):i==="]"||i==="\\rbrack"?(x="\u23A4",A="\u23A5",C="\u23A6",F="Size4-Regular",I="rbrack",B=667):i==="\\lfloor"||i==="\u230A"?(A=x="\u23A2",C="\u23A3",F="Size4-Regular",I="lfloor",B=667):i==="\\lceil"||i==="\u2308"?(x="\u23A1",A=C="\u23A2",F="Size4-Regular",I="lceil",B=667):i==="\\rfloor"||i==="\u230B"?(A=x="\u23A5",C="\u23A6",F="Size4-Regular",I="rfloor",B=667):i==="\\rceil"||i==="\u2309"?(x="\u23A4",A=C="\u23A5",F="Size4-Regular",I="rceil",B=667):i==="("||i==="\\lparen"?(x="\u239B",A="\u239C",C="\u239D",F="Size4-Regular",I="lparen",B=875):i===")"||i==="\\rparen"?(x="\u239E",A="\u239F",C="\u23A0",F="Size4-Regular",I="rparen",B=875):i==="\\{"||i==="\\lbrace"?(x="\u23A7",w="\u23A8",C="\u23A9",A="\u23AA",F="Size4-Regular"):i==="\\}"||i==="\\rbrace"?(x="\u23AB",w="\u23AC",C="\u23AD",A="\u23AA",F="Size4-Regular"):i==="\\lgroup"||i==="\u27EE"?(x="\u23A7",C="\u23A9",A="\u23AA",F="Size4-Regular"):i==="\\rgroup"||i==="\u27EF"?(x="\u23AB",C="\u23AD",A="\u23AA",F="Size4-Regular"):i==="\\lmoustache"||i==="\u23B0"?(x="\u23A7",C="\u23AD",A="\u23AA",F="Size4-Regular"):(i==="\\rmoustache"||i==="\u23B1")&&(x="\u23AB",C="\u23A9",A="\u23AA",F="Size4-Regular");let H=Lr(x,F,f),Z=H.height+H.depth,Q=Lr(A,F,f),ce=Q.height+Q.depth,ae=Lr(C,F,f),he=ae.height+ae.depth,de=0,we=1;if(w!==null){let Ie=Lr(w,F,f);de=Ie.height+Ie.depth,we=2}let We=Z+he+de,qe=Math.max(0,Math.ceil((n-We)/(we*ce))),dt=We+qe*we*ce,wr=a.fontMetrics().axisHeight;l&&(wr*=a.sizeMultiplier);let pe=dt/2-wr,ge=[];if(I.length>0){let Ie=dt-Z-he,Ee=Math.round(dt*1e3),mt=ch(I,Math.round(Ie*1e3)),N1=new Gt(I,mt),ns=(B/1e3).toFixed(3)+"em",is=(Ee/1e3).toFixed(3)+"em",E1=new zt([N1],{width:ns,height:is,viewBox:"0 0 "+B+" "+Ee}),Fn=O.makeSvgSpan([],[E1],a);Fn.height=Ee/1e3,Fn.style.width=ns,Fn.style.height=is,ge.push({type:"elem",elem:Fn})}else{if(ge.push(J0(C,F,f)),ge.push(In),w===null){let Ie=dt-Z-he+2*ei;ge.push(Q0(A,Ie,a))}else{let Ie=(dt-Z-he-de)/2+2*ei;ge.push(Q0(A,Ie,a)),ge.push(In),ge.push(J0(w,F,f)),ge.push(In),ge.push(Q0(A,Ie,a))}ge.push(In),ge.push(J0(x,F,f))}let ke=a.havingBaseStyle(Y.TEXT),Me=O.makeVList({positionType:"bottom",positionData:pe,children:ge},ke);return Z0(O.makeSpan(["delimsizing","mult"],[Me],ke),Y.TEXT,a,d)},ti=80,ri=.08,ni=function(i,n,l,a,f){let d=ah(i,a,l),x=new Gt(i,d),w=new zt([x],{width:"400em",height:U(n),viewBox:"0 0 400000 "+l,preserveAspectRatio:"xMinYMin slice"});return O.makeSvgSpan(["hide-tail"],[w],f)},i1=function(i,n){let l=n.havingBaseSizing(),a=xl("\\surd",i*l.sizeMultiplier,gl,l),f=l.sizeMultiplier,d=Math.max(0,n.minRuleThickness-n.fontMetrics().sqrtRuleThickness),x,w=0,A=0,C=0,I;return a.type==="small"?(C=1e3+1e3*d+ti,i<1?f=1:i<1.4&&(f=.7),w=(1+d+ri)/f,A=(1+d)/f,x=ni("sqrtMain",w,C,d,n),x.style.minWidth="0.853em",I=.833/f):a.type==="large"?(C=(1e3+ti)*qr[a.size],A=(qr[a.size]+d)/f,w=(qr[a.size]+d+ri)/f,x=ni("sqrtSize"+a.size,w,C,d,n),x.style.minWidth="1.02em",I=1/f):(w=i+d+ri,A=i+d,C=Math.floor(1e3*i+d)+ti,x=ni("sqrtTall",w,C,d,n),x.style.minWidth="0.742em",I=1.056),x.height=A,x.style.height=U(w),{span:x,advanceWidth:I,ruleWidth:(n.fontMetrics().sqrtRuleThickness+d)*f}},dl=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","\u230A","\u230B","\\lceil","\\rceil","\u2308","\u2309","\\surd"],o1=["\\uparrow","\\downarrow","\\updownarrow","\\Uparrow","\\Downarrow","\\Updownarrow","|","\\|","\\vert","\\Vert","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","\u27EE","\u27EF","\\lmoustache","\\rmoustache","\u23B0","\u23B1"],ml=["<",">","\\langle","\\rangle","/","\\backslash","\\lt","\\gt"],qr=[0,1.2,1.8,2.4,3],l1=function(i,n,l,a,f){if(i==="<"||i==="\\lt"||i==="\u27E8"?i="\\langle":(i===">"||i==="\\gt"||i==="\u27E9")&&(i="\\rangle"),z.contains(dl,i)||z.contains(ml,i))return fl(i,n,!1,l,a,f);if(z.contains(o1,i))return pl(i,qr[n],!1,l,a,f);throw new o("Illegal delimiter: '"+i+"'")},s1=[{type:"small",style:Y.SCRIPTSCRIPT},{type:"small",style:Y.SCRIPT},{type:"small",style:Y.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4}],a1=[{type:"small",style:Y.SCRIPTSCRIPT},{type:"small",style:Y.SCRIPT},{type:"small",style:Y.TEXT},{type:"stack"}],gl=[{type:"small",style:Y.SCRIPTSCRIPT},{type:"small",style:Y.SCRIPT},{type:"small",style:Y.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4},{type:"stack"}],u1=function(i){if(i.type==="small")return"Main-Regular";if(i.type==="large")return"Size"+i.size+"-Regular";if(i.type==="stack")return"Size4-Regular";throw new Error("Add support for delim type '"+i.type+"' here.")},xl=function(i,n,l,a){let f=Math.min(2,3-a.style.size);for(let d=f;d<l.length&&l[d].type!=="stack";d++){let x=Lr(i,u1(l[d]),"math"),w=x.height+x.depth;if(l[d].type==="small"){let A=a.havingBaseStyle(l[d].style);w*=A.sizeMultiplier}if(w>n)return l[d]}return l[l.length-1]},yl=function(i,n,l,a,f,d){i==="<"||i==="\\lt"||i==="\u27E8"?i="\\langle":(i===">"||i==="\\gt"||i==="\u27E9")&&(i="\\rangle");let x;z.contains(ml,i)?x=s1:z.contains(dl,i)?x=gl:x=a1;let w=xl(i,n,x,a);return w.type==="small"?e1(i,w.style,l,a,f,d):w.type==="large"?fl(i,w.size,l,a,f,d):pl(i,n,l,a,f,d)};var Rt={sqrtImage:i1,sizedDelim:l1,sizeToMaxHeight:qr,customSizedDelim:yl,leftRightDelim:function(i,n,l,a,f,d){let x=a.fontMetrics().axisHeight*a.sizeMultiplier,w=901,A=5/a.fontMetrics().ptPerEm,C=Math.max(n-x,l+x),I=Math.max(C/500*w,2*C-A);return yl(i,I,!0,a,f,d)}};let bl={"\\bigl":{mclass:"mopen",size:1},"\\Bigl":{mclass:"mopen",size:2},"\\biggl":{mclass:"mopen",size:3},"\\Biggl":{mclass:"mopen",size:4},"\\bigr":{mclass:"mclose",size:1},"\\Bigr":{mclass:"mclose",size:2},"\\biggr":{mclass:"mclose",size:3},"\\Biggr":{mclass:"mclose",size:4},"\\bigm":{mclass:"mrel",size:1},"\\Bigm":{mclass:"mrel",size:2},"\\biggm":{mclass:"mrel",size:3},"\\Biggm":{mclass:"mrel",size:4},"\\big":{mclass:"mord",size:1},"\\Big":{mclass:"mord",size:2},"\\bigg":{mclass:"mord",size:3},"\\Bigg":{mclass:"mord",size:4}},c1=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","\u230A","\u230B","\\lceil","\\rceil","\u2308","\u2309","<",">","\\langle","\u27E8","\\rangle","\u27E9","\\lt","\\gt","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","\u27EE","\u27EF","\\lmoustache","\\rmoustache","\u23B0","\u23B1","/","\\backslash","|","\\vert","\\|","\\Vert","\\uparrow","\\Uparrow","\\downarrow","\\Downarrow","\\updownarrow","\\Updownarrow","."];function Dn(i,n){let l=Nn(i);if(l&&z.contains(c1,l.text))return l;throw l?new o("Invalid delimiter '"+l.text+"' after '"+n.funcName+"'",i):new o("Invalid delimiter type '"+i.type+"'",i)}j({type:"delimsizing",names:["\\bigl","\\Bigl","\\biggl","\\Biggl","\\bigr","\\Bigr","\\biggr","\\Biggr","\\bigm","\\Bigm","\\biggm","\\Biggm","\\big","\\Big","\\bigg","\\Bigg"],props:{numArgs:1,argTypes:["primitive"]},handler:(i,n)=>{let l=Dn(n[0],i);return{type:"delimsizing",mode:i.parser.mode,size:bl[i.funcName].size,mclass:bl[i.funcName].mclass,delim:l.text}},htmlBuilder:(i,n)=>i.delim==="."?O.makeSpan([i.mclass]):Rt.sizedDelim(i.delim,i.size,n,i.mode,[i.mclass]),mathmlBuilder:i=>{let n=[];i.delim!=="."&&n.push(ft(i.delim,i.mode));let l=new V.MathNode("mo",n);i.mclass==="mopen"||i.mclass==="mclose"?l.setAttribute("fence","true"):l.setAttribute("fence","false"),l.setAttribute("stretchy","true");let a=U(Rt.sizeToMaxHeight[i.size]);return l.setAttribute("minsize",a),l.setAttribute("maxsize",a),l}});function vl(i){if(!i.body)throw new Error("Bug: The leftright ParseNode wasn't fully parsed.")}j({type:"leftright-right",names:["\\right"],props:{numArgs:1,primitive:!0},handler:(i,n)=>{let l=i.parser.gullet.macros.get("\\current@color");if(l&&typeof l!="string")throw new o("\\current@color set to non-string in \\right");return{type:"leftright-right",mode:i.parser.mode,delim:Dn(n[0],i).text,color:l}}}),j({type:"leftright",names:["\\left"],props:{numArgs:1,primitive:!0},handler:(i,n)=>{let l=Dn(n[0],i),a=i.parser;++a.leftrightDepth;let f=a.parseExpression(!1);--a.leftrightDepth,a.expect("\\right",!1);let d=le(a.parseFunction(),"leftright-right");return{type:"leftright",mode:a.mode,body:f,left:l.text,right:d.delim,rightColor:d.color}},htmlBuilder:(i,n)=>{vl(i);let l=Le(i.body,n,!0,["mopen","mclose"]),a=0,f=0,d=!1;for(let A=0;A<l.length;A++)l[A].isMiddle?d=!0:(a=Math.max(l[A].height,a),f=Math.max(l[A].depth,f));a*=n.sizeMultiplier,f*=n.sizeMultiplier;let x;if(i.left==="."?x=Fr(n,["mopen"]):x=Rt.leftRightDelim(i.left,a,f,n,i.mode,["mopen"]),l.unshift(x),d)for(let A=1;A<l.length;A++){let I=l[A].isMiddle;I&&(l[A]=Rt.leftRightDelim(I.delim,a,f,I.options,i.mode,[]))}let w;if(i.right===".")w=Fr(n,["mclose"]);else{let A=i.rightColor?n.withColor(i.rightColor):n;w=Rt.leftRightDelim(i.right,a,f,A,i.mode,["mclose"])}return l.push(w),O.makeSpan(["minner"],l,n)},mathmlBuilder:(i,n)=>{vl(i);let l=Qe(i.body,n);if(i.left!=="."){let a=new V.MathNode("mo",[ft(i.left,i.mode)]);a.setAttribute("fence","true"),l.unshift(a)}if(i.right!=="."){let a=new V.MathNode("mo",[ft(i.right,i.mode)]);a.setAttribute("fence","true"),i.rightColor&&a.setAttribute("mathcolor",i.rightColor),l.push(a)}return Y0(l)}}),j({type:"middle",names:["\\middle"],props:{numArgs:1,primitive:!0},handler:(i,n)=>{let l=Dn(n[0],i);if(!i.parser.leftrightDepth)throw new o("\\middle without preceding \\left",l);return{type:"middle",mode:i.parser.mode,delim:l.text}},htmlBuilder:(i,n)=>{let l;if(i.delim===".")l=Fr(n,[]);else{l=Rt.sizedDelim(i.delim,1,n,i.mode,[]);let a={delim:i.delim,options:n};l.isMiddle=a}return l},mathmlBuilder:(i,n)=>{let l=i.delim==="\\vert"||i.delim==="|"?ft("|","text"):ft(i.delim,i.mode),a=new V.MathNode("mo",[l]);return a.setAttribute("fence","true"),a.setAttribute("lspace","0.05em"),a.setAttribute("rspace","0.05em"),a}});let ii=(i,n)=>{let l=O.wrapFragment(fe(i.body,n),n),a=i.label.slice(1),f=n.sizeMultiplier,d,x=0,w=z.isCharacterBox(i.body);if(a==="sout")d=O.makeSpan(["stretchy","sout"]),d.height=n.fontMetrics().defaultRuleThickness/f,x=-.5*n.fontMetrics().xHeight;else if(a==="phase"){let C=Ce({number:.6,unit:"pt"},n),I=Ce({number:.35,unit:"ex"},n),B=n.havingBaseSizing();f=f/B.sizeMultiplier;let F=l.height+l.depth+C+I;l.style.paddingLeft=U(F/2+C);let H=Math.floor(1e3*F*f),Z=lh(H),Q=new zt([new Gt("phase",Z)],{width:"400em",height:U(H/1e3),viewBox:"0 0 400000 "+H,preserveAspectRatio:"xMinYMin slice"});d=O.makeSvgSpan(["hide-tail"],[Q],n),d.style.height=U(F),x=l.depth+C+I}else{/cancel/.test(a)?w||l.classes.push("cancel-pad"):a==="angl"?l.classes.push("anglpad"):l.classes.push("boxpad");let C=0,I=0,B=0;/box/.test(a)?(B=Math.max(n.fontMetrics().fboxrule,n.minRuleThickness),C=n.fontMetrics().fboxsep+(a==="colorbox"?0:B),I=C):a==="angl"?(B=Math.max(n.fontMetrics().defaultRuleThickness,n.minRuleThickness),C=4*B,I=Math.max(0,.25-l.depth)):(C=w?.2:0,I=C),d=Ot.encloseSpan(l,a,C,I,n),/fbox|boxed|fcolorbox/.test(a)?(d.style.borderStyle="solid",d.style.borderWidth=U(B)):a==="angl"&&B!==.049&&(d.style.borderTopWidth=U(B),d.style.borderRightWidth=U(B)),x=l.depth+I,i.backgroundColor&&(d.style.backgroundColor=i.backgroundColor,i.borderColor&&(d.style.borderColor=i.borderColor))}let A;if(i.backgroundColor)A=O.makeVList({positionType:"individualShift",children:[{type:"elem",elem:d,shift:x},{type:"elem",elem:l,shift:0}]},n);else{let C=/cancel|phase/.test(a)?["svg-align"]:[];A=O.makeVList({positionType:"individualShift",children:[{type:"elem",elem:l,shift:0},{type:"elem",elem:d,shift:x,wrapperClasses:C}]},n)}return/cancel/.test(a)&&(A.height=l.height,A.depth=l.depth),/cancel/.test(a)&&!w?O.makeSpan(["mord","cancel-lap"],[A],n):O.makeSpan(["mord"],[A],n)},oi=(i,n)=>{let l=0,a=new V.MathNode(i.label.indexOf("colorbox")>-1?"mpadded":"menclose",[ye(i.body,n)]);switch(i.label){case"\\cancel":a.setAttribute("notation","updiagonalstrike");break;case"\\bcancel":a.setAttribute("notation","downdiagonalstrike");break;case"\\phase":a.setAttribute("notation","phasorangle");break;case"\\sout":a.setAttribute("notation","horizontalstrike");break;case"\\fbox":a.setAttribute("notation","box");break;case"\\angl":a.setAttribute("notation","actuarial");break;case"\\fcolorbox":case"\\colorbox":if(l=n.fontMetrics().fboxsep*n.fontMetrics().ptPerEm,a.setAttribute("width","+"+2*l+"pt"),a.setAttribute("height","+"+2*l+"pt"),a.setAttribute("lspace",l+"pt"),a.setAttribute("voffset",l+"pt"),i.label==="\\fcolorbox"){let f=Math.max(n.fontMetrics().fboxrule,n.minRuleThickness);a.setAttribute("style","border: "+f+"em solid "+String(i.borderColor))}break;case"\\xcancel":a.setAttribute("notation","updiagonalstrike downdiagonalstrike");break}return i.backgroundColor&&a.setAttribute("mathbackground",i.backgroundColor),a};j({type:"enclose",names:["\\colorbox"],props:{numArgs:2,allowedInText:!0,argTypes:["color","text"]},handler(i,n,l){let{parser:a,funcName:f}=i,d=le(n[0],"color-token").color,x=n[1];return{type:"enclose",mode:a.mode,label:f,backgroundColor:d,body:x}},htmlBuilder:ii,mathmlBuilder:oi}),j({type:"enclose",names:["\\fcolorbox"],props:{numArgs:3,allowedInText:!0,argTypes:["color","color","text"]},handler(i,n,l){let{parser:a,funcName:f}=i,d=le(n[0],"color-token").color,x=le(n[1],"color-token").color,w=n[2];return{type:"enclose",mode:a.mode,label:f,backgroundColor:x,borderColor:d,body:w}},htmlBuilder:ii,mathmlBuilder:oi}),j({type:"enclose",names:["\\fbox"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!0},handler(i,n){let{parser:l}=i;return{type:"enclose",mode:l.mode,label:"\\fbox",body:n[0]}}}),j({type:"enclose",names:["\\cancel","\\bcancel","\\xcancel","\\sout","\\phase"],props:{numArgs:1},handler(i,n){let{parser:l,funcName:a}=i,f=n[0];return{type:"enclose",mode:l.mode,label:a,body:f}},htmlBuilder:ii,mathmlBuilder:oi}),j({type:"enclose",names:["\\angl"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!1},handler(i,n){let{parser:l}=i;return{type:"enclose",mode:l.mode,label:"\\angl",body:n[0]}}});let wl={};function _t(i){let{type:n,names:l,props:a,handler:f,htmlBuilder:d,mathmlBuilder:x}=i,w={type:n,numArgs:a.numArgs||0,allowedInText:!1,numOptionalArgs:0,handler:f};for(let A=0;A<l.length;++A)wl[l[A]]=w;d&&(_n[n]=d),x&&(Mn[n]=x)}let kl={};function k(i,n){kl[i]=n}class ut{constructor(n,l,a){this.lexer=void 0,this.start=void 0,this.end=void 0,this.lexer=n,this.start=l,this.end=a}static range(n,l){return l?!n||!n.loc||!l.loc||n.loc.lexer!==l.loc.lexer?null:new ut(n.loc.lexer,n.loc.start,l.loc.end):n&&n.loc}}class pt{constructor(n,l){this.text=void 0,this.loc=void 0,this.noexpand=void 0,this.treatAsRelax=void 0,this.text=n,this.loc=l}range(n,l){return new pt(l,ut.range(this,n))}}function Sl(i){let n=[];i.consumeSpaces();let l=i.fetch().text;for(l==="\\relax"&&(i.consume(),i.consumeSpaces(),l=i.fetch().text);l==="\\hline"||l==="\\hdashline";)i.consume(),n.push(l==="\\hdashline"),i.consumeSpaces(),l=i.fetch().text;return n}let Bn=i=>{if(!i.parser.settings.displayMode)throw new o("{"+i.envName+"} can be used only in display mode.")};function li(i){if(i.indexOf("ed")===-1)return i.indexOf("*")===-1}function Ut(i,n,l){let{hskipBeforeAndAfter:a,addJot:f,cols:d,arraystretch:x,colSeparationType:w,autoTag:A,singleRow:C,emptySingleRow:I,maxNumCols:B,leqno:F}=n;if(i.gullet.beginGroup(),C||i.gullet.macros.set("\\cr","\\\\\\relax"),!x){let we=i.gullet.expandMacroAsText("\\arraystretch");if(we==null)x=1;else if(x=parseFloat(we),!x||x<0)throw new o("Invalid \\arraystretch: "+we)}i.gullet.beginGroup();let H=[],Z=[H],Q=[],ce=[],ae=A!=null?[]:void 0;function he(){A&&i.gullet.macros.set("\\@eqnsw","1",!0)}function de(){ae&&(i.gullet.macros.get("\\df@tag")?(ae.push(i.subparse([new pt("\\df@tag")])),i.gullet.macros.set("\\df@tag",void 0,!0)):ae.push(Boolean(A)&&i.gullet.macros.get("\\@eqnsw")==="1"))}for(he(),ce.push(Sl(i));;){let we=i.parseExpression(!1,C?"\\end":"\\\\");i.gullet.endGroup(),i.gullet.beginGroup(),we={type:"ordgroup",mode:i.mode,body:we},l&&(we={type:"styling",mode:i.mode,style:l,body:[we]}),H.push(we);let We=i.fetch().text;if(We==="&"){if(B&&H.length===B){if(C||w)throw new o("Too many tab characters: &",i.nextToken);i.settings.reportNonstrict("textEnv","Too few columns specified in the {array} column argument.")}i.consume()}else if(We==="\\end"){de(),H.length===1&&we.type==="styling"&&we.body[0].body.length===0&&(Z.length>1||!I)&&Z.pop(),ce.length<Z.length+1&&ce.push([]);break}else if(We==="\\\\"){i.consume();let qe;i.gullet.future().text!==" "&&(qe=i.parseSizeGroup(!0)),Q.push(qe?qe.value:null),de(),ce.push(Sl(i)),H=[],Z.push(H),he()}else throw new o("Expected & or \\\\ or \\cr or \\end",i.nextToken)}return i.gullet.endGroup(),i.gullet.endGroup(),{type:"array",mode:i.mode,addJot:f,arraystretch:x,body:Z,cols:d,rowGaps:Q,hskipBeforeAndAfter:a,hLinesBeforeRow:ce,colSeparationType:w,tags:ae,leqno:F}}function si(i){return i.slice(0,1)==="d"?"display":"text"}let Mt=function(i,n){let l,a,f=i.body.length,d=i.hLinesBeforeRow,x=0,w=new Array(f),A=[],C=Math.max(n.fontMetrics().arrayRuleWidth,n.minRuleThickness),I=1/n.fontMetrics().ptPerEm,B=5*I;i.colSeparationType&&i.colSeparationType==="small"&&(B=.2778*(n.havingStyle(Y.SCRIPT).sizeMultiplier/n.sizeMultiplier));let F=i.colSeparationType==="CD"?Ce({number:3,unit:"ex"},n):12*I,H=3*I,Z=i.arraystretch*F,Q=.7*Z,ce=.3*Z,ae=0;function he(pe){for(let ge=0;ge<pe.length;++ge)ge>0&&(ae+=.25),A.push({pos:ae,isDashed:pe[ge]})}for(he(d[0]),l=0;l<i.body.length;++l){let pe=i.body[l],ge=Q,ke=ce;x<pe.length&&(x=pe.length);let Me=new Array(pe.length);for(a=0;a<pe.length;++a){let mt=fe(pe[a],n);ke<mt.depth&&(ke=mt.depth),ge<mt.height&&(ge=mt.height),Me[a]=mt}let Ie=i.rowGaps[l],Ee=0;Ie&&(Ee=Ce(Ie,n),Ee>0&&(Ee+=ce,ke<Ee&&(ke=Ee),Ee=0)),i.addJot&&(ke+=H),Me.height=ge,Me.depth=ke,ae+=ge,Me.pos=ae,ae+=ke+Ee,w[l]=Me,he(d[l+1])}let de=ae/2+n.fontMetrics().axisHeight,we=i.cols||[],We=[],qe,dt,wr=[];if(i.tags&&i.tags.some(pe=>pe))for(l=0;l<f;++l){let pe=w[l],ge=pe.pos-de,ke=i.tags[l],Me;ke===!0?Me=O.makeSpan(["eqn-num"],[],n):ke===!1?Me=O.makeSpan([],[],n):Me=O.makeSpan([],Le(ke,n,!0),n),Me.depth=pe.depth,Me.height=pe.height,wr.push({type:"elem",elem:Me,shift:ge})}for(a=0,dt=0;a<x||dt<we.length;++a,++dt){let pe=we[dt]||{},ge=!0;for(;pe.type==="separator";){if(ge||(qe=O.makeSpan(["arraycolsep"],[]),qe.style.width=U(n.fontMetrics().doubleRuleSep),We.push(qe)),pe.separator==="|"||pe.separator===":"){let Ie=pe.separator==="|"?"solid":"dashed",Ee=O.makeSpan(["vertical-separator"],[],n);Ee.style.height=U(ae),Ee.style.borderRightWidth=U(C),Ee.style.borderRightStyle=Ie,Ee.style.margin="0 "+U(-C/2);let mt=ae-de;mt&&(Ee.style.verticalAlign=U(-mt)),We.push(Ee)}else throw new o("Invalid separator type: "+pe.separator);dt++,pe=we[dt]||{},ge=!1}if(a>=x)continue;let ke;(a>0||i.hskipBeforeAndAfter)&&(ke=z.deflt(pe.pregap,B),ke!==0&&(qe=O.makeSpan(["arraycolsep"],[]),qe.style.width=U(ke),We.push(qe)));let Me=[];for(l=0;l<f;++l){let Ie=w[l],Ee=Ie[a];if(!Ee)continue;let mt=Ie.pos-de;Ee.depth=Ie.depth,Ee.height=Ie.height,Me.push({type:"elem",elem:Ee,shift:mt})}Me=O.makeVList({positionType:"individualShift",children:Me},n),Me=O.makeSpan(["col-align-"+(pe.align||"c")],[Me]),We.push(Me),(a<x-1||i.hskipBeforeAndAfter)&&(ke=z.deflt(pe.postgap,B),ke!==0&&(qe=O.makeSpan(["arraycolsep"],[]),qe.style.width=U(ke),We.push(qe)))}if(w=O.makeSpan(["mtable"],We),A.length>0){let pe=O.makeLineSpan("hline",n,C),ge=O.makeLineSpan("hdashline",n,C),ke=[{type:"elem",elem:w,shift:0}];for(;A.length>0;){let Me=A.pop(),Ie=Me.pos-de;Me.isDashed?ke.push({type:"elem",elem:ge,shift:Ie}):ke.push({type:"elem",elem:pe,shift:Ie})}w=O.makeVList({positionType:"individualShift",children:ke},n)}if(wr.length===0)return O.makeSpan(["mord"],[w],n);{let pe=O.makeVList({positionType:"individualShift",children:wr},n);return pe=O.makeSpan(["tag"],[pe],n),O.makeFragment([w,pe])}},h1={c:"center ",l:"left ",r:"right "},Ct=function(i,n){let l=[],a=new V.MathNode("mtd",[],["mtr-glue"]),f=new V.MathNode("mtd",[],["mml-eqn-num"]);for(let B=0;B<i.body.length;B++){let F=i.body[B],H=[];for(let Z=0;Z<F.length;Z++)H.push(new V.MathNode("mtd",[ye(F[Z],n)]));i.tags&&i.tags[B]&&(H.unshift(a),H.push(a),i.leqno?H.unshift(f):H.push(f)),l.push(new V.MathNode("mtr",H))}let d=new V.MathNode("mtable",l),x=i.arraystretch===.5?.1:.16+i.arraystretch-1+(i.addJot?.09:0);d.setAttribute("rowspacing",U(x));let w="",A="";if(i.cols&&i.cols.length>0){let B=i.cols,F="",H=!1,Z=0,Q=B.length;B[0].type==="separator"&&(w+="top ",Z=1),B[B.length-1].type==="separator"&&(w+="bottom ",Q-=1);for(let ce=Z;ce<Q;ce++)B[ce].type==="align"?(A+=h1[B[ce].align],H&&(F+="none "),H=!0):B[ce].type==="separator"&&H&&(F+=B[ce].separator==="|"?"solid ":"dashed ",H=!1);d.setAttribute("columnalign",A.trim()),/[sd]/.test(F)&&d.setAttribute("columnlines",F.trim())}if(i.colSeparationType==="align"){let B=i.cols||[],F="";for(let H=1;H<B.length;H++)F+=H%2?"0em ":"1em ";d.setAttribute("columnspacing",F.trim())}else i.colSeparationType==="alignat"||i.colSeparationType==="gather"?d.setAttribute("columnspacing","0em"):i.colSeparationType==="small"?d.setAttribute("columnspacing","0.2778em"):i.colSeparationType==="CD"?d.setAttribute("columnspacing","0.5em"):d.setAttribute("columnspacing","1em");let C="",I=i.hLinesBeforeRow;w+=I[0].length>0?"left ":"",w+=I[I.length-1].length>0?"right ":"";for(let B=1;B<I.length-1;B++)C+=I[B].length===0?"none ":I[B][0]?"dashed ":"solid ";return/[sd]/.test(C)&&d.setAttribute("rowlines",C.trim()),w!==""&&(d=new V.MathNode("menclose",[d]),d.setAttribute("notation",w.trim())),i.arraystretch&&i.arraystretch<1&&(d=new V.MathNode("mstyle",[d]),d.setAttribute("scriptlevel","1")),d},Al=function(i,n){i.envName.indexOf("ed")===-1&&Bn(i);let l=[],a=i.envName.indexOf("at")>-1?"alignat":"align",f=i.envName==="split",d=Ut(i.parser,{cols:l,addJot:!0,autoTag:f?void 0:li(i.envName),emptySingleRow:!0,colSeparationType:a,maxNumCols:f?2:void 0,leqno:i.parser.settings.leqno},"display"),x,w=0,A={type:"ordgroup",mode:i.mode,body:[]};if(n[0]&&n[0].type==="ordgroup"){let I="";for(let B=0;B<n[0].body.length;B++){let F=le(n[0].body[B],"textord");I+=F.text}x=Number(I),w=x*2}let C=!w;d.body.forEach(function(I){for(let B=1;B<I.length;B+=2){let F=le(I[B],"styling");le(F.body[0],"ordgroup").body.unshift(A)}if(C)w<I.length&&(w=I.length);else{let B=I.length/2;if(x<B)throw new o("Too many math in a row: "+("expected "+x+", but got "+B),I[0])}});for(let I=0;I<w;++I){let B="r",F=0;I%2===1?B="l":I>0&&C&&(F=1),l[I]={type:"align",align:B,pregap:F,postgap:0}}return d.colSeparationType=C?"align":"alignat",d};_t({type:"array",names:["array","darray"],props:{numArgs:1},handler(i,n){let f=(Nn(n[0])?[n[0]]:le(n[0],"ordgroup").body).map(function(x){let A=j0(x).text;if("lcr".indexOf(A)!==-1)return{type:"align",align:A};if(A==="|")return{type:"separator",separator:"|"};if(A===":")return{type:"separator",separator:":"};throw new o("Unknown column alignment: "+A,x)}),d={cols:f,hskipBeforeAndAfter:!0,maxNumCols:f.length};return Ut(i.parser,d,si(i.envName))},htmlBuilder:Mt,mathmlBuilder:Ct}),_t({type:"array",names:["matrix","pmatrix","bmatrix","Bmatrix","vmatrix","Vmatrix","matrix*","pmatrix*","bmatrix*","Bmatrix*","vmatrix*","Vmatrix*"],props:{numArgs:0},handler(i){let n={matrix:null,pmatrix:["(",")"],bmatrix:["[","]"],Bmatrix:["\\{","\\}"],vmatrix:["|","|"],Vmatrix:["\\Vert","\\Vert"]}[i.envName.replace("*","")],l="c",a={hskipBeforeAndAfter:!1,cols:[{type:"align",align:l}]};if(i.envName.charAt(i.envName.length-1)==="*"){let x=i.parser;if(x.consumeSpaces(),x.fetch().text==="["){if(x.consume(),x.consumeSpaces(),l=x.fetch().text,"lcr".indexOf(l)===-1)throw new o("Expected l or c or r",x.nextToken);x.consume(),x.consumeSpaces(),x.expect("]"),x.consume(),a.cols=[{type:"align",align:l}]}}let f=Ut(i.parser,a,si(i.envName)),d=Math.max(0,...f.body.map(x=>x.length));return f.cols=new Array(d).fill({type:"align",align:l}),n?{type:"leftright",mode:i.mode,body:[f],left:n[0],right:n[1],rightColor:void 0}:f},htmlBuilder:Mt,mathmlBuilder:Ct}),_t({type:"array",names:["smallmatrix"],props:{numArgs:0},handler(i){let n={arraystretch:.5},l=Ut(i.parser,n,"script");return l.colSeparationType="small",l},htmlBuilder:Mt,mathmlBuilder:Ct}),_t({type:"array",names:["subarray"],props:{numArgs:1},handler(i,n){let f=(Nn(n[0])?[n[0]]:le(n[0],"ordgroup").body).map(function(x){let A=j0(x).text;if("lc".indexOf(A)!==-1)return{type:"align",align:A};throw new o("Unknown column alignment: "+A,x)});if(f.length>1)throw new o("{subarray} can contain only one column");let d={cols:f,hskipBeforeAndAfter:!1,arraystretch:.5};if(d=Ut(i.parser,d,"script"),d.body.length>0&&d.body[0].length>1)throw new o("{subarray} can contain only one column");return d},htmlBuilder:Mt,mathmlBuilder:Ct}),_t({type:"array",names:["cases","dcases","rcases","drcases"],props:{numArgs:0},handler(i){let n={arraystretch:1.2,cols:[{type:"align",align:"l",pregap:0,postgap:1},{type:"align",align:"l",pregap:0,postgap:0}]},l=Ut(i.parser,n,si(i.envName));return{type:"leftright",mode:i.mode,body:[l],left:i.envName.indexOf("r")>-1?".":"\\{",right:i.envName.indexOf("r")>-1?"\\}":".",rightColor:void 0}},htmlBuilder:Mt,mathmlBuilder:Ct}),_t({type:"array",names:["align","align*","aligned","split"],props:{numArgs:0},handler:Al,htmlBuilder:Mt,mathmlBuilder:Ct}),_t({type:"array",names:["gathered","gather","gather*"],props:{numArgs:0},handler(i){z.contains(["gather","gather*"],i.envName)&&Bn(i);let n={cols:[{type:"align",align:"c"}],addJot:!0,colSeparationType:"gather",autoTag:li(i.envName),emptySingleRow:!0,leqno:i.parser.settings.leqno};return Ut(i.parser,n,"display")},htmlBuilder:Mt,mathmlBuilder:Ct}),_t({type:"array",names:["alignat","alignat*","alignedat"],props:{numArgs:1},handler:Al,htmlBuilder:Mt,mathmlBuilder:Ct}),_t({type:"array",names:["equation","equation*"],props:{numArgs:0},handler(i){Bn(i);let n={autoTag:li(i.envName),emptySingleRow:!0,singleRow:!0,maxNumCols:1,leqno:i.parser.settings.leqno};return Ut(i.parser,n,"display")},htmlBuilder:Mt,mathmlBuilder:Ct}),_t({type:"array",names:["CD"],props:{numArgs:0},handler(i){return Bn(i),Jh(i.parser)},htmlBuilder:Mt,mathmlBuilder:Ct}),k("\\nonumber","\\gdef\\@eqnsw{0}"),k("\\notag","\\nonumber"),j({type:"text",names:["\\hline","\\hdashline"],props:{numArgs:0,allowedInText:!0,allowedInMath:!0},handler(i,n){throw new o(i.funcName+" valid only within array environment")}});var _l=wl;j({type:"environment",names:["\\begin","\\end"],props:{numArgs:1,argTypes:["text"]},handler(i,n){let{parser:l,funcName:a}=i,f=n[0];if(f.type!=="ordgroup")throw new o("Invalid environment name",f);let d="";for(let x=0;x<f.body.length;++x)d+=le(f.body[x],"textord").text;if(a==="\\begin"){if(!_l.hasOwnProperty(d))throw new o("No such environment: "+d,f);let x=_l[d],{args:w,optArgs:A}=l.parseArguments("\\begin{"+d+"}",x),C={mode:l.mode,envName:d,parser:l},I=x.handler(C,w,A);l.expect("\\end",!1);let B=l.nextToken,F=le(l.parseFunction(),"environment");if(F.name!==d)throw new o("Mismatch: \\begin{"+d+"} matched by \\end{"+F.name+"}",B);return I}return{type:"environment",mode:l.mode,name:d,nameGroup:f}}});let Ml=(i,n)=>{let l=i.font,a=n.withFont(l);return fe(i.body,a)},Cl=(i,n)=>{let l=i.font,a=n.withFont(l);return ye(i.body,a)},Tl={"\\Bbb":"\\mathbb","\\bold":"\\mathbf","\\frak":"\\mathfrak","\\bm":"\\boldsymbol"};j({type:"font",names:["\\mathrm","\\mathit","\\mathbf","\\mathnormal","\\mathsfit","\\mathbb","\\mathcal","\\mathfrak","\\mathscr","\\mathsf","\\mathtt","\\Bbb","\\bold","\\frak"],props:{numArgs:1,allowedInArgument:!0},handler:(i,n)=>{let{parser:l,funcName:a}=i,f=Cn(n[0]),d=a;return d in Tl&&(d=Tl[d]),{type:"font",mode:l.mode,font:d.slice(1),body:f}},htmlBuilder:Ml,mathmlBuilder:Cl}),j({type:"mclass",names:["\\boldsymbol","\\bm"],props:{numArgs:1},handler:(i,n)=>{let{parser:l}=i,a=n[0],f=z.isCharacterBox(a);return{type:"mclass",mode:l.mode,mclass:zn(a),body:[{type:"font",mode:l.mode,font:"boldsymbol",body:a}],isCharacterBox:f}}}),j({type:"font",names:["\\rm","\\sf","\\tt","\\bf","\\it","\\cal"],props:{numArgs:0,allowedInText:!0},handler:(i,n)=>{let{parser:l,funcName:a,breakOnTokenText:f}=i,{mode:d}=l,x=l.parseExpression(!0,f),w="math"+a.slice(1);return{type:"font",mode:d,font:w,body:{type:"ordgroup",mode:l.mode,body:x}}},htmlBuilder:Ml,mathmlBuilder:Cl});let Nl=(i,n)=>{let l=n;return i==="display"?l=l.id>=Y.SCRIPT.id?l.text():Y.DISPLAY:i==="text"&&l.size===Y.DISPLAY.size?l=Y.TEXT:i==="script"?l=Y.SCRIPT:i==="scriptscript"&&(l=Y.SCRIPTSCRIPT),l},ai=(i,n)=>{let l=Nl(i.size,n.style),a=l.fracNum(),f=l.fracDen(),d;d=n.havingStyle(a);let x=fe(i.numer,d,n);if(i.continued){let he=8.5/n.fontMetrics().ptPerEm,de=3.5/n.fontMetrics().ptPerEm;x.height=x.height<he?he:x.height,x.depth=x.depth<de?de:x.depth}d=n.havingStyle(f);let w=fe(i.denom,d,n),A,C,I;i.hasBarLine?(i.barSize?(C=Ce(i.barSize,n),A=O.makeLineSpan("frac-line",n,C)):A=O.makeLineSpan("frac-line",n),C=A.height,I=A.height):(A=null,C=0,I=n.fontMetrics().defaultRuleThickness);let B,F,H;l.size===Y.DISPLAY.size||i.size==="display"?(B=n.fontMetrics().num1,C>0?F=3*I:F=7*I,H=n.fontMetrics().denom1):(C>0?(B=n.fontMetrics().num2,F=I):(B=n.fontMetrics().num3,F=3*I),H=n.fontMetrics().denom2);let Z;if(A){let he=n.fontMetrics().axisHeight;B-x.depth-(he+.5*C)<F&&(B+=F-(B-x.depth-(he+.5*C))),he-.5*C-(w.height-H)<F&&(H+=F-(he-.5*C-(w.height-H)));let de=-(he-.5*C);Z=O.makeVList({positionType:"individualShift",children:[{type:"elem",elem:w,shift:H},{type:"elem",elem:A,shift:de},{type:"elem",elem:x,shift:-B}]},n)}else{let he=B-x.depth-(w.height-H);he<F&&(B+=.5*(F-he),H+=.5*(F-he)),Z=O.makeVList({positionType:"individualShift",children:[{type:"elem",elem:w,shift:H},{type:"elem",elem:x,shift:-B}]},n)}d=n.havingStyle(l),Z.height*=d.sizeMultiplier/n.sizeMultiplier,Z.depth*=d.sizeMultiplier/n.sizeMultiplier;let Q;l.size===Y.DISPLAY.size?Q=n.fontMetrics().delim1:l.size===Y.SCRIPTSCRIPT.size?Q=n.havingStyle(Y.SCRIPT).fontMetrics().delim2:Q=n.fontMetrics().delim2;let ce,ae;return i.leftDelim==null?ce=Fr(n,["mopen"]):ce=Rt.customSizedDelim(i.leftDelim,Q,!0,n.havingStyle(l),i.mode,["mopen"]),i.continued?ae=O.makeSpan([]):i.rightDelim==null?ae=Fr(n,["mclose"]):ae=Rt.customSizedDelim(i.rightDelim,Q,!0,n.havingStyle(l),i.mode,["mclose"]),O.makeSpan(["mord"].concat(d.sizingClasses(n)),[ce,O.makeSpan(["mfrac"],[Z]),ae],n)},ui=(i,n)=>{let l=new V.MathNode("mfrac",[ye(i.numer,n),ye(i.denom,n)]);if(!i.hasBarLine)l.setAttribute("linethickness","0px");else if(i.barSize){let f=Ce(i.barSize,n);l.setAttribute("linethickness",U(f))}let a=Nl(i.size,n.style);if(a.size!==n.style.size){l=new V.MathNode("mstyle",[l]);let f=a.size===Y.DISPLAY.size?"true":"false";l.setAttribute("displaystyle",f),l.setAttribute("scriptlevel","0")}if(i.leftDelim!=null||i.rightDelim!=null){let f=[];if(i.leftDelim!=null){let d=new V.MathNode("mo",[new V.TextNode(i.leftDelim.replace("\\",""))]);d.setAttribute("fence","true"),f.push(d)}if(f.push(l),i.rightDelim!=null){let d=new V.MathNode("mo",[new V.TextNode(i.rightDelim.replace("\\",""))]);d.setAttribute("fence","true"),f.push(d)}return Y0(f)}return l};j({type:"genfrac",names:["\\dfrac","\\frac","\\tfrac","\\dbinom","\\binom","\\tbinom","\\\\atopfrac","\\\\bracefrac","\\\\brackfrac"],props:{numArgs:2,allowedInArgument:!0},handler:(i,n)=>{let{parser:l,funcName:a}=i,f=n[0],d=n[1],x,w=null,A=null,C="auto";switch(a){case"\\dfrac":case"\\frac":case"\\tfrac":x=!0;break;case"\\\\atopfrac":x=!1;break;case"\\dbinom":case"\\binom":case"\\tbinom":x=!1,w="(",A=")";break;case"\\\\bracefrac":x=!1,w="\\{",A="\\}";break;case"\\\\brackfrac":x=!1,w="[",A="]";break;default:throw new Error("Unrecognized genfrac command")}switch(a){case"\\dfrac":case"\\dbinom":C="display";break;case"\\tfrac":case"\\tbinom":C="text";break}return{type:"genfrac",mode:l.mode,continued:!1,numer:f,denom:d,hasBarLine:x,leftDelim:w,rightDelim:A,size:C,barSize:null}},htmlBuilder:ai,mathmlBuilder:ui}),j({type:"genfrac",names:["\\cfrac"],props:{numArgs:2},handler:(i,n)=>{let{parser:l,funcName:a}=i,f=n[0],d=n[1];return{type:"genfrac",mode:l.mode,continued:!0,numer:f,denom:d,hasBarLine:!0,leftDelim:null,rightDelim:null,size:"display",barSize:null}}}),j({type:"infix",names:["\\over","\\choose","\\atop","\\brace","\\brack"],props:{numArgs:0,infix:!0},handler(i){let{parser:n,funcName:l,token:a}=i,f;switch(l){case"\\over":f="\\frac";break;case"\\choose":f="\\binom";break;case"\\atop":f="\\\\atopfrac";break;case"\\brace":f="\\\\bracefrac";break;case"\\brack":f="\\\\brackfrac";break;default:throw new Error("Unrecognized infix genfrac command")}return{type:"infix",mode:n.mode,replaceWith:f,token:a}}});let El=["display","text","script","scriptscript"],zl=function(i){let n=null;return i.length>0&&(n=i,n=n==="."?null:n),n};j({type:"genfrac",names:["\\genfrac"],props:{numArgs:6,allowedInArgument:!0,argTypes:["math","math","size","text","math","math"]},handler(i,n){let{parser:l}=i,a=n[4],f=n[5],d=Cn(n[0]),x=d.type==="atom"&&d.family==="open"?zl(d.text):null,w=Cn(n[1]),A=w.type==="atom"&&w.family==="close"?zl(w.text):null,C=le(n[2],"size"),I,B=null;C.isBlank?I=!0:(B=C.value,I=B.number>0);let F="auto",H=n[3];if(H.type==="ordgroup"){if(H.body.length>0){let Z=le(H.body[0],"textord");F=El[Number(Z.text)]}}else H=le(H,"textord"),F=El[Number(H.text)];return{type:"genfrac",mode:l.mode,numer:a,denom:f,continued:!1,hasBarLine:I,barSize:B,leftDelim:x,rightDelim:A,size:F}},htmlBuilder:ai,mathmlBuilder:ui}),j({type:"infix",names:["\\above"],props:{numArgs:1,argTypes:["size"],infix:!0},handler(i,n){let{parser:l,funcName:a,token:f}=i;return{type:"infix",mode:l.mode,replaceWith:"\\\\abovefrac",size:le(n[0],"size").value,token:f}}}),j({type:"genfrac",names:["\\\\abovefrac"],props:{numArgs:3,argTypes:["math","size","math"]},handler:(i,n)=>{let{parser:l,funcName:a}=i,f=n[0],d=N(le(n[1],"infix").size),x=n[2],w=d.number>0;return{type:"genfrac",mode:l.mode,numer:f,denom:x,continued:!1,hasBarLine:w,barSize:d,leftDelim:null,rightDelim:null,size:"auto"}},htmlBuilder:ai,mathmlBuilder:ui});let Il=(i,n)=>{let l=n.style,a,f;i.type==="supsub"?(a=i.sup?fe(i.sup,n.havingStyle(l.sup()),n):fe(i.sub,n.havingStyle(l.sub()),n),f=le(i.base,"horizBrace")):f=le(i,"horizBrace");let d=fe(f.base,n.havingBaseStyle(Y.DISPLAY)),x=Ot.svgSpan(f,n),w;if(f.isOver?(w=O.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:d},{type:"kern",size:.1},{type:"elem",elem:x}]},n),w.children[0].children[0].children[1].classes.push("svg-align")):(w=O.makeVList({positionType:"bottom",positionData:d.depth+.1+x.height,children:[{type:"elem",elem:x},{type:"kern",size:.1},{type:"elem",elem:d}]},n),w.children[0].children[0].children[0].classes.push("svg-align")),a){let A=O.makeSpan(["mord",f.isOver?"mover":"munder"],[w],n);f.isOver?w=O.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:A},{type:"kern",size:.2},{type:"elem",elem:a}]},n):w=O.makeVList({positionType:"bottom",positionData:A.depth+.2+a.height+a.depth,children:[{type:"elem",elem:a},{type:"kern",size:.2},{type:"elem",elem:A}]},n)}return O.makeSpan(["mord",f.isOver?"mover":"munder"],[w],n)};j({type:"horizBrace",names:["\\overbrace","\\underbrace"],props:{numArgs:1},handler(i,n){let{parser:l,funcName:a}=i;return{type:"horizBrace",mode:l.mode,label:a,isOver:/^\\over/.test(a),base:n[0]}},htmlBuilder:Il,mathmlBuilder:(i,n)=>{let l=Ot.mathMLnode(i.label);return new V.MathNode(i.isOver?"mover":"munder",[ye(i.base,n),l])}}),j({type:"href",names:["\\href"],props:{numArgs:2,argTypes:["url","original"],allowedInText:!0},handler:(i,n)=>{let{parser:l}=i,a=n[1],f=le(n[0],"url").url;return l.settings.isTrusted({command:"\\href",url:f})?{type:"href",mode:l.mode,href:f,body:Be(a)}:l.formatUnsupportedCmd("\\href")},htmlBuilder:(i,n)=>{let l=Le(i.body,n,!1);return O.makeAnchor(i.href,[],l,n)},mathmlBuilder:(i,n)=>{let l=Yt(i.body,n);return l instanceof at||(l=new at("mrow",[l])),l.setAttribute("href",i.href),l}}),j({type:"href",names:["\\url"],props:{numArgs:1,argTypes:["url"],allowedInText:!0},handler:(i,n)=>{let{parser:l}=i,a=le(n[0],"url").url;if(!l.settings.isTrusted({command:"\\url",url:a}))return l.formatUnsupportedCmd("\\url");let f=[];for(let x=0;x<a.length;x++){let w=a[x];w==="~"&&(w="\\textasciitilde"),f.push({type:"textord",mode:"text",text:w})}let d={type:"text",mode:l.mode,font:"\\texttt",body:f};return{type:"href",mode:l.mode,href:a,body:Be(d)}}}),j({type:"hbox",names:["\\hbox"],props:{numArgs:1,argTypes:["text"],allowedInText:!0,primitive:!0},handler(i,n){let{parser:l}=i;return{type:"hbox",mode:l.mode,body:Be(n[0])}},htmlBuilder(i,n){let l=Le(i.body,n,!1);return O.makeFragment(l)},mathmlBuilder(i,n){return new V.MathNode("mrow",Qe(i.body,n))}}),j({type:"html",names:["\\htmlClass","\\htmlId","\\htmlStyle","\\htmlData"],props:{numArgs:2,argTypes:["raw","original"],allowedInText:!0},handler:(i,n)=>{let{parser:l,funcName:a,token:f}=i,d=le(n[0],"raw").string,x=n[1];l.settings.strict&&l.settings.reportNonstrict("htmlExtension","HTML extension is disabled on strict mode");let w,A={};switch(a){case"\\htmlClass":A.class=d,w={command:"\\htmlClass",class:d};break;case"\\htmlId":A.id=d,w={command:"\\htmlId",id:d};break;case"\\htmlStyle":A.style=d,w={command:"\\htmlStyle",style:d};break;case"\\htmlData":{let C=d.split(",");for(let I=0;I<C.length;I++){let B=C[I].split("=");if(B.length!==2)throw new o("Error parsing key-value for \\htmlData");A["data-"+B[0].trim()]=B[1].trim()}w={command:"\\htmlData",attributes:A};break}default:throw new Error("Unrecognized html command")}return l.settings.isTrusted(w)?{type:"html",mode:l.mode,attributes:A,body:Be(x)}:l.formatUnsupportedCmd(a)},htmlBuilder:(i,n)=>{let l=Le(i.body,n,!1),a=["enclosing"];i.attributes.class&&a.push(...i.attributes.class.trim().split(/\s+/));let f=O.makeSpan(a,l,n);for(let d in i.attributes)d!=="class"&&i.attributes.hasOwnProperty(d)&&f.setAttribute(d,i.attributes[d]);return f},mathmlBuilder:(i,n)=>Yt(i.body,n)}),j({type:"htmlmathml",names:["\\html@mathml"],props:{numArgs:2,allowedInText:!0},handler:(i,n)=>{let{parser:l}=i;return{type:"htmlmathml",mode:l.mode,html:Be(n[0]),mathml:Be(n[1])}},htmlBuilder:(i,n)=>{let l=Le(i.html,n,!1);return O.makeFragment(l)},mathmlBuilder:(i,n)=>Yt(i.mathml,n)});let ci=function(i){if(/^[-+]? *(\d+(\.\d*)?|\.\d+)$/.test(i))return{number:+i,unit:"bp"};{let n=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(i);if(!n)throw new o("Invalid size: '"+i+"' in \\includegraphics");let l={number:+(n[1]+n[2]),unit:n[3]};if(!Fo(l))throw new o("Invalid unit: '"+l.unit+"' in \\includegraphics.");return l}};j({type:"includegraphics",names:["\\includegraphics"],props:{numArgs:1,numOptionalArgs:1,argTypes:["raw","url"],allowedInText:!1},handler:(i,n,l)=>{let{parser:a}=i,f={number:0,unit:"em"},d={number:.9,unit:"em"},x={number:0,unit:"em"},w="";if(l[0]){let I=le(l[0],"raw").string.split(",");for(let B=0;B<I.length;B++){let F=I[B].split("=");if(F.length===2){let H=F[1].trim();switch(F[0].trim()){case"alt":w=H;break;case"width":f=ci(H);break;case"height":d=ci(H);break;case"totalheight":x=ci(H);break;default:throw new o("Invalid key: '"+F[0]+"' in \\includegraphics.")}}}}let A=le(n[0],"url").url;return w===""&&(w=A,w=w.replace(/^.*[\\/]/,""),w=w.substring(0,w.lastIndexOf("."))),a.settings.isTrusted({command:"\\includegraphics",url:A})?{type:"includegraphics",mode:a.mode,alt:w,width:f,height:d,totalheight:x,src:A}:a.formatUnsupportedCmd("\\includegraphics")},htmlBuilder:(i,n)=>{let l=Ce(i.height,n),a=0;i.totalheight.number>0&&(a=Ce(i.totalheight,n)-l);let f=0;i.width.number>0&&(f=Ce(i.width,n));let d={height:U(l+a)};f>0&&(d.width=U(f)),a>0&&(d.verticalAlign=U(-a));let x=new xh(i.src,i.alt,d);return x.height=l,x.depth=a,x},mathmlBuilder:(i,n)=>{let l=new V.MathNode("mglyph",[]);l.setAttribute("alt",i.alt);let a=Ce(i.height,n),f=0;if(i.totalheight.number>0&&(f=Ce(i.totalheight,n)-a,l.setAttribute("valign",U(-f))),l.setAttribute("height",U(a+f)),i.width.number>0){let d=Ce(i.width,n);l.setAttribute("width",U(d))}return l.setAttribute("src",i.src),l}}),j({type:"kern",names:["\\kern","\\mkern","\\hskip","\\mskip"],props:{numArgs:1,argTypes:["size"],primitive:!0,allowedInText:!0},handler(i,n){let{parser:l,funcName:a}=i,f=le(n[0],"size");if(l.settings.strict){let d=a[1]==="m",x=f.value.unit==="mu";d?(x||l.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+a+" supports only mu units, "+("not "+f.value.unit+" units")),l.mode!=="math"&&l.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+a+" works only in math mode")):x&&l.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+a+" doesn't support mu units")}return{type:"kern",mode:l.mode,dimension:f.value}},htmlBuilder(i,n){return O.makeGlue(i.dimension,n)},mathmlBuilder(i,n){let l=Ce(i.dimension,n);return new V.SpaceNode(l)}}),j({type:"lap",names:["\\mathllap","\\mathrlap","\\mathclap"],props:{numArgs:1,allowedInText:!0},handler:(i,n)=>{let{parser:l,funcName:a}=i,f=n[0];return{type:"lap",mode:l.mode,alignment:a.slice(5),body:f}},htmlBuilder:(i,n)=>{let l;i.alignment==="clap"?(l=O.makeSpan([],[fe(i.body,n)]),l=O.makeSpan(["inner"],[l],n)):l=O.makeSpan(["inner"],[fe(i.body,n)]);let a=O.makeSpan(["fix"],[]),f=O.makeSpan([i.alignment],[l,a],n),d=O.makeSpan(["strut"]);return d.style.height=U(f.height+f.depth),f.depth&&(d.style.verticalAlign=U(-f.depth)),f.children.unshift(d),f=O.makeSpan(["thinbox"],[f],n),O.makeSpan(["mord","vbox"],[f],n)},mathmlBuilder:(i,n)=>{let l=new V.MathNode("mpadded",[ye(i.body,n)]);if(i.alignment!=="rlap"){let a=i.alignment==="llap"?"-1":"-0.5";l.setAttribute("lspace",a+"width")}return l.setAttribute("width","0px"),l}}),j({type:"styling",names:["\\(","$"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(i,n){let{funcName:l,parser:a}=i,f=a.mode;a.switchMode("math");let d=l==="\\("?"\\)":"$",x=a.parseExpression(!1,d);return a.expect(d),a.switchMode(f),{type:"styling",mode:a.mode,style:"text",body:x}}}),j({type:"text",names:["\\)","\\]"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(i,n){throw new o("Mismatched "+i.funcName)}});let Dl=(i,n)=>{switch(n.style.size){case Y.DISPLAY.size:return i.display;case Y.TEXT.size:return i.text;case Y.SCRIPT.size:return i.script;case Y.SCRIPTSCRIPT.size:return i.scriptscript;default:return i.text}};j({type:"mathchoice",names:["\\mathchoice"],props:{numArgs:4,primitive:!0},handler:(i,n)=>{let{parser:l}=i;return{type:"mathchoice",mode:l.mode,display:Be(n[0]),text:Be(n[1]),script:Be(n[2]),scriptscript:Be(n[3])}},htmlBuilder:(i,n)=>{let l=Dl(i,n),a=Le(l,n,!1);return O.makeFragment(a)},mathmlBuilder:(i,n)=>{let l=Dl(i,n);return Yt(l,n)}});let Bl=(i,n,l,a,f,d,x)=>{i=O.makeSpan([],[i]);let w=l&&z.isCharacterBox(l),A,C;if(n){let F=fe(n,a.havingStyle(f.sup()),a);C={elem:F,kern:Math.max(a.fontMetrics().bigOpSpacing1,a.fontMetrics().bigOpSpacing3-F.depth)}}if(l){let F=fe(l,a.havingStyle(f.sub()),a);A={elem:F,kern:Math.max(a.fontMetrics().bigOpSpacing2,a.fontMetrics().bigOpSpacing4-F.height)}}let I;if(C&&A){let F=a.fontMetrics().bigOpSpacing5+A.elem.height+A.elem.depth+A.kern+i.depth+x;I=O.makeVList({positionType:"bottom",positionData:F,children:[{type:"kern",size:a.fontMetrics().bigOpSpacing5},{type:"elem",elem:A.elem,marginLeft:U(-d)},{type:"kern",size:A.kern},{type:"elem",elem:i},{type:"kern",size:C.kern},{type:"elem",elem:C.elem,marginLeft:U(d)},{type:"kern",size:a.fontMetrics().bigOpSpacing5}]},a)}else if(A){let F=i.height-x;I=O.makeVList({positionType:"top",positionData:F,children:[{type:"kern",size:a.fontMetrics().bigOpSpacing5},{type:"elem",elem:A.elem,marginLeft:U(-d)},{type:"kern",size:A.kern},{type:"elem",elem:i}]},a)}else if(C){let F=i.depth+x;I=O.makeVList({positionType:"bottom",positionData:F,children:[{type:"elem",elem:i},{type:"kern",size:C.kern},{type:"elem",elem:C.elem,marginLeft:U(d)},{type:"kern",size:a.fontMetrics().bigOpSpacing5}]},a)}else return i;let B=[I];if(A&&d!==0&&!w){let F=O.makeSpan(["mspace"],[],a);F.style.marginRight=U(d),B.unshift(F)}return O.makeSpan(["mop","op-limits"],B,a)},Ol=["\\smallint"],vr=(i,n)=>{let l,a,f=!1,d;i.type==="supsub"?(l=i.sup,a=i.sub,d=le(i.base,"op"),f=!0):d=le(i,"op");let x=n.style,w=!1;x.size===Y.DISPLAY.size&&d.symbol&&!z.contains(Ol,d.name)&&(w=!0);let A;if(d.symbol){let B=w?"Size2-Regular":"Size1-Regular",F="";if((d.name==="\\oiint"||d.name==="\\oiiint")&&(F=d.name.slice(1),d.name=F==="oiint"?"\\iint":"\\iiint"),A=O.makeSymbol(d.name,B,"math",n,["mop","op-symbol",w?"large-op":"small-op"]),F.length>0){let H=A.italic,Z=O.staticSvg(F+"Size"+(w?"2":"1"),n);A=O.makeVList({positionType:"individualShift",children:[{type:"elem",elem:A,shift:0},{type:"elem",elem:Z,shift:w?.08:0}]},n),d.name="\\"+F,A.classes.unshift("mop"),A.italic=H}}else if(d.body){let B=Le(d.body,n,!0);B.length===1&&B[0]instanceof ht?(A=B[0],A.classes[0]="mop"):A=O.makeSpan(["mop"],B,n)}else{let B=[];for(let F=1;F<d.name.length;F++)B.push(O.mathsym(d.name[F],d.mode,n));A=O.makeSpan(["mop"],B,n)}let C=0,I=0;return(A instanceof ht||d.name==="\\oiint"||d.name==="\\oiiint")&&!d.suppressBaseShift&&(C=(A.height-A.depth)/2-n.fontMetrics().axisHeight,I=A.italic),f?Bl(A,l,a,n,x,I,C):(C&&(A.style.position="relative",A.style.top=U(C)),A)},Pr=(i,n)=>{let l;if(i.symbol)l=new at("mo",[ft(i.name,i.mode)]),z.contains(Ol,i.name)&&l.setAttribute("largeop","false");else if(i.body)l=new at("mo",Qe(i.body,n));else{l=new at("mi",[new At(i.name.slice(1))]);let a=new at("mo",[ft("\u2061","text")]);i.parentIsSupSub?l=new at("mrow",[l,a]):l=Jo([l,a])}return l},f1={"\u220F":"\\prod","\u2210":"\\coprod","\u2211":"\\sum","\u22C0":"\\bigwedge","\u22C1":"\\bigvee","\u22C2":"\\bigcap","\u22C3":"\\bigcup","\u2A00":"\\bigodot","\u2A01":"\\bigoplus","\u2A02":"\\bigotimes","\u2A04":"\\biguplus","\u2A06":"\\bigsqcup"};j({type:"op",names:["\\coprod","\\bigvee","\\bigwedge","\\biguplus","\\bigcap","\\bigcup","\\intop","\\prod","\\sum","\\bigotimes","\\bigoplus","\\bigodot","\\bigsqcup","\\smallint","\u220F","\u2210","\u2211","\u22C0","\u22C1","\u22C2","\u22C3","\u2A00","\u2A01","\u2A02","\u2A04","\u2A06"],props:{numArgs:0},handler:(i,n)=>{let{parser:l,funcName:a}=i,f=a;return f.length===1&&(f=f1[f]),{type:"op",mode:l.mode,limits:!0,parentIsSupSub:!1,symbol:!0,name:f}},htmlBuilder:vr,mathmlBuilder:Pr}),j({type:"op",names:["\\mathop"],props:{numArgs:1,primitive:!0},handler:(i,n)=>{let{parser:l}=i,a=n[0];return{type:"op",mode:l.mode,limits:!1,parentIsSupSub:!1,symbol:!1,body:Be(a)}},htmlBuilder:vr,mathmlBuilder:Pr});let p1={"\u222B":"\\int","\u222C":"\\iint","\u222D":"\\iiint","\u222E":"\\oint","\u222F":"\\oiint","\u2230":"\\oiiint"};j({type:"op",names:["\\arcsin","\\arccos","\\arctan","\\arctg","\\arcctg","\\arg","\\ch","\\cos","\\cosec","\\cosh","\\cot","\\cotg","\\coth","\\csc","\\ctg","\\cth","\\deg","\\dim","\\exp","\\hom","\\ker","\\lg","\\ln","\\log","\\sec","\\sin","\\sinh","\\sh","\\tan","\\tanh","\\tg","\\th"],props:{numArgs:0},handler(i){let{parser:n,funcName:l}=i;return{type:"op",mode:n.mode,limits:!1,parentIsSupSub:!1,symbol:!1,name:l}},htmlBuilder:vr,mathmlBuilder:Pr}),j({type:"op",names:["\\det","\\gcd","\\inf","\\lim","\\max","\\min","\\Pr","\\sup"],props:{numArgs:0},handler(i){let{parser:n,funcName:l}=i;return{type:"op",mode:n.mode,limits:!0,parentIsSupSub:!1,symbol:!1,name:l}},htmlBuilder:vr,mathmlBuilder:Pr}),j({type:"op",names:["\\int","\\iint","\\iiint","\\oint","\\oiint","\\oiiint","\u222B","\u222C","\u222D","\u222E","\u222F","\u2230"],props:{numArgs:0},handler(i){let{parser:n,funcName:l}=i,a=l;return a.length===1&&(a=p1[a]),{type:"op",mode:n.mode,limits:!1,parentIsSupSub:!1,symbol:!0,name:a}},htmlBuilder:vr,mathmlBuilder:Pr});let Rl=(i,n)=>{let l,a,f=!1,d;i.type==="supsub"?(l=i.sup,a=i.sub,d=le(i.base,"operatorname"),f=!0):d=le(i,"operatorname");let x;if(d.body.length>0){let w=d.body.map(C=>{let I=C.text;return typeof I=="string"?{type:"textord",mode:C.mode,text:I}:C}),A=Le(w,n.withFont("mathrm"),!0);for(let C=0;C<A.length;C++){let I=A[C];I instanceof ht&&(I.text=I.text.replace(/\u2212/,"-").replace(/\u2217/,"*"))}x=O.makeSpan(["mop"],A,n)}else x=O.makeSpan(["mop"],[],n);return f?Bl(x,l,a,n,n.style,0,0):x};j({type:"operatorname",names:["\\operatorname@","\\operatornamewithlimits"],props:{numArgs:1},handler:(i,n)=>{let{parser:l,funcName:a}=i,f=n[0];return{type:"operatorname",mode:l.mode,body:Be(f),alwaysHandleSupSub:a==="\\operatornamewithlimits",limits:!1,parentIsSupSub:!1}},htmlBuilder:Rl,mathmlBuilder:(i,n)=>{let l=Qe(i.body,n.withFont("mathrm")),a=!0;for(let x=0;x<l.length;x++){let w=l[x];if(!(w instanceof V.SpaceNode))if(w instanceof V.MathNode)switch(w.type){case"mi":case"mn":case"ms":case"mspace":case"mtext":break;case"mo":{let A=w.children[0];w.children.length===1&&A instanceof V.TextNode?A.text=A.text.replace(/\u2212/,"-").replace(/\u2217/,"*"):a=!1;break}default:a=!1}else a=!1}if(a){let x=l.map(w=>w.toText()).join("");l=[new V.TextNode(x)]}let f=new V.MathNode("mi",l);f.setAttribute("mathvariant","normal");let d=new V.MathNode("mo",[ft("\u2061","text")]);return i.parentIsSupSub?new V.MathNode("mrow",[f,d]):V.newDocumentFragment([f,d])}}),k("\\operatorname","\\@ifstar\\operatornamewithlimits\\operatorname@"),rr({type:"ordgroup",htmlBuilder(i,n){return i.semisimple?O.makeFragment(Le(i.body,n,!1)):O.makeSpan(["mord"],Le(i.body,n,!0),n)},mathmlBuilder(i,n){return Yt(i.body,n,!0)}}),j({type:"overline",names:["\\overline"],props:{numArgs:1},handler(i,n){let{parser:l}=i,a=n[0];return{type:"overline",mode:l.mode,body:a}},htmlBuilder(i,n){let l=fe(i.body,n.havingCrampedStyle()),a=O.makeLineSpan("overline-line",n),f=n.fontMetrics().defaultRuleThickness,d=O.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:l},{type:"kern",size:3*f},{type:"elem",elem:a},{type:"kern",size:f}]},n);return O.makeSpan(["mord","overline"],[d],n)},mathmlBuilder(i,n){let l=new V.MathNode("mo",[new V.TextNode("\u203E")]);l.setAttribute("stretchy","true");let a=new V.MathNode("mover",[ye(i.body,n),l]);return a.setAttribute("accent","true"),a}}),j({type:"phantom",names:["\\phantom"],props:{numArgs:1,allowedInText:!0},handler:(i,n)=>{let{parser:l}=i,a=n[0];return{type:"phantom",mode:l.mode,body:Be(a)}},htmlBuilder:(i,n)=>{let l=Le(i.body,n.withPhantom(),!1);return O.makeFragment(l)},mathmlBuilder:(i,n)=>{let l=Qe(i.body,n);return new V.MathNode("mphantom",l)}}),j({type:"hphantom",names:["\\hphantom"],props:{numArgs:1,allowedInText:!0},handler:(i,n)=>{let{parser:l}=i,a=n[0];return{type:"hphantom",mode:l.mode,body:a}},htmlBuilder:(i,n)=>{let l=O.makeSpan([],[fe(i.body,n.withPhantom())]);if(l.height=0,l.depth=0,l.children)for(let a=0;a<l.children.length;a++)l.children[a].height=0,l.children[a].depth=0;return l=O.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:l}]},n),O.makeSpan(["mord"],[l],n)},mathmlBuilder:(i,n)=>{let l=Qe(Be(i.body),n),a=new V.MathNode("mphantom",l),f=new V.MathNode("mpadded",[a]);return f.setAttribute("height","0px"),f.setAttribute("depth","0px"),f}}),j({type:"vphantom",names:["\\vphantom"],props:{numArgs:1,allowedInText:!0},handler:(i,n)=>{let{parser:l}=i,a=n[0];return{type:"vphantom",mode:l.mode,body:a}},htmlBuilder:(i,n)=>{let l=O.makeSpan(["inner"],[fe(i.body,n.withPhantom())]),a=O.makeSpan(["fix"],[]);return O.makeSpan(["mord","rlap"],[l,a],n)},mathmlBuilder:(i,n)=>{let l=Qe(Be(i.body),n),a=new V.MathNode("mphantom",l),f=new V.MathNode("mpadded",[a]);return f.setAttribute("width","0px"),f}}),j({type:"raisebox",names:["\\raisebox"],props:{numArgs:2,argTypes:["size","hbox"],allowedInText:!0},handler(i,n){let{parser:l}=i,a=le(n[0],"size").value,f=n[1];return{type:"raisebox",mode:l.mode,dy:a,body:f}},htmlBuilder(i,n){let l=fe(i.body,n),a=Ce(i.dy,n);return O.makeVList({positionType:"shift",positionData:-a,children:[{type:"elem",elem:l}]},n)},mathmlBuilder(i,n){let l=new V.MathNode("mpadded",[ye(i.body,n)]),a=i.dy.number+i.dy.unit;return l.setAttribute("voffset",a),l}}),j({type:"internal",names:["\\relax"],props:{numArgs:0,allowedInText:!0,allowedInArgument:!0},handler(i){let{parser:n}=i;return{type:"internal",mode:n.mode}}}),j({type:"rule",names:["\\rule"],props:{numArgs:2,numOptionalArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["size","size","size"]},handler(i,n,l){let{parser:a}=i,f=l[0],d=le(n[0],"size"),x=le(n[1],"size");return{type:"rule",mode:a.mode,shift:f&&le(f,"size").value,width:d.value,height:x.value}},htmlBuilder(i,n){let l=O.makeSpan(["mord","rule"],[],n),a=Ce(i.width,n),f=Ce(i.height,n),d=i.shift?Ce(i.shift,n):0;return l.style.borderRightWidth=U(a),l.style.borderTopWidth=U(f),l.style.bottom=U(d),l.width=a,l.height=f+d,l.depth=-d,l.maxFontSize=f*1.125*n.sizeMultiplier,l},mathmlBuilder(i,n){let l=Ce(i.width,n),a=Ce(i.height,n),f=i.shift?Ce(i.shift,n):0,d=n.color&&n.getColor()||"black",x=new V.MathNode("mspace");x.setAttribute("mathbackground",d),x.setAttribute("width",U(l)),x.setAttribute("height",U(a));let w=new V.MathNode("mpadded",[x]);return f>=0?w.setAttribute("height",U(f)):(w.setAttribute("height",U(f)),w.setAttribute("depth",U(-f))),w.setAttribute("voffset",U(f)),w}});function Fl(i,n,l){let a=Le(i,n,!1),f=n.sizeMultiplier/l.sizeMultiplier;for(let d=0;d<a.length;d++){let x=a[d].classes.indexOf("sizing");x<0?Array.prototype.push.apply(a[d].classes,n.sizingClasses(l)):a[d].classes[x+1]==="reset-size"+n.size&&(a[d].classes[x+1]="reset-size"+l.size),a[d].height*=f,a[d].depth*=f}return O.makeFragment(a)}let Ll=["\\tiny","\\sixptsize","\\scriptsize","\\footnotesize","\\small","\\normalsize","\\large","\\Large","\\LARGE","\\huge","\\Huge"];j({type:"sizing",names:Ll,props:{numArgs:0,allowedInText:!0},handler:(i,n)=>{let{breakOnTokenText:l,funcName:a,parser:f}=i,d=f.parseExpression(!1,l);return{type:"sizing",mode:f.mode,size:Ll.indexOf(a)+1,body:d}},htmlBuilder:(i,n)=>{let l=n.havingSize(i.size);return Fl(i.body,l,n)},mathmlBuilder:(i,n)=>{let l=n.havingSize(i.size),a=Qe(i.body,l),f=new V.MathNode("mstyle",a);return f.setAttribute("mathsize",U(l.sizeMultiplier)),f}}),j({type:"smash",names:["\\smash"],props:{numArgs:1,numOptionalArgs:1,allowedInText:!0},handler:(i,n,l)=>{let{parser:a}=i,f=!1,d=!1,x=l[0]&&le(l[0],"ordgroup");if(x){let A="";for(let C=0;C<x.body.length;++C)if(A=x.body[C].text,A==="t")f=!0;else if(A==="b")d=!0;else{f=!1,d=!1;break}}else f=!0,d=!0;let w=n[0];return{type:"smash",mode:a.mode,body:w,smashHeight:f,smashDepth:d}},htmlBuilder:(i,n)=>{let l=O.makeSpan([],[fe(i.body,n)]);if(!i.smashHeight&&!i.smashDepth)return l;if(i.smashHeight&&(l.height=0,l.children))for(let f=0;f<l.children.length;f++)l.children[f].height=0;if(i.smashDepth&&(l.depth=0,l.children))for(let f=0;f<l.children.length;f++)l.children[f].depth=0;let a=O.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:l}]},n);return O.makeSpan(["mord"],[a],n)},mathmlBuilder:(i,n)=>{let l=new V.MathNode("mpadded",[ye(i.body,n)]);return i.smashHeight&&l.setAttribute("height","0px"),i.smashDepth&&l.setAttribute("depth","0px"),l}}),j({type:"sqrt",names:["\\sqrt"],props:{numArgs:1,numOptionalArgs:1},handler(i,n,l){let{parser:a}=i,f=l[0],d=n[0];return{type:"sqrt",mode:a.mode,body:d,index:f}},htmlBuilder(i,n){let l=fe(i.body,n.havingCrampedStyle());l.height===0&&(l.height=n.fontMetrics().xHeight),l=O.wrapFragment(l,n);let f=n.fontMetrics().defaultRuleThickness,d=f;n.style.id<Y.TEXT.id&&(d=n.fontMetrics().xHeight);let x=f+d/4,w=l.height+l.depth+x+f,{span:A,ruleWidth:C,advanceWidth:I}=Rt.sqrtImage(w,n),B=A.height-C;B>l.height+l.depth+x&&(x=(x+B-l.height-l.depth)/2);let F=A.height-l.height-x-C;l.style.paddingLeft=U(I);let H=O.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:l,wrapperClasses:["svg-align"]},{type:"kern",size:-(l.height+F)},{type:"elem",elem:A},{type:"kern",size:C}]},n);if(i.index){let Z=n.havingStyle(Y.SCRIPTSCRIPT),Q=fe(i.index,Z,n),ce=.6*(H.height-H.depth),ae=O.makeVList({positionType:"shift",positionData:-ce,children:[{type:"elem",elem:Q}]},n),he=O.makeSpan(["root"],[ae]);return O.makeSpan(["mord","sqrt"],[he,H],n)}else return O.makeSpan(["mord","sqrt"],[H],n)},mathmlBuilder(i,n){let{body:l,index:a}=i;return a?new V.MathNode("mroot",[ye(l,n),ye(a,n)]):new V.MathNode("msqrt",[ye(l,n)])}});let ql={display:Y.DISPLAY,text:Y.TEXT,script:Y.SCRIPT,scriptscript:Y.SCRIPTSCRIPT};j({type:"styling",names:["\\displaystyle","\\textstyle","\\scriptstyle","\\scriptscriptstyle"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(i,n){let{breakOnTokenText:l,funcName:a,parser:f}=i,d=f.parseExpression(!0,l),x=a.slice(1,a.length-5);return{type:"styling",mode:f.mode,style:x,body:d}},htmlBuilder(i,n){let l=ql[i.style],a=n.havingStyle(l).withFont("");return Fl(i.body,a,n)},mathmlBuilder(i,n){let l=ql[i.style],a=n.havingStyle(l),f=Qe(i.body,a),d=new V.MathNode("mstyle",f),w={display:["0","true"],text:["0","false"],script:["1","false"],scriptscript:["2","false"]}[i.style];return d.setAttribute("scriptlevel",w[0]),d.setAttribute("displaystyle",w[1]),d}});let d1=function(i,n){let l=i.base;return l?l.type==="op"?l.limits&&(n.style.size===Y.DISPLAY.size||l.alwaysHandleSupSub)?vr:null:l.type==="operatorname"?l.alwaysHandleSupSub&&(n.style.size===Y.DISPLAY.size||l.limits)?Rl:null:l.type==="accent"?z.isCharacterBox(l.base)?W0:null:l.type==="horizBrace"&&!i.sub===l.isOver?Il:null:null};rr({type:"supsub",htmlBuilder(i,n){let l=d1(i,n);if(l)return l(i,n);let{base:a,sup:f,sub:d}=i,x=fe(a,n),w,A,C=n.fontMetrics(),I=0,B=0,F=a&&z.isCharacterBox(a);if(f){let de=n.havingStyle(n.style.sup());w=fe(f,de,n),F||(I=x.height-de.fontMetrics().supDrop*de.sizeMultiplier/n.sizeMultiplier)}if(d){let de=n.havingStyle(n.style.sub());A=fe(d,de,n),F||(B=x.depth+de.fontMetrics().subDrop*de.sizeMultiplier/n.sizeMultiplier)}let H;n.style===Y.DISPLAY?H=C.sup1:n.style.cramped?H=C.sup3:H=C.sup2;let Z=n.sizeMultiplier,Q=U(.5/C.ptPerEm/Z),ce=null;if(A){let de=i.base&&i.base.type==="op"&&i.base.name&&(i.base.name==="\\oiint"||i.base.name==="\\oiiint");(x instanceof ht||de)&&(ce=U(-x.italic))}let ae;if(w&&A){I=Math.max(I,H,w.depth+.25*C.xHeight),B=Math.max(B,C.sub2);let we=4*C.defaultRuleThickness;if(I-w.depth-(A.height-B)<we){B=we-(I-w.depth)+A.height;let qe=.8*C.xHeight-(I-w.depth);qe>0&&(I+=qe,B-=qe)}let We=[{type:"elem",elem:A,shift:B,marginRight:Q,marginLeft:ce},{type:"elem",elem:w,shift:-I,marginRight:Q}];ae=O.makeVList({positionType:"individualShift",children:We},n)}else if(A){B=Math.max(B,C.sub1,A.height-.8*C.xHeight);let de=[{type:"elem",elem:A,marginLeft:ce,marginRight:Q}];ae=O.makeVList({positionType:"shift",positionData:B,children:de},n)}else if(w)I=Math.max(I,H,w.depth+.25*C.xHeight),ae=O.makeVList({positionType:"shift",positionData:-I,children:[{type:"elem",elem:w,marginRight:Q}]},n);else throw new Error("supsub must have either sup or sub.");let he=V0(x,"right")||"mord";return O.makeSpan([he],[x,O.makeSpan(["msupsub"],[ae])],n)},mathmlBuilder(i,n){let l=!1,a,f;i.base&&i.base.type==="horizBrace"&&(f=!!i.sup,f===i.base.isOver&&(l=!0,a=i.base.isOver)),i.base&&(i.base.type==="op"||i.base.type==="operatorname")&&(i.base.parentIsSupSub=!0);let d=[ye(i.base,n)];i.sub&&d.push(ye(i.sub,n)),i.sup&&d.push(ye(i.sup,n));let x;if(l)x=a?"mover":"munder";else if(i.sub)if(i.sup){let w=i.base;w&&w.type==="op"&&w.limits&&n.style===Y.DISPLAY||w&&w.type==="operatorname"&&w.alwaysHandleSupSub&&(n.style===Y.DISPLAY||w.limits)?x="munderover":x="msubsup"}else{let w=i.base;w&&w.type==="op"&&w.limits&&(n.style===Y.DISPLAY||w.alwaysHandleSupSub)||w&&w.type==="operatorname"&&w.alwaysHandleSupSub&&(w.limits||n.style===Y.DISPLAY)?x="munder":x="msub"}else{let w=i.base;w&&w.type==="op"&&w.limits&&(n.style===Y.DISPLAY||w.alwaysHandleSupSub)||w&&w.type==="operatorname"&&w.alwaysHandleSupSub&&(w.limits||n.style===Y.DISPLAY)?x="mover":x="msup"}return new V.MathNode(x,d)}}),rr({type:"atom",htmlBuilder(i,n){return O.mathsym(i.text,i.mode,n,["m"+i.family])},mathmlBuilder(i,n){let l=new V.MathNode("mo",[ft(i.text,i.mode)]);if(i.family==="bin"){let a=U0(i,n);a==="bold-italic"&&l.setAttribute("mathvariant",a)}else i.family==="punct"?l.setAttribute("separator","true"):(i.family==="open"||i.family==="close")&&l.setAttribute("stretchy","false");return l}});let Pl={mi:"italic",mn:"normal",mtext:"normal"};rr({type:"mathord",htmlBuilder(i,n){return O.makeOrd(i,n,"mathord")},mathmlBuilder(i,n){let l=new V.MathNode("mi",[ft(i.text,i.mode,n)]),a=U0(i,n)||"italic";return a!==Pl[l.type]&&l.setAttribute("mathvariant",a),l}}),rr({type:"textord",htmlBuilder(i,n){return O.makeOrd(i,n,"textord")},mathmlBuilder(i,n){let l=ft(i.text,i.mode,n),a=U0(i,n)||"normal",f;return i.mode==="text"?f=new V.MathNode("mtext",[l]):/[0-9]/.test(i.text)?f=new V.MathNode("mn",[l]):i.text==="\\prime"?f=new V.MathNode("mo",[l]):f=new V.MathNode("mi",[l]),a!==Pl[f.type]&&f.setAttribute("mathvariant",a),f}});let hi={"\\nobreak":"nobreak","\\allowbreak":"allowbreak"},fi={" ":{},"\\ ":{},"~":{className:"nobreak"},"\\space":{},"\\nobreakspace":{className:"nobreak"}};rr({type:"spacing",htmlBuilder(i,n){if(fi.hasOwnProperty(i.text)){let l=fi[i.text].className||"";if(i.mode==="text"){let a=O.makeOrd(i,n,"textord");return a.classes.push(l),a}else return O.makeSpan(["mspace",l],[O.mathsym(i.text,i.mode,n)],n)}else{if(hi.hasOwnProperty(i.text))return O.makeSpan(["mspace",hi[i.text]],[],n);throw new o('Unknown type of space "'+i.text+'"')}},mathmlBuilder(i,n){let l;if(fi.hasOwnProperty(i.text))l=new V.MathNode("mtext",[new V.TextNode("\xA0")]);else{if(hi.hasOwnProperty(i.text))return new V.MathNode("mspace");throw new o('Unknown type of space "'+i.text+'"')}return l}});let Hl=()=>{let i=new V.MathNode("mtd",[]);return i.setAttribute("width","50%"),i};rr({type:"tag",mathmlBuilder(i,n){let l=new V.MathNode("mtable",[new V.MathNode("mtr",[Hl(),new V.MathNode("mtd",[Yt(i.body,n)]),Hl(),new V.MathNode("mtd",[Yt(i.tag,n)])])]);return l.setAttribute("width","100%"),l}});let $l={"\\text":void 0,"\\textrm":"textrm","\\textsf":"textsf","\\texttt":"texttt","\\textnormal":"textrm"},Vl={"\\textbf":"textbf","\\textmd":"textmd"},m1={"\\textit":"textit","\\textup":"textup"},Gl=(i,n)=>{let l=i.font;if(l){if($l[l])return n.withTextFontFamily($l[l]);if(Vl[l])return n.withTextFontWeight(Vl[l]);if(l==="\\emph")return n.fontShape==="textit"?n.withTextFontShape("textup"):n.withTextFontShape("textit")}else return n;return n.withTextFontShape(m1[l])};j({type:"text",names:["\\text","\\textrm","\\textsf","\\texttt","\\textnormal","\\textbf","\\textmd","\\textit","\\textup","\\emph"],props:{numArgs:1,argTypes:["text"],allowedInArgument:!0,allowedInText:!0},handler(i,n){let{parser:l,funcName:a}=i,f=n[0];return{type:"text",mode:l.mode,body:Be(f),font:a}},htmlBuilder(i,n){let l=Gl(i,n),a=Le(i.body,l,!0);return O.makeSpan(["mord","text"],a,l)},mathmlBuilder(i,n){let l=Gl(i,n);return Yt(i.body,l)}}),j({type:"underline",names:["\\underline"],props:{numArgs:1,allowedInText:!0},handler(i,n){let{parser:l}=i;return{type:"underline",mode:l.mode,body:n[0]}},htmlBuilder(i,n){let l=fe(i.body,n),a=O.makeLineSpan("underline-line",n),f=n.fontMetrics().defaultRuleThickness,d=O.makeVList({positionType:"top",positionData:l.height,children:[{type:"kern",size:f},{type:"elem",elem:a},{type:"kern",size:3*f},{type:"elem",elem:l}]},n);return O.makeSpan(["mord","underline"],[d],n)},mathmlBuilder(i,n){let l=new V.MathNode("mo",[new V.TextNode("\u203E")]);l.setAttribute("stretchy","true");let a=new V.MathNode("munder",[ye(i.body,n),l]);return a.setAttribute("accentunder","true"),a}}),j({type:"vcenter",names:["\\vcenter"],props:{numArgs:1,argTypes:["original"],allowedInText:!1},handler(i,n){let{parser:l}=i;return{type:"vcenter",mode:l.mode,body:n[0]}},htmlBuilder(i,n){let l=fe(i.body,n),a=n.fontMetrics().axisHeight,f=.5*(l.height-a-(l.depth+a));return O.makeVList({positionType:"shift",positionData:f,children:[{type:"elem",elem:l}]},n)},mathmlBuilder(i,n){return new V.MathNode("mpadded",[ye(i.body,n)],["vcenter"])}}),j({type:"verb",names:["\\verb"],props:{numArgs:0,allowedInText:!0},handler(i,n,l){throw new o("\\verb ended by end of line instead of matching delimiter")},htmlBuilder(i,n){let l=Yl(i),a=[],f=n.havingStyle(n.style.text());for(let d=0;d<l.length;d++){let x=l[d];x==="~"&&(x="\\textasciitilde"),a.push(O.makeSymbol(x,"Typewriter-Regular",i.mode,f,["mord","texttt"]))}return O.makeSpan(["mord","text"].concat(f.sizingClasses(n)),O.tryCombineChars(a),f)},mathmlBuilder(i,n){let l=new V.TextNode(Yl(i)),a=new V.MathNode("mtext",[l]);return a.setAttribute("mathvariant","monospace"),a}});let Yl=i=>i.body.replace(/ /g,i.star?"\u2423":"\xA0");var Xt=Ko;let Ul=`[ \r
	]`,g1="\\\\[a-zA-Z@]+",x1="\\\\[^\uD800-\uDFFF]",y1="("+g1+")"+Ul+"*",b1=`\\\\(
|[ \r	]+
?)[ \r	]*`,pi="[\u0300-\u036F]",v1=new RegExp(pi+"+$"),w1="("+Ul+"+)|"+(b1+"|")+"([!-\\[\\]-\u2027\u202A-\uD7FF\uF900-\uFFFF]"+(pi+"*")+"|[\uD800-\uDBFF][\uDC00-\uDFFF]"+(pi+"*")+"|\\\\verb\\*([^]).*?\\4|\\\\verb([^*a-zA-Z]).*?\\5"+("|"+y1)+("|"+x1+")");class Xl{constructor(n,l){this.input=void 0,this.settings=void 0,this.tokenRegex=void 0,this.catcodes=void 0,this.input=n,this.settings=l,this.tokenRegex=new RegExp(w1,"g"),this.catcodes={"%":14,"~":13}}setCatcode(n,l){this.catcodes[n]=l}lex(){let n=this.input,l=this.tokenRegex.lastIndex;if(l===n.length)return new pt("EOF",new ut(this,l,l));let a=this.tokenRegex.exec(n);if(a===null||a.index!==l)throw new o("Unexpected character: '"+n[l]+"'",new pt(n[l],new ut(this,l,l+1)));let f=a[6]||a[3]||(a[2]?"\\ ":" ");if(this.catcodes[f]===14){let d=n.indexOf(`
`,this.tokenRegex.lastIndex);return d===-1?(this.tokenRegex.lastIndex=n.length,this.settings.reportNonstrict("commentAtEnd","% comment has no terminating newline; LaTeX would fail because of commenting the end of math mode (e.g. $)")):this.tokenRegex.lastIndex=d+1,this.lex()}return new pt(f,new ut(this,l,this.tokenRegex.lastIndex))}}class k1{constructor(n,l){n===void 0&&(n={}),l===void 0&&(l={}),this.current=void 0,this.builtins=void 0,this.undefStack=void 0,this.current=l,this.builtins=n,this.undefStack=[]}beginGroup(){this.undefStack.push({})}endGroup(){if(this.undefStack.length===0)throw new o("Unbalanced namespace destruction: attempt to pop global namespace; please report this as a bug");let n=this.undefStack.pop();for(let l in n)n.hasOwnProperty(l)&&(n[l]==null?delete this.current[l]:this.current[l]=n[l])}endGroups(){for(;this.undefStack.length>0;)this.endGroup()}has(n){return this.current.hasOwnProperty(n)||this.builtins.hasOwnProperty(n)}get(n){return this.current.hasOwnProperty(n)?this.current[n]:this.builtins[n]}set(n,l,a){if(a===void 0&&(a=!1),a){for(let f=0;f<this.undefStack.length;f++)delete this.undefStack[f][n];this.undefStack.length>0&&(this.undefStack[this.undefStack.length-1][n]=l)}else{let f=this.undefStack[this.undefStack.length-1];f&&!f.hasOwnProperty(n)&&(f[n]=this.current[n])}l==null?delete this.current[n]:this.current[n]=l}}var S1=kl;k("\\noexpand",function(i){let n=i.popToken();return i.isExpandable(n.text)&&(n.noexpand=!0,n.treatAsRelax=!0),{tokens:[n],numArgs:0}}),k("\\expandafter",function(i){let n=i.popToken();return i.expandOnce(!0),{tokens:[n],numArgs:0}}),k("\\@firstoftwo",function(i){return{tokens:i.consumeArgs(2)[0],numArgs:0}}),k("\\@secondoftwo",function(i){return{tokens:i.consumeArgs(2)[1],numArgs:0}}),k("\\@ifnextchar",function(i){let n=i.consumeArgs(3);i.consumeSpaces();let l=i.future();return n[0].length===1&&n[0][0].text===l.text?{tokens:n[1],numArgs:0}:{tokens:n[2],numArgs:0}}),k("\\@ifstar","\\@ifnextchar *{\\@firstoftwo{#1}}"),k("\\TextOrMath",function(i){let n=i.consumeArgs(2);return i.mode==="text"?{tokens:n[0],numArgs:0}:{tokens:n[1],numArgs:0}});let jl={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15};k("\\char",function(i){let n=i.popToken(),l,a="";if(n.text==="'")l=8,n=i.popToken();else if(n.text==='"')l=16,n=i.popToken();else if(n.text==="`")if(n=i.popToken(),n.text[0]==="\\")a=n.text.charCodeAt(1);else{if(n.text==="EOF")throw new o("\\char` missing argument");a=n.text.charCodeAt(0)}else l=10;if(l){if(a=jl[n.text],a==null||a>=l)throw new o("Invalid base-"+l+" digit "+n.text);let f;for(;(f=jl[i.future().text])!=null&&f<l;)a*=l,a+=f,i.popToken()}return"\\@char{"+a+"}"});let di=(i,n,l,a)=>{let f=i.consumeArg().tokens;if(f.length!==1)throw new o("\\newcommand's first argument must be a macro name");let d=f[0].text,x=i.isDefined(d);if(x&&!n)throw new o("\\newcommand{"+d+"} attempting to redefine "+(d+"; use \\renewcommand"));if(!x&&!l)throw new o("\\renewcommand{"+d+"} when command "+d+" does not yet exist; use \\newcommand");let w=0;if(f=i.consumeArg().tokens,f.length===1&&f[0].text==="["){let A="",C=i.expandNextToken();for(;C.text!=="]"&&C.text!=="EOF";)A+=C.text,C=i.expandNextToken();if(!A.match(/^\s*[0-9]+\s*$/))throw new o("Invalid number of arguments: "+A);w=parseInt(A),f=i.consumeArg().tokens}return x&&a||i.macros.set(d,{tokens:f,numArgs:w}),""};k("\\newcommand",i=>di(i,!1,!0,!1)),k("\\renewcommand",i=>di(i,!0,!1,!1)),k("\\providecommand",i=>di(i,!0,!0,!0)),k("\\message",i=>{let n=i.consumeArgs(1)[0];return console.log(n.reverse().map(l=>l.text).join("")),""}),k("\\errmessage",i=>{let n=i.consumeArgs(1)[0];return console.error(n.reverse().map(l=>l.text).join("")),""}),k("\\show",i=>{let n=i.popToken(),l=n.text;return console.log(n,i.macros.get(l),Xt[l],Te.math[l],Te.text[l]),""}),k("\\bgroup","{"),k("\\egroup","}"),k("~","\\nobreakspace"),k("\\lq","`"),k("\\rq","'"),k("\\aa","\\r a"),k("\\AA","\\r A"),k("\\textcopyright","\\html@mathml{\\textcircled{c}}{\\char`\xA9}"),k("\\copyright","\\TextOrMath{\\textcopyright}{\\text{\\textcopyright}}"),k("\\textregistered","\\html@mathml{\\textcircled{\\scriptsize R}}{\\char`\xAE}"),k("\u212C","\\mathscr{B}"),k("\u2130","\\mathscr{E}"),k("\u2131","\\mathscr{F}"),k("\u210B","\\mathscr{H}"),k("\u2110","\\mathscr{I}"),k("\u2112","\\mathscr{L}"),k("\u2133","\\mathscr{M}"),k("\u211B","\\mathscr{R}"),k("\u212D","\\mathfrak{C}"),k("\u210C","\\mathfrak{H}"),k("\u2128","\\mathfrak{Z}"),k("\\Bbbk","\\Bbb{k}"),k("\xB7","\\cdotp"),k("\\llap","\\mathllap{\\textrm{#1}}"),k("\\rlap","\\mathrlap{\\textrm{#1}}"),k("\\clap","\\mathclap{\\textrm{#1}}"),k("\\mathstrut","\\vphantom{(}"),k("\\underbar","\\underline{\\text{#1}}"),k("\\not",'\\html@mathml{\\mathrel{\\mathrlap\\@not}}{\\char"338}'),k("\\neq","\\html@mathml{\\mathrel{\\not=}}{\\mathrel{\\char`\u2260}}"),k("\\ne","\\neq"),k("\u2260","\\neq"),k("\\notin","\\html@mathml{\\mathrel{{\\in}\\mathllap{/\\mskip1mu}}}{\\mathrel{\\char`\u2209}}"),k("\u2209","\\notin"),k("\u2258","\\html@mathml{\\mathrel{=\\kern{-1em}\\raisebox{0.4em}{$\\scriptsize\\frown$}}}{\\mathrel{\\char`\u2258}}"),k("\u2259","\\html@mathml{\\stackrel{\\tiny\\wedge}{=}}{\\mathrel{\\char`\u2258}}"),k("\u225A","\\html@mathml{\\stackrel{\\tiny\\vee}{=}}{\\mathrel{\\char`\u225A}}"),k("\u225B","\\html@mathml{\\stackrel{\\scriptsize\\star}{=}}{\\mathrel{\\char`\u225B}}"),k("\u225D","\\html@mathml{\\stackrel{\\tiny\\mathrm{def}}{=}}{\\mathrel{\\char`\u225D}}"),k("\u225E","\\html@mathml{\\stackrel{\\tiny\\mathrm{m}}{=}}{\\mathrel{\\char`\u225E}}"),k("\u225F","\\html@mathml{\\stackrel{\\tiny?}{=}}{\\mathrel{\\char`\u225F}}"),k("\u27C2","\\perp"),k("\u203C","\\mathclose{!\\mkern-0.8mu!}"),k("\u220C","\\notni"),k("\u231C","\\ulcorner"),k("\u231D","\\urcorner"),k("\u231E","\\llcorner"),k("\u231F","\\lrcorner"),k("\xA9","\\copyright"),k("\xAE","\\textregistered"),k("\uFE0F","\\textregistered"),k("\\ulcorner",'\\html@mathml{\\@ulcorner}{\\mathop{\\char"231c}}'),k("\\urcorner",'\\html@mathml{\\@urcorner}{\\mathop{\\char"231d}}'),k("\\llcorner",'\\html@mathml{\\@llcorner}{\\mathop{\\char"231e}}'),k("\\lrcorner",'\\html@mathml{\\@lrcorner}{\\mathop{\\char"231f}}'),k("\\vdots","{\\varvdots\\rule{0pt}{15pt}}"),k("\u22EE","\\vdots"),k("\\varGamma","\\mathit{\\Gamma}"),k("\\varDelta","\\mathit{\\Delta}"),k("\\varTheta","\\mathit{\\Theta}"),k("\\varLambda","\\mathit{\\Lambda}"),k("\\varXi","\\mathit{\\Xi}"),k("\\varPi","\\mathit{\\Pi}"),k("\\varSigma","\\mathit{\\Sigma}"),k("\\varUpsilon","\\mathit{\\Upsilon}"),k("\\varPhi","\\mathit{\\Phi}"),k("\\varPsi","\\mathit{\\Psi}"),k("\\varOmega","\\mathit{\\Omega}"),k("\\substack","\\begin{subarray}{c}#1\\end{subarray}"),k("\\colon","\\nobreak\\mskip2mu\\mathpunct{}\\mathchoice{\\mkern-3mu}{\\mkern-3mu}{}{}{:}\\mskip6mu\\relax"),k("\\boxed","\\fbox{$\\displaystyle{#1}$}"),k("\\iff","\\DOTSB\\;\\Longleftrightarrow\\;"),k("\\implies","\\DOTSB\\;\\Longrightarrow\\;"),k("\\impliedby","\\DOTSB\\;\\Longleftarrow\\;"),k("\\dddot","{\\overset{\\raisebox{-0.1ex}{\\normalsize ...}}{#1}}"),k("\\ddddot","{\\overset{\\raisebox{-0.1ex}{\\normalsize ....}}{#1}}");let Wl={",":"\\dotsc","\\not":"\\dotsb","+":"\\dotsb","=":"\\dotsb","<":"\\dotsb",">":"\\dotsb","-":"\\dotsb","*":"\\dotsb",":":"\\dotsb","\\DOTSB":"\\dotsb","\\coprod":"\\dotsb","\\bigvee":"\\dotsb","\\bigwedge":"\\dotsb","\\biguplus":"\\dotsb","\\bigcap":"\\dotsb","\\bigcup":"\\dotsb","\\prod":"\\dotsb","\\sum":"\\dotsb","\\bigotimes":"\\dotsb","\\bigoplus":"\\dotsb","\\bigodot":"\\dotsb","\\bigsqcup":"\\dotsb","\\And":"\\dotsb","\\longrightarrow":"\\dotsb","\\Longrightarrow":"\\dotsb","\\longleftarrow":"\\dotsb","\\Longleftarrow":"\\dotsb","\\longleftrightarrow":"\\dotsb","\\Longleftrightarrow":"\\dotsb","\\mapsto":"\\dotsb","\\longmapsto":"\\dotsb","\\hookrightarrow":"\\dotsb","\\doteq":"\\dotsb","\\mathbin":"\\dotsb","\\mathrel":"\\dotsb","\\relbar":"\\dotsb","\\Relbar":"\\dotsb","\\xrightarrow":"\\dotsb","\\xleftarrow":"\\dotsb","\\DOTSI":"\\dotsi","\\int":"\\dotsi","\\oint":"\\dotsi","\\iint":"\\dotsi","\\iiint":"\\dotsi","\\iiiint":"\\dotsi","\\idotsint":"\\dotsi","\\DOTSX":"\\dotsx"};k("\\dots",function(i){let n="\\dotso",l=i.expandAfterFuture().text;return l in Wl?n=Wl[l]:(l.slice(0,4)==="\\not"||l in Te.math&&z.contains(["bin","rel"],Te.math[l].group))&&(n="\\dotsb"),n});let mi={")":!0,"]":!0,"\\rbrack":!0,"\\}":!0,"\\rbrace":!0,"\\rangle":!0,"\\rceil":!0,"\\rfloor":!0,"\\rgroup":!0,"\\rmoustache":!0,"\\right":!0,"\\bigr":!0,"\\biggr":!0,"\\Bigr":!0,"\\Biggr":!0,$:!0,";":!0,".":!0,",":!0};k("\\dotso",function(i){return i.future().text in mi?"\\ldots\\,":"\\ldots"}),k("\\dotsc",function(i){let n=i.future().text;return n in mi&&n!==","?"\\ldots\\,":"\\ldots"}),k("\\cdots",function(i){return i.future().text in mi?"\\@cdots\\,":"\\@cdots"}),k("\\dotsb","\\cdots"),k("\\dotsm","\\cdots"),k("\\dotsi","\\!\\cdots"),k("\\dotsx","\\ldots\\,"),k("\\DOTSI","\\relax"),k("\\DOTSB","\\relax"),k("\\DOTSX","\\relax"),k("\\tmspace","\\TextOrMath{\\kern#1#3}{\\mskip#1#2}\\relax"),k("\\,","\\tmspace+{3mu}{.1667em}"),k("\\thinspace","\\,"),k("\\>","\\mskip{4mu}"),k("\\:","\\tmspace+{4mu}{.2222em}"),k("\\medspace","\\:"),k("\\;","\\tmspace+{5mu}{.2777em}"),k("\\thickspace","\\;"),k("\\!","\\tmspace-{3mu}{.1667em}"),k("\\negthinspace","\\!"),k("\\negmedspace","\\tmspace-{4mu}{.2222em}"),k("\\negthickspace","\\tmspace-{5mu}{.277em}"),k("\\enspace","\\kern.5em "),k("\\enskip","\\hskip.5em\\relax"),k("\\quad","\\hskip1em\\relax"),k("\\qquad","\\hskip2em\\relax"),k("\\tag","\\@ifstar\\tag@literal\\tag@paren"),k("\\tag@paren","\\tag@literal{({#1})}"),k("\\tag@literal",i=>{if(i.macros.get("\\df@tag"))throw new o("Multiple \\tag");return"\\gdef\\df@tag{\\text{#1}}"}),k("\\bmod","\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}\\mathbin{\\rm mod}\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}"),k("\\pod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern8mu}{\\mkern8mu}{\\mkern8mu}(#1)"),k("\\pmod","\\pod{{\\rm mod}\\mkern6mu#1}"),k("\\mod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern12mu}{\\mkern12mu}{\\mkern12mu}{\\rm mod}\\,\\,#1"),k("\\newline","\\\\\\relax"),k("\\TeX","\\textrm{\\html@mathml{T\\kern-.1667em\\raisebox{-.5ex}{E}\\kern-.125emX}{TeX}}");let Kl=U(St["Main-Regular"]["T".charCodeAt(0)][1]-.7*St["Main-Regular"]["A".charCodeAt(0)][1]);k("\\LaTeX","\\textrm{\\html@mathml{"+("L\\kern-.36em\\raisebox{"+Kl+"}{\\scriptstyle A}")+"\\kern-.15em\\TeX}{LaTeX}}"),k("\\KaTeX","\\textrm{\\html@mathml{"+("K\\kern-.17em\\raisebox{"+Kl+"}{\\scriptstyle A}")+"\\kern-.15em\\TeX}{KaTeX}}"),k("\\hspace","\\@ifstar\\@hspacer\\@hspace"),k("\\@hspace","\\hskip #1\\relax"),k("\\@hspacer","\\rule{0pt}{0pt}\\hskip #1\\relax"),k("\\ordinarycolon",":"),k("\\vcentcolon","\\mathrel{\\mathop\\ordinarycolon}"),k("\\dblcolon",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-.9mu}\\vcentcolon}}{\\mathop{\\char"2237}}'),k("\\coloneqq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2254}}'),k("\\Coloneqq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2237\\char"3d}}'),k("\\coloneq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"3a\\char"2212}}'),k("\\Coloneq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"2237\\char"2212}}'),k("\\eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2255}}'),k("\\Eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"3d\\char"2237}}'),k("\\eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2239}}'),k("\\Eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"2212\\char"2237}}'),k("\\colonapprox",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"3a\\char"2248}}'),k("\\Colonapprox",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"2237\\char"2248}}'),k("\\colonsim",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"3a\\char"223c}}'),k("\\Colonsim",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"2237\\char"223c}}'),k("\u2237","\\dblcolon"),k("\u2239","\\eqcolon"),k("\u2254","\\coloneqq"),k("\u2255","\\eqqcolon"),k("\u2A74","\\Coloneqq"),k("\\ratio","\\vcentcolon"),k("\\coloncolon","\\dblcolon"),k("\\colonequals","\\coloneqq"),k("\\coloncolonequals","\\Coloneqq"),k("\\equalscolon","\\eqqcolon"),k("\\equalscoloncolon","\\Eqqcolon"),k("\\colonminus","\\coloneq"),k("\\coloncolonminus","\\Coloneq"),k("\\minuscolon","\\eqcolon"),k("\\minuscoloncolon","\\Eqcolon"),k("\\coloncolonapprox","\\Colonapprox"),k("\\coloncolonsim","\\Colonsim"),k("\\simcolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),k("\\simcoloncolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\dblcolon}"),k("\\approxcolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),k("\\approxcoloncolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\dblcolon}"),k("\\notni","\\html@mathml{\\not\\ni}{\\mathrel{\\char`\u220C}}"),k("\\limsup","\\DOTSB\\operatorname*{lim\\,sup}"),k("\\liminf","\\DOTSB\\operatorname*{lim\\,inf}"),k("\\injlim","\\DOTSB\\operatorname*{inj\\,lim}"),k("\\projlim","\\DOTSB\\operatorname*{proj\\,lim}"),k("\\varlimsup","\\DOTSB\\operatorname*{\\overline{lim}}"),k("\\varliminf","\\DOTSB\\operatorname*{\\underline{lim}}"),k("\\varinjlim","\\DOTSB\\operatorname*{\\underrightarrow{lim}}"),k("\\varprojlim","\\DOTSB\\operatorname*{\\underleftarrow{lim}}"),k("\\gvertneqq","\\html@mathml{\\@gvertneqq}{\u2269}"),k("\\lvertneqq","\\html@mathml{\\@lvertneqq}{\u2268}"),k("\\ngeqq","\\html@mathml{\\@ngeqq}{\u2271}"),k("\\ngeqslant","\\html@mathml{\\@ngeqslant}{\u2271}"),k("\\nleqq","\\html@mathml{\\@nleqq}{\u2270}"),k("\\nleqslant","\\html@mathml{\\@nleqslant}{\u2270}"),k("\\nshortmid","\\html@mathml{\\@nshortmid}{\u2224}"),k("\\nshortparallel","\\html@mathml{\\@nshortparallel}{\u2226}"),k("\\nsubseteqq","\\html@mathml{\\@nsubseteqq}{\u2288}"),k("\\nsupseteqq","\\html@mathml{\\@nsupseteqq}{\u2289}"),k("\\varsubsetneq","\\html@mathml{\\@varsubsetneq}{\u228A}"),k("\\varsubsetneqq","\\html@mathml{\\@varsubsetneqq}{\u2ACB}"),k("\\varsupsetneq","\\html@mathml{\\@varsupsetneq}{\u228B}"),k("\\varsupsetneqq","\\html@mathml{\\@varsupsetneqq}{\u2ACC}"),k("\\imath","\\html@mathml{\\@imath}{\u0131}"),k("\\jmath","\\html@mathml{\\@jmath}{\u0237}"),k("\\llbracket","\\html@mathml{\\mathopen{[\\mkern-3.2mu[}}{\\mathopen{\\char`\u27E6}}"),k("\\rrbracket","\\html@mathml{\\mathclose{]\\mkern-3.2mu]}}{\\mathclose{\\char`\u27E7}}"),k("\u27E6","\\llbracket"),k("\u27E7","\\rrbracket"),k("\\lBrace","\\html@mathml{\\mathopen{\\{\\mkern-3.2mu[}}{\\mathopen{\\char`\u2983}}"),k("\\rBrace","\\html@mathml{\\mathclose{]\\mkern-3.2mu\\}}}{\\mathclose{\\char`\u2984}}"),k("\u2983","\\lBrace"),k("\u2984","\\rBrace"),k("\\minuso","\\mathbin{\\html@mathml{{\\mathrlap{\\mathchoice{\\kern{0.145em}}{\\kern{0.145em}}{\\kern{0.1015em}}{\\kern{0.0725em}}\\circ}{-}}}{\\char`\u29B5}}"),k("\u29B5","\\minuso"),k("\\darr","\\downarrow"),k("\\dArr","\\Downarrow"),k("\\Darr","\\Downarrow"),k("\\lang","\\langle"),k("\\rang","\\rangle"),k("\\uarr","\\uparrow"),k("\\uArr","\\Uparrow"),k("\\Uarr","\\Uparrow"),k("\\N","\\mathbb{N}"),k("\\R","\\mathbb{R}"),k("\\Z","\\mathbb{Z}"),k("\\alef","\\aleph"),k("\\alefsym","\\aleph"),k("\\Alpha","\\mathrm{A}"),k("\\Beta","\\mathrm{B}"),k("\\bull","\\bullet"),k("\\Chi","\\mathrm{X}"),k("\\clubs","\\clubsuit"),k("\\cnums","\\mathbb{C}"),k("\\Complex","\\mathbb{C}"),k("\\Dagger","\\ddagger"),k("\\diamonds","\\diamondsuit"),k("\\empty","\\emptyset"),k("\\Epsilon","\\mathrm{E}"),k("\\Eta","\\mathrm{H}"),k("\\exist","\\exists"),k("\\harr","\\leftrightarrow"),k("\\hArr","\\Leftrightarrow"),k("\\Harr","\\Leftrightarrow"),k("\\hearts","\\heartsuit"),k("\\image","\\Im"),k("\\infin","\\infty"),k("\\Iota","\\mathrm{I}"),k("\\isin","\\in"),k("\\Kappa","\\mathrm{K}"),k("\\larr","\\leftarrow"),k("\\lArr","\\Leftarrow"),k("\\Larr","\\Leftarrow"),k("\\lrarr","\\leftrightarrow"),k("\\lrArr","\\Leftrightarrow"),k("\\Lrarr","\\Leftrightarrow"),k("\\Mu","\\mathrm{M}"),k("\\natnums","\\mathbb{N}"),k("\\Nu","\\mathrm{N}"),k("\\Omicron","\\mathrm{O}"),k("\\plusmn","\\pm"),k("\\rarr","\\rightarrow"),k("\\rArr","\\Rightarrow"),k("\\Rarr","\\Rightarrow"),k("\\real","\\Re"),k("\\reals","\\mathbb{R}"),k("\\Reals","\\mathbb{R}"),k("\\Rho","\\mathrm{P}"),k("\\sdot","\\cdot"),k("\\sect","\\S"),k("\\spades","\\spadesuit"),k("\\sub","\\subset"),k("\\sube","\\subseteq"),k("\\supe","\\supseteq"),k("\\Tau","\\mathrm{T}"),k("\\thetasym","\\vartheta"),k("\\weierp","\\wp"),k("\\Zeta","\\mathrm{Z}"),k("\\argmin","\\DOTSB\\operatorname*{arg\\,min}"),k("\\argmax","\\DOTSB\\operatorname*{arg\\,max}"),k("\\plim","\\DOTSB\\mathop{\\operatorname{plim}}\\limits"),k("\\bra","\\mathinner{\\langle{#1}|}"),k("\\ket","\\mathinner{|{#1}\\rangle}"),k("\\braket","\\mathinner{\\langle{#1}\\rangle}"),k("\\Bra","\\left\\langle#1\\right|"),k("\\Ket","\\left|#1\\right\\rangle");let Zl=i=>n=>{let l=n.consumeArg().tokens,a=n.consumeArg().tokens,f=n.consumeArg().tokens,d=n.consumeArg().tokens,x=n.macros.get("|"),w=n.macros.get("\\|");n.macros.beginGroup();let A=B=>F=>{i&&(F.macros.set("|",x),f.length&&F.macros.set("\\|",w));let H=B;return!B&&f.length&&F.future().text==="|"&&(F.popToken(),H=!0),{tokens:H?f:a,numArgs:0}};n.macros.set("|",A(!1)),f.length&&n.macros.set("\\|",A(!0));let C=n.consumeArg().tokens,I=n.expandTokens([...d,...C,...l]);return n.macros.endGroup(),{tokens:I.reverse(),numArgs:0}};k("\\bra@ket",Zl(!1)),k("\\bra@set",Zl(!0)),k("\\Braket","\\bra@ket{\\left\\langle}{\\,\\middle\\vert\\,}{\\,\\middle\\vert\\,}{\\right\\rangle}"),k("\\Set","\\bra@set{\\left\\{\\:}{\\;\\middle\\vert\\;}{\\;\\middle\\Vert\\;}{\\:\\right\\}}"),k("\\set","\\bra@set{\\{\\,}{\\mid}{}{\\,\\}}"),k("\\angln","{\\angl n}"),k("\\blue","\\textcolor{##6495ed}{#1}"),k("\\orange","\\textcolor{##ffa500}{#1}"),k("\\pink","\\textcolor{##ff00af}{#1}"),k("\\red","\\textcolor{##df0030}{#1}"),k("\\green","\\textcolor{##28ae7b}{#1}"),k("\\gray","\\textcolor{gray}{#1}"),k("\\purple","\\textcolor{##9d38bd}{#1}"),k("\\blueA","\\textcolor{##ccfaff}{#1}"),k("\\blueB","\\textcolor{##80f6ff}{#1}"),k("\\blueC","\\textcolor{##63d9ea}{#1}"),k("\\blueD","\\textcolor{##11accd}{#1}"),k("\\blueE","\\textcolor{##0c7f99}{#1}"),k("\\tealA","\\textcolor{##94fff5}{#1}"),k("\\tealB","\\textcolor{##26edd5}{#1}"),k("\\tealC","\\textcolor{##01d1c1}{#1}"),k("\\tealD","\\textcolor{##01a995}{#1}"),k("\\tealE","\\textcolor{##208170}{#1}"),k("\\greenA","\\textcolor{##b6ffb0}{#1}"),k("\\greenB","\\textcolor{##8af281}{#1}"),k("\\greenC","\\textcolor{##74cf70}{#1}"),k("\\greenD","\\textcolor{##1fab54}{#1}"),k("\\greenE","\\textcolor{##0d923f}{#1}"),k("\\goldA","\\textcolor{##ffd0a9}{#1}"),k("\\goldB","\\textcolor{##ffbb71}{#1}"),k("\\goldC","\\textcolor{##ff9c39}{#1}"),k("\\goldD","\\textcolor{##e07d10}{#1}"),k("\\goldE","\\textcolor{##a75a05}{#1}"),k("\\redA","\\textcolor{##fca9a9}{#1}"),k("\\redB","\\textcolor{##ff8482}{#1}"),k("\\redC","\\textcolor{##f9685d}{#1}"),k("\\redD","\\textcolor{##e84d39}{#1}"),k("\\redE","\\textcolor{##bc2612}{#1}"),k("\\maroonA","\\textcolor{##ffbde0}{#1}"),k("\\maroonB","\\textcolor{##ff92c6}{#1}"),k("\\maroonC","\\textcolor{##ed5fa6}{#1}"),k("\\maroonD","\\textcolor{##ca337c}{#1}"),k("\\maroonE","\\textcolor{##9e034e}{#1}"),k("\\purpleA","\\textcolor{##ddd7ff}{#1}"),k("\\purpleB","\\textcolor{##c6b9fc}{#1}"),k("\\purpleC","\\textcolor{##aa87ff}{#1}"),k("\\purpleD","\\textcolor{##7854ab}{#1}"),k("\\purpleE","\\textcolor{##543b78}{#1}"),k("\\mintA","\\textcolor{##f5f9e8}{#1}"),k("\\mintB","\\textcolor{##edf2df}{#1}"),k("\\mintC","\\textcolor{##e0e5cc}{#1}"),k("\\grayA","\\textcolor{##f6f7f7}{#1}"),k("\\grayB","\\textcolor{##f0f1f2}{#1}"),k("\\grayC","\\textcolor{##e3e5e6}{#1}"),k("\\grayD","\\textcolor{##d6d8da}{#1}"),k("\\grayE","\\textcolor{##babec2}{#1}"),k("\\grayF","\\textcolor{##888d93}{#1}"),k("\\grayG","\\textcolor{##626569}{#1}"),k("\\grayH","\\textcolor{##3b3e40}{#1}"),k("\\grayI","\\textcolor{##21242c}{#1}"),k("\\kaBlue","\\textcolor{##314453}{#1}"),k("\\kaGreen","\\textcolor{##71B307}{#1}");let Jl={"^":!0,_:!0,"\\limits":!0,"\\nolimits":!0};class A1{constructor(n,l,a){this.settings=void 0,this.expansionCount=void 0,this.lexer=void 0,this.macros=void 0,this.stack=void 0,this.mode=void 0,this.settings=l,this.expansionCount=0,this.feed(n),this.macros=new k1(S1,l.macros),this.mode=a,this.stack=[]}feed(n){this.lexer=new Xl(n,this.settings)}switchMode(n){this.mode=n}beginGroup(){this.macros.beginGroup()}endGroup(){this.macros.endGroup()}endGroups(){this.macros.endGroups()}future(){return this.stack.length===0&&this.pushToken(this.lexer.lex()),this.stack[this.stack.length-1]}popToken(){return this.future(),this.stack.pop()}pushToken(n){this.stack.push(n)}pushTokens(n){this.stack.push(...n)}scanArgument(n){let l,a,f;if(n){if(this.consumeSpaces(),this.future().text!=="[")return null;l=this.popToken(),{tokens:f,end:a}=this.consumeArg(["]"])}else({tokens:f,start:l,end:a}=this.consumeArg());return this.pushToken(new pt("EOF",a.loc)),this.pushTokens(f),l.range(a,"")}consumeSpaces(){for(;this.future().text===" ";)this.stack.pop()}consumeArg(n){let l=[],a=n&&n.length>0;a||this.consumeSpaces();let f=this.future(),d,x=0,w=0;do{if(d=this.popToken(),l.push(d),d.text==="{")++x;else if(d.text==="}"){if(--x,x===-1)throw new o("Extra }",d)}else if(d.text==="EOF")throw new o("Unexpected end of input in a macro argument, expected '"+(n&&a?n[w]:"}")+"'",d);if(n&&a)if((x===0||x===1&&n[w]==="{")&&d.text===n[w]){if(++w,w===n.length){l.splice(-w,w);break}}else w=0}while(x!==0||a);return f.text==="{"&&l[l.length-1].text==="}"&&(l.pop(),l.shift()),l.reverse(),{tokens:l,start:f,end:d}}consumeArgs(n,l){if(l){if(l.length!==n+1)throw new o("The length of delimiters doesn't match the number of args!");let f=l[0];for(let d=0;d<f.length;d++){let x=this.popToken();if(f[d]!==x.text)throw new o("Use of the macro doesn't match its definition",x)}}let a=[];for(let f=0;f<n;f++)a.push(this.consumeArg(l&&l[f+1]).tokens);return a}countExpansion(n){if(this.expansionCount+=n,this.expansionCount>this.settings.maxExpand)throw new o("Too many expansions: infinite loop or need to increase maxExpand setting")}expandOnce(n){let l=this.popToken(),a=l.text,f=l.noexpand?null:this._getExpansion(a);if(f==null||n&&f.unexpandable){if(n&&f==null&&a[0]==="\\"&&!this.isDefined(a))throw new o("Undefined control sequence: "+a);return this.pushToken(l),!1}this.countExpansion(1);let d=f.tokens,x=this.consumeArgs(f.numArgs,f.delimiters);if(f.numArgs){d=d.slice();for(let w=d.length-1;w>=0;--w){let A=d[w];if(A.text==="#"){if(w===0)throw new o("Incomplete placeholder at end of macro body",A);if(A=d[--w],A.text==="#")d.splice(w+1,1);else if(/^[1-9]$/.test(A.text))d.splice(w,2,...x[+A.text-1]);else throw new o("Not a valid argument number",A)}}}return this.pushTokens(d),d.length}expandAfterFuture(){return this.expandOnce(),this.future()}expandNextToken(){for(;;)if(this.expandOnce()===!1){let n=this.stack.pop();return n.treatAsRelax&&(n.text="\\relax"),n}throw new Error}expandMacro(n){return this.macros.has(n)?this.expandTokens([new pt(n)]):void 0}expandTokens(n){let l=[],a=this.stack.length;for(this.pushTokens(n);this.stack.length>a;)if(this.expandOnce(!0)===!1){let f=this.stack.pop();f.treatAsRelax&&(f.noexpand=!1,f.treatAsRelax=!1),l.push(f)}return this.countExpansion(l.length),l}expandMacroAsText(n){let l=this.expandMacro(n);return l&&l.map(a=>a.text).join("")}_getExpansion(n){let l=this.macros.get(n);if(l==null)return l;if(n.length===1){let f=this.lexer.catcodes[n];if(f!=null&&f!==13)return}let a=typeof l=="function"?l(this):l;if(typeof a=="string"){let f=0;if(a.indexOf("#")!==-1){let C=a.replace(/##/g,"");for(;C.indexOf("#"+(f+1))!==-1;)++f}let d=new Xl(a,this.settings),x=[],w=d.lex();for(;w.text!=="EOF";)x.push(w),w=d.lex();return x.reverse(),{tokens:x,numArgs:f}}return a}isDefined(n){return this.macros.has(n)||Xt.hasOwnProperty(n)||Te.math.hasOwnProperty(n)||Te.text.hasOwnProperty(n)||Jl.hasOwnProperty(n)}isExpandable(n){let l=this.macros.get(n);return l!=null?typeof l=="string"||typeof l=="function"||!l.unexpandable:Xt.hasOwnProperty(n)&&!Xt[n].primitive}}let Ql=/^[₊₋₌₍₎₀₁₂₃₄₅₆₇₈₉ₐₑₕᵢⱼₖₗₘₙₒₚᵣₛₜᵤᵥₓᵦᵧᵨᵩᵪ]/,On=Object.freeze({"\u208A":"+","\u208B":"-","\u208C":"=","\u208D":"(","\u208E":")","\u2080":"0","\u2081":"1","\u2082":"2","\u2083":"3","\u2084":"4","\u2085":"5","\u2086":"6","\u2087":"7","\u2088":"8","\u2089":"9","\u2090":"a","\u2091":"e","\u2095":"h","\u1D62":"i","\u2C7C":"j","\u2096":"k","\u2097":"l","\u2098":"m","\u2099":"n","\u2092":"o","\u209A":"p","\u1D63":"r","\u209B":"s","\u209C":"t","\u1D64":"u","\u1D65":"v","\u2093":"x","\u1D66":"\u03B2","\u1D67":"\u03B3","\u1D68":"\u03C1","\u1D69":"\u03D5","\u1D6A":"\u03C7","\u207A":"+","\u207B":"-","\u207C":"=","\u207D":"(","\u207E":")","\u2070":"0","\xB9":"1","\xB2":"2","\xB3":"3","\u2074":"4","\u2075":"5","\u2076":"6","\u2077":"7","\u2078":"8","\u2079":"9","\u1D2C":"A","\u1D2E":"B","\u1D30":"D","\u1D31":"E","\u1D33":"G","\u1D34":"H","\u1D35":"I","\u1D36":"J","\u1D37":"K","\u1D38":"L","\u1D39":"M","\u1D3A":"N","\u1D3C":"O","\u1D3E":"P","\u1D3F":"R","\u1D40":"T","\u1D41":"U","\u2C7D":"V","\u1D42":"W","\u1D43":"a","\u1D47":"b","\u1D9C":"c","\u1D48":"d","\u1D49":"e","\u1DA0":"f","\u1D4D":"g",\u02B0:"h","\u2071":"i",\u02B2:"j","\u1D4F":"k",\u02E1:"l","\u1D50":"m",\u207F:"n","\u1D52":"o","\u1D56":"p",\u02B3:"r",\u02E2:"s","\u1D57":"t","\u1D58":"u","\u1D5B":"v",\u02B7:"w",\u02E3:"x",\u02B8:"y","\u1DBB":"z","\u1D5D":"\u03B2","\u1D5E":"\u03B3","\u1D5F":"\u03B4","\u1D60":"\u03D5","\u1D61":"\u03C7","\u1DBF":"\u03B8"}),gi={"\u0301":{text:"\\'",math:"\\acute"},"\u0300":{text:"\\`",math:"\\grave"},"\u0308":{text:'\\"',math:"\\ddot"},"\u0303":{text:"\\~",math:"\\tilde"},"\u0304":{text:"\\=",math:"\\bar"},"\u0306":{text:"\\u",math:"\\breve"},"\u030C":{text:"\\v",math:"\\check"},"\u0302":{text:"\\^",math:"\\hat"},"\u0307":{text:"\\.",math:"\\dot"},"\u030A":{text:"\\r",math:"\\mathring"},"\u030B":{text:"\\H"},"\u0327":{text:"\\c"}},es={\u00E1:"a\u0301",\u00E0:"a\u0300",\u00E4:"a\u0308",\u01DF:"a\u0308\u0304",\u00E3:"a\u0303",\u0101:"a\u0304",\u0103:"a\u0306",\u1EAF:"a\u0306\u0301",\u1EB1:"a\u0306\u0300",\u1EB5:"a\u0306\u0303",\u01CE:"a\u030C",\u00E2:"a\u0302",\u1EA5:"a\u0302\u0301",\u1EA7:"a\u0302\u0300",\u1EAB:"a\u0302\u0303",\u0227:"a\u0307",\u01E1:"a\u0307\u0304",\u00E5:"a\u030A",\u01FB:"a\u030A\u0301",\u1E03:"b\u0307",\u0107:"c\u0301",\u1E09:"c\u0327\u0301",\u010D:"c\u030C",\u0109:"c\u0302",\u010B:"c\u0307",\u00E7:"c\u0327",\u010F:"d\u030C",\u1E0B:"d\u0307",\u1E11:"d\u0327",\u00E9:"e\u0301",\u00E8:"e\u0300",\u00EB:"e\u0308",\u1EBD:"e\u0303",\u0113:"e\u0304",\u1E17:"e\u0304\u0301",\u1E15:"e\u0304\u0300",\u0115:"e\u0306",\u1E1D:"e\u0327\u0306",\u011B:"e\u030C",\u00EA:"e\u0302",\u1EBF:"e\u0302\u0301",\u1EC1:"e\u0302\u0300",\u1EC5:"e\u0302\u0303",\u0117:"e\u0307",\u0229:"e\u0327",\u1E1F:"f\u0307",\u01F5:"g\u0301",\u1E21:"g\u0304",\u011F:"g\u0306",\u01E7:"g\u030C",\u011D:"g\u0302",\u0121:"g\u0307",\u0123:"g\u0327",\u1E27:"h\u0308",\u021F:"h\u030C",\u0125:"h\u0302",\u1E23:"h\u0307",\u1E29:"h\u0327",\u00ED:"i\u0301",\u00EC:"i\u0300",\u00EF:"i\u0308",\u1E2F:"i\u0308\u0301",\u0129:"i\u0303",\u012B:"i\u0304",\u012D:"i\u0306",\u01D0:"i\u030C",\u00EE:"i\u0302",\u01F0:"j\u030C",\u0135:"j\u0302",\u1E31:"k\u0301",\u01E9:"k\u030C",\u0137:"k\u0327",\u013A:"l\u0301",\u013E:"l\u030C",\u013C:"l\u0327",\u1E3F:"m\u0301",\u1E41:"m\u0307",\u0144:"n\u0301",\u01F9:"n\u0300",\u00F1:"n\u0303",\u0148:"n\u030C",\u1E45:"n\u0307",\u0146:"n\u0327",\u00F3:"o\u0301",\u00F2:"o\u0300",\u00F6:"o\u0308",\u022B:"o\u0308\u0304",\u00F5:"o\u0303",\u1E4D:"o\u0303\u0301",\u1E4F:"o\u0303\u0308",\u022D:"o\u0303\u0304",\u014D:"o\u0304",\u1E53:"o\u0304\u0301",\u1E51:"o\u0304\u0300",\u014F:"o\u0306",\u01D2:"o\u030C",\u00F4:"o\u0302",\u1ED1:"o\u0302\u0301",\u1ED3:"o\u0302\u0300",\u1ED7:"o\u0302\u0303",\u022F:"o\u0307",\u0231:"o\u0307\u0304",\u0151:"o\u030B",\u1E55:"p\u0301",\u1E57:"p\u0307",\u0155:"r\u0301",\u0159:"r\u030C",\u1E59:"r\u0307",\u0157:"r\u0327",\u015B:"s\u0301",\u1E65:"s\u0301\u0307",\u0161:"s\u030C",\u1E67:"s\u030C\u0307",\u015D:"s\u0302",\u1E61:"s\u0307",\u015F:"s\u0327",\u1E97:"t\u0308",\u0165:"t\u030C",\u1E6B:"t\u0307",\u0163:"t\u0327",\u00FA:"u\u0301",\u00F9:"u\u0300",\u00FC:"u\u0308",\u01D8:"u\u0308\u0301",\u01DC:"u\u0308\u0300",\u01D6:"u\u0308\u0304",\u01DA:"u\u0308\u030C",\u0169:"u\u0303",\u1E79:"u\u0303\u0301",\u016B:"u\u0304",\u1E7B:"u\u0304\u0308",\u016D:"u\u0306",\u01D4:"u\u030C",\u00FB:"u\u0302",\u016F:"u\u030A",\u0171:"u\u030B",\u1E7D:"v\u0303",\u1E83:"w\u0301",\u1E81:"w\u0300",\u1E85:"w\u0308",\u0175:"w\u0302",\u1E87:"w\u0307",\u1E98:"w\u030A",\u1E8D:"x\u0308",\u1E8B:"x\u0307",\u00FD:"y\u0301",\u1EF3:"y\u0300",\u00FF:"y\u0308",\u1EF9:"y\u0303",\u0233:"y\u0304",\u0177:"y\u0302",\u1E8F:"y\u0307",\u1E99:"y\u030A",\u017A:"z\u0301",\u017E:"z\u030C",\u1E91:"z\u0302",\u017C:"z\u0307",\u00C1:"A\u0301",\u00C0:"A\u0300",\u00C4:"A\u0308",\u01DE:"A\u0308\u0304",\u00C3:"A\u0303",\u0100:"A\u0304",\u0102:"A\u0306",\u1EAE:"A\u0306\u0301",\u1EB0:"A\u0306\u0300",\u1EB4:"A\u0306\u0303",\u01CD:"A\u030C",\u00C2:"A\u0302",\u1EA4:"A\u0302\u0301",\u1EA6:"A\u0302\u0300",\u1EAA:"A\u0302\u0303",\u0226:"A\u0307",\u01E0:"A\u0307\u0304",\u00C5:"A\u030A",\u01FA:"A\u030A\u0301",\u1E02:"B\u0307",\u0106:"C\u0301",\u1E08:"C\u0327\u0301",\u010C:"C\u030C",\u0108:"C\u0302",\u010A:"C\u0307",\u00C7:"C\u0327",\u010E:"D\u030C",\u1E0A:"D\u0307",\u1E10:"D\u0327",\u00C9:"E\u0301",\u00C8:"E\u0300",\u00CB:"E\u0308",\u1EBC:"E\u0303",\u0112:"E\u0304",\u1E16:"E\u0304\u0301",\u1E14:"E\u0304\u0300",\u0114:"E\u0306",\u1E1C:"E\u0327\u0306",\u011A:"E\u030C",\u00CA:"E\u0302",\u1EBE:"E\u0302\u0301",\u1EC0:"E\u0302\u0300",\u1EC4:"E\u0302\u0303",\u0116:"E\u0307",\u0228:"E\u0327",\u1E1E:"F\u0307",\u01F4:"G\u0301",\u1E20:"G\u0304",\u011E:"G\u0306",\u01E6:"G\u030C",\u011C:"G\u0302",\u0120:"G\u0307",\u0122:"G\u0327",\u1E26:"H\u0308",\u021E:"H\u030C",\u0124:"H\u0302",\u1E22:"H\u0307",\u1E28:"H\u0327",\u00CD:"I\u0301",\u00CC:"I\u0300",\u00CF:"I\u0308",\u1E2E:"I\u0308\u0301",\u0128:"I\u0303",\u012A:"I\u0304",\u012C:"I\u0306",\u01CF:"I\u030C",\u00CE:"I\u0302",\u0130:"I\u0307",\u0134:"J\u0302",\u1E30:"K\u0301",\u01E8:"K\u030C",\u0136:"K\u0327",\u0139:"L\u0301",\u013D:"L\u030C",\u013B:"L\u0327",\u1E3E:"M\u0301",\u1E40:"M\u0307",\u0143:"N\u0301",\u01F8:"N\u0300",\u00D1:"N\u0303",\u0147:"N\u030C",\u1E44:"N\u0307",\u0145:"N\u0327",\u00D3:"O\u0301",\u00D2:"O\u0300",\u00D6:"O\u0308",\u022A:"O\u0308\u0304",\u00D5:"O\u0303",\u1E4C:"O\u0303\u0301",\u1E4E:"O\u0303\u0308",\u022C:"O\u0303\u0304",\u014C:"O\u0304",\u1E52:"O\u0304\u0301",\u1E50:"O\u0304\u0300",\u014E:"O\u0306",\u01D1:"O\u030C",\u00D4:"O\u0302",\u1ED0:"O\u0302\u0301",\u1ED2:"O\u0302\u0300",\u1ED6:"O\u0302\u0303",\u022E:"O\u0307",\u0230:"O\u0307\u0304",\u0150:"O\u030B",\u1E54:"P\u0301",\u1E56:"P\u0307",\u0154:"R\u0301",\u0158:"R\u030C",\u1E58:"R\u0307",\u0156:"R\u0327",\u015A:"S\u0301",\u1E64:"S\u0301\u0307",\u0160:"S\u030C",\u1E66:"S\u030C\u0307",\u015C:"S\u0302",\u1E60:"S\u0307",\u015E:"S\u0327",\u0164:"T\u030C",\u1E6A:"T\u0307",\u0162:"T\u0327",\u00DA:"U\u0301",\u00D9:"U\u0300",\u00DC:"U\u0308",\u01D7:"U\u0308\u0301",\u01DB:"U\u0308\u0300",\u01D5:"U\u0308\u0304",\u01D9:"U\u0308\u030C",\u0168:"U\u0303",\u1E78:"U\u0303\u0301",\u016A:"U\u0304",\u1E7A:"U\u0304\u0308",\u016C:"U\u0306",\u01D3:"U\u030C",\u00DB:"U\u0302",\u016E:"U\u030A",\u0170:"U\u030B",\u1E7C:"V\u0303",\u1E82:"W\u0301",\u1E80:"W\u0300",\u1E84:"W\u0308",\u0174:"W\u0302",\u1E86:"W\u0307",\u1E8C:"X\u0308",\u1E8A:"X\u0307",\u00DD:"Y\u0301",\u1EF2:"Y\u0300",\u0178:"Y\u0308",\u1EF8:"Y\u0303",\u0232:"Y\u0304",\u0176:"Y\u0302",\u1E8E:"Y\u0307",\u0179:"Z\u0301",\u017D:"Z\u030C",\u1E90:"Z\u0302",\u017B:"Z\u0307",\u03AC:"\u03B1\u0301",\u1F70:"\u03B1\u0300",\u1FB1:"\u03B1\u0304",\u1FB0:"\u03B1\u0306",\u03AD:"\u03B5\u0301",\u1F72:"\u03B5\u0300",\u03AE:"\u03B7\u0301",\u1F74:"\u03B7\u0300",\u03AF:"\u03B9\u0301",\u1F76:"\u03B9\u0300",\u03CA:"\u03B9\u0308",\u0390:"\u03B9\u0308\u0301",\u1FD2:"\u03B9\u0308\u0300",\u1FD1:"\u03B9\u0304",\u1FD0:"\u03B9\u0306",\u03CC:"\u03BF\u0301",\u1F78:"\u03BF\u0300",\u03CD:"\u03C5\u0301",\u1F7A:"\u03C5\u0300",\u03CB:"\u03C5\u0308",\u03B0:"\u03C5\u0308\u0301",\u1FE2:"\u03C5\u0308\u0300",\u1FE1:"\u03C5\u0304",\u1FE0:"\u03C5\u0306",\u03CE:"\u03C9\u0301",\u1F7C:"\u03C9\u0300",\u038E:"\u03A5\u0301",\u1FEA:"\u03A5\u0300",\u03AB:"\u03A5\u0308",\u1FE9:"\u03A5\u0304",\u1FE8:"\u03A5\u0306",\u038F:"\u03A9\u0301",\u1FFA:"\u03A9\u0300"};class Rn{constructor(n,l){this.mode=void 0,this.gullet=void 0,this.settings=void 0,this.leftrightDepth=void 0,this.nextToken=void 0,this.mode="math",this.gullet=new A1(n,l,this.mode),this.settings=l,this.leftrightDepth=0}expect(n,l){if(l===void 0&&(l=!0),this.fetch().text!==n)throw new o("Expected '"+n+"', got '"+this.fetch().text+"'",this.fetch());l&&this.consume()}consume(){this.nextToken=null}fetch(){return this.nextToken==null&&(this.nextToken=this.gullet.expandNextToken()),this.nextToken}switchMode(n){this.mode=n,this.gullet.switchMode(n)}parse(){this.settings.globalGroup||this.gullet.beginGroup(),this.settings.colorIsTextColor&&this.gullet.macros.set("\\color","\\textcolor");try{let n=this.parseExpression(!1);return this.expect("EOF"),this.settings.globalGroup||this.gullet.endGroup(),n}finally{this.gullet.endGroups()}}subparse(n){let l=this.nextToken;this.consume(),this.gullet.pushToken(new pt("}")),this.gullet.pushTokens(n);let a=this.parseExpression(!1);return this.expect("}"),this.nextToken=l,a}parseExpression(n,l){let a=[];for(;;){this.mode==="math"&&this.consumeSpaces();let f=this.fetch();if(Rn.endOfExpression.indexOf(f.text)!==-1||l&&f.text===l||n&&Xt[f.text]&&Xt[f.text].infix)break;let d=this.parseAtom(l);if(d){if(d.type==="internal")continue}else break;a.push(d)}return this.mode==="text"&&this.formLigatures(a),this.handleInfixNodes(a)}handleInfixNodes(n){let l=-1,a;for(let f=0;f<n.length;f++)if(n[f].type==="infix"){if(l!==-1)throw new o("only one infix operator per group",n[f].token);l=f,a=n[f].replaceWith}if(l!==-1&&a){let f,d,x=n.slice(0,l),w=n.slice(l+1);x.length===1&&x[0].type==="ordgroup"?f=x[0]:f={type:"ordgroup",mode:this.mode,body:x},w.length===1&&w[0].type==="ordgroup"?d=w[0]:d={type:"ordgroup",mode:this.mode,body:w};let A;return a==="\\\\abovefrac"?A=this.callFunction(a,[f,n[l],d],[]):A=this.callFunction(a,[f,d],[]),[A]}else return n}handleSupSubscript(n){let l=this.fetch(),a=l.text;this.consume(),this.consumeSpaces();let f;do{var d;f=this.parseGroup(n)}while(((d=f)==null?void 0:d.type)==="internal");if(!f)throw new o("Expected group after '"+a+"'",l);return f}formatUnsupportedCmd(n){let l=[];for(let d=0;d<n.length;d++)l.push({type:"textord",mode:"text",text:n[d]});let a={type:"text",mode:this.mode,body:l};return{type:"color",mode:this.mode,color:this.settings.errorColor,body:[a]}}parseAtom(n){let l=this.parseGroup("atom",n);if((l==null?void 0:l.type)==="internal"||this.mode==="text")return l;let a,f;for(;;){this.consumeSpaces();let d=this.fetch();if(d.text==="\\limits"||d.text==="\\nolimits"){if(l&&l.type==="op"){let x=d.text==="\\limits";l.limits=x,l.alwaysHandleSupSub=!0}else if(l&&l.type==="operatorname")l.alwaysHandleSupSub&&(l.limits=d.text==="\\limits");else throw new o("Limit controls must follow a math operator",d);this.consume()}else if(d.text==="^"){if(a)throw new o("Double superscript",d);a=this.handleSupSubscript("superscript")}else if(d.text==="_"){if(f)throw new o("Double subscript",d);f=this.handleSupSubscript("subscript")}else if(d.text==="'"){if(a)throw new o("Double superscript",d);let x={type:"textord",mode:this.mode,text:"\\prime"},w=[x];for(this.consume();this.fetch().text==="'";)w.push(x),this.consume();this.fetch().text==="^"&&w.push(this.handleSupSubscript("superscript")),a={type:"ordgroup",mode:this.mode,body:w}}else if(On[d.text]){let x=Ql.test(d.text),w=[];for(w.push(new pt(On[d.text])),this.consume();;){let C=this.fetch().text;if(!On[C]||Ql.test(C)!==x)break;w.unshift(new pt(On[C])),this.consume()}let A=this.subparse(w);x?f={type:"ordgroup",mode:"math",body:A}:a={type:"ordgroup",mode:"math",body:A}}else break}return a||f?{type:"supsub",mode:this.mode,base:l,sup:a,sub:f}:l}parseFunction(n,l){let a=this.fetch(),f=a.text,d=Xt[f];if(!d)return null;if(this.consume(),l&&l!=="atom"&&!d.allowedInArgument)throw new o("Got function '"+f+"' with no arguments"+(l?" as "+l:""),a);if(this.mode==="text"&&!d.allowedInText)throw new o("Can't use function '"+f+"' in text mode",a);if(this.mode==="math"&&d.allowedInMath===!1)throw new o("Can't use function '"+f+"' in math mode",a);let{args:x,optArgs:w}=this.parseArguments(f,d);return this.callFunction(f,x,w,a,n)}callFunction(n,l,a,f,d){let x={funcName:n,parser:this,token:f,breakOnTokenText:d},w=Xt[n];if(w&&w.handler)return w.handler(x,l,a);throw new o("No function handler for "+n)}parseArguments(n,l){let a=l.numArgs+l.numOptionalArgs;if(a===0)return{args:[],optArgs:[]};let f=[],d=[];for(let x=0;x<a;x++){let w=l.argTypes&&l.argTypes[x],A=x<l.numOptionalArgs;(l.primitive&&w==null||l.type==="sqrt"&&x===1&&d[0]==null)&&(w="primitive");let C=this.parseGroupOfType("argument to '"+n+"'",w,A);if(A)d.push(C);else if(C!=null)f.push(C);else throw new o("Null argument, please report this as a bug")}return{args:f,optArgs:d}}parseGroupOfType(n,l,a){switch(l){case"color":return this.parseColorGroup(a);case"size":return this.parseSizeGroup(a);case"url":return this.parseUrlGroup(a);case"math":case"text":return this.parseArgumentGroup(a,l);case"hbox":{let f=this.parseArgumentGroup(a,"text");return f!=null?{type:"styling",mode:f.mode,body:[f],style:"text"}:null}case"raw":{let f=this.parseStringGroup("raw",a);return f!=null?{type:"raw",mode:"text",string:f.text}:null}case"primitive":{if(a)throw new o("A primitive argument cannot be optional");let f=this.parseGroup(n);if(f==null)throw new o("Expected group as "+n,this.fetch());return f}case"original":case null:case void 0:return this.parseArgumentGroup(a);default:throw new o("Unknown group type as "+n,this.fetch())}}consumeSpaces(){for(;this.fetch().text===" ";)this.consume()}parseStringGroup(n,l){let a=this.gullet.scanArgument(l);if(a==null)return null;let f="",d;for(;(d=this.fetch()).text!=="EOF";)f+=d.text,this.consume();return this.consume(),a.text=f,a}parseRegexGroup(n,l){let a=this.fetch(),f=a,d="",x;for(;(x=this.fetch()).text!=="EOF"&&n.test(d+x.text);)f=x,d+=f.text,this.consume();if(d==="")throw new o("Invalid "+l+": '"+a.text+"'",a);return a.range(f,d)}parseColorGroup(n){let l=this.parseStringGroup("color",n);if(l==null)return null;let a=/^(#[a-f0-9]{3}|#?[a-f0-9]{6}|[a-z]+)$/i.exec(l.text);if(!a)throw new o("Invalid color: '"+l.text+"'",l);let f=a[0];return/^[0-9a-f]{6}$/i.test(f)&&(f="#"+f),{type:"color-token",mode:this.mode,color:f}}parseSizeGroup(n){let l,a=!1;if(this.gullet.consumeSpaces(),!n&&this.gullet.future().text!=="{"?l=this.parseRegexGroup(/^[-+]? *(?:$|\d+|\d+\.\d*|\.\d*) *[a-z]{0,2} *$/,"size"):l=this.parseStringGroup("size",n),!l)return null;!n&&l.text.length===0&&(l.text="0pt",a=!0);let f=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(l.text);if(!f)throw new o("Invalid size: '"+l.text+"'",l);let d={number:+(f[1]+f[2]),unit:f[3]};if(!Fo(d))throw new o("Invalid unit: '"+d.unit+"'",l);return{type:"size",mode:this.mode,value:d,isBlank:a}}parseUrlGroup(n){this.gullet.lexer.setCatcode("%",13),this.gullet.lexer.setCatcode("~",12);let l=this.parseStringGroup("url",n);if(this.gullet.lexer.setCatcode("%",14),this.gullet.lexer.setCatcode("~",13),l==null)return null;let a=l.text.replace(/\\([#$%&~_^{}])/g,"$1");return{type:"url",mode:this.mode,url:a}}parseArgumentGroup(n,l){let a=this.gullet.scanArgument(n);if(a==null)return null;let f=this.mode;l&&this.switchMode(l),this.gullet.beginGroup();let d=this.parseExpression(!1,"EOF");this.expect("EOF"),this.gullet.endGroup();let x={type:"ordgroup",mode:this.mode,loc:a.loc,body:d};return l&&this.switchMode(f),x}parseGroup(n,l){let a=this.fetch(),f=a.text,d;if(f==="{"||f==="\\begingroup"){this.consume();let x=f==="{"?"}":"\\endgroup";this.gullet.beginGroup();let w=this.parseExpression(!1,x),A=this.fetch();this.expect(x),this.gullet.endGroup(),d={type:"ordgroup",mode:this.mode,loc:ut.range(a,A),body:w,semisimple:f==="\\begingroup"||void 0}}else if(d=this.parseFunction(l,n)||this.parseSymbol(),d==null&&f[0]==="\\"&&!Jl.hasOwnProperty(f)){if(this.settings.throwOnError)throw new o("Undefined control sequence: "+f,a);d=this.formatUnsupportedCmd(f),this.consume()}return d}formLigatures(n){let l=n.length-1;for(let a=0;a<l;++a){let f=n[a],d=f.text;d==="-"&&n[a+1].text==="-"&&(a+1<l&&n[a+2].text==="-"?(n.splice(a,3,{type:"textord",mode:"text",loc:ut.range(f,n[a+2]),text:"---"}),l-=2):(n.splice(a,2,{type:"textord",mode:"text",loc:ut.range(f,n[a+1]),text:"--"}),l-=1)),(d==="'"||d==="`")&&n[a+1].text===d&&(n.splice(a,2,{type:"textord",mode:"text",loc:ut.range(f,n[a+1]),text:d+d}),l-=1)}}parseSymbol(){let n=this.fetch(),l=n.text;if(/^\\verb[^a-zA-Z]/.test(l)){this.consume();let d=l.slice(5),x=d.charAt(0)==="*";if(x&&(d=d.slice(1)),d.length<2||d.charAt(0)!==d.slice(-1))throw new o(`\\verb assertion failed --
                    please report what input caused this bug`);return d=d.slice(1,-1),{type:"verb",mode:"text",body:d,star:x}}es.hasOwnProperty(l[0])&&!Te[this.mode][l[0]]&&(this.settings.strict&&this.mode==="math"&&this.settings.reportNonstrict("unicodeTextInMathMode",'Accented Unicode text character "'+l[0]+'" used in math mode',n),l=es[l[0]]+l.slice(1));let a=v1.exec(l);a&&(l=l.substring(0,a.index),l==="i"?l="\u0131":l==="j"&&(l="\u0237"));let f;if(Te[this.mode][l]){this.settings.strict&&this.mode==="math"&&q0.indexOf(l)>=0&&this.settings.reportNonstrict("unicodeTextInMathMode",'Latin-1/Unicode text character "'+l[0]+'" used in math mode',n);let d=Te[this.mode][l].group,x=ut.range(n),w;if(vh.hasOwnProperty(d)){let A=d;w={type:"atom",mode:this.mode,family:A,loc:x,text:l}}else w={type:d,mode:this.mode,loc:x,text:l};f=w}else if(l.charCodeAt(0)>=128)this.settings.strict&&(it(l.charCodeAt(0))?this.mode==="math"&&this.settings.reportNonstrict("unicodeTextInMathMode",'Unicode text character "'+l[0]+'" used in math mode',n):this.settings.reportNonstrict("unknownSymbol",'Unrecognized Unicode character "'+l[0]+'"'+(" ("+l.charCodeAt(0)+")"),n)),f={type:"textord",mode:"text",loc:ut.range(n),text:l};else return null;if(this.consume(),a)for(let d=0;d<a[0].length;d++){let x=a[0][d];if(!gi[x])throw new o("Unknown accent ' "+x+"'",n);let w=gi[x][this.mode]||gi[x].text;if(!w)throw new o("Accent "+x+" unsupported in "+this.mode+" mode",n);f={type:"accent",mode:this.mode,loc:ut.range(n),label:w,isStretchy:!1,isShifty:!0,base:f}}return f}}Rn.endOfExpression=["}","\\endgroup","\\end","\\right","&"];var xi=function(i,n){if(!(typeof i=="string"||i instanceof String))throw new TypeError("KaTeX can only parse string typed expression");let l=new Rn(i,n);delete l.gullet.macros.current["\\df@tag"];let a=l.parse();if(delete l.gullet.macros.current["\\current@color"],delete l.gullet.macros.current["\\color"],l.gullet.macros.get("\\df@tag")){if(!n.displayMode)throw new o("\\tag works only in display equations");a=[{type:"tag",mode:"text",body:a,tag:l.subparse([new pt("\\df@tag")])}]}return a};let ts=function(i,n,l){n.textContent="";let a=yi(i,l).toNode();n.appendChild(a)};typeof document!="undefined"&&document.compatMode!=="CSS1Compat"&&(typeof console!="undefined"&&console.warn("Warning: KaTeX doesn't work in quirks mode. Make sure your website has a suitable doctype."),ts=function(){throw new o("KaTeX doesn't work in quirks mode.")});let _1=function(i,n){return yi(i,n).toMarkup()},M1=function(i,n){let l=new X(n);return xi(i,l)},rs=function(i,n,l){if(l.throwOnError||!(i instanceof o))throw i;let a=O.makeSpan(["katex-error"],[new ht(n)]);return a.setAttribute("title",i.toString()),a.setAttribute("style","color:"+l.errorColor),a},yi=function(i,n){let l=new X(n);try{let a=xi(i,l);return Hh(a,i,l)}catch(a){return rs(a,i,l)}};var C1={version:"0.16.22",render:ts,renderToString:_1,ParseError:o,SETTINGS_SCHEMA:P,__parse:M1,__renderToDomTree:yi,__renderToHTMLTree:function(i,n){let l=new X(n);try{let a=xi(i,l);return $h(a,i,l)}catch(a){return rs(a,i,l)}},__setFontMetrics:hh,__defineSymbol:p,__defineFunction:j,__defineMacro:k,__domTree:{Span:Rr,Anchor:F0,SymbolNode:ht,SvgNode:zt,PathNode:Gt,LineNode:L0}},T1=C1;return t=t.default,t}()})});var Ps=os((A6,qs)=>{"use strict";var Xp=(e,t)=>{let r="\\",o="$",u=(t||{}).delimiter||o;if(u.length!==1)throw new Error("invalid delimiter");let c=Ls(),h=(y,S)=>c.renderToString(y,{displayMode:S,throwOnError:!1}),g=(y,S,T)=>{let N=!1,R=y.bMarks[S]+y.tShift[S],z=y.eMarks[S];if(R+1>z)return!1;let P=y.src.charAt(R);if(P!==u)return!1;let L=R;R=y.skipChars(R,P);let X=R-L;if(X!==2)return!1;let ne=S;for(;++ne,!(ne>=T||(R=L=y.bMarks[ne]+y.tShift[ne],z=y.eMarks[ne],R<z&&y.tShift[ne]<y.blkIndent));)if(y.src.charAt(R)===u&&!(y.tShift[ne]-y.blkIndent>=4)&&(R=y.skipChars(R,P),!(R-L<X)&&(R=y.skipSpaces(R),!(R<z)))){N=!0;break}X=y.tShift[S],y.line=ne+(N?1:0);let ue=y.getLines(S+1,ne,X,!0).replace(/[ \n]+/g," ").trim();return y.tokens.push({type:"katex",params:null,content:ue,lines:[S,y.line],level:y.level,block:!0}),!0},b=(y,S)=>{let T=y.pos,N=y.posMax,R=T;if(y.src.charAt(R)!==u)return!1;for(++R;R<N&&y.src.charAt(R)===u;)++R;let z=y.src.slice(T,R);if(z.length>2)return!1;let P=R,L=0;for(;R<N;){let X=y.src.charAt(R);if(X==="{"&&(R==0||y.src.charAt(R-1)!=r))L+=1;else if(X==="}"&&(R==0||y.src.charAt(R-1)!=r)){if(L-=1,L<0)return!1}else if(X===u&&L===0){let ne=R,ue=R+1;for(;ue<N&&y.src.charAt(ue)===u;)++ue;if(ue-ne===z.length){if(!S){let me=y.src.slice(P,ne).replace(/[ \n]+/g," ").trim();y.push({type:"katex",content:me,block:z.length>1,level:y.level})}return y.pos=ue,!0}}R+=1}return S||(y.pending+=z),y.pos+=z.length,!0};e.inline.ruler.push("katex",b,t),e.block.ruler.push("katex",g,t),e.renderer.rules.katex=(y,S)=>h(y[S].content,y[S].block),e.renderer.rules.katex.delimiter=u};qs.exports=Xp});var i6={};R1(i6,{default:()=>I0});module.exports=L1(i6);var D0=require("obsidian");var qn;function ms(e){return qn=qn||document.createElement("textarea"),qn.innerHTML="&"+e+";",qn.value}var q1=Object.prototype.hasOwnProperty;function P1(e,t){return e?q1.call(e,t):!1}function gs(e){var t=[].slice.call(arguments,1);return t.forEach(function(r){if(r){if(typeof r!="object")throw new TypeError(r+"must be object");Object.keys(r).forEach(function(o){e[o]=r[o]})}}),e}var H1=/\\([\\!"#$%&'()*+,.\/:;<=>?@[\]^_`{|}~-])/g;function Hr(e){return e.indexOf("\\")<0?e:e.replace(H1,"$1")}function xs(e){return!(e>=55296&&e<=57343||e>=64976&&e<=65007||(e&65535)===65535||(e&65535)===65534||e>=0&&e<=8||e===11||e>=14&&e<=31||e>=127&&e<=159||e>1114111)}function Ai(e){if(e>65535){e-=65536;var t=55296+(e>>10),r=56320+(e&1023);return String.fromCharCode(t,r)}return String.fromCharCode(e)}var $1=/&([a-z#][a-z0-9]{1,31});/gi,V1=/^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))/i;function G1(e,t){var r=0,o=ms(t);return t!==o?o:t.charCodeAt(0)===35&&V1.test(t)&&(r=t[1].toLowerCase()==="x"?parseInt(t.slice(2),16):parseInt(t.slice(1),10),xs(r))?Ai(r):e}function nr(e){return e.indexOf("&")<0?e:e.replace($1,G1)}var Y1=/[&<>"]/,U1=/[&<>"]/g,X1={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};function j1(e){return X1[e]}function et(e){return Y1.test(e)?e.replace(U1,j1):e}var J={};J.blockquote_open=function(){return`<blockquote>
`};J.blockquote_close=function(e,t){return"</blockquote>"+ir(e,t)};J.code=function(e,t){return e[t].block?"<pre><code>"+et(e[t].content)+"</code></pre>"+ir(e,t):"<code>"+et(e[t].content)+"</code>"};J.fence=function(e,t,r,o,s){var u=e[t],c="",h=r.langPrefix,g="",b,y,S;if(u.params){if(b=u.params.split(/\s+/g),y=b.join(" "),P1(s.rules.fence_custom,b[0]))return s.rules.fence_custom[b[0]](e,t,r,o,s);g=et(nr(Hr(y))),c=' class="'+h+g+'"'}return r.highlight?S=r.highlight.apply(r.highlight,[u.content].concat(b))||et(u.content):S=et(u.content),"<pre><code"+c+">"+S+"</code></pre>"+ir(e,t)};J.fence_custom={};J.heading_open=function(e,t){return"<h"+e[t].hLevel+">"};J.heading_close=function(e,t){return"</h"+e[t].hLevel+`>
`};J.hr=function(e,t,r){return(r.xhtmlOut?"<hr />":"<hr>")+ir(e,t)};J.bullet_list_open=function(){return`<ul>
`};J.bullet_list_close=function(e,t){return"</ul>"+ir(e,t)};J.list_item_open=function(){return"<li>"};J.list_item_close=function(){return`</li>
`};J.ordered_list_open=function(e,t){var r=e[t],o=r.order>1?' start="'+r.order+'"':"";return"<ol"+o+`>
`};J.ordered_list_close=function(e,t){return"</ol>"+ir(e,t)};J.paragraph_open=function(e,t){return e[t].tight?"":"<p>"};J.paragraph_close=function(e,t){var r=!(e[t].tight&&t&&e[t-1].type==="inline"&&!e[t-1].content);return(e[t].tight?"":"</p>")+(r?ir(e,t):"")};J.link_open=function(e,t,r){var o=e[t].title?' title="'+et(nr(e[t].title))+'"':"",s=r.linkTarget?' target="'+r.linkTarget+'"':"";return'<a href="'+et(e[t].href)+'"'+o+s+">"};J.link_close=function(){return"</a>"};J.image=function(e,t,r){var o=' src="'+et(e[t].src)+'"',s=e[t].title?' title="'+et(nr(e[t].title))+'"':"",u=' alt="'+(e[t].alt?et(nr(Hr(e[t].alt))):"")+'"',c=r.xhtmlOut?" /":"";return"<img"+o+u+s+c+">"};J.table_open=function(){return`<table>
`};J.table_close=function(){return`</table>
`};J.thead_open=function(){return`<thead>
`};J.thead_close=function(){return`</thead>
`};J.tbody_open=function(){return`<tbody>
`};J.tbody_close=function(){return`</tbody>
`};J.tr_open=function(){return"<tr>"};J.tr_close=function(){return`</tr>
`};J.th_open=function(e,t){var r=e[t];return"<th"+(r.align?' style="text-align:'+r.align+'"':"")+">"};J.th_close=function(){return"</th>"};J.td_open=function(e,t){var r=e[t];return"<td"+(r.align?' style="text-align:'+r.align+'"':"")+">"};J.td_close=function(){return"</td>"};J.strong_open=function(){return"<strong>"};J.strong_close=function(){return"</strong>"};J.em_open=function(){return"<em>"};J.em_close=function(){return"</em>"};J.del_open=function(){return"<del>"};J.del_close=function(){return"</del>"};J.ins_open=function(){return"<ins>"};J.ins_close=function(){return"</ins>"};J.mark_open=function(){return"<mark>"};J.mark_close=function(){return"</mark>"};J.sub=function(e,t){return"<sub>"+et(e[t].content)+"</sub>"};J.sup=function(e,t){return"<sup>"+et(e[t].content)+"</sup>"};J.hardbreak=function(e,t,r){return r.xhtmlOut?`<br />
`:`<br>
`};J.softbreak=function(e,t,r){return r.breaks?r.xhtmlOut?`<br />
`:`<br>
`:`
`};J.text=function(e,t){return et(e[t].content)};J.htmlblock=function(e,t){return e[t].content};J.htmltag=function(e,t){return e[t].content};J.abbr_open=function(e,t){return'<abbr title="'+et(nr(e[t].title))+'">'};J.abbr_close=function(){return"</abbr>"};J.footnote_ref=function(e,t){var r=Number(e[t].id+1).toString(),o="fnref"+r;return e[t].subId>0&&(o+=":"+e[t].subId),'<sup class="footnote-ref"><a href="#fn'+r+'" id="'+o+'">['+r+"]</a></sup>"};J.footnote_block_open=function(e,t,r){var o=r.xhtmlOut?`<hr class="footnotes-sep" />
`:`<hr class="footnotes-sep">
`;return o+`<section class="footnotes">
<ol class="footnotes-list">
`};J.footnote_block_close=function(){return`</ol>
</section>
`};J.footnote_open=function(e,t){var r=Number(e[t].id+1).toString();return'<li id="fn'+r+'"  class="footnote-item">'};J.footnote_close=function(){return`</li>
`};J.footnote_anchor=function(e,t){var r=Number(e[t].id+1).toString(),o="fnref"+r;return e[t].subId>0&&(o+=":"+e[t].subId),' <a href="#'+o+'" class="footnote-backref">\u21A9</a>'};J.dl_open=function(){return`<dl>
`};J.dt_open=function(){return"<dt>"};J.dd_open=function(){return"<dd>"};J.dl_close=function(){return`</dl>
`};J.dt_close=function(){return`</dt>
`};J.dd_close=function(){return`</dd>
`};function ys(e,t){return++t>=e.length-2?t:e[t].type==="paragraph_open"&&e[t].tight&&e[t+1].type==="inline"&&e[t+1].content.length===0&&e[t+2].type==="paragraph_close"&&e[t+2].tight?ys(e,t+2):t}var ir=J.getBreak=function(t,r){return r=ys(t,r),r<t.length&&t[r].type==="list_item_close"?"":`
`};function Mi(){this.rules=gs({},J),this.getBreak=J.getBreak}Mi.prototype.renderInline=function(e,t,r){for(var o=this.rules,s=e.length,u=0,c="";s--;)c+=o[e[u].type](e,u++,t,r,this);return c};Mi.prototype.render=function(e,t,r){for(var o=this.rules,s=e.length,u=-1,c="";++u<s;)e[u].type==="inline"?c+=this.renderInline(e[u].children,t,r):c+=o[e[u].type](e,u,t,r,this);return c};function ct(){this.__rules__=[],this.__cache__=null}ct.prototype.__find__=function(e){for(var t=this.__rules__.length,r=-1;t--;)if(this.__rules__[++r].name===e)return r;return-1};ct.prototype.__compile__=function(){var e=this,t=[""];e.__rules__.forEach(function(r){r.enabled&&r.alt.forEach(function(o){t.indexOf(o)<0&&t.push(o)})}),e.__cache__={},t.forEach(function(r){e.__cache__[r]=[],e.__rules__.forEach(function(o){o.enabled&&(r&&o.alt.indexOf(r)<0||e.__cache__[r].push(o.fn))})})};ct.prototype.at=function(e,t,r){var o=this.__find__(e),s=r||{};if(o===-1)throw new Error("Parser rule not found: "+e);this.__rules__[o].fn=t,this.__rules__[o].alt=s.alt||[],this.__cache__=null};ct.prototype.before=function(e,t,r,o){var s=this.__find__(e),u=o||{};if(s===-1)throw new Error("Parser rule not found: "+e);this.__rules__.splice(s,0,{name:t,enabled:!0,fn:r,alt:u.alt||[]}),this.__cache__=null};ct.prototype.after=function(e,t,r,o){var s=this.__find__(e),u=o||{};if(s===-1)throw new Error("Parser rule not found: "+e);this.__rules__.splice(s+1,0,{name:t,enabled:!0,fn:r,alt:u.alt||[]}),this.__cache__=null};ct.prototype.push=function(e,t,r){var o=r||{};this.__rules__.push({name:e,enabled:!0,fn:t,alt:o.alt||[]}),this.__cache__=null};ct.prototype.enable=function(e,t){e=Array.isArray(e)?e:[e],t&&this.__rules__.forEach(function(r){r.enabled=!1}),e.forEach(function(r){var o=this.__find__(r);if(o<0)throw new Error("Rules manager: invalid rule name "+r);this.__rules__[o].enabled=!0},this),this.__cache__=null};ct.prototype.disable=function(e){e=Array.isArray(e)?e:[e],e.forEach(function(t){var r=this.__find__(t);if(r<0)throw new Error("Rules manager: invalid rule name "+t);this.__rules__[r].enabled=!1},this),this.__cache__=null};ct.prototype.getRules=function(e){return this.__cache__===null&&this.__compile__(),this.__cache__[e]||[]};function W1(e){e.inlineMode?e.tokens.push({type:"inline",content:e.src.replace(/\n/g," ").trim(),level:0,lines:[0,1],children:[]}):e.block.parse(e.src,e.options,e.env,e.tokens)}function or(e,t,r,o,s){this.src=e,this.env=o,this.options=r,this.parser=t,this.tokens=s,this.pos=0,this.posMax=this.src.length,this.level=0,this.pending="",this.pendingLevel=0,this.cache=[],this.isInLabel=!1,this.linkLevel=0,this.linkContent="",this.labelUnmatchedScopes=0}or.prototype.pushPending=function(){this.tokens.push({type:"text",content:this.pending,level:this.pendingLevel}),this.pending=""};or.prototype.push=function(e){this.pending&&this.pushPending(),this.tokens.push(e),this.pendingLevel=this.level};or.prototype.cacheSet=function(e,t){for(var r=this.cache.length;r<=e;r++)this.cache.push(0);this.cache[e]=t};or.prototype.cacheGet=function(e){return e<this.cache.length?this.cache[e]:0};function $r(e,t){var r,o,s,u=-1,c=e.posMax,h=e.pos,g=e.isInLabel;if(e.isInLabel)return-1;if(e.labelUnmatchedScopes)return e.labelUnmatchedScopes--,-1;for(e.pos=t+1,e.isInLabel=!0,r=1;e.pos<c;){if(s=e.src.charCodeAt(e.pos),s===91)r++;else if(s===93&&(r--,r===0)){o=!0;break}e.parser.skipToken(e)}return o?(u=e.pos,e.labelUnmatchedScopes=0):e.labelUnmatchedScopes=r-1,e.pos=h,e.isInLabel=g,u}function K1(e,t,r,o){var s,u,c,h,g,b;if(e.charCodeAt(0)!==42||e.charCodeAt(1)!==91||e.indexOf("]:")===-1||(s=new or(e,t,r,o,[]),u=$r(s,1),u<0||e.charCodeAt(u+1)!==58))return-1;for(h=s.posMax,c=u+2;c<h&&s.src.charCodeAt(c)!==10;c++);return g=e.slice(2,u),b=e.slice(u+2,c).trim(),b.length===0?-1:(o.abbreviations||(o.abbreviations={}),typeof o.abbreviations[":"+g]=="undefined"&&(o.abbreviations[":"+g]=b),c)}function Z1(e){var t=e.tokens,r,o,s,u;if(!e.inlineMode){for(r=1,o=t.length-1;r<o;r++)if(t[r-1].type==="paragraph_open"&&t[r].type==="inline"&&t[r+1].type==="paragraph_close"){for(s=t[r].content;s.length&&(u=K1(s,e.inline,e.options,e.env),!(u<0));)s=s.slice(u).trim();t[r].content=s,s.length||(t[r-1].tight=!0,t[r+1].tight=!0)}}}function _i(e){var t=nr(e);try{t=decodeURI(t)}catch(r){}return encodeURI(t)}function bs(e,t){var r,o,s,u=t,c=e.posMax;if(e.src.charCodeAt(t)===60){for(t++;t<c;){if(r=e.src.charCodeAt(t),r===10)return!1;if(r===62)return s=_i(Hr(e.src.slice(u+1,t))),e.parser.validateLink(s)?(e.pos=t+1,e.linkContent=s,!0):!1;if(r===92&&t+1<c){t+=2;continue}t++}return!1}for(o=0;t<c&&(r=e.src.charCodeAt(t),!(r===32||r<32||r===127));){if(r===92&&t+1<c){t+=2;continue}if(r===40&&(o++,o>1)||r===41&&(o--,o<0))break;t++}return u===t||(s=Hr(e.src.slice(u,t)),!e.parser.validateLink(s))?!1:(e.linkContent=s,e.pos=t,!0)}function vs(e,t){var r,o=t,s=e.posMax,u=e.src.charCodeAt(t);if(u!==34&&u!==39&&u!==40)return!1;for(t++,u===40&&(u=41);t<s;){if(r=e.src.charCodeAt(t),r===u)return e.pos=t+1,e.linkContent=Hr(e.src.slice(o+1,t)),!0;if(r===92&&t+1<s){t+=2;continue}t++}return!1}function ws(e){return e.trim().replace(/\s+/g," ").toUpperCase()}function J1(e,t,r,o){var s,u,c,h,g,b,y,S,T;if(e.charCodeAt(0)!==91||e.indexOf("]:")===-1||(s=new or(e,t,r,o,[]),u=$r(s,0),u<0||e.charCodeAt(u+1)!==58))return-1;for(h=s.posMax,c=u+2;c<h&&(g=s.src.charCodeAt(c),!(g!==32&&g!==10));c++);if(!bs(s,c))return-1;for(y=s.linkContent,c=s.pos,b=c,c=c+1;c<h&&(g=s.src.charCodeAt(c),!(g!==32&&g!==10));c++);for(c<h&&b!==c&&vs(s,c)?(S=s.linkContent,c=s.pos):(S="",c=b);c<h&&s.src.charCodeAt(c)===32;)c++;return c<h&&s.src.charCodeAt(c)!==10?-1:(T=ws(e.slice(1,u)),typeof o.references[T]=="undefined"&&(o.references[T]={title:S,href:y}),c)}function Q1(e){var t=e.tokens,r,o,s,u;if(e.env.references=e.env.references||{},!e.inlineMode){for(r=1,o=t.length-1;r<o;r++)if(t[r].type==="inline"&&t[r-1].type==="paragraph_open"&&t[r+1].type==="paragraph_close"){for(s=t[r].content;s.length&&(u=J1(s,e.inline,e.options,e.env),!(u<0));)s=s.slice(u).trim();t[r].content=s,s.length||(t[r-1].tight=!0,t[r+1].tight=!0)}}}function ef(e){var t=e.tokens,r,o,s;for(o=0,s=t.length;o<s;o++)r=t[o],r.type==="inline"&&e.inline.parse(r.content,e.options,e.env,r.children)}function tf(e){var t,r,o,s,u,c,h,g,b,y=0,S=!1,T={};if(e.env.footnotes&&(e.tokens=e.tokens.filter(function(N){return N.type==="footnote_reference_open"?(S=!0,g=[],b=N.label,!1):N.type==="footnote_reference_close"?(S=!1,T[":"+b]=g,!1):(S&&g.push(N),!S)}),!!e.env.footnotes.list)){for(c=e.env.footnotes.list,e.tokens.push({type:"footnote_block_open",level:y++}),t=0,r=c.length;t<r;t++){for(e.tokens.push({type:"footnote_open",id:t,level:y++}),c[t].tokens?(h=[],h.push({type:"paragraph_open",tight:!1,level:y++}),h.push({type:"inline",content:"",level:y,children:c[t].tokens}),h.push({type:"paragraph_close",tight:!1,level:--y})):c[t].label&&(h=T[":"+c[t].label]),e.tokens=e.tokens.concat(h),e.tokens[e.tokens.length-1].type==="paragraph_close"?u=e.tokens.pop():u=null,s=c[t].count>0?c[t].count:1,o=0;o<s;o++)e.tokens.push({type:"footnote_anchor",id:t,subId:o,level:y});u&&e.tokens.push(u),e.tokens.push({type:"footnote_close",level:--y})}e.tokens.push({type:"footnote_block_close",level:--y})}}var ss=` 
()[]'".,!?-`;function bi(e){return e.replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1")}function rf(e){var t,r,o,s,u,c,h,g,b,y,S,T,N=e.tokens;if(e.env.abbreviations){for(e.env.abbrRegExp||(T="(^|["+ss.split("").map(bi).join("")+"])("+Object.keys(e.env.abbreviations).map(function(R){return R.substr(1)}).sort(function(R,z){return z.length-R.length}).map(bi).join("|")+")($|["+ss.split("").map(bi).join("")+"])",e.env.abbrRegExp=new RegExp(T,"g")),y=e.env.abbrRegExp,r=0,o=N.length;r<o;r++)if(N[r].type==="inline"){for(s=N[r].children,t=s.length-1;t>=0;t--)if(u=s[t],u.type==="text"){for(g=0,c=u.content,y.lastIndex=0,b=u.level,h=[];S=y.exec(c);)y.lastIndex>g&&h.push({type:"text",content:c.slice(g,S.index+S[1].length),level:b}),h.push({type:"abbr_open",title:e.env.abbreviations[":"+S[2]],level:b++}),h.push({type:"text",content:S[2],level:b}),h.push({type:"abbr_close",level:--b}),g=y.lastIndex-S[3].length;h.length&&(g<c.length&&h.push({type:"text",content:c.slice(g),level:b}),N[r].children=s=[].concat(s.slice(0,t),h,s.slice(t+1)))}}}}var nf=/\+-|\.\.|\?\?\?\?|!!!!|,,|--/,of=/\((c|tm|r|p)\)/ig,lf={c:"\xA9",r:"\xAE",p:"\xA7",tm:"\u2122"};function sf(e){return e.indexOf("(")<0?e:e.replace(of,function(t,r){return lf[r.toLowerCase()]})}function af(e){var t,r,o,s,u;if(e.options.typographer){for(u=e.tokens.length-1;u>=0;u--)if(e.tokens[u].type==="inline")for(s=e.tokens[u].children,t=s.length-1;t>=0;t--)r=s[t],r.type==="text"&&(o=r.content,o=sf(o),nf.test(o)&&(o=o.replace(/\+-/g,"\xB1").replace(/\.{2,}/g,"\u2026").replace(/([?!])…/g,"$1..").replace(/([?!]){4,}/g,"$1$1$1").replace(/,{2,}/g,",").replace(/(^|[^-])---([^-]|$)/mg,"$1\u2014$2").replace(/(^|\s)--(\s|$)/mg,"$1\u2013$2").replace(/(^|[^-\s])--([^-\s]|$)/mg,"$1\u2013$2")),r.content=o)}}var uf=/['"]/,as=/['"]/g,cf=/[-\s()\[\]]/,us="\u2019";function cs(e,t){return t<0||t>=e.length?!1:!cf.test(e[t])}function kr(e,t,r){return e.substr(0,t)+r+e.substr(t+1)}function hf(e){var t,r,o,s,u,c,h,g,b,y,S,T,N,R,z,P,L;if(e.options.typographer){for(L=[],z=e.tokens.length-1;z>=0;z--)if(e.tokens[z].type==="inline")for(P=e.tokens[z].children,L.length=0,t=0;t<P.length;t++){if(r=P[t],r.type!=="text"||uf.test(r.text))continue;for(h=P[t].level,N=L.length-1;N>=0&&!(L[N].level<=h);N--);L.length=N+1,o=r.content,u=0,c=o.length;e:for(;u<c&&(as.lastIndex=u,s=as.exec(o),!!s);){if(g=!cs(o,s.index-1),u=s.index+1,R=s[0]==="'",b=!cs(o,u),!b&&!g){R&&(r.content=kr(r.content,s.index,us));continue}if(S=!b,T=!g,T){for(N=L.length-1;N>=0&&(y=L[N],!(L[N].level<h));N--)if(y.single===R&&L[N].level===h){y=L[N],R?(P[y.token].content=kr(P[y.token].content,y.pos,e.options.quotes[2]),r.content=kr(r.content,s.index,e.options.quotes[3])):(P[y.token].content=kr(P[y.token].content,y.pos,e.options.quotes[0]),r.content=kr(r.content,s.index,e.options.quotes[1])),L.length=N;continue e}}S?L.push({token:t,pos:s.index,single:R,level:h}):T&&R&&(r.content=kr(r.content,s.index,us))}}}}var vi=[["block",W1],["abbr",Z1],["references",Q1],["inline",ef],["footnote_tail",tf],["abbr2",rf],["replacements",af],["smartquotes",hf]];function ks(){this.options={},this.ruler=new ct;for(var e=0;e<vi.length;e++)this.ruler.push(vi[e][0],vi[e][1])}ks.prototype.process=function(e){var t,r,o;for(o=this.ruler.getRules(""),t=0,r=o.length;t<r;t++)o[t](e)};function lr(e,t,r,o,s){var u,c,h,g,b,y,S;for(this.src=e,this.parser=t,this.options=r,this.env=o,this.tokens=s,this.bMarks=[],this.eMarks=[],this.tShift=[],this.blkIndent=0,this.line=0,this.lineMax=0,this.tight=!1,this.parentType="root",this.ddIndent=-1,this.level=0,this.result="",c=this.src,y=0,S=!1,h=g=y=0,b=c.length;g<b;g++){if(u=c.charCodeAt(g),!S)if(u===32){y++;continue}else S=!0;(u===10||g===b-1)&&(u!==10&&g++,this.bMarks.push(h),this.eMarks.push(g),this.tShift.push(y),S=!1,y=0,h=g+1)}this.bMarks.push(c.length),this.eMarks.push(c.length),this.tShift.push(0),this.lineMax=this.bMarks.length-1}lr.prototype.isEmpty=function(t){return this.bMarks[t]+this.tShift[t]>=this.eMarks[t]};lr.prototype.skipEmptyLines=function(t){for(var r=this.lineMax;t<r&&!(this.bMarks[t]+this.tShift[t]<this.eMarks[t]);t++);return t};lr.prototype.skipSpaces=function(t){for(var r=this.src.length;t<r&&this.src.charCodeAt(t)===32;t++);return t};lr.prototype.skipChars=function(t,r){for(var o=this.src.length;t<o&&this.src.charCodeAt(t)===r;t++);return t};lr.prototype.skipCharsBack=function(t,r,o){if(t<=o)return t;for(;t>o;)if(r!==this.src.charCodeAt(--t))return t+1;return t};lr.prototype.getLines=function(t,r,o,s){var u,c,h,g,b,y=t;if(t>=r)return"";if(y+1===r)return c=this.bMarks[y]+Math.min(this.tShift[y],o),h=s?this.eMarks[y]+1:this.eMarks[y],this.src.slice(c,h);for(g=new Array(r-t),u=0;y<r;y++,u++)b=this.tShift[y],b>o&&(b=o),b<0&&(b=0),c=this.bMarks[y]+b,y+1<r||s?h=this.eMarks[y]+1:h=this.eMarks[y],g[u]=this.src.slice(c,h);return g.join("")};function ff(e,t,r){var o,s;if(e.tShift[t]-e.blkIndent<4)return!1;for(s=o=t+1;o<r;){if(e.isEmpty(o)){o++;continue}if(e.tShift[o]-e.blkIndent>=4){o++,s=o;continue}break}return e.line=o,e.tokens.push({type:"code",content:e.getLines(t,s,4+e.blkIndent,!0),block:!0,lines:[t,e.line],level:e.level}),!0}function pf(e,t,r,o){var s,u,c,h,g,b=!1,y=e.bMarks[t]+e.tShift[t],S=e.eMarks[t];if(y+3>S||(s=e.src.charCodeAt(y),s!==126&&s!==96)||(g=y,y=e.skipChars(y,s),u=y-g,u<3)||(c=e.src.slice(y,S).trim(),c.indexOf("`")>=0))return!1;if(o)return!0;for(h=t;h++,!(h>=r||(y=g=e.bMarks[h]+e.tShift[h],S=e.eMarks[h],y<S&&e.tShift[h]<e.blkIndent));)if(e.src.charCodeAt(y)===s&&!(e.tShift[h]-e.blkIndent>=4)&&(y=e.skipChars(y,s),!(y-g<u)&&(y=e.skipSpaces(y),!(y<S)))){b=!0;break}return u=e.tShift[t],e.line=h+(b?1:0),e.tokens.push({type:"fence",params:c,content:e.getLines(t+1,h,u,!0),lines:[t,e.line],level:e.level}),!0}function df(e,t,r,o){var s,u,c,h,g,b,y,S,T,N,R,z=e.bMarks[t]+e.tShift[t],P=e.eMarks[t];if(z>P||e.src.charCodeAt(z++)!==62||e.level>=e.options.maxNesting)return!1;if(o)return!0;for(e.src.charCodeAt(z)===32&&z++,g=e.blkIndent,e.blkIndent=0,h=[e.bMarks[t]],e.bMarks[t]=z,z=z<P?e.skipSpaces(z):z,u=z>=P,c=[e.tShift[t]],e.tShift[t]=z-e.bMarks[t],S=e.parser.ruler.getRules("blockquote"),s=t+1;s<r&&(z=e.bMarks[s]+e.tShift[s],P=e.eMarks[s],!(z>=P));s++){if(e.src.charCodeAt(z++)===62){e.src.charCodeAt(z)===32&&z++,h.push(e.bMarks[s]),e.bMarks[s]=z,z=z<P?e.skipSpaces(z):z,u=z>=P,c.push(e.tShift[s]),e.tShift[s]=z-e.bMarks[s];continue}if(u)break;for(R=!1,T=0,N=S.length;T<N;T++)if(S[T](e,s,r,!0)){R=!0;break}if(R)break;h.push(e.bMarks[s]),c.push(e.tShift[s]),e.tShift[s]=-1337}for(b=e.parentType,e.parentType="blockquote",e.tokens.push({type:"blockquote_open",lines:y=[t,0],level:e.level++}),e.parser.tokenize(e,t,s),e.tokens.push({type:"blockquote_close",level:--e.level}),e.parentType=b,y[1]=e.line,T=0;T<c.length;T++)e.bMarks[T+t]=h[T],e.tShift[T+t]=c[T];return e.blkIndent=g,!0}function mf(e,t,r,o){var s,u,c,h=e.bMarks[t],g=e.eMarks[t];if(h+=e.tShift[t],h>g||(s=e.src.charCodeAt(h++),s!==42&&s!==45&&s!==95))return!1;for(u=1;h<g;){if(c=e.src.charCodeAt(h++),c!==s&&c!==32)return!1;c===s&&u++}return u<3?!1:(o||(e.line=t+1,e.tokens.push({type:"hr",lines:[t,e.line],level:e.level})),!0)}function hs(e,t){var r,o,s;return o=e.bMarks[t]+e.tShift[t],s=e.eMarks[t],o>=s||(r=e.src.charCodeAt(o++),r!==42&&r!==45&&r!==43)||o<s&&e.src.charCodeAt(o)!==32?-1:o}function fs(e,t){var r,o=e.bMarks[t]+e.tShift[t],s=e.eMarks[t];if(o+1>=s||(r=e.src.charCodeAt(o++),r<48||r>57))return-1;for(;;){if(o>=s)return-1;if(r=e.src.charCodeAt(o++),!(r>=48&&r<=57)){if(r===41||r===46)break;return-1}}return o<s&&e.src.charCodeAt(o)!==32?-1:o}function gf(e,t){var r,o,s=e.level+2;for(r=t+2,o=e.tokens.length-2;r<o;r++)e.tokens[r].level===s&&e.tokens[r].type==="paragraph_open"&&(e.tokens[r+2].tight=!0,e.tokens[r].tight=!0,r+=2)}function xf(e,t,r,o){var s,u,c,h,g,b,y,S,T,N,R,z,P,L,X,ne,ue,me,be=!0,Se,$e,ze,Ve;if((S=fs(e,t))>=0)P=!0;else if((S=hs(e,t))>=0)P=!1;else return!1;if(e.level>=e.options.maxNesting)return!1;if(z=e.src.charCodeAt(S-1),o)return!0;for(X=e.tokens.length,P?(y=e.bMarks[t]+e.tShift[t],R=Number(e.src.substr(y,S-y-1)),e.tokens.push({type:"ordered_list_open",order:R,lines:ue=[t,0],level:e.level++})):e.tokens.push({type:"bullet_list_open",lines:ue=[t,0],level:e.level++}),s=t,ne=!1,Se=e.parser.ruler.getRules("list");s<r&&(L=e.skipSpaces(S),T=e.eMarks[s],L>=T?N=1:N=L-S,N>4&&(N=1),N<1&&(N=1),u=S-e.bMarks[s]+N,e.tokens.push({type:"list_item_open",lines:me=[t,0],level:e.level++}),h=e.blkIndent,g=e.tight,c=e.tShift[t],b=e.parentType,e.tShift[t]=L-e.bMarks[t],e.blkIndent=u,e.tight=!0,e.parentType="list",e.parser.tokenize(e,t,r,!0),(!e.tight||ne)&&(be=!1),ne=e.line-t>1&&e.isEmpty(e.line-1),e.blkIndent=h,e.tShift[t]=c,e.tight=g,e.parentType=b,e.tokens.push({type:"list_item_close",level:--e.level}),s=t=e.line,me[1]=s,L=e.bMarks[t],!(s>=r||e.isEmpty(s)||e.tShift[s]<e.blkIndent));){for(Ve=!1,$e=0,ze=Se.length;$e<ze;$e++)if(Se[$e](e,s,r,!0)){Ve=!0;break}if(Ve)break;if(P){if(S=fs(e,s),S<0)break}else if(S=hs(e,s),S<0)break;if(z!==e.src.charCodeAt(S-1))break}return e.tokens.push({type:P?"ordered_list_close":"bullet_list_close",level:--e.level}),ue[1]=s,e.line=s,be&&gf(e,X),!0}function yf(e,t,r,o){var s,u,c,h,g,b=e.bMarks[t]+e.tShift[t],y=e.eMarks[t];if(b+4>y||e.src.charCodeAt(b)!==91||e.src.charCodeAt(b+1)!==94||e.level>=e.options.maxNesting)return!1;for(h=b+2;h<y;h++){if(e.src.charCodeAt(h)===32)return!1;if(e.src.charCodeAt(h)===93)break}return h===b+2||h+1>=y||e.src.charCodeAt(++h)!==58?!1:(o||(h++,e.env.footnotes||(e.env.footnotes={}),e.env.footnotes.refs||(e.env.footnotes.refs={}),g=e.src.slice(b+2,h-2),e.env.footnotes.refs[":"+g]=-1,e.tokens.push({type:"footnote_reference_open",label:g,level:e.level++}),s=e.bMarks[t],u=e.tShift[t],c=e.parentType,e.tShift[t]=e.skipSpaces(h)-h,e.bMarks[t]=h,e.blkIndent+=4,e.parentType="footnote",e.tShift[t]<e.blkIndent&&(e.tShift[t]+=e.blkIndent,e.bMarks[t]-=e.blkIndent),e.parser.tokenize(e,t,r,!0),e.parentType=c,e.blkIndent-=4,e.tShift[t]=u,e.bMarks[t]=s,e.tokens.push({type:"footnote_reference_close",level:--e.level})),!0)}function bf(e,t,r,o){var s,u,c,h=e.bMarks[t]+e.tShift[t],g=e.eMarks[t];if(h>=g||(s=e.src.charCodeAt(h),s!==35||h>=g))return!1;for(u=1,s=e.src.charCodeAt(++h);s===35&&h<g&&u<=6;)u++,s=e.src.charCodeAt(++h);return u>6||h<g&&s!==32?!1:(o||(g=e.skipCharsBack(g,32,h),c=e.skipCharsBack(g,35,h),c>h&&e.src.charCodeAt(c-1)===32&&(g=c),e.line=t+1,e.tokens.push({type:"heading_open",hLevel:u,lines:[t,e.line],level:e.level}),h<g&&e.tokens.push({type:"inline",content:e.src.slice(h,g).trim(),level:e.level+1,lines:[t,e.line],children:[]}),e.tokens.push({type:"heading_close",hLevel:u,level:e.level})),!0)}function vf(e,t,r){var o,s,u,c=t+1;return c>=r||e.tShift[c]<e.blkIndent||e.tShift[c]-e.blkIndent>3||(s=e.bMarks[c]+e.tShift[c],u=e.eMarks[c],s>=u)||(o=e.src.charCodeAt(s),o!==45&&o!==61)||(s=e.skipChars(s,o),s=e.skipSpaces(s),s<u)?!1:(s=e.bMarks[t]+e.tShift[t],e.line=c+1,e.tokens.push({type:"heading_open",hLevel:o===61?1:2,lines:[t,e.line],level:e.level}),e.tokens.push({type:"inline",content:e.src.slice(s,e.eMarks[t]).trim(),level:e.level+1,lines:[t,e.line-1],children:[]}),e.tokens.push({type:"heading_close",hLevel:o===61?1:2,level:e.level}),!0)}var Ss={};["article","aside","button","blockquote","body","canvas","caption","col","colgroup","dd","div","dl","dt","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","iframe","li","map","object","ol","output","p","pre","progress","script","section","style","table","tbody","td","textarea","tfoot","th","tr","thead","ul","video"].forEach(function(e){Ss[e]=!0});var wf=/^<([a-zA-Z]{1,15})[\s\/>]/,kf=/^<\/([a-zA-Z]{1,15})[\s>]/;function Sf(e){var t=e|32;return t>=97&&t<=122}function Af(e,t,r,o){var s,u,c,h=e.bMarks[t],g=e.eMarks[t],b=e.tShift[t];if(h+=b,!e.options.html||b>3||h+2>=g||e.src.charCodeAt(h)!==60)return!1;if(s=e.src.charCodeAt(h+1),s===33||s===63){if(o)return!0}else if(s===47||Sf(s)){if(s===47){if(u=e.src.slice(h,g).match(kf),!u)return!1}else if(u=e.src.slice(h,g).match(wf),!u)return!1;if(Ss[u[1].toLowerCase()]!==!0)return!1;if(o)return!0}else return!1;for(c=t+1;c<e.lineMax&&!e.isEmpty(c);)c++;return e.line=c,e.tokens.push({type:"htmlblock",level:e.level,lines:[t,e.line],content:e.getLines(t,c,0,!0)}),!0}function wi(e,t){var r=e.bMarks[t]+e.blkIndent,o=e.eMarks[t];return e.src.substr(r,o-r)}function _f(e,t,r,o){var s,u,c,h,g,b,y,S,T,N,R;if(t+2>r||(g=t+1,e.tShift[g]<e.blkIndent)||(c=e.bMarks[g]+e.tShift[g],c>=e.eMarks[g])||(s=e.src.charCodeAt(c),s!==124&&s!==45&&s!==58)||(u=wi(e,t+1),!/^[-:| ]+$/.test(u))||(b=u.split("|"),b<=2))return!1;for(S=[],h=0;h<b.length;h++){if(T=b[h].trim(),!T){if(h===0||h===b.length-1)continue;return!1}if(!/^:?-+:?$/.test(T))return!1;T.charCodeAt(T.length-1)===58?S.push(T.charCodeAt(0)===58?"center":"right"):T.charCodeAt(0)===58?S.push("left"):S.push("")}if(u=wi(e,t).trim(),u.indexOf("|")===-1||(b=u.replace(/^\||\|$/g,"").split("|"),S.length!==b.length))return!1;if(o)return!0;for(e.tokens.push({type:"table_open",lines:N=[t,0],level:e.level++}),e.tokens.push({type:"thead_open",lines:[t,t+1],level:e.level++}),e.tokens.push({type:"tr_open",lines:[t,t+1],level:e.level++}),h=0;h<b.length;h++)e.tokens.push({type:"th_open",align:S[h],lines:[t,t+1],level:e.level++}),e.tokens.push({type:"inline",content:b[h].trim(),lines:[t,t+1],level:e.level,children:[]}),e.tokens.push({type:"th_close",level:--e.level});for(e.tokens.push({type:"tr_close",level:--e.level}),e.tokens.push({type:"thead_close",level:--e.level}),e.tokens.push({type:"tbody_open",lines:R=[t+2,0],level:e.level++}),g=t+2;g<r&&!(e.tShift[g]<e.blkIndent||(u=wi(e,g).trim(),u.indexOf("|")===-1));g++){for(b=u.replace(/^\||\|$/g,"").split("|"),e.tokens.push({type:"tr_open",level:e.level++}),h=0;h<b.length;h++)e.tokens.push({type:"td_open",align:S[h],level:e.level++}),y=b[h].substring(b[h].charCodeAt(0)===124?1:0,b[h].charCodeAt(b[h].length-1)===124?b[h].length-1:b[h].length).trim(),e.tokens.push({type:"inline",content:y,level:e.level,children:[]}),e.tokens.push({type:"td_close",level:--e.level});e.tokens.push({type:"tr_close",level:--e.level})}return e.tokens.push({type:"tbody_close",level:--e.level}),e.tokens.push({type:"table_close",level:--e.level}),N[1]=R[1]=g,e.line=g,!0}function Pn(e,t){var r,o,s=e.bMarks[t]+e.tShift[t],u=e.eMarks[t];return s>=u||(o=e.src.charCodeAt(s++),o!==126&&o!==58)||(r=e.skipSpaces(s),s===r)||r>=u?-1:r}function Mf(e,t){var r,o,s=e.level+2;for(r=t+2,o=e.tokens.length-2;r<o;r++)e.tokens[r].level===s&&e.tokens[r].type==="paragraph_open"&&(e.tokens[r+2].tight=!0,e.tokens[r].tight=!0,r+=2)}function Cf(e,t,r,o){var s,u,c,h,g,b,y,S,T,N,R,z,P,L;if(o)return e.ddIndent<0?!1:Pn(e,t)>=0;if(y=t+1,e.isEmpty(y)&&++y>r||e.tShift[y]<e.blkIndent||(s=Pn(e,y),s<0)||e.level>=e.options.maxNesting)return!1;b=e.tokens.length,e.tokens.push({type:"dl_open",lines:g=[t,0],level:e.level++}),c=t,u=y;e:for(;;){for(L=!0,P=!1,e.tokens.push({type:"dt_open",lines:[c,c],level:e.level++}),e.tokens.push({type:"inline",content:e.getLines(c,c+1,e.blkIndent,!1).trim(),level:e.level+1,lines:[c,c],children:[]}),e.tokens.push({type:"dt_close",level:--e.level});;){if(e.tokens.push({type:"dd_open",lines:h=[y,0],level:e.level++}),z=e.tight,T=e.ddIndent,S=e.blkIndent,R=e.tShift[u],N=e.parentType,e.blkIndent=e.ddIndent=e.tShift[u]+2,e.tShift[u]=s-e.bMarks[u],e.tight=!0,e.parentType="deflist",e.parser.tokenize(e,u,r,!0),(!e.tight||P)&&(L=!1),P=e.line-u>1&&e.isEmpty(e.line-1),e.tShift[u]=R,e.tight=z,e.parentType=N,e.blkIndent=S,e.ddIndent=T,e.tokens.push({type:"dd_close",level:--e.level}),h[1]=y=e.line,y>=r||e.tShift[y]<e.blkIndent)break e;if(s=Pn(e,y),s<0)break;u=y}if(y>=r||(c=y,e.isEmpty(c))||e.tShift[c]<e.blkIndent||(u=c+1,u>=r)||(e.isEmpty(u)&&u++,u>=r)||e.tShift[u]<e.blkIndent||(s=Pn(e,u),s<0))break}return e.tokens.push({type:"dl_close",level:--e.level}),g[1]=y,e.line=y,L&&Mf(e,b),!0}function Tf(e,t){var r,o,s,u,c,h=t+1,g;if(r=e.lineMax,h<r&&!e.isEmpty(h)){for(g=e.parser.ruler.getRules("paragraph");h<r&&!e.isEmpty(h);h++)if(!(e.tShift[h]-e.blkIndent>3)){for(s=!1,u=0,c=g.length;u<c;u++)if(g[u](e,h,r,!0)){s=!0;break}if(s)break}}return o=e.getLines(t,h,e.blkIndent,!1).trim(),e.line=h,o.length&&(e.tokens.push({type:"paragraph_open",tight:!1,lines:[t,e.line],level:e.level}),e.tokens.push({type:"inline",content:o,level:e.level+1,lines:[t,e.line],children:[]}),e.tokens.push({type:"paragraph_close",tight:!1,level:e.level})),!0}var Hn=[["code",ff],["fences",pf,["paragraph","blockquote","list"]],["blockquote",df,["paragraph","blockquote","list"]],["hr",mf,["paragraph","blockquote","list"]],["list",xf,["paragraph","blockquote"]],["footnote",yf,["paragraph"]],["heading",bf,["paragraph","blockquote"]],["lheading",vf],["htmlblock",Af,["paragraph","blockquote"]],["table",_f,["paragraph"]],["deflist",Cf,["paragraph"]],["paragraph",Tf]];function Ci(){this.ruler=new ct;for(var e=0;e<Hn.length;e++)this.ruler.push(Hn[e][0],Hn[e][1],{alt:(Hn[e][2]||[]).slice()})}Ci.prototype.tokenize=function(e,t,r){for(var o=this.ruler.getRules(""),s=o.length,u=t,c=!1,h,g;u<r&&(e.line=u=e.skipEmptyLines(u),!(u>=r||e.tShift[u]<e.blkIndent));){for(g=0;g<s&&(h=o[g](e,u,r,!1),!h);g++);if(e.tight=!c,e.isEmpty(e.line-1)&&(c=!0),u=e.line,u<r&&e.isEmpty(u)){if(c=!0,u++,u<r&&e.parentType==="list"&&e.isEmpty(u))break;e.line=u}}};var Nf=/[\n\t]/g,Ef=/\r[\n\u0085]|[\u2424\u2028\u0085]/g,zf=/\u00a0/g;Ci.prototype.parse=function(e,t,r,o){var s,u=0,c=0;if(!e)return[];e=e.replace(zf," "),e=e.replace(Ef,`
`),e.indexOf("	")>=0&&(e=e.replace(Nf,function(h,g){var b;return e.charCodeAt(g)===10?(u=g+1,c=0,h):(b="    ".slice((g-u-c)%4),c=g-u+1,b)})),s=new lr(e,this,t,r,o),this.tokenize(s,s.line,s.lineMax)};function If(e){switch(e){case 10:case 92:case 96:case 42:case 95:case 94:case 91:case 93:case 33:case 38:case 60:case 62:case 123:case 125:case 36:case 37:case 64:case 126:case 43:case 61:case 58:return!0;default:return!1}}function Df(e,t){for(var r=e.pos;r<e.posMax&&!If(e.src.charCodeAt(r));)r++;return r===e.pos?!1:(t||(e.pending+=e.src.slice(e.pos,r)),e.pos=r,!0)}function Bf(e,t){var r,o,s=e.pos;if(e.src.charCodeAt(s)!==10)return!1;if(r=e.pending.length-1,o=e.posMax,!t)if(r>=0&&e.pending.charCodeAt(r)===32)if(r>=1&&e.pending.charCodeAt(r-1)===32){for(var u=r-2;u>=0;u--)if(e.pending.charCodeAt(u)!==32){e.pending=e.pending.substring(0,u+1);break}e.push({type:"hardbreak",level:e.level})}else e.pending=e.pending.slice(0,-1),e.push({type:"softbreak",level:e.level});else e.push({type:"softbreak",level:e.level});for(s++;s<o&&e.src.charCodeAt(s)===32;)s++;return e.pos=s,!0}var Ti=[];for(ki=0;ki<256;ki++)Ti.push(0);var ki;"\\!\"#$%&'()*+,./:;<=>?@[]^_`{|}~-".split("").forEach(function(e){Ti[e.charCodeAt(0)]=1});function Of(e,t){var r,o=e.pos,s=e.posMax;if(e.src.charCodeAt(o)!==92)return!1;if(o++,o<s){if(r=e.src.charCodeAt(o),r<256&&Ti[r]!==0)return t||(e.pending+=e.src[o]),e.pos+=2,!0;if(r===10){for(t||e.push({type:"hardbreak",level:e.level}),o++;o<s&&e.src.charCodeAt(o)===32;)o++;return e.pos=o,!0}}return t||(e.pending+="\\"),e.pos++,!0}function Rf(e,t){var r,o,s,u,c,h=e.pos,g=e.src.charCodeAt(h);if(g!==96)return!1;for(r=h,h++,o=e.posMax;h<o&&e.src.charCodeAt(h)===96;)h++;for(s=e.src.slice(r,h),u=c=h;(u=e.src.indexOf("`",c))!==-1;){for(c=u+1;c<o&&e.src.charCodeAt(c)===96;)c++;if(c-u===s.length)return t||e.push({type:"code",content:e.src.slice(h,u).replace(/[ \n]+/g," ").trim(),block:!1,level:e.level}),e.pos=c,!0}return t||(e.pending+=s),e.pos+=s.length,!0}function Ff(e,t){var r,o,s,u=e.posMax,c=e.pos,h,g;if(e.src.charCodeAt(c)!==126||t||c+4>=u||e.src.charCodeAt(c+1)!==126||e.level>=e.options.maxNesting||(h=c>0?e.src.charCodeAt(c-1):-1,g=e.src.charCodeAt(c+2),h===126)||g===126||g===32||g===10)return!1;for(o=c+2;o<u&&e.src.charCodeAt(o)===126;)o++;if(o>c+3)return e.pos+=o-c,t||(e.pending+=e.src.slice(c,o)),!0;for(e.pos=c+2,s=1;e.pos+1<u;){if(e.src.charCodeAt(e.pos)===126&&e.src.charCodeAt(e.pos+1)===126&&(h=e.src.charCodeAt(e.pos-1),g=e.pos+2<u?e.src.charCodeAt(e.pos+2):-1,g!==126&&h!==126&&(h!==32&&h!==10?s--:g!==32&&g!==10&&s++,s<=0))){r=!0;break}e.parser.skipToken(e)}return r?(e.posMax=e.pos,e.pos=c+2,t||(e.push({type:"del_open",level:e.level++}),e.parser.tokenize(e),e.push({type:"del_close",level:--e.level})),e.pos=e.posMax+2,e.posMax=u,!0):(e.pos=c,!1)}function Lf(e,t){var r,o,s,u=e.posMax,c=e.pos,h,g;if(e.src.charCodeAt(c)!==43||t||c+4>=u||e.src.charCodeAt(c+1)!==43||e.level>=e.options.maxNesting||(h=c>0?e.src.charCodeAt(c-1):-1,g=e.src.charCodeAt(c+2),h===43)||g===43||g===32||g===10)return!1;for(o=c+2;o<u&&e.src.charCodeAt(o)===43;)o++;if(o!==c+2)return e.pos+=o-c,t||(e.pending+=e.src.slice(c,o)),!0;for(e.pos=c+2,s=1;e.pos+1<u;){if(e.src.charCodeAt(e.pos)===43&&e.src.charCodeAt(e.pos+1)===43&&(h=e.src.charCodeAt(e.pos-1),g=e.pos+2<u?e.src.charCodeAt(e.pos+2):-1,g!==43&&h!==43&&(h!==32&&h!==10?s--:g!==32&&g!==10&&s++,s<=0))){r=!0;break}e.parser.skipToken(e)}return r?(e.posMax=e.pos,e.pos=c+2,t||(e.push({type:"ins_open",level:e.level++}),e.parser.tokenize(e),e.push({type:"ins_close",level:--e.level})),e.pos=e.posMax+2,e.posMax=u,!0):(e.pos=c,!1)}function qf(e,t){var r,o,s,u=e.posMax,c=e.pos,h,g;if(e.src.charCodeAt(c)!==61||t||c+4>=u||e.src.charCodeAt(c+1)!==61||e.level>=e.options.maxNesting||(h=c>0?e.src.charCodeAt(c-1):-1,g=e.src.charCodeAt(c+2),h===61)||g===61||g===32||g===10)return!1;for(o=c+2;o<u&&e.src.charCodeAt(o)===61;)o++;if(o!==c+2)return e.pos+=o-c,t||(e.pending+=e.src.slice(c,o)),!0;for(e.pos=c+2,s=1;e.pos+1<u;){if(e.src.charCodeAt(e.pos)===61&&e.src.charCodeAt(e.pos+1)===61&&(h=e.src.charCodeAt(e.pos-1),g=e.pos+2<u?e.src.charCodeAt(e.pos+2):-1,g!==61&&h!==61&&(h!==32&&h!==10?s--:g!==32&&g!==10&&s++,s<=0))){r=!0;break}e.parser.skipToken(e)}return r?(e.posMax=e.pos,e.pos=c+2,t||(e.push({type:"mark_open",level:e.level++}),e.parser.tokenize(e),e.push({type:"mark_close",level:--e.level})),e.pos=e.posMax+2,e.posMax=u,!0):(e.pos=c,!1)}function ps(e){return e>=48&&e<=57||e>=65&&e<=90||e>=97&&e<=122}function ds(e,t){var r=t,o,s,u,c=!0,h=!0,g=e.posMax,b=e.src.charCodeAt(t);for(o=t>0?e.src.charCodeAt(t-1):-1;r<g&&e.src.charCodeAt(r)===b;)r++;return r>=g&&(c=!1),u=r-t,u>=4?c=h=!1:(s=r<g?e.src.charCodeAt(r):-1,(s===32||s===10)&&(c=!1),(o===32||o===10)&&(h=!1),b===95&&(ps(o)&&(c=!1),ps(s)&&(h=!1))),{can_open:c,can_close:h,delims:u}}function Pf(e,t){var r,o,s,u,c,h,g,b=e.posMax,y=e.pos,S=e.src.charCodeAt(y);if(S!==95&&S!==42||t)return!1;if(g=ds(e,y),r=g.delims,!g.can_open)return e.pos+=r,t||(e.pending+=e.src.slice(y,e.pos)),!0;if(e.level>=e.options.maxNesting)return!1;for(e.pos=y+r,h=[r];e.pos<b;){if(e.src.charCodeAt(e.pos)===S){if(g=ds(e,e.pos),o=g.delims,g.can_close){for(u=h.pop(),c=o;u!==c;){if(c<u){h.push(u-c);break}if(c-=u,h.length===0)break;e.pos+=u,u=h.pop()}if(h.length===0){r=u,s=!0;break}e.pos+=o;continue}g.can_open&&h.push(o),e.pos+=o;continue}e.parser.skipToken(e)}return s?(e.posMax=e.pos,e.pos=y+r,t||((r===2||r===3)&&e.push({type:"strong_open",level:e.level++}),(r===1||r===3)&&e.push({type:"em_open",level:e.level++}),e.parser.tokenize(e),(r===1||r===3)&&e.push({type:"em_close",level:--e.level}),(r===2||r===3)&&e.push({type:"strong_close",level:--e.level})),e.pos=e.posMax+r,e.posMax=b,!0):(e.pos=y,!1)}var Hf=/\\([ \\!"#$%&'()*+,.\/:;<=>?@[\]^_`{|}~-])/g;function $f(e,t){var r,o,s=e.posMax,u=e.pos;if(e.src.charCodeAt(u)!==126||t||u+2>=s||e.level>=e.options.maxNesting)return!1;for(e.pos=u+1;e.pos<s;){if(e.src.charCodeAt(e.pos)===126){r=!0;break}e.parser.skipToken(e)}return!r||u+1===e.pos||(o=e.src.slice(u+1,e.pos),o.match(/(^|[^\\])(\\\\)*\s/))?(e.pos=u,!1):(e.posMax=e.pos,e.pos=u+1,t||e.push({type:"sub",level:e.level,content:o.replace(Hf,"$1")}),e.pos=e.posMax+1,e.posMax=s,!0)}var Vf=/\\([ \\!"#$%&'()*+,.\/:;<=>?@[\]^_`{|}~-])/g;function Gf(e,t){var r,o,s=e.posMax,u=e.pos;if(e.src.charCodeAt(u)!==94||t||u+2>=s||e.level>=e.options.maxNesting)return!1;for(e.pos=u+1;e.pos<s;){if(e.src.charCodeAt(e.pos)===94){r=!0;break}e.parser.skipToken(e)}return!r||u+1===e.pos||(o=e.src.slice(u+1,e.pos),o.match(/(^|[^\\])(\\\\)*\s/))?(e.pos=u,!1):(e.posMax=e.pos,e.pos=u+1,t||e.push({type:"sup",level:e.level,content:o.replace(Vf,"$1")}),e.pos=e.posMax+1,e.posMax=s,!0)}function Yf(e,t){var r,o,s,u,c,h,g,b,y=!1,S=e.pos,T=e.posMax,N=e.pos,R=e.src.charCodeAt(N);if(R===33&&(y=!0,R=e.src.charCodeAt(++N)),R!==91||e.level>=e.options.maxNesting||(r=N+1,o=$r(e,N),o<0))return!1;if(h=o+1,h<T&&e.src.charCodeAt(h)===40){for(h++;h<T&&(b=e.src.charCodeAt(h),!(b!==32&&b!==10));h++);if(h>=T)return!1;for(N=h,bs(e,h)?(u=e.linkContent,h=e.pos):u="",N=h;h<T&&(b=e.src.charCodeAt(h),!(b!==32&&b!==10));h++);if(h<T&&N!==h&&vs(e,h))for(c=e.linkContent,h=e.pos;h<T&&(b=e.src.charCodeAt(h),!(b!==32&&b!==10));h++);else c="";if(h>=T||e.src.charCodeAt(h)!==41)return e.pos=S,!1;h++}else{if(e.linkLevel>0)return!1;for(;h<T&&(b=e.src.charCodeAt(h),!(b!==32&&b!==10));h++);if(h<T&&e.src.charCodeAt(h)===91&&(N=h+1,h=$r(e,h),h>=0?s=e.src.slice(N,h++):h=N-1),s||(typeof s=="undefined"&&(h=o+1),s=e.src.slice(r,o)),g=e.env.references[ws(s)],!g)return e.pos=S,!1;u=g.href,c=g.title}return t||(e.pos=r,e.posMax=o,y?e.push({type:"image",src:u,title:c,alt:e.src.substr(r,o-r),level:e.level}):(e.push({type:"link_open",href:u,title:c,level:e.level++}),e.linkLevel++,e.parser.tokenize(e),e.linkLevel--,e.push({type:"link_close",level:--e.level}))),e.pos=h,e.posMax=T,!0}function Uf(e,t){var r,o,s,u,c=e.posMax,h=e.pos;return h+2>=c||e.src.charCodeAt(h)!==94||e.src.charCodeAt(h+1)!==91||e.level>=e.options.maxNesting||(r=h+2,o=$r(e,h+1),o<0)?!1:(t||(e.env.footnotes||(e.env.footnotes={}),e.env.footnotes.list||(e.env.footnotes.list=[]),s=e.env.footnotes.list.length,e.pos=r,e.posMax=o,e.push({type:"footnote_ref",id:s,level:e.level}),e.linkLevel++,u=e.tokens.length,e.parser.tokenize(e),e.env.footnotes.list[s]={tokens:e.tokens.splice(u)},e.linkLevel--),e.pos=o+1,e.posMax=c,!0)}function Xf(e,t){var r,o,s,u,c=e.posMax,h=e.pos;if(h+3>c||!e.env.footnotes||!e.env.footnotes.refs||e.src.charCodeAt(h)!==91||e.src.charCodeAt(h+1)!==94||e.level>=e.options.maxNesting)return!1;for(o=h+2;o<c;o++){if(e.src.charCodeAt(o)===32||e.src.charCodeAt(o)===10)return!1;if(e.src.charCodeAt(o)===93)break}return o===h+2||o>=c||(o++,r=e.src.slice(h+2,o-1),typeof e.env.footnotes.refs[":"+r]=="undefined")?!1:(t||(e.env.footnotes.list||(e.env.footnotes.list=[]),e.env.footnotes.refs[":"+r]<0?(s=e.env.footnotes.list.length,e.env.footnotes.list[s]={label:r,count:0},e.env.footnotes.refs[":"+r]=s):s=e.env.footnotes.refs[":"+r],u=e.env.footnotes.list[s].count,e.env.footnotes.list[s].count++,e.push({type:"footnote_ref",id:s,subId:u,level:e.level})),e.pos=o,e.posMax=c,!0)}var jf=["coap","doi","javascript","aaa","aaas","about","acap","cap","cid","crid","data","dav","dict","dns","file","ftp","geo","go","gopher","h323","http","https","iax","icap","im","imap","info","ipp","iris","iris.beep","iris.xpc","iris.xpcs","iris.lwz","ldap","mailto","mid","msrp","msrps","mtqp","mupdate","news","nfs","ni","nih","nntp","opaquelocktoken","pop","pres","rtsp","service","session","shttp","sieve","sip","sips","sms","snmp","soap.beep","soap.beeps","tag","tel","telnet","tftp","thismessage","tn3270","tip","tv","urn","vemmi","ws","wss","xcon","xcon-userid","xmlrpc.beep","xmlrpc.beeps","xmpp","z39.50r","z39.50s","adiumxtra","afp","afs","aim","apt","attachment","aw","beshare","bitcoin","bolo","callto","chrome","chrome-extension","com-eventbrite-attendee","content","cvs","dlna-playsingle","dlna-playcontainer","dtn","dvb","ed2k","facetime","feed","finger","fish","gg","git","gizmoproject","gtalk","hcp","icon","ipn","irc","irc6","ircs","itms","jar","jms","keyparc","lastfm","ldaps","magnet","maps","market","message","mms","ms-help","msnim","mumble","mvn","notes","oid","palm","paparazzi","platform","proxy","psyc","query","res","resource","rmi","rsync","rtmp","secondlife","sftp","sgn","skype","smb","soldat","spotify","ssh","steam","svn","teamspeak","things","udp","unreal","ut2004","ventrilo","view-source","webcal","wtai","wyciwyg","xfire","xri","ymsgr"],Wf=/^<([a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)>/,Kf=/^<([a-zA-Z.\-]{1,25}):([^<>\x00-\x20]*)>/;function Zf(e,t){var r,o,s,u,c,h=e.pos;return e.src.charCodeAt(h)!==60||(r=e.src.slice(h),r.indexOf(">")<0)?!1:(o=r.match(Kf),o?jf.indexOf(o[1].toLowerCase())<0||(u=o[0].slice(1,-1),c=_i(u),!e.parser.validateLink(u))?!1:(t||(e.push({type:"link_open",href:c,level:e.level}),e.push({type:"text",content:u,level:e.level+1}),e.push({type:"link_close",level:e.level})),e.pos+=o[0].length,!0):(s=r.match(Wf),s?(u=s[0].slice(1,-1),c=_i("mailto:"+u),e.parser.validateLink(c)?(t||(e.push({type:"link_open",href:c,level:e.level}),e.push({type:"text",content:u,level:e.level+1}),e.push({type:"link_close",level:e.level})),e.pos+=s[0].length,!0):!1):!1))}function $n(e,t){return e=e.source,t=t||"",function r(o,s){return o?(s=s.source||s,e=e.replace(o,s),r):new RegExp(e,t)}}var Jf=/[a-zA-Z_:][a-zA-Z0-9:._-]*/,Qf=/[^"'=<>`\x00-\x20]+/,ep=/'[^']*'/,tp=/"[^"]*"/,rp=$n(/(?:unquoted|single_quoted|double_quoted)/)("unquoted",Qf)("single_quoted",ep)("double_quoted",tp)(),np=$n(/(?:\s+attr_name(?:\s*=\s*attr_value)?)/)("attr_name",Jf)("attr_value",rp)(),ip=$n(/<[A-Za-z][A-Za-z0-9]*attribute*\s*\/?>/)("attribute",np)(),op=/<\/[A-Za-z][A-Za-z0-9]*\s*>/,lp=/<!---->|<!--(?:-?[^>-])(?:-?[^-])*-->/,sp=/<[?].*?[?]>/,ap=/<![A-Z]+\s+[^>]*>/,up=/<!\[CDATA\[[\s\S]*?\]\]>/,cp=$n(/^(?:open_tag|close_tag|comment|processing|declaration|cdata)/)("open_tag",ip)("close_tag",op)("comment",lp)("processing",sp)("declaration",ap)("cdata",up)();function hp(e){var t=e|32;return t>=97&&t<=122}function fp(e,t){var r,o,s,u=e.pos;return!e.options.html||(s=e.posMax,e.src.charCodeAt(u)!==60||u+2>=s)||(r=e.src.charCodeAt(u+1),r!==33&&r!==63&&r!==47&&!hp(r))||(o=e.src.slice(u).match(cp),!o)?!1:(t||e.push({type:"htmltag",content:e.src.slice(u,u+o[0].length),level:e.level}),e.pos+=o[0].length,!0)}var pp=/^&#((?:x[a-f0-9]{1,8}|[0-9]{1,8}));/i,dp=/^&([a-z][a-z0-9]{1,31});/i;function mp(e,t){var r,o,s,u=e.pos,c=e.posMax;if(e.src.charCodeAt(u)!==38)return!1;if(u+1<c){if(r=e.src.charCodeAt(u+1),r===35){if(s=e.src.slice(u).match(pp),s)return t||(o=s[1][0].toLowerCase()==="x"?parseInt(s[1].slice(1),16):parseInt(s[1],10),e.pending+=xs(o)?Ai(o):Ai(65533)),e.pos+=s[0].length,!0}else if(s=e.src.slice(u).match(dp),s){var h=ms(s[1]);if(s[1]!==h)return t||(e.pending+=h),e.pos+=s[0].length,!0}}return t||(e.pending+="&"),e.pos++,!0}var Si=[["text",Df],["newline",Bf],["escape",Of],["backticks",Rf],["del",Ff],["ins",Lf],["mark",qf],["emphasis",Pf],["sub",$f],["sup",Gf],["links",Yf],["footnote_inline",Uf],["footnote_ref",Xf],["autolink",Zf],["htmltag",fp],["entity",mp]];function Vn(){this.ruler=new ct;for(var e=0;e<Si.length;e++)this.ruler.push(Si[e][0],Si[e][1]);this.validateLink=gp}Vn.prototype.skipToken=function(e){var t=this.ruler.getRules(""),r=t.length,o=e.pos,s,u;if((u=e.cacheGet(o))>0){e.pos=u;return}for(s=0;s<r;s++)if(t[s](e,!0)){e.cacheSet(o,e.pos);return}e.pos++,e.cacheSet(o,e.pos)};Vn.prototype.tokenize=function(e){for(var t=this.ruler.getRules(""),r=t.length,o=e.posMax,s,u;e.pos<o;){for(u=0;u<r&&(s=t[u](e,!1),!s);u++);if(s){if(e.pos>=o)break;continue}e.pending+=e.src[e.pos++]}e.pending&&e.pushPending()};Vn.prototype.parse=function(e,t,r,o){var s=new or(e,this,t,r,o);this.tokenize(s)};function gp(e){var t=["vbscript","javascript","file","data"],r=e.trim().toLowerCase();return r=nr(r),!(r.indexOf(":")!==-1&&t.indexOf(r.split(":")[0])!==-1)}var xp={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkTarget:"",typographer:!1,quotes:"\u201C\u201D\u2018\u2019",highlight:null,maxNesting:20},components:{core:{rules:["block","inline","references","replacements","smartquotes","references","abbr2","footnote_tail"]},block:{rules:["blockquote","code","fences","footnote","heading","hr","htmlblock","lheading","list","paragraph","table"]},inline:{rules:["autolink","backticks","del","emphasis","entity","escape","footnote_ref","htmltag","links","newline","text"]}}},yp={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkTarget:"",typographer:!1,quotes:"\u201C\u201D\u2018\u2019",highlight:null,maxNesting:20},components:{core:{},block:{},inline:{}}},bp={options:{html:!0,xhtmlOut:!0,breaks:!1,langPrefix:"language-",linkTarget:"",typographer:!1,quotes:"\u201C\u201D\u2018\u2019",highlight:null,maxNesting:20},components:{core:{rules:["block","inline","references","abbr2"]},block:{rules:["blockquote","code","fences","heading","hr","htmlblock","lheading","list","paragraph"]},inline:{rules:["autolink","backticks","emphasis","entity","escape","htmltag","links","newline","text"]}}},vp={default:xp,full:yp,commonmark:bp};function As(e,t,r){this.src=t,this.env=r,this.options=e.options,this.tokens=[],this.inlineMode=!1,this.inline=e.inline,this.block=e.block,this.renderer=e.renderer,this.typographer=e.typographer}function Ft(e,t){typeof e!="string"&&(t=e,e="default"),t&&t.linkify!=null&&console.warn(`linkify option is removed. Use linkify plugin instead:

import Remarkable from 'remarkable';
import linkify from 'remarkable/linkify';
new Remarkable().use(linkify)
`),this.inline=new Vn,this.block=new Ci,this.core=new ks,this.renderer=new Mi,this.ruler=new ct,this.options={},this.configure(vp[e]),this.set(t||{})}Ft.prototype.set=function(e){gs(this.options,e)};Ft.prototype.configure=function(e){var t=this;if(!e)throw new Error("Wrong `remarkable` preset, check name/content");e.options&&t.set(e.options),e.components&&Object.keys(e.components).forEach(function(r){e.components[r].rules&&t[r].ruler.enable(e.components[r].rules,!0)})};Ft.prototype.use=function(e,t){return e(this,t),this};Ft.prototype.parse=function(e,t){var r=new As(this,e,t);return this.core.process(r),r.tokens};Ft.prototype.render=function(e,t){return t=t||{},this.renderer.render(this.parse(e,t),this.options,t)};Ft.prototype.parseInline=function(e,t){var r=new As(this,e,t);return r.inlineMode=!0,this.core.process(r),r.tokens};Ft.prototype.renderInline=function(e,t){return t=t||{},this.renderer.render(this.parseInline(e,t),this.options,t)};var wp="npm2url/dist/index.cjs",kp={jsdelivr:e=>`https://cdn.jsdelivr.net/npm/${e}`,unpkg:e=>`https://unpkg.com/${e}`};async function Sp(e,t){let r=await fetch(e,{signal:t});if(!r.ok)throw r;await r.text()}var Vr=class{constructor(){this.providers={...kp},this.provider="jsdelivr"}async getFastestProvider(t=5e3,r=wp){let o=new AbortController,s=0;try{return await new Promise((u,c)=>{Promise.all(Object.entries(this.providers).map(async([h,g])=>{try{await Sp(g(r),o.signal),u(h)}catch(b){}})).then(()=>c(new Error("All providers failed"))),s=setTimeout(c,t,new Error("Timed out"))})}finally{o.abort(),clearTimeout(s)}}async findFastestProvider(t,r){return this.provider=await this.getFastestProvider(t,r),this.provider}setProvider(t,r){r?this.providers[t]=r:delete this.providers[t]}getFullUrl(t,r=this.provider){if(t.includes("://"))return t;let o=this.providers[r];if(!o)throw new Error(`Provider ${r} not found`);return o(t)}},k6=new Vr,vt=class{constructor(){this.listeners=[]}tap(t){return this.listeners.push(t),()=>this.revoke(t)}revoke(t){let r=this.listeners.indexOf(t);r>=0&&this.listeners.splice(r,1)}revokeAll(){this.listeners.splice(0)}call(...t){for(let r of this.listeners)r(...t)}},Ap={"&":"&amp;","<":"&lt;",'"':"&quot;"};function _s(e){return e.replace(/[&<"]/g,t=>Ap[t])}function _p(e){return e.replace(/<(\/script>)/g,"\\x3c$2")}function Ms(e,t){let r=t?Object.entries(t).map(([o,s])=>{if(!(s==null||s===!1))return o=` ${_s(o)}`,s===!0?o:`${o}="${_s(s)}"`}).filter(Boolean).join(""):"";return`<${e}${r}>`}function Mp(e){return`</${e}>`}function Gn(e,t,r){return t==null?Ms(e,r):Ms(e,r)+(t||"")+Mp(e)}function Cp(e,t){let r=t.map(o=>typeof o=="function"?o.toString():JSON.stringify(o!=null?o:null)).join(",");return`(${e.toString()})(${r})`}function Es(e,t){return e.map(r=>{if(r.type==="script"){let{textContent:o,...s}=r.data;return Gn("script",o||"",s)}if(r.type==="iife"){let{fn:o,getParams:s}=r.data;return Gn("script",_p(Cp(o,(s==null?void 0:s(t))||[])))}return""})}function zs(e){return e.map(t=>t.type==="stylesheet"?Gn("link",null,{rel:"stylesheet",...t.data}):Gn("style",t.data))}var Tp=Math.random().toString(36).slice(2,8),Cs=0;function Is(){return Cs+=1,`mm-${Tp}-${Cs}`}function sr(){}function Yn(e,t){let r=(o,s)=>t(o,()=>{var u;(u=o.children)==null||u.forEach(c=>{r(c,o)})},s);r(e)}function Ds(e,...t){let r=(e||"").split(" ").filter(Boolean);return t.forEach(o=>{o&&r.indexOf(o)<0&&r.push(o)}),r.join(" ")}function ar(e){if(typeof e=="string"){let r=e;e=o=>o.tagName===r}let t=e;return function(){let o=Array.from(this.childNodes);return t&&(o=o.filter(s=>t(s))),o}}function Bs(e,t){return(...r)=>t(e,...r)}function Np(){let e={};return e.promise=new Promise((t,r)=>{e.resolve=t,e.reject=r}),e}function Ep(e){let t={};return function(...o){let s=`${o[0]}`,u=t[s];return u||(u={value:e(...o)},t[s]=u),u.value}}var Os=1,Rs=2,zp="http://www.w3.org/2000/svg",Ni="http://www.w3.org/1999/xlink",Ip={show:Ni,actuate:Ni,href:Ni},Dp=e=>typeof e=="string"||typeof e=="number",Bp=e=>(e==null?void 0:e.vtype)===Os,Op=e=>(e==null?void 0:e.vtype)===Rs;function Rp(e,t,...r){return t=Object.assign({},t,{children:r.length===1?r[0]:r}),Fp(e,t)}function Fp(e,t){let r;if(typeof e=="string")r=Os;else if(typeof e=="function")r=Rs;else throw new Error("Invalid VNode type");return{vtype:r,type:e,props:t}}function Lp(e){return e.children}var qp={isSvg:!1};function Ts(e,t){Array.isArray(t)||(t=[t]),t=t.filter(Boolean),t.length&&e.append(...t)}function Pp(e,t,r){for(let o in t)if(!(o==="key"||o==="children"||o==="ref"))if(o==="dangerouslySetInnerHTML")e.innerHTML=t[o].__html;else if(o==="innerHTML"||o==="textContent"||o==="innerText"||o==="value"&&["textarea","select"].includes(e.tagName)){let s=t[o];s!=null&&(e[o]=s)}else o.startsWith("on")?e[o.toLowerCase()]=t[o]:$p(e,o,t[o],r.isSvg)}var Hp={className:"class",labelFor:"for"};function $p(e,t,r,o){if(t=Hp[t]||t,r===!0)e.setAttribute(t,"");else if(r===!1)e.removeAttribute(t);else{let s=o?Ip[t]:void 0;s!==void 0?e.setAttributeNS(s,t,r):e.setAttribute(t,r)}}function Vp(e){return e.reduce((t,r)=>t.concat(r),[])}function Ei(e,t){return Array.isArray(e)?Vp(e.map(r=>Ei(r,t))):zi(e,t)}function zi(e,t=qp){if(e==null||typeof e=="boolean")return null;if(e instanceof Node)return e;if(Op(e)){let{type:r,props:o}=e;if(r===Lp){let u=document.createDocumentFragment();if(o.children){let c=Ei(o.children,t);Ts(u,c)}return u}let s=r(o);return zi(s,t)}if(Dp(e))return document.createTextNode(`${e}`);if(Bp(e)){let r,{type:o,props:s}=e;if(!t.isSvg&&o==="svg"&&(t=Object.assign({},t,{isSvg:!0})),t.isSvg?r=document.createElementNS(zp,o):r=document.createElement(o),Pp(r,s,t),s.children){let c=t;t.isSvg&&o==="foreignObject"&&(c=Object.assign({},c,{isSvg:!1}));let h=Ei(s.children,c);h!=null&&Ts(r,h)}let{ref:u}=s;return typeof u=="function"&&u(r),r}throw new Error("mount: Invalid Vnode!")}function Gp(e){return zi(e)}function Fs(...e){return Gp(Rp(...e))}var Yp=Ep(e=>{document.head.append(Fs("link",{rel:"preload",as:"script",href:e}))}),Ns={};async function Up(e,t){var r;let o=e.type==="script"&&((r=e.data)==null?void 0:r.src)||"";if(e.loaded||(e.loaded=Ns[o]),!e.loaded){let s=Np();if(e.loaded=s.promise,e.type==="script"&&(document.head.append(Fs("script",{...e.data,onLoad:()=>s.resolve(),onError:s.reject})),o?Ns[o]=e.loaded:s.resolve()),e.type==="iife"){let{fn:u,getParams:c}=e.data;u(...(c==null?void 0:c(t))||[]),s.resolve()}}await e.loaded}async function Un(e,t){e.forEach(r=>{var o;r.type==="script"&&((o=r.data)!=null&&o.src)&&Yp(r.data.src)}),t={getMarkmap:()=>window.markmap,...t};for(let r of e)await Up(r,t)}function Gr(e){return{type:"script",data:{src:e}}}function Ii(e){return{type:"stylesheet",data:{href:e}}}var Ya=F1(Ps(),1);function ra(e){return typeof e=="undefined"||e===null}function jp(e){return typeof e=="object"&&e!==null}function Wp(e){return Array.isArray(e)?e:ra(e)?[]:[e]}function Kp(e,t){var r,o,s,u;if(t)for(u=Object.keys(t),r=0,o=u.length;r<o;r+=1)s=u[r],e[s]=t[s];return e}function Zp(e,t){var r="",o;for(o=0;o<t;o+=1)r+=e;return r}function Jp(e){return e===0&&Number.NEGATIVE_INFINITY===1/e}var Qp=ra,ed=jp,td=Wp,rd=Zp,nd=Jp,id=Kp,Pe={isNothing:Qp,isObject:ed,toArray:td,repeat:rd,isNegativeZero:nd,extend:id};function na(e,t){var r="",o=e.reason||"(unknown reason)";return e.mark?(e.mark.name&&(r+='in "'+e.mark.name+'" '),r+="("+(e.mark.line+1)+":"+(e.mark.column+1)+")",!t&&e.mark.snippet&&(r+=`

`+e.mark.snippet),o+" "+r):o}function Xr(e,t){Error.call(this),this.name="YAMLException",this.reason=e,this.mark=t,this.message=na(this,!1),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack||""}Xr.prototype=Object.create(Error.prototype);Xr.prototype.constructor=Xr;Xr.prototype.toString=function(t){return this.name+": "+na(this,t)};var Ke=Xr;function Bi(e,t,r,o,s){var u="",c="",h=Math.floor(s/2)-1;return o-t>h&&(u=" ... ",t=o-h+u.length),r-o>h&&(c=" ...",r=o+h-c.length),{str:u+e.slice(t,r).replace(/\t/g,"\u2192")+c,pos:o-t+u.length}}function Oi(e,t){return Pe.repeat(" ",t-e.length)+e}function od(e,t){if(t=Object.create(t||null),!e.buffer)return null;t.maxLength||(t.maxLength=79),typeof t.indent!="number"&&(t.indent=1),typeof t.linesBefore!="number"&&(t.linesBefore=3),typeof t.linesAfter!="number"&&(t.linesAfter=2);for(var r=/\r?\n|\r|\0/g,o=[0],s=[],u,c=-1;u=r.exec(e.buffer);)s.push(u.index),o.push(u.index+u[0].length),e.position<=u.index&&c<0&&(c=o.length-2);c<0&&(c=o.length-1);var h="",g,b,y=Math.min(e.line+t.linesAfter,s.length).toString().length,S=t.maxLength-(t.indent+y+3);for(g=1;g<=t.linesBefore&&!(c-g<0);g++)b=Bi(e.buffer,o[c-g],s[c-g],e.position-(o[c]-o[c-g]),S),h=Pe.repeat(" ",t.indent)+Oi((e.line-g+1).toString(),y)+" | "+b.str+`
`+h;for(b=Bi(e.buffer,o[c],s[c],e.position,S),h+=Pe.repeat(" ",t.indent)+Oi((e.line+1).toString(),y)+" | "+b.str+`
`,h+=Pe.repeat("-",t.indent+y+3+b.pos)+`^
`,g=1;g<=t.linesAfter&&!(c+g>=s.length);g++)b=Bi(e.buffer,o[c+g],s[c+g],e.position-(o[c]-o[c+g]),S),h+=Pe.repeat(" ",t.indent)+Oi((e.line+g+1).toString(),y)+" | "+b.str+`
`;return h.replace(/\n$/,"")}var ld=od,sd=["kind","multi","resolve","construct","instanceOf","predicate","represent","representName","defaultStyle","styleAliases"],ad=["scalar","sequence","mapping"];function ud(e){var t={};return e!==null&&Object.keys(e).forEach(function(r){e[r].forEach(function(o){t[String(o)]=r})}),t}function cd(e,t){if(t=t||{},Object.keys(t).forEach(function(r){if(sd.indexOf(r)===-1)throw new Ke('Unknown option "'+r+'" is met in definition of "'+e+'" YAML type.')}),this.options=t,this.tag=e,this.kind=t.kind||null,this.resolve=t.resolve||function(){return!0},this.construct=t.construct||function(r){return r},this.instanceOf=t.instanceOf||null,this.predicate=t.predicate||null,this.represent=t.represent||null,this.representName=t.representName||null,this.defaultStyle=t.defaultStyle||null,this.multi=t.multi||!1,this.styleAliases=ud(t.styleAliases||null),ad.indexOf(this.kind)===-1)throw new Ke('Unknown kind "'+this.kind+'" is specified for "'+e+'" YAML type.')}var Ge=cd;function Hs(e,t){var r=[];return e[t].forEach(function(o){var s=r.length;r.forEach(function(u,c){u.tag===o.tag&&u.kind===o.kind&&u.multi===o.multi&&(s=c)}),r[s]=o}),r}function hd(){var e={scalar:{},sequence:{},mapping:{},fallback:{},multi:{scalar:[],sequence:[],mapping:[],fallback:[]}},t,r;function o(s){s.multi?(e.multi[s.kind].push(s),e.multi.fallback.push(s)):e[s.kind][s.tag]=e.fallback[s.tag]=s}for(t=0,r=arguments.length;t<r;t+=1)arguments[t].forEach(o);return e}function Fi(e){return this.extend(e)}Fi.prototype.extend=function(t){var r=[],o=[];if(t instanceof Ge)o.push(t);else if(Array.isArray(t))o=o.concat(t);else if(t&&(Array.isArray(t.implicit)||Array.isArray(t.explicit)))t.implicit&&(r=r.concat(t.implicit)),t.explicit&&(o=o.concat(t.explicit));else throw new Ke("Schema.extend argument should be a Type, [ Type ], or a schema definition ({ implicit: [...], explicit: [...] })");r.forEach(function(u){if(!(u instanceof Ge))throw new Ke("Specified list of YAML types (or a single Type object) contains a non-Type object.");if(u.loadKind&&u.loadKind!=="scalar")throw new Ke("There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.");if(u.multi)throw new Ke("There is a multi type in the implicit list of a schema. Multi tags can only be listed as explicit.")}),o.forEach(function(u){if(!(u instanceof Ge))throw new Ke("Specified list of YAML types (or a single Type object) contains a non-Type object.")});var s=Object.create(Fi.prototype);return s.implicit=(this.implicit||[]).concat(r),s.explicit=(this.explicit||[]).concat(o),s.compiledImplicit=Hs(s,"implicit"),s.compiledExplicit=Hs(s,"explicit"),s.compiledTypeMap=hd(s.compiledImplicit,s.compiledExplicit),s};var ia=Fi,oa=new Ge("tag:yaml.org,2002:str",{kind:"scalar",construct:function(e){return e!==null?e:""}}),la=new Ge("tag:yaml.org,2002:seq",{kind:"sequence",construct:function(e){return e!==null?e:[]}}),sa=new Ge("tag:yaml.org,2002:map",{kind:"mapping",construct:function(e){return e!==null?e:{}}}),aa=new ia({explicit:[oa,la,sa]});function fd(e){if(e===null)return!0;var t=e.length;return t===1&&e==="~"||t===4&&(e==="null"||e==="Null"||e==="NULL")}function pd(){return null}function dd(e){return e===null}var ua=new Ge("tag:yaml.org,2002:null",{kind:"scalar",resolve:fd,construct:pd,predicate:dd,represent:{canonical:function(){return"~"},lowercase:function(){return"null"},uppercase:function(){return"NULL"},camelcase:function(){return"Null"},empty:function(){return""}},defaultStyle:"lowercase"});function md(e){if(e===null)return!1;var t=e.length;return t===4&&(e==="true"||e==="True"||e==="TRUE")||t===5&&(e==="false"||e==="False"||e==="FALSE")}function gd(e){return e==="true"||e==="True"||e==="TRUE"}function xd(e){return Object.prototype.toString.call(e)==="[object Boolean]"}var ca=new Ge("tag:yaml.org,2002:bool",{kind:"scalar",resolve:md,construct:gd,predicate:xd,represent:{lowercase:function(e){return e?"true":"false"},uppercase:function(e){return e?"TRUE":"FALSE"},camelcase:function(e){return e?"True":"False"}},defaultStyle:"lowercase"});function yd(e){return 48<=e&&e<=57||65<=e&&e<=70||97<=e&&e<=102}function bd(e){return 48<=e&&e<=55}function vd(e){return 48<=e&&e<=57}function wd(e){if(e===null)return!1;var t=e.length,r=0,o=!1,s;if(!t)return!1;if(s=e[r],(s==="-"||s==="+")&&(s=e[++r]),s==="0"){if(r+1===t)return!0;if(s=e[++r],s==="b"){for(r++;r<t;r++)if(s=e[r],s!=="_"){if(s!=="0"&&s!=="1")return!1;o=!0}return o&&s!=="_"}if(s==="x"){for(r++;r<t;r++)if(s=e[r],s!=="_"){if(!yd(e.charCodeAt(r)))return!1;o=!0}return o&&s!=="_"}if(s==="o"){for(r++;r<t;r++)if(s=e[r],s!=="_"){if(!bd(e.charCodeAt(r)))return!1;o=!0}return o&&s!=="_"}}if(s==="_")return!1;for(;r<t;r++)if(s=e[r],s!=="_"){if(!vd(e.charCodeAt(r)))return!1;o=!0}return!(!o||s==="_")}function kd(e){var t=e,r=1,o;if(t.indexOf("_")!==-1&&(t=t.replace(/_/g,"")),o=t[0],(o==="-"||o==="+")&&(o==="-"&&(r=-1),t=t.slice(1),o=t[0]),t==="0")return 0;if(o==="0"){if(t[1]==="b")return r*parseInt(t.slice(2),2);if(t[1]==="x")return r*parseInt(t.slice(2),16);if(t[1]==="o")return r*parseInt(t.slice(2),8)}return r*parseInt(t,10)}function Sd(e){return Object.prototype.toString.call(e)==="[object Number]"&&e%1===0&&!Pe.isNegativeZero(e)}var ha=new Ge("tag:yaml.org,2002:int",{kind:"scalar",resolve:wd,construct:kd,predicate:Sd,represent:{binary:function(e){return e>=0?"0b"+e.toString(2):"-0b"+e.toString(2).slice(1)},octal:function(e){return e>=0?"0o"+e.toString(8):"-0o"+e.toString(8).slice(1)},decimal:function(e){return e.toString(10)},hexadecimal:function(e){return e>=0?"0x"+e.toString(16).toUpperCase():"-0x"+e.toString(16).toUpperCase().slice(1)}},defaultStyle:"decimal",styleAliases:{binary:[2,"bin"],octal:[8,"oct"],decimal:[10,"dec"],hexadecimal:[16,"hex"]}}),Ad=new RegExp("^(?:[-+]?(?:[0-9][0-9_]*)(?:\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?|\\.[0-9_]+(?:[eE][-+]?[0-9]+)?|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN))$");function _d(e){return!(e===null||!Ad.test(e)||e[e.length-1]==="_")}function Md(e){var t,r;return t=e.replace(/_/g,"").toLowerCase(),r=t[0]==="-"?-1:1,"+-".indexOf(t[0])>=0&&(t=t.slice(1)),t===".inf"?r===1?Number.POSITIVE_INFINITY:Number.NEGATIVE_INFINITY:t===".nan"?NaN:r*parseFloat(t,10)}var Cd=/^[-+]?[0-9]+e/;function Td(e,t){var r;if(isNaN(e))switch(t){case"lowercase":return".nan";case"uppercase":return".NAN";case"camelcase":return".NaN"}else if(Number.POSITIVE_INFINITY===e)switch(t){case"lowercase":return".inf";case"uppercase":return".INF";case"camelcase":return".Inf"}else if(Number.NEGATIVE_INFINITY===e)switch(t){case"lowercase":return"-.inf";case"uppercase":return"-.INF";case"camelcase":return"-.Inf"}else if(Pe.isNegativeZero(e))return"-0.0";return r=e.toString(10),Cd.test(r)?r.replace("e",".e"):r}function Nd(e){return Object.prototype.toString.call(e)==="[object Number]"&&(e%1!==0||Pe.isNegativeZero(e))}var fa=new Ge("tag:yaml.org,2002:float",{kind:"scalar",resolve:_d,construct:Md,predicate:Nd,represent:Td,defaultStyle:"lowercase"}),pa=aa.extend({implicit:[ua,ca,ha,fa]}),da=pa,ma=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9])-([0-9][0-9])$"),ga=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9]?)-([0-9][0-9]?)(?:[Tt]|[ \\t]+)([0-9][0-9]?):([0-9][0-9]):([0-9][0-9])(?:\\.([0-9]*))?(?:[ \\t]*(Z|([-+])([0-9][0-9]?)(?::([0-9][0-9]))?))?$");function Ed(e){return e===null?!1:ma.exec(e)!==null||ga.exec(e)!==null}function zd(e){var t,r,o,s,u,c,h,g=0,b=null,y,S,T;if(t=ma.exec(e),t===null&&(t=ga.exec(e)),t===null)throw new Error("Date resolve error");if(r=+t[1],o=+t[2]-1,s=+t[3],!t[4])return new Date(Date.UTC(r,o,s));if(u=+t[4],c=+t[5],h=+t[6],t[7]){for(g=t[7].slice(0,3);g.length<3;)g+="0";g=+g}return t[9]&&(y=+t[10],S=+(t[11]||0),b=(y*60+S)*6e4,t[9]==="-"&&(b=-b)),T=new Date(Date.UTC(r,o,s,u,c,h,g)),b&&T.setTime(T.getTime()-b),T}function Id(e){return e.toISOString()}var xa=new Ge("tag:yaml.org,2002:timestamp",{kind:"scalar",resolve:Ed,construct:zd,instanceOf:Date,represent:Id});function Dd(e){return e==="<<"||e===null}var ya=new Ge("tag:yaml.org,2002:merge",{kind:"scalar",resolve:Dd}),$i=`ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=
\r`;function Bd(e){if(e===null)return!1;var t,r,o=0,s=e.length,u=$i;for(r=0;r<s;r++)if(t=u.indexOf(e.charAt(r)),!(t>64)){if(t<0)return!1;o+=6}return o%8===0}function Od(e){var t,r,o=e.replace(/[\r\n=]/g,""),s=o.length,u=$i,c=0,h=[];for(t=0;t<s;t++)t%4===0&&t&&(h.push(c>>16&255),h.push(c>>8&255),h.push(c&255)),c=c<<6|u.indexOf(o.charAt(t));return r=s%4*6,r===0?(h.push(c>>16&255),h.push(c>>8&255),h.push(c&255)):r===18?(h.push(c>>10&255),h.push(c>>2&255)):r===12&&h.push(c>>4&255),new Uint8Array(h)}function Rd(e){var t="",r=0,o,s,u=e.length,c=$i;for(o=0;o<u;o++)o%3===0&&o&&(t+=c[r>>18&63],t+=c[r>>12&63],t+=c[r>>6&63],t+=c[r&63]),r=(r<<8)+e[o];return s=u%3,s===0?(t+=c[r>>18&63],t+=c[r>>12&63],t+=c[r>>6&63],t+=c[r&63]):s===2?(t+=c[r>>10&63],t+=c[r>>4&63],t+=c[r<<2&63],t+=c[64]):s===1&&(t+=c[r>>2&63],t+=c[r<<4&63],t+=c[64],t+=c[64]),t}function Fd(e){return Object.prototype.toString.call(e)==="[object Uint8Array]"}var ba=new Ge("tag:yaml.org,2002:binary",{kind:"scalar",resolve:Bd,construct:Od,predicate:Fd,represent:Rd}),Ld=Object.prototype.hasOwnProperty,qd=Object.prototype.toString;function Pd(e){if(e===null)return!0;var t=[],r,o,s,u,c,h=e;for(r=0,o=h.length;r<o;r+=1){if(s=h[r],c=!1,qd.call(s)!=="[object Object]")return!1;for(u in s)if(Ld.call(s,u))if(!c)c=!0;else return!1;if(!c)return!1;if(t.indexOf(u)===-1)t.push(u);else return!1}return!0}function Hd(e){return e!==null?e:[]}var va=new Ge("tag:yaml.org,2002:omap",{kind:"sequence",resolve:Pd,construct:Hd}),$d=Object.prototype.toString;function Vd(e){if(e===null)return!0;var t,r,o,s,u,c=e;for(u=new Array(c.length),t=0,r=c.length;t<r;t+=1){if(o=c[t],$d.call(o)!=="[object Object]"||(s=Object.keys(o),s.length!==1))return!1;u[t]=[s[0],o[s[0]]]}return!0}function Gd(e){if(e===null)return[];var t,r,o,s,u,c=e;for(u=new Array(c.length),t=0,r=c.length;t<r;t+=1)o=c[t],s=Object.keys(o),u[t]=[s[0],o[s[0]]];return u}var wa=new Ge("tag:yaml.org,2002:pairs",{kind:"sequence",resolve:Vd,construct:Gd}),Yd=Object.prototype.hasOwnProperty;function Ud(e){if(e===null)return!0;var t,r=e;for(t in r)if(Yd.call(r,t)&&r[t]!==null)return!1;return!0}function Xd(e){return e!==null?e:{}}var ka=new Ge("tag:yaml.org,2002:set",{kind:"mapping",resolve:Ud,construct:Xd}),Vi=da.extend({implicit:[xa,ya],explicit:[ba,va,wa,ka]}),Wt=Object.prototype.hasOwnProperty,Xn=1,Sa=2,Aa=3,jn=4,Ri=1,jd=2,$s=3,Wd=/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,Kd=/[\x85\u2028\u2029]/,Zd=/[,\[\]\{\}]/,_a=/^(?:!|!!|![a-z\-]+!)$/i,Ma=/^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i;function Vs(e){return Object.prototype.toString.call(e)}function Tt(e){return e===10||e===13}function cr(e){return e===9||e===32}function tt(e){return e===9||e===32||e===10||e===13}function Ar(e){return e===44||e===91||e===93||e===123||e===125}function Jd(e){var t;return 48<=e&&e<=57?e-48:(t=e|32,97<=t&&t<=102?t-97+10:-1)}function Qd(e){return e===120?2:e===117?4:e===85?8:0}function em(e){return 48<=e&&e<=57?e-48:-1}function Gs(e){return e===48?"\0":e===97?"\x07":e===98?"\b":e===116||e===9?"	":e===110?`
`:e===118?"\v":e===102?"\f":e===114?"\r":e===101?"\x1B":e===32?" ":e===34?'"':e===47?"/":e===92?"\\":e===78?"\x85":e===95?"\xA0":e===76?"\u2028":e===80?"\u2029":""}function tm(e){return e<=65535?String.fromCharCode(e):String.fromCharCode((e-65536>>10)+55296,(e-65536&1023)+56320)}var Ca=new Array(256),Ta=new Array(256);for(ur=0;ur<256;ur++)Ca[ur]=Gs(ur)?1:0,Ta[ur]=Gs(ur);var ur;function rm(e,t){this.input=e,this.filename=t.filename||null,this.schema=t.schema||Vi,this.onWarning=t.onWarning||null,this.legacy=t.legacy||!1,this.json=t.json||!1,this.listener=t.listener||null,this.implicitTypes=this.schema.compiledImplicit,this.typeMap=this.schema.compiledTypeMap,this.length=e.length,this.position=0,this.line=0,this.lineStart=0,this.lineIndent=0,this.firstTabInLine=-1,this.documents=[]}function Na(e,t){var r={name:e.filename,buffer:e.input.slice(0,-1),position:e.position,line:e.line,column:e.position-e.lineStart};return r.snippet=ld(r),new Ke(t,r)}function te(e,t){throw Na(e,t)}function Wn(e,t){e.onWarning&&e.onWarning.call(null,Na(e,t))}var Ys={YAML:function(t,r,o){var s,u,c;t.version!==null&&te(t,"duplication of %YAML directive"),o.length!==1&&te(t,"YAML directive accepts exactly one argument"),s=/^([0-9]+)\.([0-9]+)$/.exec(o[0]),s===null&&te(t,"ill-formed argument of the YAML directive"),u=parseInt(s[1],10),c=parseInt(s[2],10),u!==1&&te(t,"unacceptable YAML version of the document"),t.version=o[0],t.checkLineBreaks=c<2,c!==1&&c!==2&&Wn(t,"unsupported YAML version of the document")},TAG:function(t,r,o){var s,u;o.length!==2&&te(t,"TAG directive accepts exactly two arguments"),s=o[0],u=o[1],_a.test(s)||te(t,"ill-formed tag handle (first argument) of the TAG directive"),Wt.call(t.tagMap,s)&&te(t,'there is a previously declared suffix for "'+s+'" tag handle'),Ma.test(u)||te(t,"ill-formed tag prefix (second argument) of the TAG directive");try{u=decodeURIComponent(u)}catch(c){te(t,"tag prefix is malformed: "+u)}t.tagMap[s]=u}};function jt(e,t,r,o){var s,u,c,h;if(t<r){if(h=e.input.slice(t,r),o)for(s=0,u=h.length;s<u;s+=1)c=h.charCodeAt(s),c===9||32<=c&&c<=1114111||te(e,"expected valid JSON character");else Wd.test(h)&&te(e,"the stream contains non-printable characters");e.result+=h}}function Us(e,t,r,o){var s,u,c,h;for(Pe.isObject(r)||te(e,"cannot merge mappings; the provided source object is unacceptable"),s=Object.keys(r),c=0,h=s.length;c<h;c+=1)u=s[c],Wt.call(t,u)||(t[u]=r[u],o[u]=!0)}function _r(e,t,r,o,s,u,c,h,g){var b,y;if(Array.isArray(s))for(s=Array.prototype.slice.call(s),b=0,y=s.length;b<y;b+=1)Array.isArray(s[b])&&te(e,"nested arrays are not supported inside keys"),typeof s=="object"&&Vs(s[b])==="[object Object]"&&(s[b]="[object Object]");if(typeof s=="object"&&Vs(s)==="[object Object]"&&(s="[object Object]"),s=String(s),t===null&&(t={}),o==="tag:yaml.org,2002:merge")if(Array.isArray(u))for(b=0,y=u.length;b<y;b+=1)Us(e,t,u[b],r);else Us(e,t,u,r);else!e.json&&!Wt.call(r,s)&&Wt.call(t,s)&&(e.line=c||e.line,e.lineStart=h||e.lineStart,e.position=g||e.position,te(e,"duplicated mapping key")),s==="__proto__"?Object.defineProperty(t,s,{configurable:!0,enumerable:!0,writable:!0,value:u}):t[s]=u,delete r[s];return t}function Gi(e){var t;t=e.input.charCodeAt(e.position),t===10?e.position++:t===13?(e.position++,e.input.charCodeAt(e.position)===10&&e.position++):te(e,"a line break is expected"),e.line+=1,e.lineStart=e.position,e.firstTabInLine=-1}function Fe(e,t,r){for(var o=0,s=e.input.charCodeAt(e.position);s!==0;){for(;cr(s);)s===9&&e.firstTabInLine===-1&&(e.firstTabInLine=e.position),s=e.input.charCodeAt(++e.position);if(t&&s===35)do s=e.input.charCodeAt(++e.position);while(s!==10&&s!==13&&s!==0);if(Tt(s))for(Gi(e),s=e.input.charCodeAt(e.position),o++,e.lineIndent=0;s===32;)e.lineIndent++,s=e.input.charCodeAt(++e.position);else break}return r!==-1&&o!==0&&e.lineIndent<r&&Wn(e,"deficient indentation"),o}function Jn(e){var t=e.position,r;return r=e.input.charCodeAt(t),!!((r===45||r===46)&&r===e.input.charCodeAt(t+1)&&r===e.input.charCodeAt(t+2)&&(t+=3,r=e.input.charCodeAt(t),r===0||tt(r)))}function Yi(e,t){t===1?e.result+=" ":t>1&&(e.result+=Pe.repeat(`
`,t-1))}function nm(e,t,r){var o,s,u,c,h,g,b,y,S=e.kind,T=e.result,N;if(N=e.input.charCodeAt(e.position),tt(N)||Ar(N)||N===35||N===38||N===42||N===33||N===124||N===62||N===39||N===34||N===37||N===64||N===96||(N===63||N===45)&&(s=e.input.charCodeAt(e.position+1),tt(s)||r&&Ar(s)))return!1;for(e.kind="scalar",e.result="",u=c=e.position,h=!1;N!==0;){if(N===58){if(s=e.input.charCodeAt(e.position+1),tt(s)||r&&Ar(s))break}else if(N===35){if(o=e.input.charCodeAt(e.position-1),tt(o))break}else{if(e.position===e.lineStart&&Jn(e)||r&&Ar(N))break;if(Tt(N))if(g=e.line,b=e.lineStart,y=e.lineIndent,Fe(e,!1,-1),e.lineIndent>=t){h=!0,N=e.input.charCodeAt(e.position);continue}else{e.position=c,e.line=g,e.lineStart=b,e.lineIndent=y;break}}h&&(jt(e,u,c,!1),Yi(e,e.line-g),u=c=e.position,h=!1),cr(N)||(c=e.position+1),N=e.input.charCodeAt(++e.position)}return jt(e,u,c,!1),e.result?!0:(e.kind=S,e.result=T,!1)}function im(e,t){var r,o,s;if(r=e.input.charCodeAt(e.position),r!==39)return!1;for(e.kind="scalar",e.result="",e.position++,o=s=e.position;(r=e.input.charCodeAt(e.position))!==0;)if(r===39)if(jt(e,o,e.position,!0),r=e.input.charCodeAt(++e.position),r===39)o=e.position,e.position++,s=e.position;else return!0;else Tt(r)?(jt(e,o,s,!0),Yi(e,Fe(e,!1,t)),o=s=e.position):e.position===e.lineStart&&Jn(e)?te(e,"unexpected end of the document within a single quoted scalar"):(e.position++,s=e.position);te(e,"unexpected end of the stream within a single quoted scalar")}function om(e,t){var r,o,s,u,c,h;if(h=e.input.charCodeAt(e.position),h!==34)return!1;for(e.kind="scalar",e.result="",e.position++,r=o=e.position;(h=e.input.charCodeAt(e.position))!==0;){if(h===34)return jt(e,r,e.position,!0),e.position++,!0;if(h===92){if(jt(e,r,e.position,!0),h=e.input.charCodeAt(++e.position),Tt(h))Fe(e,!1,t);else if(h<256&&Ca[h])e.result+=Ta[h],e.position++;else if((c=Qd(h))>0){for(s=c,u=0;s>0;s--)h=e.input.charCodeAt(++e.position),(c=Jd(h))>=0?u=(u<<4)+c:te(e,"expected hexadecimal character");e.result+=tm(u),e.position++}else te(e,"unknown escape sequence");r=o=e.position}else Tt(h)?(jt(e,r,o,!0),Yi(e,Fe(e,!1,t)),r=o=e.position):e.position===e.lineStart&&Jn(e)?te(e,"unexpected end of the document within a double quoted scalar"):(e.position++,o=e.position)}te(e,"unexpected end of the stream within a double quoted scalar")}function lm(e,t){var r=!0,o,s,u,c=e.tag,h,g=e.anchor,b,y,S,T,N,R=Object.create(null),z,P,L,X;if(X=e.input.charCodeAt(e.position),X===91)y=93,N=!1,h=[];else if(X===123)y=125,N=!0,h={};else return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=h),X=e.input.charCodeAt(++e.position);X!==0;){if(Fe(e,!0,t),X=e.input.charCodeAt(e.position),X===y)return e.position++,e.tag=c,e.anchor=g,e.kind=N?"mapping":"sequence",e.result=h,!0;r?X===44&&te(e,"expected the node content, but found ','"):te(e,"missed comma between flow collection entries"),P=z=L=null,S=T=!1,X===63&&(b=e.input.charCodeAt(e.position+1),tt(b)&&(S=T=!0,e.position++,Fe(e,!0,t))),o=e.line,s=e.lineStart,u=e.position,Mr(e,t,Xn,!1,!0),P=e.tag,z=e.result,Fe(e,!0,t),X=e.input.charCodeAt(e.position),(T||e.line===o)&&X===58&&(S=!0,X=e.input.charCodeAt(++e.position),Fe(e,!0,t),Mr(e,t,Xn,!1,!0),L=e.result),N?_r(e,h,R,P,z,L,o,s,u):S?h.push(_r(e,null,R,P,z,L,o,s,u)):h.push(z),Fe(e,!0,t),X=e.input.charCodeAt(e.position),X===44?(r=!0,X=e.input.charCodeAt(++e.position)):r=!1}te(e,"unexpected end of the stream within a flow collection")}function sm(e,t){var r,o,s=Ri,u=!1,c=!1,h=t,g=0,b=!1,y,S;if(S=e.input.charCodeAt(e.position),S===124)o=!1;else if(S===62)o=!0;else return!1;for(e.kind="scalar",e.result="";S!==0;)if(S=e.input.charCodeAt(++e.position),S===43||S===45)Ri===s?s=S===43?$s:jd:te(e,"repeat of a chomping mode identifier");else if((y=em(S))>=0)y===0?te(e,"bad explicit indentation width of a block scalar; it cannot be less than one"):c?te(e,"repeat of an indentation width identifier"):(h=t+y-1,c=!0);else break;if(cr(S)){do S=e.input.charCodeAt(++e.position);while(cr(S));if(S===35)do S=e.input.charCodeAt(++e.position);while(!Tt(S)&&S!==0)}for(;S!==0;){for(Gi(e),e.lineIndent=0,S=e.input.charCodeAt(e.position);(!c||e.lineIndent<h)&&S===32;)e.lineIndent++,S=e.input.charCodeAt(++e.position);if(!c&&e.lineIndent>h&&(h=e.lineIndent),Tt(S)){g++;continue}if(e.lineIndent<h){s===$s?e.result+=Pe.repeat(`
`,u?1+g:g):s===Ri&&u&&(e.result+=`
`);break}for(o?cr(S)?(b=!0,e.result+=Pe.repeat(`
`,u?1+g:g)):b?(b=!1,e.result+=Pe.repeat(`
`,g+1)):g===0?u&&(e.result+=" "):e.result+=Pe.repeat(`
`,g):e.result+=Pe.repeat(`
`,u?1+g:g),u=!0,c=!0,g=0,r=e.position;!Tt(S)&&S!==0;)S=e.input.charCodeAt(++e.position);jt(e,r,e.position,!1)}return!0}function Xs(e,t){var r,o=e.tag,s=e.anchor,u=[],c,h=!1,g;if(e.firstTabInLine!==-1)return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=u),g=e.input.charCodeAt(e.position);g!==0&&(e.firstTabInLine!==-1&&(e.position=e.firstTabInLine,te(e,"tab characters must not be used in indentation")),!(g!==45||(c=e.input.charCodeAt(e.position+1),!tt(c))));){if(h=!0,e.position++,Fe(e,!0,-1)&&e.lineIndent<=t){u.push(null),g=e.input.charCodeAt(e.position);continue}if(r=e.line,Mr(e,t,Aa,!1,!0),u.push(e.result),Fe(e,!0,-1),g=e.input.charCodeAt(e.position),(e.line===r||e.lineIndent>t)&&g!==0)te(e,"bad indentation of a sequence entry");else if(e.lineIndent<t)break}return h?(e.tag=o,e.anchor=s,e.kind="sequence",e.result=u,!0):!1}function am(e,t,r){var o,s,u,c,h,g,b=e.tag,y=e.anchor,S={},T=Object.create(null),N=null,R=null,z=null,P=!1,L=!1,X;if(e.firstTabInLine!==-1)return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=S),X=e.input.charCodeAt(e.position);X!==0;){if(!P&&e.firstTabInLine!==-1&&(e.position=e.firstTabInLine,te(e,"tab characters must not be used in indentation")),o=e.input.charCodeAt(e.position+1),u=e.line,(X===63||X===58)&&tt(o))X===63?(P&&(_r(e,S,T,N,R,null,c,h,g),N=R=z=null),L=!0,P=!0,s=!0):P?(P=!1,s=!0):te(e,"incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line"),e.position+=1,X=o;else{if(c=e.line,h=e.lineStart,g=e.position,!Mr(e,r,Sa,!1,!0))break;if(e.line===u){for(X=e.input.charCodeAt(e.position);cr(X);)X=e.input.charCodeAt(++e.position);if(X===58)X=e.input.charCodeAt(++e.position),tt(X)||te(e,"a whitespace character is expected after the key-value separator within a block mapping"),P&&(_r(e,S,T,N,R,null,c,h,g),N=R=z=null),L=!0,P=!1,s=!1,N=e.tag,R=e.result;else if(L)te(e,"can not read an implicit mapping pair; a colon is missed");else return e.tag=b,e.anchor=y,!0}else if(L)te(e,"can not read a block mapping entry; a multiline key may not be an implicit key");else return e.tag=b,e.anchor=y,!0}if((e.line===u||e.lineIndent>t)&&(P&&(c=e.line,h=e.lineStart,g=e.position),Mr(e,t,jn,!0,s)&&(P?R=e.result:z=e.result),P||(_r(e,S,T,N,R,z,c,h,g),N=R=z=null),Fe(e,!0,-1),X=e.input.charCodeAt(e.position)),(e.line===u||e.lineIndent>t)&&X!==0)te(e,"bad indentation of a mapping entry");else if(e.lineIndent<t)break}return P&&_r(e,S,T,N,R,null,c,h,g),L&&(e.tag=b,e.anchor=y,e.kind="mapping",e.result=S),L}function um(e){var t,r=!1,o=!1,s,u,c;if(c=e.input.charCodeAt(e.position),c!==33)return!1;if(e.tag!==null&&te(e,"duplication of a tag property"),c=e.input.charCodeAt(++e.position),c===60?(r=!0,c=e.input.charCodeAt(++e.position)):c===33?(o=!0,s="!!",c=e.input.charCodeAt(++e.position)):s="!",t=e.position,r){do c=e.input.charCodeAt(++e.position);while(c!==0&&c!==62);e.position<e.length?(u=e.input.slice(t,e.position),c=e.input.charCodeAt(++e.position)):te(e,"unexpected end of the stream within a verbatim tag")}else{for(;c!==0&&!tt(c);)c===33&&(o?te(e,"tag suffix cannot contain exclamation marks"):(s=e.input.slice(t-1,e.position+1),_a.test(s)||te(e,"named tag handle cannot contain such characters"),o=!0,t=e.position+1)),c=e.input.charCodeAt(++e.position);u=e.input.slice(t,e.position),Zd.test(u)&&te(e,"tag suffix cannot contain flow indicator characters")}u&&!Ma.test(u)&&te(e,"tag name cannot contain such characters: "+u);try{u=decodeURIComponent(u)}catch(h){te(e,"tag name is malformed: "+u)}return r?e.tag=u:Wt.call(e.tagMap,s)?e.tag=e.tagMap[s]+u:s==="!"?e.tag="!"+u:s==="!!"?e.tag="tag:yaml.org,2002:"+u:te(e,'undeclared tag handle "'+s+'"'),!0}function cm(e){var t,r;if(r=e.input.charCodeAt(e.position),r!==38)return!1;for(e.anchor!==null&&te(e,"duplication of an anchor property"),r=e.input.charCodeAt(++e.position),t=e.position;r!==0&&!tt(r)&&!Ar(r);)r=e.input.charCodeAt(++e.position);return e.position===t&&te(e,"name of an anchor node must contain at least one character"),e.anchor=e.input.slice(t,e.position),!0}function hm(e){var t,r,o;if(o=e.input.charCodeAt(e.position),o!==42)return!1;for(o=e.input.charCodeAt(++e.position),t=e.position;o!==0&&!tt(o)&&!Ar(o);)o=e.input.charCodeAt(++e.position);return e.position===t&&te(e,"name of an alias node must contain at least one character"),r=e.input.slice(t,e.position),Wt.call(e.anchorMap,r)||te(e,'unidentified alias "'+r+'"'),e.result=e.anchorMap[r],Fe(e,!0,-1),!0}function Mr(e,t,r,o,s){var u,c,h,g=1,b=!1,y=!1,S,T,N,R,z,P;if(e.listener!==null&&e.listener("open",e),e.tag=null,e.anchor=null,e.kind=null,e.result=null,u=c=h=jn===r||Aa===r,o&&Fe(e,!0,-1)&&(b=!0,e.lineIndent>t?g=1:e.lineIndent===t?g=0:e.lineIndent<t&&(g=-1)),g===1)for(;um(e)||cm(e);)Fe(e,!0,-1)?(b=!0,h=u,e.lineIndent>t?g=1:e.lineIndent===t?g=0:e.lineIndent<t&&(g=-1)):h=!1;if(h&&(h=b||s),(g===1||jn===r)&&(Xn===r||Sa===r?z=t:z=t+1,P=e.position-e.lineStart,g===1?h&&(Xs(e,P)||am(e,P,z))||lm(e,z)?y=!0:(c&&sm(e,z)||im(e,z)||om(e,z)?y=!0:hm(e)?(y=!0,(e.tag!==null||e.anchor!==null)&&te(e,"alias node should not have any properties")):nm(e,z,Xn===r)&&(y=!0,e.tag===null&&(e.tag="?")),e.anchor!==null&&(e.anchorMap[e.anchor]=e.result)):g===0&&(y=h&&Xs(e,P))),e.tag===null)e.anchor!==null&&(e.anchorMap[e.anchor]=e.result);else if(e.tag==="?"){for(e.result!==null&&e.kind!=="scalar"&&te(e,'unacceptable node kind for !<?> tag; it should be "scalar", not "'+e.kind+'"'),S=0,T=e.implicitTypes.length;S<T;S+=1)if(R=e.implicitTypes[S],R.resolve(e.result)){e.result=R.construct(e.result),e.tag=R.tag,e.anchor!==null&&(e.anchorMap[e.anchor]=e.result);break}}else if(e.tag!=="!"){if(Wt.call(e.typeMap[e.kind||"fallback"],e.tag))R=e.typeMap[e.kind||"fallback"][e.tag];else for(R=null,N=e.typeMap.multi[e.kind||"fallback"],S=0,T=N.length;S<T;S+=1)if(e.tag.slice(0,N[S].tag.length)===N[S].tag){R=N[S];break}R||te(e,"unknown tag !<"+e.tag+">"),e.result!==null&&R.kind!==e.kind&&te(e,"unacceptable node kind for !<"+e.tag+'> tag; it should be "'+R.kind+'", not "'+e.kind+'"'),R.resolve(e.result,e.tag)?(e.result=R.construct(e.result,e.tag),e.anchor!==null&&(e.anchorMap[e.anchor]=e.result)):te(e,"cannot resolve a node with !<"+e.tag+"> explicit tag")}return e.listener!==null&&e.listener("close",e),e.tag!==null||e.anchor!==null||y}function fm(e){var t=e.position,r,o,s,u=!1,c;for(e.version=null,e.checkLineBreaks=e.legacy,e.tagMap=Object.create(null),e.anchorMap=Object.create(null);(c=e.input.charCodeAt(e.position))!==0&&(Fe(e,!0,-1),c=e.input.charCodeAt(e.position),!(e.lineIndent>0||c!==37));){for(u=!0,c=e.input.charCodeAt(++e.position),r=e.position;c!==0&&!tt(c);)c=e.input.charCodeAt(++e.position);for(o=e.input.slice(r,e.position),s=[],o.length<1&&te(e,"directive name must not be less than one character in length");c!==0;){for(;cr(c);)c=e.input.charCodeAt(++e.position);if(c===35){do c=e.input.charCodeAt(++e.position);while(c!==0&&!Tt(c));break}if(Tt(c))break;for(r=e.position;c!==0&&!tt(c);)c=e.input.charCodeAt(++e.position);s.push(e.input.slice(r,e.position))}c!==0&&Gi(e),Wt.call(Ys,o)?Ys[o](e,o,s):Wn(e,'unknown document directive "'+o+'"')}if(Fe(e,!0,-1),e.lineIndent===0&&e.input.charCodeAt(e.position)===45&&e.input.charCodeAt(e.position+1)===45&&e.input.charCodeAt(e.position+2)===45?(e.position+=3,Fe(e,!0,-1)):u&&te(e,"directives end mark is expected"),Mr(e,e.lineIndent-1,jn,!1,!0),Fe(e,!0,-1),e.checkLineBreaks&&Kd.test(e.input.slice(t,e.position))&&Wn(e,"non-ASCII line breaks are interpreted as content"),e.documents.push(e.result),e.position===e.lineStart&&Jn(e)){e.input.charCodeAt(e.position)===46&&(e.position+=3,Fe(e,!0,-1));return}if(e.position<e.length-1)te(e,"end of the stream or a document separator is expected");else return}function Ea(e,t){e=String(e),t=t||{},e.length!==0&&(e.charCodeAt(e.length-1)!==10&&e.charCodeAt(e.length-1)!==13&&(e+=`
`),e.charCodeAt(0)===65279&&(e=e.slice(1)));var r=new rm(e,t),o=e.indexOf("\0");for(o!==-1&&(r.position=o,te(r,"null byte is not allowed in input")),r.input+="\0";r.input.charCodeAt(r.position)===32;)r.lineIndent+=1,r.position+=1;for(;r.position<r.length-1;)fm(r);return r.documents}function pm(e,t,r){t!==null&&typeof t=="object"&&typeof r=="undefined"&&(r=t,t=null);var o=Ea(e,r);if(typeof t!="function")return o;for(var s=0,u=o.length;s<u;s+=1)t(o[s])}function dm(e,t){var r=Ea(e,t);if(r.length!==0){if(r.length===1)return r[0];throw new Ke("expected a single document in the stream, but found more")}}var mm=pm,gm=dm,za={loadAll:mm,load:gm},Ia=Object.prototype.toString,Da=Object.prototype.hasOwnProperty,Ui=65279,xm=9,jr=10,ym=13,bm=32,vm=33,wm=34,Li=35,km=37,Sm=38,Am=39,_m=42,Ba=44,Mm=45,Kn=58,Cm=61,Tm=62,Nm=63,Em=64,Oa=91,Ra=93,zm=96,Fa=123,Im=124,La=125,Ue={};Ue[0]="\\0";Ue[7]="\\a";Ue[8]="\\b";Ue[9]="\\t";Ue[10]="\\n";Ue[11]="\\v";Ue[12]="\\f";Ue[13]="\\r";Ue[27]="\\e";Ue[34]='\\"';Ue[92]="\\\\";Ue[133]="\\N";Ue[160]="\\_";Ue[8232]="\\L";Ue[8233]="\\P";var Dm=["y","Y","yes","Yes","YES","on","On","ON","n","N","no","No","NO","off","Off","OFF"],Bm=/^[-+]?[0-9_]+(?::[0-9_]+)+(?:\.[0-9_]*)?$/;function Om(e,t){var r,o,s,u,c,h,g;if(t===null)return{};for(r={},o=Object.keys(t),s=0,u=o.length;s<u;s+=1)c=o[s],h=String(t[c]),c.slice(0,2)==="!!"&&(c="tag:yaml.org,2002:"+c.slice(2)),g=e.compiledTypeMap.fallback[c],g&&Da.call(g.styleAliases,h)&&(h=g.styleAliases[h]),r[c]=h;return r}function Rm(e){var t,r,o;if(t=e.toString(16).toUpperCase(),e<=255)r="x",o=2;else if(e<=65535)r="u",o=4;else if(e<=4294967295)r="U",o=8;else throw new Ke("code point within a string may not be greater than 0xFFFFFFFF");return"\\"+r+Pe.repeat("0",o-t.length)+t}var Fm=1,Wr=2;function Lm(e){this.schema=e.schema||Vi,this.indent=Math.max(1,e.indent||2),this.noArrayIndent=e.noArrayIndent||!1,this.skipInvalid=e.skipInvalid||!1,this.flowLevel=Pe.isNothing(e.flowLevel)?-1:e.flowLevel,this.styleMap=Om(this.schema,e.styles||null),this.sortKeys=e.sortKeys||!1,this.lineWidth=e.lineWidth||80,this.noRefs=e.noRefs||!1,this.noCompatMode=e.noCompatMode||!1,this.condenseFlow=e.condenseFlow||!1,this.quotingType=e.quotingType==='"'?Wr:Fm,this.forceQuotes=e.forceQuotes||!1,this.replacer=typeof e.replacer=="function"?e.replacer:null,this.implicitTypes=this.schema.compiledImplicit,this.explicitTypes=this.schema.compiledExplicit,this.tag=null,this.result="",this.duplicates=[],this.usedDuplicates=null}function js(e,t){for(var r=Pe.repeat(" ",t),o=0,s=-1,u="",c,h=e.length;o<h;)s=e.indexOf(`
`,o),s===-1?(c=e.slice(o),o=h):(c=e.slice(o,s+1),o=s+1),c.length&&c!==`
`&&(u+=r),u+=c;return u}function qi(e,t){return`
`+Pe.repeat(" ",e.indent*t)}function qm(e,t){var r,o,s;for(r=0,o=e.implicitTypes.length;r<o;r+=1)if(s=e.implicitTypes[r],s.resolve(t))return!0;return!1}function Zn(e){return e===bm||e===xm}function Kr(e){return 32<=e&&e<=126||161<=e&&e<=55295&&e!==8232&&e!==8233||57344<=e&&e<=65533&&e!==Ui||65536<=e&&e<=1114111}function Ws(e){return Kr(e)&&e!==Ui&&e!==ym&&e!==jr}function Ks(e,t,r){var o=Ws(e),s=o&&!Zn(e);return(r?o:o&&e!==Ba&&e!==Oa&&e!==Ra&&e!==Fa&&e!==La)&&e!==Li&&!(t===Kn&&!s)||Ws(t)&&!Zn(t)&&e===Li||t===Kn&&s}function Pm(e){return Kr(e)&&e!==Ui&&!Zn(e)&&e!==Mm&&e!==Nm&&e!==Kn&&e!==Ba&&e!==Oa&&e!==Ra&&e!==Fa&&e!==La&&e!==Li&&e!==Sm&&e!==_m&&e!==vm&&e!==Im&&e!==Cm&&e!==Tm&&e!==Am&&e!==wm&&e!==km&&e!==Em&&e!==zm}function Hm(e){return!Zn(e)&&e!==Kn}function Ur(e,t){var r=e.charCodeAt(t),o;return r>=55296&&r<=56319&&t+1<e.length&&(o=e.charCodeAt(t+1),o>=56320&&o<=57343)?(r-55296)*1024+o-56320+65536:r}function qa(e){var t=/^\n* /;return t.test(e)}var Pa=1,Pi=2,Ha=3,$a=4,Sr=5;function $m(e,t,r,o,s,u,c,h){var g,b=0,y=null,S=!1,T=!1,N=o!==-1,R=-1,z=Pm(Ur(e,0))&&Hm(Ur(e,e.length-1));if(t||c)for(g=0;g<e.length;b>=65536?g+=2:g++){if(b=Ur(e,g),!Kr(b))return Sr;z=z&&Ks(b,y,h),y=b}else{for(g=0;g<e.length;b>=65536?g+=2:g++){if(b=Ur(e,g),b===jr)S=!0,N&&(T=T||g-R-1>o&&e[R+1]!==" ",R=g);else if(!Kr(b))return Sr;z=z&&Ks(b,y,h),y=b}T=T||N&&g-R-1>o&&e[R+1]!==" "}return!S&&!T?z&&!c&&!s(e)?Pa:u===Wr?Sr:Pi:r>9&&qa(e)?Sr:c?u===Wr?Sr:Pi:T?$a:Ha}function Vm(e,t,r,o,s){e.dump=function(){if(t.length===0)return e.quotingType===Wr?'""':"''";if(!e.noCompatMode&&(Dm.indexOf(t)!==-1||Bm.test(t)))return e.quotingType===Wr?'"'+t+'"':"'"+t+"'";var u=e.indent*Math.max(1,r),c=e.lineWidth===-1?-1:Math.max(Math.min(e.lineWidth,40),e.lineWidth-u),h=o||e.flowLevel>-1&&r>=e.flowLevel;function g(b){return qm(e,b)}switch($m(t,h,e.indent,c,g,e.quotingType,e.forceQuotes&&!o,s)){case Pa:return t;case Pi:return"'"+t.replace(/'/g,"''")+"'";case Ha:return"|"+Zs(t,e.indent)+Js(js(t,u));case $a:return">"+Zs(t,e.indent)+Js(js(Gm(t,c),u));case Sr:return'"'+Ym(t)+'"';default:throw new Ke("impossible error: invalid scalar style")}}()}function Zs(e,t){var r=qa(e)?String(t):"",o=e[e.length-1]===`
`,s=o&&(e[e.length-2]===`
`||e===`
`),u=s?"+":o?"":"-";return r+u+`
`}function Js(e){return e[e.length-1]===`
`?e.slice(0,-1):e}function Gm(e,t){for(var r=/(\n+)([^\n]*)/g,o=function(){var b=e.indexOf(`
`);return b=b!==-1?b:e.length,r.lastIndex=b,Qs(e.slice(0,b),t)}(),s=e[0]===`
`||e[0]===" ",u,c;c=r.exec(e);){var h=c[1],g=c[2];u=g[0]===" ",o+=h+(!s&&!u&&g!==""?`
`:"")+Qs(g,t),s=u}return o}function Qs(e,t){if(e===""||e[0]===" ")return e;for(var r=/ [^ ]/g,o,s=0,u,c=0,h=0,g="";o=r.exec(e);)h=o.index,h-s>t&&(u=c>s?c:h,g+=`
`+e.slice(s,u),s=u+1),c=h;return g+=`
`,e.length-s>t&&c>s?g+=e.slice(s,c)+`
`+e.slice(c+1):g+=e.slice(s),g.slice(1)}function Ym(e){for(var t="",r=0,o,s=0;s<e.length;r>=65536?s+=2:s++)r=Ur(e,s),o=Ue[r],!o&&Kr(r)?(t+=e[s],r>=65536&&(t+=e[s+1])):t+=o||Rm(r);return t}function Um(e,t,r){var o="",s=e.tag,u,c,h;for(u=0,c=r.length;u<c;u+=1)h=r[u],e.replacer&&(h=e.replacer.call(r,String(u),h)),(Lt(e,t,h,!1,!1)||typeof h=="undefined"&&Lt(e,t,null,!1,!1))&&(o!==""&&(o+=","+(e.condenseFlow?"":" ")),o+=e.dump);e.tag=s,e.dump="["+o+"]"}function ea(e,t,r,o){var s="",u=e.tag,c,h,g;for(c=0,h=r.length;c<h;c+=1)g=r[c],e.replacer&&(g=e.replacer.call(r,String(c),g)),(Lt(e,t+1,g,!0,!0,!1,!0)||typeof g=="undefined"&&Lt(e,t+1,null,!0,!0,!1,!0))&&((!o||s!=="")&&(s+=qi(e,t)),e.dump&&jr===e.dump.charCodeAt(0)?s+="-":s+="- ",s+=e.dump);e.tag=u,e.dump=s||"[]"}function Xm(e,t,r){var o="",s=e.tag,u=Object.keys(r),c,h,g,b,y;for(c=0,h=u.length;c<h;c+=1)y="",o!==""&&(y+=", "),e.condenseFlow&&(y+='"'),g=u[c],b=r[g],e.replacer&&(b=e.replacer.call(r,g,b)),Lt(e,t,g,!1,!1)&&(e.dump.length>1024&&(y+="? "),y+=e.dump+(e.condenseFlow?'"':"")+":"+(e.condenseFlow?"":" "),Lt(e,t,b,!1,!1)&&(y+=e.dump,o+=y));e.tag=s,e.dump="{"+o+"}"}function jm(e,t,r,o){var s="",u=e.tag,c=Object.keys(r),h,g,b,y,S,T;if(e.sortKeys===!0)c.sort();else if(typeof e.sortKeys=="function")c.sort(e.sortKeys);else if(e.sortKeys)throw new Ke("sortKeys must be a boolean or a function");for(h=0,g=c.length;h<g;h+=1)T="",(!o||s!=="")&&(T+=qi(e,t)),b=c[h],y=r[b],e.replacer&&(y=e.replacer.call(r,b,y)),Lt(e,t+1,b,!0,!0,!0)&&(S=e.tag!==null&&e.tag!=="?"||e.dump&&e.dump.length>1024,S&&(e.dump&&jr===e.dump.charCodeAt(0)?T+="?":T+="? "),T+=e.dump,S&&(T+=qi(e,t)),Lt(e,t+1,y,!0,S)&&(e.dump&&jr===e.dump.charCodeAt(0)?T+=":":T+=": ",T+=e.dump,s+=T));e.tag=u,e.dump=s||"{}"}function ta(e,t,r){var o,s,u,c,h,g;for(s=r?e.explicitTypes:e.implicitTypes,u=0,c=s.length;u<c;u+=1)if(h=s[u],(h.instanceOf||h.predicate)&&(!h.instanceOf||typeof t=="object"&&t instanceof h.instanceOf)&&(!h.predicate||h.predicate(t))){if(r?h.multi&&h.representName?e.tag=h.representName(t):e.tag=h.tag:e.tag="?",h.represent){if(g=e.styleMap[h.tag]||h.defaultStyle,Ia.call(h.represent)==="[object Function]")o=h.represent(t,g);else if(Da.call(h.represent,g))o=h.represent[g](t,g);else throw new Ke("!<"+h.tag+'> tag resolver accepts not "'+g+'" style');e.dump=o}return!0}return!1}function Lt(e,t,r,o,s,u,c){e.tag=null,e.dump=r,ta(e,r,!1)||ta(e,r,!0);var h=Ia.call(e.dump),g=o,b;o&&(o=e.flowLevel<0||e.flowLevel>t);var y=h==="[object Object]"||h==="[object Array]",S,T;if(y&&(S=e.duplicates.indexOf(r),T=S!==-1),(e.tag!==null&&e.tag!=="?"||T||e.indent!==2&&t>0)&&(s=!1),T&&e.usedDuplicates[S])e.dump="*ref_"+S;else{if(y&&T&&!e.usedDuplicates[S]&&(e.usedDuplicates[S]=!0),h==="[object Object]")o&&Object.keys(e.dump).length!==0?(jm(e,t,e.dump,s),T&&(e.dump="&ref_"+S+e.dump)):(Xm(e,t,e.dump),T&&(e.dump="&ref_"+S+" "+e.dump));else if(h==="[object Array]")o&&e.dump.length!==0?(e.noArrayIndent&&!c&&t>0?ea(e,t-1,e.dump,s):ea(e,t,e.dump,s),T&&(e.dump="&ref_"+S+e.dump)):(Um(e,t,e.dump),T&&(e.dump="&ref_"+S+" "+e.dump));else if(h==="[object String]")e.tag!=="?"&&Vm(e,e.dump,t,u,g);else{if(h==="[object Undefined]")return!1;if(e.skipInvalid)return!1;throw new Ke("unacceptable kind of an object to dump "+h)}e.tag!==null&&e.tag!=="?"&&(b=encodeURI(e.tag[0]==="!"?e.tag.slice(1):e.tag).replace(/!/g,"%21"),e.tag[0]==="!"?b="!"+b:b.slice(0,18)==="tag:yaml.org,2002:"?b="!!"+b.slice(18):b="!<"+b+">",e.dump=b+" "+e.dump)}return!0}function Wm(e,t){var r=[],o=[],s,u;for(Hi(e,r,o),s=0,u=o.length;s<u;s+=1)t.duplicates.push(r[o[s]]);t.usedDuplicates=new Array(u)}function Hi(e,t,r){var o,s,u;if(e!==null&&typeof e=="object")if(s=t.indexOf(e),s!==-1)r.indexOf(s)===-1&&r.push(s);else if(t.push(e),Array.isArray(e))for(s=0,u=e.length;s<u;s+=1)Hi(e[s],t,r);else for(o=Object.keys(e),s=0,u=o.length;s<u;s+=1)Hi(e[o[s]],t,r)}function Km(e,t){t=t||{};var r=new Lm(t);r.noRefs||Wm(e,r);var o=e;return r.replacer&&(o=r.replacer.call({"":o},"",o)),Lt(r,0,o,!0,!0)?r.dump+`
`:""}var Zm=Km,Jm={dump:Zm};function Xi(e,t){return function(){throw new Error("Function yaml."+e+" is removed in js-yaml 4. Use yaml."+t+" instead, which is now safe by default.")}}var Qm=Ge,e4=ia,t4=aa,r4=pa,n4=da,i4=Vi,o4=za.load,l4=za.loadAll,s4=Jm.dump,a4=Ke,u4={binary:ba,float:fa,map:sa,null:ua,pairs:wa,set:ka,timestamp:xa,bool:ca,int:ha,merge:ya,omap:va,seq:la,str:oa},c4=Xi("safeLoad","load"),h4=Xi("safeLoadAll","loadAll"),f4=Xi("safeDump","dump"),p4={Type:Qm,Schema:e4,FAILSAFE_SCHEMA:t4,JSON_SCHEMA:r4,CORE_SCHEMA:n4,DEFAULT_SCHEMA:i4,load:o4,loadAll:l4,dump:s4,YAMLException:a4,types:u4,safeLoad:c4,safeLoadAll:h4,safeDump:f4},Va=p4;var d4=`<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<title>Markmap</title>
<style>
* {
  margin: 0;
  padding: 0;
}
#mindmap {
  display: block;
  width: 100vw;
  height: 100vh;
}
</style>
<!--CSS-->
</head>
<body>
<svg id="mindmap"></svg>
<!--JS-->
</body>
</html>
`,m4=["d3@7.8.5/dist/d3.min.js","markmap-view@0.15.8/dist/browser/index.js"],ji="katex",g4=["katex@0.16.8/dist/katex.min.js"].map(e=>Gr(e)),Ua=Gr("webfontloader@1.6.28/webfontloader.js");Ua.data.defer=!0;var x4=["katex@0.16.8/dist/katex.min.css"].map(e=>Ii(e)),y4={versions:{katex:"0.16.8",webfontloader:"1.6.28"},preloadScripts:g4,scripts:[{type:"iife",data:{fn:e=>{window.WebFontConfig={custom:{families:["KaTeX_AMS","KaTeX_Caligraphic:n4,n7","KaTeX_Fraktur:n4,n7","KaTeX_Main:n4,n7,i4,i7","KaTeX_Math:i4,i7","KaTeX_Script","KaTeX_SansSerif:n4,n7,i4","KaTeX_Size1","KaTeX_Size2","KaTeX_Size3","KaTeX_Size4","KaTeX_Typewriter"]},active:()=>{e().refreshHook.call()}}},getParams({getMarkmap:e}){return[e]}}},Ua],styles:x4};function b4(e,t,r){return e.map(o=>{if(typeof o=="string"&&!o.includes("://")){o.startsWith("npm:")||(o=`npm:${o}`);let s=4+t.length;o.startsWith(`npm:${t}/`)&&(o=`${o.slice(0,s)}@${r}${o.slice(s)}`)}return o})}function Ki(e,t){return t.type==="script"&&t.data.src?{...t,data:{...t.data,src:e.getFullUrl(t.data.src)}}:t}function v4(e,t){return t.type==="stylesheet"&&t.data.href?{...t,data:{...t.data,href:e.getFullUrl(t.data.href)}}:t}function w4(e){return{transformer:e,parser:new vt,beforeParse:new vt,afterParse:new vt,htmltag:new vt,retransform:new vt}}var Zr={name:ji,config:y4,transform(e){var t,r,o,s;let u,c=((r=(t=Zr.config)==null?void 0:t.preloadScripts)==null?void 0:r.map(y=>Ki(e.transformer.urlBuilder,y)))||[],h=()=>(u||(u=Un(c)),u),g=(y,S)=>{let{katex:T}=window;return T?T.renderToString(y,{displayMode:S,throwOnError:!1}):(h().then(()=>{e.retransform.call()}),y)},b=sr;return e.parser.tap(y=>{y.use(Ya.default),y.renderer.rules.katex=(S,T)=>(b(),g(S[T].content,!!S[T].block))}),e.beforeParse.tap((y,S)=>{b=()=>{S.features[ji]=!0}}),e.afterParse.tap((y,S)=>{var T;let N=(T=S.frontmatter)==null?void 0:T.markmap;N&&["extraJs","extraCss"].forEach(R=>{var z,P;let L=N[R];L&&(N[R]=b4(L,ji,((P=(z=Zr.config)==null?void 0:z.versions)==null?void 0:P.katex)||""))})}),{styles:(o=Zr.config)==null?void 0:o.styles,scripts:(s=Zr.config)==null?void 0:s.scripts}}},k4="frontmatter",S4={name:k4,transform(e){return e.beforeParse.tap((t,r)=>{let{content:o}=r;if(!/^---\r?\n/.test(o))return;let s=/\n---\r?\n/.exec(o);if(!s)return;let u=o.slice(4,s.index),c;try{c=Va.load(u),c!=null&&c.markmap&&(c.markmap=A4(c.markmap))}catch(h){return}r.frontmatter=c,r.content=o.slice(s.index+s[0].length),r.contentLineOffset=o.slice(0,s.index).split(`
`).length+1}),{}}};function A4(e){if(e)return["color","extraJs","extraCss"].forEach(t=>{e[t]!=null&&(e[t]=_4(e[t]))}),["duration","maxWidth","initialExpandLevel"].forEach(t=>{e[t]!=null&&(e[t]=M4(e[t]))}),e}function _4(e){let t;return typeof e=="string"?t=[e]:Array.isArray(e)&&(t=e.filter(r=>r&&typeof r=="string")),t!=null&&t.length?t:void 0}function M4(e){if(!isNaN(+e))return+e}var C4="npmUrl",T4={name:C4,transform(e){return e.afterParse.tap((t,r)=>{let{frontmatter:o}=r,s=o==null?void 0:o.markmap;s&&["extraJs","extraCss"].forEach(u=>{let c=s[u];c&&(s[u]=c.map(h=>h.startsWith("npm:")?e.transformer.urlBuilder.getFullUrl(h.slice(4)):h))})}),{}}},Ga="hljs",N4=["@highlightjs/cdn-assets@11.8.0/highlight.min.js"].map(e=>Gr(e)),E4=["@highlightjs/cdn-assets@11.8.0/styles/default.min.css"].map(e=>Ii(e)),z4={versions:{hljs:"11.8.0"},preloadScripts:N4,styles:E4},Wi={name:Ga,config:z4,transform(e){var t,r,o;let s,u=((r=(t=Wi.config)==null?void 0:t.preloadScripts)==null?void 0:r.map(g=>Ki(e.transformer.urlBuilder,g)))||[],c=()=>(s||(s=Un(u)),s),h=sr;return e.parser.tap(g=>{g.set({highlight:(b,y)=>{h();let{hljs:S}=window;return S?S.highlightAuto(b,y?[y]:void 0).value:(c().then(()=>{e.retransform.call()}),b)}})}),e.beforeParse.tap((g,b)=>{h=()=>{b.features[Ga]=!0}}),{styles:(o=Wi.config)==null?void 0:o.styles}}},I4=[S4,Zr,Wi,T4];function Xa(e){var o;var t,r;if(e.type==="heading")e.children=e.children.filter(s=>s.type!=="paragraph");else if(e.type==="list_item")e.children=e.children.filter(s=>["paragraph","fence"].includes(s.type)?(e.content||(e.content=s.content,e.payload={...e.payload,...s.payload}),!1):!0),((t=e.payload)==null?void 0:t.index)!=null&&(e.content=`${e.payload.index}. ${e.content}`);else if(e.type==="ordered_list"){let s=(o=(r=e.payload)==null?void 0:r.startIndex)!=null?o:1;e.children.forEach(u=>{u.type==="list_item"&&(u.payload={...u.payload,index:s},s+=1)})}e.children.length>0&&(e.children.forEach(s=>Xa(s)),e.children.length===1&&!e.children[0].content&&(e.children=e.children[0].children))}function ja(e,t=0){e.depth=t,e.children.forEach(r=>{ja(r,t+1)})}var Qn=class{constructor(t=I4){this.assetsMap={},this.urlBuilder=new Vr,this.hooks=w4(this),this.plugins=t.map(s=>typeof s=="function"?s():s);let r={};for(let{name:s,transform:u}of this.plugins)r[s]=u(this.hooks);this.assetsMap=r;let o=new Ft("full",{html:!0,breaks:!0,maxNesting:1/0});o.renderer.rules.htmltag=Bs(o.renderer.rules.htmltag,(s,...u)=>{let c=s(...u);return this.hooks.htmltag.call({args:u,result:c}),c}),this.md=o,this.hooks.parser.call(o)}buildTree(t){let{md:r}=this,o={type:"root",depth:0,content:"",children:[],payload:{}},s=[o],u=0;for(let c of t){let h={};c.lines&&(h.lines=c.lines);let g=s[s.length-1];if(c.type.endsWith("_open")){let b=c.type.slice(0,-5);if(b==="heading")for(u=c.hLevel;(g==null?void 0:g.depth)>=u;)s.pop(),g=s[s.length-1];else u=Math.max(u,(g==null?void 0:g.depth)||0)+1,b==="ordered_list"&&(h.startIndex=c.order);let y={type:b,depth:u,payload:h,content:"",children:[]};g.children.push(y),s.push(y)}else if(g){if(c.type===`${g.type}_close`)g.type==="heading"?u=g.depth:(s.pop(),u=0);else if(c.type==="inline"){let b=this.hooks.htmltag.tap(S=>{var T;let N=(T=S.result)==null?void 0:T.match(/^<!--([\s\S]*?)-->$/),R=N==null?void 0:N[1].trim().split(" ");(R==null?void 0:R[0])==="fold"&&(g.payload={...g.payload,fold:["all","recursively"].includes(R[1])?2:1},S.result="")}),y=r.renderer.render([c],r.options,{});b(),g.content=`${g.content||""}${y}`}else if(c.type==="fence"){let b=r.renderer.render([c],r.options,{});g.children.push({type:c.type,depth:u+1,content:b,children:[],payload:h})}}else continue}return o}transform(t){var r;let o={content:t,features:{},contentLineOffset:0};this.hooks.beforeParse.call(this.md,o);let s=this.md.parse(o.content,{});this.hooks.afterParse.call(this.md,o);let u=this.buildTree(s);return Xa(u),((r=u.children)==null?void 0:r.length)===1&&(u=u.children[0]),ja(u),{...o,root:u}}getAssets(t){let r=[],o=[];t!=null||(t=this.plugins.map(s=>s.name));for(let s of t.map(u=>this.assetsMap[u]))s&&(s.styles&&r.push(...s.styles),s.scripts&&o.push(...s.scripts));return{styles:r.map(s=>v4(this.urlBuilder,s)),scripts:o.map(s=>Ki(this.urlBuilder,s))}}getUsedAssets(t){let r=this.plugins.map(o=>o.name).filter(o=>t[o]);return this.getAssets(r)}fillTemplate(t,r,o){var y;o={...o},(y=o.baseJs)!=null||(o.baseJs=m4.map(S=>this.urlBuilder.getFullUrl(S)).map(S=>Gr(S)));let{scripts:s,styles:u}=r,c=[...u?zs(u):[]],h={getMarkmap:()=>window.markmap,getOptions:o.getOptions,jsonOptions:o.jsonOptions,root:t},g=[...Es([...o.baseJs,...s||[],{type:"iife",data:{fn:(S,T,N,R)=>{let z=S();window.mm=z.Markmap.create("svg#mindmap",(T||z.deriveOptions)(R),N)},getParams:({getMarkmap:S,getOptions:T,root:N,jsonOptions:R})=>[S,T,N,R]}}],h)];return d4.replace("<!--CSS-->",()=>c.join("")).replace("<!--JS-->",()=>g.join(""))}};var Cr=class extends Map{constructor(t,r=O4){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:r}}),t!=null)for(let[o,s]of t)this.set(o,s)}get(t){return super.get(Wa(this,t))}has(t){return super.has(Wa(this,t))}set(t,r){return super.set(D4(this,t),r)}delete(t){return super.delete(B4(this,t))}};function Wa({_intern:e,_key:t},r){let o=t(r);return e.has(o)?e.get(o):r}function D4({_intern:e,_key:t},r){let o=t(r);return e.has(o)?e.get(o):(e.set(o,r),r)}function B4({_intern:e,_key:t},r){let o=t(r);return e.has(o)&&(r=e.get(o),e.delete(o)),r}function O4(e){return e!==null&&typeof e=="object"?e.valueOf():e}function Jr(e,t){let r;if(t===void 0)for(let o of e)o!=null&&(r<o||r===void 0&&o>=o)&&(r=o);else{let o=-1;for(let s of e)(s=t(s,++o,e))!=null&&(r<s||r===void 0&&s>=s)&&(r=s)}return r}function Qr(e,t){let r;if(t===void 0)for(let o of e)o!=null&&(r>o||r===void 0&&o>=o)&&(r=o);else{let o=-1;for(let s of e)(s=t(s,++o,e))!=null&&(r>s||r===void 0&&s>=s)&&(r=s)}return r}function e0(e,t){let r,o=-1,s=-1;if(t===void 0)for(let u of e)++s,u!=null&&(r>u||r===void 0&&u>=u)&&(r=u,o=s);else for(let u of e)(u=t(u,++s,e))!=null&&(r>u||r===void 0&&u>=u)&&(r=u,o=s);return o}var R4={value:()=>{}};function Za(){for(var e=0,t=arguments.length,r={},o;e<t;++e){if(!(o=arguments[e]+"")||o in r||/[\s.]/.test(o))throw new Error("illegal type: "+o);r[o]=[]}return new t0(r)}function t0(e){this._=e}function F4(e,t){return e.trim().split(/^|\s+/).map(function(r){var o="",s=r.indexOf(".");if(s>=0&&(o=r.slice(s+1),r=r.slice(0,s)),r&&!t.hasOwnProperty(r))throw new Error("unknown type: "+r);return{type:r,name:o}})}t0.prototype=Za.prototype={constructor:t0,on:function(e,t){var r=this._,o=F4(e+"",r),s,u=-1,c=o.length;if(arguments.length<2){for(;++u<c;)if((s=(e=o[u]).type)&&(s=L4(r[s],e.name)))return s;return}if(t!=null&&typeof t!="function")throw new Error("invalid callback: "+t);for(;++u<c;)if(s=(e=o[u]).type)r[s]=Ka(r[s],e.name,t);else if(t==null)for(s in r)r[s]=Ka(r[s],e.name,null);return this},copy:function(){var e={},t=this._;for(var r in t)e[r]=t[r].slice();return new t0(e)},call:function(e,t){if((s=arguments.length-2)>0)for(var r=new Array(s),o=0,s,u;o<s;++o)r[o]=arguments[o+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(u=this._[e],o=0,s=u.length;o<s;++o)u[o].value.apply(t,r)},apply:function(e,t,r){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var o=this._[e],s=0,u=o.length;s<u;++s)o[s].value.apply(t,r)}};function L4(e,t){for(var r=0,o=e.length,s;r<o;++r)if((s=e[r]).name===t)return s.value}function Ka(e,t,r){for(var o=0,s=e.length;o<s;++o)if(e[o].name===t){e[o]=R4,e=e.slice(0,o).concat(e.slice(o+1));break}return r!=null&&e.push({name:t,value:r}),e}var en=Za;var r0="http://www.w3.org/1999/xhtml",Zi={svg:"http://www.w3.org/2000/svg",xhtml:r0,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function qt(e){var t=e+="",r=t.indexOf(":");return r>=0&&(t=e.slice(0,r))!=="xmlns"&&(e=e.slice(r+1)),Zi.hasOwnProperty(t)?{space:Zi[t],local:e}:e}function q4(e){return function(){var t=this.ownerDocument,r=this.namespaceURI;return r===r0&&t.documentElement.namespaceURI===r0?t.createElement(e):t.createElementNS(r,e)}}function P4(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function n0(e){var t=qt(e);return(t.local?P4:q4)(t)}function H4(){}function hr(e){return e==null?H4:function(){return this.querySelector(e)}}function Ja(e){typeof e!="function"&&(e=hr(e));for(var t=this._groups,r=t.length,o=new Array(r),s=0;s<r;++s)for(var u=t[s],c=u.length,h=o[s]=new Array(c),g,b,y=0;y<c;++y)(g=u[y])&&(b=e.call(g,g.__data__,y,u))&&("__data__"in g&&(b.__data__=g.__data__),h[y]=b);return new De(o,this._parents)}function Ji(e){return e==null?[]:Array.isArray(e)?e:Array.from(e)}function $4(){return[]}function tn(e){return e==null?$4:function(){return this.querySelectorAll(e)}}function V4(e){return function(){return Ji(e.apply(this,arguments))}}function Qa(e){typeof e=="function"?e=V4(e):e=tn(e);for(var t=this._groups,r=t.length,o=[],s=[],u=0;u<r;++u)for(var c=t[u],h=c.length,g,b=0;b<h;++b)(g=c[b])&&(o.push(e.call(g,g.__data__,b,c)),s.push(g));return new De(o,s)}function rn(e){return function(){return this.matches(e)}}function i0(e){return function(t){return t.matches(e)}}var G4=Array.prototype.find;function Y4(e){return function(){return G4.call(this.children,e)}}function U4(){return this.firstElementChild}function eu(e){return this.select(e==null?U4:Y4(typeof e=="function"?e:i0(e)))}var X4=Array.prototype.filter;function j4(){return Array.from(this.children)}function W4(e){return function(){return X4.call(this.children,e)}}function tu(e){return this.selectAll(e==null?j4:W4(typeof e=="function"?e:i0(e)))}function ru(e){typeof e!="function"&&(e=rn(e));for(var t=this._groups,r=t.length,o=new Array(r),s=0;s<r;++s)for(var u=t[s],c=u.length,h=o[s]=[],g,b=0;b<c;++b)(g=u[b])&&e.call(g,g.__data__,b,u)&&h.push(g);return new De(o,this._parents)}function o0(e){return new Array(e.length)}function nu(){return new De(this._enter||this._groups.map(o0),this._parents)}function nn(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}nn.prototype={constructor:nn,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};function iu(e){return function(){return e}}function K4(e,t,r,o,s,u){for(var c=0,h,g=t.length,b=u.length;c<b;++c)(h=t[c])?(h.__data__=u[c],o[c]=h):r[c]=new nn(e,u[c]);for(;c<g;++c)(h=t[c])&&(s[c]=h)}function Z4(e,t,r,o,s,u,c){var h,g,b=new Map,y=t.length,S=u.length,T=new Array(y),N;for(h=0;h<y;++h)(g=t[h])&&(T[h]=N=c.call(g,g.__data__,h,t)+"",b.has(N)?s[h]=g:b.set(N,g));for(h=0;h<S;++h)N=c.call(e,u[h],h,u)+"",(g=b.get(N))?(o[h]=g,g.__data__=u[h],b.delete(N)):r[h]=new nn(e,u[h]);for(h=0;h<y;++h)(g=t[h])&&b.get(T[h])===g&&(s[h]=g)}function J4(e){return e.__data__}function ou(e,t){if(!arguments.length)return Array.from(this,J4);var r=t?Z4:K4,o=this._parents,s=this._groups;typeof e!="function"&&(e=iu(e));for(var u=s.length,c=new Array(u),h=new Array(u),g=new Array(u),b=0;b<u;++b){var y=o[b],S=s[b],T=S.length,N=Q4(e.call(y,y&&y.__data__,b,o)),R=N.length,z=h[b]=new Array(R),P=c[b]=new Array(R),L=g[b]=new Array(T);r(y,S,z,P,L,N,t);for(var X=0,ne=0,ue,me;X<R;++X)if(ue=z[X]){for(X>=ne&&(ne=X+1);!(me=P[ne])&&++ne<R;);ue._next=me||null}}return c=new De(c,o),c._enter=h,c._exit=g,c}function Q4(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function lu(){return new De(this._exit||this._groups.map(o0),this._parents)}function su(e,t,r){var o=this.enter(),s=this,u=this.exit();return typeof e=="function"?(o=e(o),o&&(o=o.selection())):o=o.append(e+""),t!=null&&(s=t(s),s&&(s=s.selection())),r==null?u.remove():r(u),o&&s?o.merge(s).order():s}function au(e){for(var t=e.selection?e.selection():e,r=this._groups,o=t._groups,s=r.length,u=o.length,c=Math.min(s,u),h=new Array(s),g=0;g<c;++g)for(var b=r[g],y=o[g],S=b.length,T=h[g]=new Array(S),N,R=0;R<S;++R)(N=b[R]||y[R])&&(T[R]=N);for(;g<s;++g)h[g]=r[g];return new De(h,this._parents)}function uu(){for(var e=this._groups,t=-1,r=e.length;++t<r;)for(var o=e[t],s=o.length-1,u=o[s],c;--s>=0;)(c=o[s])&&(u&&c.compareDocumentPosition(u)^4&&u.parentNode.insertBefore(c,u),u=c);return this}function cu(e){e||(e=e2);function t(S,T){return S&&T?e(S.__data__,T.__data__):!S-!T}for(var r=this._groups,o=r.length,s=new Array(o),u=0;u<o;++u){for(var c=r[u],h=c.length,g=s[u]=new Array(h),b,y=0;y<h;++y)(b=c[y])&&(g[y]=b);g.sort(t)}return new De(s,this._parents).order()}function e2(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function hu(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this}function fu(){return Array.from(this)}function pu(){for(var e=this._groups,t=0,r=e.length;t<r;++t)for(var o=e[t],s=0,u=o.length;s<u;++s){var c=o[s];if(c)return c}return null}function du(){let e=0;for(let t of this)++e;return e}function mu(){return!this.node()}function gu(e){for(var t=this._groups,r=0,o=t.length;r<o;++r)for(var s=t[r],u=0,c=s.length,h;u<c;++u)(h=s[u])&&e.call(h,h.__data__,u,s);return this}function t2(e){return function(){this.removeAttribute(e)}}function r2(e){return function(){this.removeAttributeNS(e.space,e.local)}}function n2(e,t){return function(){this.setAttribute(e,t)}}function i2(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function o2(e,t){return function(){var r=t.apply(this,arguments);r==null?this.removeAttribute(e):this.setAttribute(e,r)}}function l2(e,t){return function(){var r=t.apply(this,arguments);r==null?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,r)}}function xu(e,t){var r=qt(e);if(arguments.length<2){var o=this.node();return r.local?o.getAttributeNS(r.space,r.local):o.getAttribute(r)}return this.each((t==null?r.local?r2:t2:typeof t=="function"?r.local?l2:o2:r.local?i2:n2)(r,t))}function l0(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function s2(e){return function(){this.style.removeProperty(e)}}function a2(e,t,r){return function(){this.style.setProperty(e,t,r)}}function u2(e,t,r){return function(){var o=t.apply(this,arguments);o==null?this.style.removeProperty(e):this.style.setProperty(e,o,r)}}function yu(e,t,r){return arguments.length>1?this.each((t==null?s2:typeof t=="function"?u2:a2)(e,t,r==null?"":r)):Kt(this.node(),e)}function Kt(e,t){return e.style.getPropertyValue(t)||l0(e).getComputedStyle(e,null).getPropertyValue(t)}function c2(e){return function(){delete this[e]}}function h2(e,t){return function(){this[e]=t}}function f2(e,t){return function(){var r=t.apply(this,arguments);r==null?delete this[e]:this[e]=r}}function bu(e,t){return arguments.length>1?this.each((t==null?c2:typeof t=="function"?f2:h2)(e,t)):this.node()[e]}function vu(e){return e.trim().split(/^|\s+/)}function Qi(e){return e.classList||new wu(e)}function wu(e){this._node=e,this._names=vu(e.getAttribute("class")||"")}wu.prototype={add:function(e){var t=this._names.indexOf(e);t<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};function ku(e,t){for(var r=Qi(e),o=-1,s=t.length;++o<s;)r.add(t[o])}function Su(e,t){for(var r=Qi(e),o=-1,s=t.length;++o<s;)r.remove(t[o])}function p2(e){return function(){ku(this,e)}}function d2(e){return function(){Su(this,e)}}function m2(e,t){return function(){(t.apply(this,arguments)?ku:Su)(this,e)}}function Au(e,t){var r=vu(e+"");if(arguments.length<2){for(var o=Qi(this.node()),s=-1,u=r.length;++s<u;)if(!o.contains(r[s]))return!1;return!0}return this.each((typeof t=="function"?m2:t?p2:d2)(r,t))}function g2(){this.textContent=""}function x2(e){return function(){this.textContent=e}}function y2(e){return function(){var t=e.apply(this,arguments);this.textContent=t==null?"":t}}function _u(e){return arguments.length?this.each(e==null?g2:(typeof e=="function"?y2:x2)(e)):this.node().textContent}function b2(){this.innerHTML=""}function v2(e){return function(){this.innerHTML=e}}function w2(e){return function(){var t=e.apply(this,arguments);this.innerHTML=t==null?"":t}}function Mu(e){return arguments.length?this.each(e==null?b2:(typeof e=="function"?w2:v2)(e)):this.node().innerHTML}function k2(){this.nextSibling&&this.parentNode.appendChild(this)}function Cu(){return this.each(k2)}function S2(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Tu(){return this.each(S2)}function Nu(e){var t=typeof e=="function"?e:n0(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})}function A2(){return null}function Eu(e,t){var r=typeof e=="function"?e:n0(e),o=t==null?A2:typeof t=="function"?t:hr(t);return this.select(function(){return this.insertBefore(r.apply(this,arguments),o.apply(this,arguments)||null)})}function _2(){var e=this.parentNode;e&&e.removeChild(this)}function zu(){return this.each(_2)}function M2(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function C2(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function Iu(e){return this.select(e?C2:M2)}function Du(e){return arguments.length?this.property("__data__",e):this.node().__data__}function T2(e){return function(t){e.call(this,t,this.__data__)}}function N2(e){return e.trim().split(/^|\s+/).map(function(t){var r="",o=t.indexOf(".");return o>=0&&(r=t.slice(o+1),t=t.slice(0,o)),{type:t,name:r}})}function E2(e){return function(){var t=this.__on;if(t){for(var r=0,o=-1,s=t.length,u;r<s;++r)u=t[r],(!e.type||u.type===e.type)&&u.name===e.name?this.removeEventListener(u.type,u.listener,u.options):t[++o]=u;++o?t.length=o:delete this.__on}}}function z2(e,t,r){return function(){var o=this.__on,s,u=T2(t);if(o){for(var c=0,h=o.length;c<h;++c)if((s=o[c]).type===e.type&&s.name===e.name){this.removeEventListener(s.type,s.listener,s.options),this.addEventListener(s.type,s.listener=u,s.options=r),s.value=t;return}}this.addEventListener(e.type,u,r),s={type:e.type,name:e.name,value:t,listener:u,options:r},o?o.push(s):this.__on=[s]}}function Bu(e,t,r){var o=N2(e+""),s,u=o.length,c;if(arguments.length<2){var h=this.node().__on;if(h){for(var g=0,b=h.length,y;g<b;++g)for(s=0,y=h[g];s<u;++s)if((c=o[s]).type===y.type&&c.name===y.name)return y.value}return}for(h=t?z2:E2,s=0;s<u;++s)this.each(h(o[s],t,r));return this}function Ou(e,t,r){var o=l0(e),s=o.CustomEvent;typeof s=="function"?s=new s(t,r):(s=o.document.createEvent("Event"),r?(s.initEvent(t,r.bubbles,r.cancelable),s.detail=r.detail):s.initEvent(t,!1,!1)),e.dispatchEvent(s)}function I2(e,t){return function(){return Ou(this,e,t)}}function D2(e,t){return function(){return Ou(this,e,t.apply(this,arguments))}}function Ru(e,t){return this.each((typeof t=="function"?D2:I2)(e,t))}function*Fu(){for(var e=this._groups,t=0,r=e.length;t<r;++t)for(var o=e[t],s=0,u=o.length,c;s<u;++s)(c=o[s])&&(yield c)}var eo=[null];function De(e,t){this._groups=e,this._parents=t}function Lu(){return new De([[document.documentElement]],eo)}function B2(){return this}De.prototype=Lu.prototype={constructor:De,select:Ja,selectAll:Qa,selectChild:eu,selectChildren:tu,filter:ru,data:ou,enter:nu,exit:lu,join:su,merge:au,selection:B2,order:uu,sort:cu,call:hu,nodes:fu,node:pu,size:du,empty:mu,each:gu,attr:xu,style:yu,property:bu,classed:Au,text:_u,html:Mu,raise:Cu,lower:Tu,append:Nu,insert:Eu,remove:zu,clone:Iu,datum:Du,on:Bu,dispatch:Ru,[Symbol.iterator]:Fu};var Pt=Lu;function rt(e){return typeof e=="string"?new De([[document.querySelector(e)]],[document.documentElement]):new De([[e]],eo)}function qu(e){let t;for(;t=e.sourceEvent;)e=t;return e}function Ht(e,t){if(e=qu(e),t===void 0&&(t=e.currentTarget),t){var r=t.ownerSVGElement||t;if(r.createSVGPoint){var o=r.createSVGPoint();return o.x=e.clientX,o.y=e.clientY,o=o.matrixTransform(t.getScreenCTM().inverse()),[o.x,o.y]}if(t.getBoundingClientRect){var s=t.getBoundingClientRect();return[e.clientX-s.left-t.clientLeft,e.clientY-s.top-t.clientTop]}}return[e.pageX,e.pageY]}var s0={capture:!0,passive:!1};function a0(e){e.preventDefault(),e.stopImmediatePropagation()}function to(e){var t=e.document.documentElement,r=rt(e).on("dragstart.drag",a0,s0);"onselectstart"in t?r.on("selectstart.drag",a0,s0):(t.__noselect=t.style.MozUserSelect,t.style.MozUserSelect="none")}function ro(e,t){var r=e.document.documentElement,o=rt(e).on("dragstart.drag",null);t&&(o.on("click.drag",a0,s0),setTimeout(function(){o.on("click.drag",null)},0)),"onselectstart"in r?o.on("selectstart.drag",null):(r.style.MozUserSelect=r.__noselect,delete r.__noselect)}function u0(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function no(e,t){var r=Object.create(e.prototype);for(var o in t)r[o]=t[o];return r}function sn(){}var on=.7,f0=1/on,Tr="\\s*([+-]?\\d+)\\s*",ln="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Nt="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",O2=/^#([0-9a-f]{3,8})$/,R2=new RegExp(`^rgb\\(${Tr},${Tr},${Tr}\\)$`),F2=new RegExp(`^rgb\\(${Nt},${Nt},${Nt}\\)$`),L2=new RegExp(`^rgba\\(${Tr},${Tr},${Tr},${ln}\\)$`),q2=new RegExp(`^rgba\\(${Nt},${Nt},${Nt},${ln}\\)$`),P2=new RegExp(`^hsl\\(${ln},${Nt},${Nt}\\)$`),H2=new RegExp(`^hsla\\(${ln},${Nt},${Nt},${ln}\\)$`),Pu={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};u0(sn,Zt,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Hu,formatHex:Hu,formatHex8:$2,formatHsl:V2,formatRgb:$u,toString:$u});function Hu(){return this.rgb().formatHex()}function $2(){return this.rgb().formatHex8()}function V2(){return ju(this).formatHsl()}function $u(){return this.rgb().formatRgb()}function Zt(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=O2.exec(e))?(r=t[1].length,t=parseInt(t[1],16),r===6?Vu(t):r===3?new nt(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):r===8?c0(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):r===4?c0(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=R2.exec(e))?new nt(t[1],t[2],t[3],1):(t=F2.exec(e))?new nt(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=L2.exec(e))?c0(t[1],t[2],t[3],t[4]):(t=q2.exec(e))?c0(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=P2.exec(e))?Uu(t[1],t[2]/100,t[3]/100,1):(t=H2.exec(e))?Uu(t[1],t[2]/100,t[3]/100,t[4]):Pu.hasOwnProperty(e)?Vu(Pu[e]):e==="transparent"?new nt(NaN,NaN,NaN,0):null}function Vu(e){return new nt(e>>16&255,e>>8&255,e&255,1)}function c0(e,t,r,o){return o<=0&&(e=t=r=NaN),new nt(e,t,r,o)}function G2(e){return e instanceof sn||(e=Zt(e)),e?(e=e.rgb(),new nt(e.r,e.g,e.b,e.opacity)):new nt}function Nr(e,t,r,o){return arguments.length===1?G2(e):new nt(e,t,r,o==null?1:o)}function nt(e,t,r,o){this.r=+e,this.g=+t,this.b=+r,this.opacity=+o}u0(nt,Nr,no(sn,{brighter(e){return e=e==null?f0:Math.pow(f0,e),new nt(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?on:Math.pow(on,e),new nt(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new nt(pr(this.r),pr(this.g),pr(this.b),p0(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Gu,formatHex:Gu,formatHex8:Y2,formatRgb:Yu,toString:Yu}));function Gu(){return`#${fr(this.r)}${fr(this.g)}${fr(this.b)}`}function Y2(){return`#${fr(this.r)}${fr(this.g)}${fr(this.b)}${fr((isNaN(this.opacity)?1:this.opacity)*255)}`}function Yu(){let e=p0(this.opacity);return`${e===1?"rgb(":"rgba("}${pr(this.r)}, ${pr(this.g)}, ${pr(this.b)}${e===1?")":`, ${e})`}`}function p0(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function pr(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function fr(e){return e=pr(e),(e<16?"0":"")+e.toString(16)}function Uu(e,t,r,o){return o<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new wt(e,t,r,o)}function ju(e){if(e instanceof wt)return new wt(e.h,e.s,e.l,e.opacity);if(e instanceof sn||(e=Zt(e)),!e)return new wt;if(e instanceof wt)return e;e=e.rgb();var t=e.r/255,r=e.g/255,o=e.b/255,s=Math.min(t,r,o),u=Math.max(t,r,o),c=NaN,h=u-s,g=(u+s)/2;return h?(t===u?c=(r-o)/h+(r<o)*6:r===u?c=(o-t)/h+2:c=(t-r)/h+4,h/=g<.5?u+s:2-u-s,c*=60):h=g>0&&g<1?0:c,new wt(c,h,g,e.opacity)}function Wu(e,t,r,o){return arguments.length===1?ju(e):new wt(e,t,r,o==null?1:o)}function wt(e,t,r,o){this.h=+e,this.s=+t,this.l=+r,this.opacity=+o}u0(wt,Wu,no(sn,{brighter(e){return e=e==null?f0:Math.pow(f0,e),new wt(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?on:Math.pow(on,e),new wt(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,o=r+(r<.5?r:1-r)*t,s=2*r-o;return new nt(io(e>=240?e-240:e+120,s,o),io(e,s,o),io(e<120?e+240:e-120,s,o),this.opacity)},clamp(){return new wt(Xu(this.h),h0(this.s),h0(this.l),p0(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=p0(this.opacity);return`${e===1?"hsl(":"hsla("}${Xu(this.h)}, ${h0(this.s)*100}%, ${h0(this.l)*100}%${e===1?")":`, ${e})`}`}}));function Xu(e){return e=(e||0)%360,e<0?e+360:e}function h0(e){return Math.max(0,Math.min(1,e||0))}function io(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function oo(e,t,r,o,s){var u=e*e,c=u*e;return((1-3*e+3*u-c)*t+(4-6*u+3*c)*r+(1+3*e+3*u-3*c)*o+c*s)/6}function Ku(e){var t=e.length-1;return function(r){var o=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),s=e[o],u=e[o+1],c=o>0?e[o-1]:2*s-u,h=o<t-1?e[o+2]:2*u-s;return oo((r-o/t)*t,c,s,u,h)}}function Zu(e){var t=e.length;return function(r){var o=Math.floor(((r%=1)<0?++r:r)*t),s=e[(o+t-1)%t],u=e[o%t],c=e[(o+1)%t],h=e[(o+2)%t];return oo((r-o/t)*t,s,u,c,h)}}var lo=e=>()=>e;function U2(e,t){return function(r){return e+r*t}}function X2(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(o){return Math.pow(e+o*t,r)}}function Ju(e){return(e=+e)==1?d0:function(t,r){return r-t?X2(t,r,e):lo(isNaN(t)?r:t)}}function d0(e,t){var r=t-e;return r?U2(e,r):lo(isNaN(e)?t:e)}var m0=function e(t){var r=Ju(t);function o(s,u){var c=r((s=Nr(s)).r,(u=Nr(u)).r),h=r(s.g,u.g),g=r(s.b,u.b),b=d0(s.opacity,u.opacity);return function(y){return s.r=c(y),s.g=h(y),s.b=g(y),s.opacity=b(y),s+""}}return o.gamma=e,o}(1);function Qu(e){return function(t){var r=t.length,o=new Array(r),s=new Array(r),u=new Array(r),c,h;for(c=0;c<r;++c)h=Nr(t[c]),o[c]=h.r||0,s[c]=h.g||0,u[c]=h.b||0;return o=e(o),s=e(s),u=e(u),h.opacity=1,function(g){return h.r=o(g),h.g=s(g),h.b=u(g),h+""}}}var j2=Qu(Ku),W2=Qu(Zu);function gt(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}var ao=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,so=new RegExp(ao.source,"g");function K2(e){return function(){return e}}function Z2(e){return function(t){return e(t)+""}}function uo(e,t){var r=ao.lastIndex=so.lastIndex=0,o,s,u,c=-1,h=[],g=[];for(e=e+"",t=t+"";(o=ao.exec(e))&&(s=so.exec(t));)(u=s.index)>r&&(u=t.slice(r,u),h[c]?h[c]+=u:h[++c]=u),(o=o[0])===(s=s[0])?h[c]?h[c]+=s:h[++c]=s:(h[++c]=null,g.push({i:c,x:gt(o,s)})),r=so.lastIndex;return r<t.length&&(u=t.slice(r),h[c]?h[c]+=u:h[++c]=u),h.length<2?g[0]?Z2(g[0].x):K2(t):(t=g.length,function(b){for(var y=0,S;y<t;++y)h[(S=g[y]).i]=S.x(b);return h.join("")})}var ec=180/Math.PI,g0={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function co(e,t,r,o,s,u){var c,h,g;return(c=Math.sqrt(e*e+t*t))&&(e/=c,t/=c),(g=e*r+t*o)&&(r-=e*g,o-=t*g),(h=Math.sqrt(r*r+o*o))&&(r/=h,o/=h,g/=h),e*o<t*r&&(e=-e,t=-t,g=-g,c=-c),{translateX:s,translateY:u,rotate:Math.atan2(t,e)*ec,skewX:Math.atan(g)*ec,scaleX:c,scaleY:h}}var x0;function tc(e){let t=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?g0:co(t.a,t.b,t.c,t.d,t.e,t.f)}function rc(e){return e==null?g0:(x0||(x0=document.createElementNS("http://www.w3.org/2000/svg","g")),x0.setAttribute("transform",e),(e=x0.transform.baseVal.consolidate())?(e=e.matrix,co(e.a,e.b,e.c,e.d,e.e,e.f)):g0)}function nc(e,t,r,o){function s(b){return b.length?b.pop()+" ":""}function u(b,y,S,T,N,R){if(b!==S||y!==T){var z=N.push("translate(",null,t,null,r);R.push({i:z-4,x:gt(b,S)},{i:z-2,x:gt(y,T)})}else(S||T)&&N.push("translate("+S+t+T+r)}function c(b,y,S,T){b!==y?(b-y>180?y+=360:y-b>180&&(b+=360),T.push({i:S.push(s(S)+"rotate(",null,o)-2,x:gt(b,y)})):y&&S.push(s(S)+"rotate("+y+o)}function h(b,y,S,T){b!==y?T.push({i:S.push(s(S)+"skewX(",null,o)-2,x:gt(b,y)}):y&&S.push(s(S)+"skewX("+y+o)}function g(b,y,S,T,N,R){if(b!==S||y!==T){var z=N.push(s(N)+"scale(",null,",",null,")");R.push({i:z-4,x:gt(b,S)},{i:z-2,x:gt(y,T)})}else(S!==1||T!==1)&&N.push(s(N)+"scale("+S+","+T+")")}return function(b,y){var S=[],T=[];return b=e(b),y=e(y),u(b.translateX,b.translateY,y.translateX,y.translateY,S,T),c(b.rotate,y.rotate,S,T),h(b.skewX,y.skewX,S,T),g(b.scaleX,b.scaleY,y.scaleX,y.scaleY,S,T),b=y=null,function(N){for(var R=-1,z=T.length,P;++R<z;)S[(P=T[R]).i]=P.x(N);return S.join("")}}}var ho=nc(tc,"px, ","px)","deg)"),fo=nc(rc,", ",")",")");var J2=1e-12;function ic(e){return((e=Math.exp(e))+1/e)/2}function Q2(e){return((e=Math.exp(e))-1/e)/2}function e5(e){return((e=Math.exp(2*e))-1)/(e+1)}var po=function e(t,r,o){function s(u,c){var h=u[0],g=u[1],b=u[2],y=c[0],S=c[1],T=c[2],N=y-h,R=S-g,z=N*N+R*R,P,L;if(z<J2)L=Math.log(T/b)/t,P=function(Se){return[h+Se*N,g+Se*R,b*Math.exp(t*Se*L)]};else{var X=Math.sqrt(z),ne=(T*T-b*b+o*z)/(2*b*r*X),ue=(T*T-b*b-o*z)/(2*T*r*X),me=Math.log(Math.sqrt(ne*ne+1)-ne),be=Math.log(Math.sqrt(ue*ue+1)-ue);L=(be-me)/t,P=function(Se){var $e=Se*L,ze=ic(me),Ve=b/(r*X)*(ze*e5(t*$e+me)-Q2(me));return[h+Ve*N,g+Ve*R,b*ze/ic(t*$e+me)]}}return P.duration=L*1e3*t/Math.SQRT2,P}return s.rho=function(u){var c=Math.max(.001,+u),h=c*c,g=h*h;return e(c,h,g)},s}(Math.SQRT2,2,4);var Er=0,un=0,an=0,lc=1e3,y0,cn,b0=0,dr=0,v0=0,hn=typeof performance=="object"&&performance.now?performance:Date,sc=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function pn(){return dr||(sc(t5),dr=hn.now()+v0)}function t5(){dr=0}function fn(){this._call=this._time=this._next=null}fn.prototype=w0.prototype={constructor:fn,restart:function(e,t,r){if(typeof e!="function")throw new TypeError("callback is not a function");r=(r==null?pn():+r)+(t==null?0:+t),!this._next&&cn!==this&&(cn?cn._next=this:y0=this,cn=this),this._call=e,this._time=r,mo()},stop:function(){this._call&&(this._call=null,this._time=1/0,mo())}};function w0(e,t,r){var o=new fn;return o.restart(e,t,r),o}function ac(){pn(),++Er;for(var e=y0,t;e;)(t=dr-e._time)>=0&&e._call.call(void 0,t),e=e._next;--Er}function oc(){dr=(b0=hn.now())+v0,Er=un=0;try{ac()}finally{Er=0,n5(),dr=0}}function r5(){var e=hn.now(),t=e-b0;t>lc&&(v0-=t,b0=e)}function n5(){for(var e,t=y0,r,o=1/0;t;)t._call?(o>t._time&&(o=t._time),e=t,t=t._next):(r=t._next,t._next=null,t=e?e._next=r:y0=r);cn=e,mo(o)}function mo(e){if(!Er){un&&(un=clearTimeout(un));var t=e-dr;t>24?(e<1/0&&(un=setTimeout(oc,e-hn.now()-v0)),an&&(an=clearInterval(an))):(an||(b0=hn.now(),an=setInterval(r5,lc)),Er=1,sc(oc))}}function k0(e,t,r){var o=new fn;return t=t==null?0:+t,o.restart(s=>{o.stop(),e(s+t)},t,r),o}var i5=en("start","end","cancel","interrupt"),o5=[],hc=0,uc=1,A0=2,S0=3,cc=4,_0=5,dn=6;function Jt(e,t,r,o,s,u){var c=e.__transition;if(!c)e.__transition={};else if(r in c)return;l5(e,r,{name:t,index:o,group:s,on:i5,tween:o5,time:u.time,delay:u.delay,duration:u.duration,ease:u.ease,timer:null,state:hc})}function mn(e,t){var r=He(e,t);if(r.state>hc)throw new Error("too late; already scheduled");return r}function Ye(e,t){var r=He(e,t);if(r.state>S0)throw new Error("too late; already running");return r}function He(e,t){var r=e.__transition;if(!r||!(r=r[t]))throw new Error("transition not found");return r}function l5(e,t,r){var o=e.__transition,s;o[t]=r,r.timer=w0(u,0,r.time);function u(b){r.state=uc,r.timer.restart(c,r.delay,r.time),r.delay<=b&&c(b-r.delay)}function c(b){var y,S,T,N;if(r.state!==uc)return g();for(y in o)if(N=o[y],N.name===r.name){if(N.state===S0)return k0(c);N.state===cc?(N.state=dn,N.timer.stop(),N.on.call("interrupt",e,e.__data__,N.index,N.group),delete o[y]):+y<t&&(N.state=dn,N.timer.stop(),N.on.call("cancel",e,e.__data__,N.index,N.group),delete o[y])}if(k0(function(){r.state===S0&&(r.state=cc,r.timer.restart(h,r.delay,r.time),h(b))}),r.state=A0,r.on.call("start",e,e.__data__,r.index,r.group),r.state===A0){for(r.state=S0,s=new Array(T=r.tween.length),y=0,S=-1;y<T;++y)(N=r.tween[y].value.call(e,e.__data__,r.index,r.group))&&(s[++S]=N);s.length=S+1}}function h(b){for(var y=b<r.duration?r.ease.call(null,b/r.duration):(r.timer.restart(g),r.state=_0,1),S=-1,T=s.length;++S<T;)s[S].call(e,y);r.state===_0&&(r.on.call("end",e,e.__data__,r.index,r.group),g())}function g(){r.state=dn,r.timer.stop(),delete o[t];for(var b in o)return;delete e.__transition}}function Qt(e,t){var r=e.__transition,o,s,u=!0,c;if(r){t=t==null?null:t+"";for(c in r){if((o=r[c]).name!==t){u=!1;continue}s=o.state>A0&&o.state<_0,o.state=dn,o.timer.stop(),o.on.call(s?"interrupt":"cancel",e,e.__data__,o.index,o.group),delete r[c]}u&&delete e.__transition}}function fc(e){return this.each(function(){Qt(this,e)})}function s5(e,t){var r,o;return function(){var s=Ye(this,e),u=s.tween;if(u!==r){o=r=u;for(var c=0,h=o.length;c<h;++c)if(o[c].name===t){o=o.slice(),o.splice(c,1);break}}s.tween=o}}function a5(e,t,r){var o,s;if(typeof r!="function")throw new Error;return function(){var u=Ye(this,e),c=u.tween;if(c!==o){s=(o=c).slice();for(var h={name:t,value:r},g=0,b=s.length;g<b;++g)if(s[g].name===t){s[g]=h;break}g===b&&s.push(h)}u.tween=s}}function pc(e,t){var r=this._id;if(e+="",arguments.length<2){for(var o=He(this.node(),r).tween,s=0,u=o.length,c;s<u;++s)if((c=o[s]).name===e)return c.value;return null}return this.each((t==null?s5:a5)(r,e,t))}function zr(e,t,r){var o=e._id;return e.each(function(){var s=Ye(this,o);(s.value||(s.value={}))[t]=r.apply(this,arguments)}),function(s){return He(s,o).value[t]}}function M0(e,t){var r;return(typeof t=="number"?gt:t instanceof Zt?m0:(r=Zt(t))?(t=r,m0):uo)(e,t)}function u5(e){return function(){this.removeAttribute(e)}}function c5(e){return function(){this.removeAttributeNS(e.space,e.local)}}function h5(e,t,r){var o,s=r+"",u;return function(){var c=this.getAttribute(e);return c===s?null:c===o?u:u=t(o=c,r)}}function f5(e,t,r){var o,s=r+"",u;return function(){var c=this.getAttributeNS(e.space,e.local);return c===s?null:c===o?u:u=t(o=c,r)}}function p5(e,t,r){var o,s,u;return function(){var c,h=r(this),g;return h==null?void this.removeAttribute(e):(c=this.getAttribute(e),g=h+"",c===g?null:c===o&&g===s?u:(s=g,u=t(o=c,h)))}}function d5(e,t,r){var o,s,u;return function(){var c,h=r(this),g;return h==null?void this.removeAttributeNS(e.space,e.local):(c=this.getAttributeNS(e.space,e.local),g=h+"",c===g?null:c===o&&g===s?u:(s=g,u=t(o=c,h)))}}function dc(e,t){var r=qt(e),o=r==="transform"?fo:M0;return this.attrTween(e,typeof t=="function"?(r.local?d5:p5)(r,o,zr(this,"attr."+e,t)):t==null?(r.local?c5:u5)(r):(r.local?f5:h5)(r,o,t))}function m5(e,t){return function(r){this.setAttribute(e,t.call(this,r))}}function g5(e,t){return function(r){this.setAttributeNS(e.space,e.local,t.call(this,r))}}function x5(e,t){var r,o;function s(){var u=t.apply(this,arguments);return u!==o&&(r=(o=u)&&g5(e,u)),r}return s._value=t,s}function y5(e,t){var r,o;function s(){var u=t.apply(this,arguments);return u!==o&&(r=(o=u)&&m5(e,u)),r}return s._value=t,s}function mc(e,t){var r="attr."+e;if(arguments.length<2)return(r=this.tween(r))&&r._value;if(t==null)return this.tween(r,null);if(typeof t!="function")throw new Error;var o=qt(e);return this.tween(r,(o.local?x5:y5)(o,t))}function b5(e,t){return function(){mn(this,e).delay=+t.apply(this,arguments)}}function v5(e,t){return t=+t,function(){mn(this,e).delay=t}}function gc(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?b5:v5)(t,e)):He(this.node(),t).delay}function w5(e,t){return function(){Ye(this,e).duration=+t.apply(this,arguments)}}function k5(e,t){return t=+t,function(){Ye(this,e).duration=t}}function xc(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?w5:k5)(t,e)):He(this.node(),t).duration}function S5(e,t){if(typeof t!="function")throw new Error;return function(){Ye(this,e).ease=t}}function yc(e){var t=this._id;return arguments.length?this.each(S5(t,e)):He(this.node(),t).ease}function A5(e,t){return function(){var r=t.apply(this,arguments);if(typeof r!="function")throw new Error;Ye(this,e).ease=r}}function bc(e){if(typeof e!="function")throw new Error;return this.each(A5(this._id,e))}function vc(e){typeof e!="function"&&(e=rn(e));for(var t=this._groups,r=t.length,o=new Array(r),s=0;s<r;++s)for(var u=t[s],c=u.length,h=o[s]=[],g,b=0;b<c;++b)(g=u[b])&&e.call(g,g.__data__,b,u)&&h.push(g);return new Ze(o,this._parents,this._name,this._id)}function wc(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,r=e._groups,o=t.length,s=r.length,u=Math.min(o,s),c=new Array(o),h=0;h<u;++h)for(var g=t[h],b=r[h],y=g.length,S=c[h]=new Array(y),T,N=0;N<y;++N)(T=g[N]||b[N])&&(S[N]=T);for(;h<o;++h)c[h]=t[h];return new Ze(c,this._parents,this._name,this._id)}function _5(e){return(e+"").trim().split(/^|\s+/).every(function(t){var r=t.indexOf(".");return r>=0&&(t=t.slice(0,r)),!t||t==="start"})}function M5(e,t,r){var o,s,u=_5(t)?mn:Ye;return function(){var c=u(this,e),h=c.on;h!==o&&(s=(o=h).copy()).on(t,r),c.on=s}}function kc(e,t){var r=this._id;return arguments.length<2?He(this.node(),r).on.on(e):this.each(M5(r,e,t))}function C5(e){return function(){var t=this.parentNode;for(var r in this.__transition)if(+r!==e)return;t&&t.removeChild(this)}}function Sc(){return this.on("end.remove",C5(this._id))}function Ac(e){var t=this._name,r=this._id;typeof e!="function"&&(e=hr(e));for(var o=this._groups,s=o.length,u=new Array(s),c=0;c<s;++c)for(var h=o[c],g=h.length,b=u[c]=new Array(g),y,S,T=0;T<g;++T)(y=h[T])&&(S=e.call(y,y.__data__,T,h))&&("__data__"in y&&(S.__data__=y.__data__),b[T]=S,Jt(b[T],t,r,T,b,He(y,r)));return new Ze(u,this._parents,t,r)}function _c(e){var t=this._name,r=this._id;typeof e!="function"&&(e=tn(e));for(var o=this._groups,s=o.length,u=[],c=[],h=0;h<s;++h)for(var g=o[h],b=g.length,y,S=0;S<b;++S)if(y=g[S]){for(var T=e.call(y,y.__data__,S,g),N,R=He(y,r),z=0,P=T.length;z<P;++z)(N=T[z])&&Jt(N,t,r,z,T,R);u.push(T),c.push(y)}return new Ze(u,c,t,r)}var T5=Pt.prototype.constructor;function Mc(){return new T5(this._groups,this._parents)}function N5(e,t){var r,o,s;return function(){var u=Kt(this,e),c=(this.style.removeProperty(e),Kt(this,e));return u===c?null:u===r&&c===o?s:s=t(r=u,o=c)}}function Cc(e){return function(){this.style.removeProperty(e)}}function E5(e,t,r){var o,s=r+"",u;return function(){var c=Kt(this,e);return c===s?null:c===o?u:u=t(o=c,r)}}function z5(e,t,r){var o,s,u;return function(){var c=Kt(this,e),h=r(this),g=h+"";return h==null&&(g=h=(this.style.removeProperty(e),Kt(this,e))),c===g?null:c===o&&g===s?u:(s=g,u=t(o=c,h))}}function I5(e,t){var r,o,s,u="style."+t,c="end."+u,h;return function(){var g=Ye(this,e),b=g.on,y=g.value[u]==null?h||(h=Cc(t)):void 0;(b!==r||s!==y)&&(o=(r=b).copy()).on(c,s=y),g.on=o}}function Tc(e,t,r){var o=(e+="")=="transform"?ho:M0;return t==null?this.styleTween(e,N5(e,o)).on("end.style."+e,Cc(e)):typeof t=="function"?this.styleTween(e,z5(e,o,zr(this,"style."+e,t))).each(I5(this._id,e)):this.styleTween(e,E5(e,o,t),r).on("end.style."+e,null)}function D5(e,t,r){return function(o){this.style.setProperty(e,t.call(this,o),r)}}function B5(e,t,r){var o,s;function u(){var c=t.apply(this,arguments);return c!==s&&(o=(s=c)&&D5(e,c,r)),o}return u._value=t,u}function Nc(e,t,r){var o="style."+(e+="");if(arguments.length<2)return(o=this.tween(o))&&o._value;if(t==null)return this.tween(o,null);if(typeof t!="function")throw new Error;return this.tween(o,B5(e,t,r==null?"":r))}function O5(e){return function(){this.textContent=e}}function R5(e){return function(){var t=e(this);this.textContent=t==null?"":t}}function Ec(e){return this.tween("text",typeof e=="function"?R5(zr(this,"text",e)):O5(e==null?"":e+""))}function F5(e){return function(t){this.textContent=e.call(this,t)}}function L5(e){var t,r;function o(){var s=e.apply(this,arguments);return s!==r&&(t=(r=s)&&F5(s)),t}return o._value=e,o}function zc(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(e==null)return this.tween(t,null);if(typeof e!="function")throw new Error;return this.tween(t,L5(e))}function Ic(){for(var e=this._name,t=this._id,r=C0(),o=this._groups,s=o.length,u=0;u<s;++u)for(var c=o[u],h=c.length,g,b=0;b<h;++b)if(g=c[b]){var y=He(g,t);Jt(g,e,r,b,c,{time:y.time+y.delay+y.duration,delay:0,duration:y.duration,ease:y.ease})}return new Ze(o,this._parents,e,r)}function Dc(){var e,t,r=this,o=r._id,s=r.size();return new Promise(function(u,c){var h={value:c},g={value:function(){--s===0&&u()}};r.each(function(){var b=Ye(this,o),y=b.on;y!==e&&(t=(e=y).copy(),t._.cancel.push(h),t._.interrupt.push(h),t._.end.push(g)),b.on=t}),s===0&&u()})}var q5=0;function Ze(e,t,r,o){this._groups=e,this._parents=t,this._name=r,this._id=o}function Bc(e){return Pt().transition(e)}function C0(){return++q5}var $t=Pt.prototype;Ze.prototype=Bc.prototype={constructor:Ze,select:Ac,selectAll:_c,selectChild:$t.selectChild,selectChildren:$t.selectChildren,filter:vc,merge:wc,selection:Mc,transition:Ic,call:$t.call,nodes:$t.nodes,node:$t.node,size:$t.size,empty:$t.empty,each:$t.each,on:kc,attr:dc,attrTween:mc,style:Tc,styleTween:Nc,text:Ec,textTween:zc,remove:Sc,tween:pc,delay:gc,duration:xc,ease:yc,easeVarying:bc,end:Dc,[Symbol.iterator]:$t[Symbol.iterator]};function T0(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}var P5={time:null,delay:0,duration:250,ease:T0};function H5(e,t){for(var r;!(r=e.__transition)||!(r=r[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return r}function Oc(e){var t,r;e instanceof Ze?(t=e._id,e=e._name):(t=C0(),(r=P5).time=pn(),e=e==null?null:e+"");for(var o=this._groups,s=o.length,u=0;u<s;++u)for(var c=o[u],h=c.length,g,b=0;b<h;++b)(g=c[b])&&Jt(g,e,t,b,c,r||H5(g,t));return new Ze(o,this._parents,e,t)}Pt.prototype.interrupt=fc;Pt.prototype.transition=Oc;var{abs:Ux,max:Xx,min:jx}=Math;function Rc(e){return[+e[0],+e[1]]}function $5(e){return[Rc(e[0]),Rc(e[1])]}var Wx={name:"x",handles:["w","e"].map(go),input:function(e,t){return e==null?null:[[+e[0],t[0][1]],[+e[1],t[1][1]]]},output:function(e){return e&&[e[0][0],e[1][0]]}},Kx={name:"y",handles:["n","s"].map(go),input:function(e,t){return e==null?null:[[t[0][0],+e[0]],[t[1][0],+e[1]]]},output:function(e){return e&&[e[0][1],e[1][1]]}},Zx={name:"xy",handles:["n","w","e","s","nw","ne","sw","se"].map(go),input:function(e){return e==null?null:$5(e)},output:function(e){return e}};function go(e){return{type:e}}var xo=Math.PI,yo=2*xo,mr=1e-6,V5=yo-mr;function Fc(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}function G5(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return Fc;let r=10**t;return function(o){this._+=o[0];for(let s=1,u=o.length;s<u;++s)this._+=Math.round(arguments[s]*r)/r+o[s]}}var gr=class{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=t==null?Fc:G5(t)}moveTo(t,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,r){this._append`L${this._x1=+t},${this._y1=+r}`}quadraticCurveTo(t,r,o,s){this._append`Q${+t},${+r},${this._x1=+o},${this._y1=+s}`}bezierCurveTo(t,r,o,s,u,c){this._append`C${+t},${+r},${+o},${+s},${this._x1=+u},${this._y1=+c}`}arcTo(t,r,o,s,u){if(t=+t,r=+r,o=+o,s=+s,u=+u,u<0)throw new Error(`negative radius: ${u}`);let c=this._x1,h=this._y1,g=o-t,b=s-r,y=c-t,S=h-r,T=y*y+S*S;if(this._x1===null)this._append`M${this._x1=t},${this._y1=r}`;else if(T>mr)if(!(Math.abs(S*g-b*y)>mr)||!u)this._append`L${this._x1=t},${this._y1=r}`;else{let N=o-c,R=s-h,z=g*g+b*b,P=N*N+R*R,L=Math.sqrt(z),X=Math.sqrt(T),ne=u*Math.tan((xo-Math.acos((z+T-P)/(2*L*X)))/2),ue=ne/X,me=ne/L;Math.abs(ue-1)>mr&&this._append`L${t+ue*y},${r+ue*S}`,this._append`A${u},${u},0,0,${+(S*N>y*R)},${this._x1=t+me*g},${this._y1=r+me*b}`}}arc(t,r,o,s,u,c){if(t=+t,r=+r,o=+o,c=!!c,o<0)throw new Error(`negative radius: ${o}`);let h=o*Math.cos(s),g=o*Math.sin(s),b=t+h,y=r+g,S=1^c,T=c?s-u:u-s;this._x1===null?this._append`M${b},${y}`:(Math.abs(this._x1-b)>mr||Math.abs(this._y1-y)>mr)&&this._append`L${b},${y}`,o&&(T<0&&(T=T%yo+yo),T>V5?this._append`A${o},${o},0,1,${S},${t-h},${r-g}A${o},${o},0,1,${S},${this._x1=b},${this._y1=y}`:T>mr&&this._append`A${o},${o},0,${+(T>=xo)},${S},${this._x1=t+o*Math.cos(u)},${this._y1=r+o*Math.sin(u)}`)}rect(t,r,o,s){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}h${o=+o}v${+s}h${-o}Z`}toString(){return this._}};function Lc(){return new gr}Lc.prototype=gr.prototype;function qc(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e);break}return this}var bo=Symbol("implicit");function gn(){var e=new Cr,t=[],r=[],o=bo;function s(u){let c=e.get(u);if(c===void 0){if(o!==bo)return o;e.set(u,c=t.push(u)-1)}return r[c%r.length]}return s.domain=function(u){if(!arguments.length)return t.slice();t=[],e=new Cr;for(let c of u)e.has(c)||e.set(c,t.push(c)-1);return s},s.range=function(u){return arguments.length?(r=Array.from(u),s):r.slice()},s.unknown=function(u){return arguments.length?(o=u,s):o},s.copy=function(){return gn(t,r).unknown(o)},qc.apply(s,arguments),s}function Pc(e){for(var t=e.length/6|0,r=new Array(t),o=0;o<t;)r[o]="#"+e.slice(o*6,++o*6);return r}var vo=Pc("1f77b4ff7f0e2ca02cd627289467bd8c564be377c27f7f7fbcbd2217becf");function wo(e){return function(){return e}}function Hc(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(r==null)t=null;else{let o=Math.floor(r);if(!(o>=0))throw new RangeError(`invalid digits: ${r}`);t=o}return e},()=>new gr(t)}var $c=Array.prototype.slice;function Vc(e){return e[0]}function Gc(e){return e[1]}var ko=class{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}};function Yc(e){return new ko(e,!0)}function Y5(e){return e.source}function U5(e){return e.target}function Uc(e){let t=Y5,r=U5,o=Vc,s=Gc,u=null,c=null,h=Hc(g);function g(){let b,y=$c.call(arguments),S=t.apply(this,y),T=r.apply(this,y);if(u==null&&(c=e(b=h())),c.lineStart(),y[0]=S,c.point(+o.apply(this,y),+s.apply(this,y)),y[0]=T,c.point(+o.apply(this,y),+s.apply(this,y)),c.lineEnd(),b)return c=null,b+""||null}return g.source=function(b){return arguments.length?(t=b,g):t},g.target=function(b){return arguments.length?(r=b,g):r},g.x=function(b){return arguments.length?(o=typeof b=="function"?b:wo(+b),g):o},g.y=function(b){return arguments.length?(s=typeof b=="function"?b:wo(+b),g):s},g.context=function(b){return arguments.length?(b==null?u=c=null:c=e(u=b),g):u},g}function So(){return Uc(Yc)}var xn=e=>()=>e;function Ao(e,{sourceEvent:t,target:r,transform:o,dispatch:s}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},transform:{value:o,enumerable:!0,configurable:!0},_:{value:s}})}function kt(e,t,r){this.k=e,this.x=t,this.y=r}kt.prototype={constructor:kt,scale:function(e){return e===1?this:new kt(this.k*e,this.x,this.y)},translate:function(e,t){return e===0&t===0?this:new kt(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var xr=new kt(1,0,0);yr.prototype=kt.prototype;function yr(e){for(;!e.__zoom;)if(!(e=e.parentNode))return xr;return e.__zoom}function N0(e){e.stopImmediatePropagation()}function Ir(e){e.preventDefault(),e.stopImmediatePropagation()}function X5(e){return(!e.ctrlKey||e.type==="wheel")&&!e.button}function j5(){var e=this;return e instanceof SVGElement?(e=e.ownerSVGElement||e,e.hasAttribute("viewBox")?(e=e.viewBox.baseVal,[[e.x,e.y],[e.x+e.width,e.y+e.height]]):[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]):[[0,0],[e.clientWidth,e.clientHeight]]}function Xc(){return this.__zoom||xr}function W5(e){return-e.deltaY*(e.deltaMode===1?.05:e.deltaMode?1:.002)*(e.ctrlKey?10:1)}function K5(){return navigator.maxTouchPoints||"ontouchstart"in this}function Z5(e,t,r){var o=e.invertX(t[0][0])-r[0][0],s=e.invertX(t[1][0])-r[1][0],u=e.invertY(t[0][1])-r[0][1],c=e.invertY(t[1][1])-r[1][1];return e.translate(s>o?(o+s)/2:Math.min(0,o)||Math.max(0,s),c>u?(u+c)/2:Math.min(0,u)||Math.max(0,c))}function _o(){var e=X5,t=j5,r=Z5,o=W5,s=K5,u=[0,1/0],c=[[-1/0,-1/0],[1/0,1/0]],h=250,g=po,b=en("start","zoom","end"),y,S,T,N=500,R=150,z=0,P=10;function L(D){D.property("__zoom",Xc).on("wheel.zoom",$e,{passive:!1}).on("mousedown.zoom",ze).on("dblclick.zoom",Ve).filter(s).on("touchstart.zoom",Oe).on("touchmove.zoom",Xe).on("touchend.zoom touchcancel.zoom",q).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}L.transform=function(D,W,G,ee){var oe=D.selection?D.selection():D;oe.property("__zoom",Xc),D!==oe?me(D,W,G,ee):oe.interrupt().each(function(){be(this,arguments).event(ee).start().zoom(null,typeof W=="function"?W.apply(this,arguments):W).end()})},L.scaleBy=function(D,W,G,ee){L.scaleTo(D,function(){var oe=this.__zoom.k,Y=typeof W=="function"?W.apply(this,arguments):W;return oe*Y},G,ee)},L.scaleTo=function(D,W,G,ee){L.transform(D,function(){var oe=t.apply(this,arguments),Y=this.__zoom,se=G==null?ue(oe):typeof G=="function"?G.apply(this,arguments):G,xe=Y.invert(se),ve=typeof W=="function"?W.apply(this,arguments):W;return r(ne(X(Y,ve),se,xe),oe,c)},G,ee)},L.translateBy=function(D,W,G,ee){L.transform(D,function(){return r(this.__zoom.translate(typeof W=="function"?W.apply(this,arguments):W,typeof G=="function"?G.apply(this,arguments):G),t.apply(this,arguments),c)},null,ee)},L.translateTo=function(D,W,G,ee,oe){L.transform(D,function(){var Y=t.apply(this,arguments),se=this.__zoom,xe=ee==null?ue(Y):typeof ee=="function"?ee.apply(this,arguments):ee;return r(xr.translate(xe[0],xe[1]).scale(se.k).translate(typeof W=="function"?-W.apply(this,arguments):-W,typeof G=="function"?-G.apply(this,arguments):-G),Y,c)},ee,oe)};function X(D,W){return W=Math.max(u[0],Math.min(u[1],W)),W===D.k?D:new kt(W,D.x,D.y)}function ne(D,W,G){var ee=W[0]-G[0]*D.k,oe=W[1]-G[1]*D.k;return ee===D.x&&oe===D.y?D:new kt(D.k,ee,oe)}function ue(D){return[(+D[0][0]+ +D[1][0])/2,(+D[0][1]+ +D[1][1])/2]}function me(D,W,G,ee){D.on("start.zoom",function(){be(this,arguments).event(ee).start()}).on("interrupt.zoom end.zoom",function(){be(this,arguments).event(ee).end()}).tween("zoom",function(){var oe=this,Y=arguments,se=be(oe,Y).event(ee),xe=t.apply(oe,Y),ve=G==null?ue(xe):typeof G=="function"?G.apply(oe,Y):G,it=Math.max(xe[1][0]-xe[0][0],xe[1][1]-xe[0][1]),Ae=oe.__zoom,ot=typeof W=="function"?W.apply(oe,Y):W,xt=g(Ae.invert(ve).concat(it/Ae.k),ot.invert(ve).concat(it/ot.k));return function(lt){if(lt===1)lt=ot;else{var yt=xt(lt),Br=it/yt[2];lt=new kt(Br,ve[0]-yt[0]*Br,ve[1]-yt[1]*Br)}se.zoom(null,lt)}})}function be(D,W,G){return!G&&D.__zooming||new Se(D,W)}function Se(D,W){this.that=D,this.args=W,this.active=0,this.sourceEvent=null,this.extent=t.apply(D,W),this.taps=0}Se.prototype={event:function(D){return D&&(this.sourceEvent=D),this},start:function(){return++this.active===1&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(D,W){return this.mouse&&D!=="mouse"&&(this.mouse[1]=W.invert(this.mouse[0])),this.touch0&&D!=="touch"&&(this.touch0[1]=W.invert(this.touch0[0])),this.touch1&&D!=="touch"&&(this.touch1[1]=W.invert(this.touch1[0])),this.that.__zoom=W,this.emit("zoom"),this},end:function(){return--this.active===0&&(delete this.that.__zooming,this.emit("end")),this},emit:function(D){var W=rt(this.that).datum();b.call(D,this.that,new Ao(D,{sourceEvent:this.sourceEvent,target:L,type:D,transform:this.that.__zoom,dispatch:b}),W)}};function $e(D,...W){if(!e.apply(this,arguments))return;var G=be(this,W).event(D),ee=this.__zoom,oe=Math.max(u[0],Math.min(u[1],ee.k*Math.pow(2,o.apply(this,arguments)))),Y=Ht(D);if(G.wheel)(G.mouse[0][0]!==Y[0]||G.mouse[0][1]!==Y[1])&&(G.mouse[1]=ee.invert(G.mouse[0]=Y)),clearTimeout(G.wheel);else{if(ee.k===oe)return;G.mouse=[Y,ee.invert(Y)],Qt(this),G.start()}Ir(D),G.wheel=setTimeout(se,R),G.zoom("mouse",r(ne(X(ee,oe),G.mouse[0],G.mouse[1]),G.extent,c));function se(){G.wheel=null,G.end()}}function ze(D,...W){if(T||!e.apply(this,arguments))return;var G=D.currentTarget,ee=be(this,W,!0).event(D),oe=rt(D.view).on("mousemove.zoom",ve,!0).on("mouseup.zoom",it,!0),Y=Ht(D,G),se=D.clientX,xe=D.clientY;to(D.view),N0(D),ee.mouse=[Y,this.__zoom.invert(Y)],Qt(this),ee.start();function ve(Ae){if(Ir(Ae),!ee.moved){var ot=Ae.clientX-se,xt=Ae.clientY-xe;ee.moved=ot*ot+xt*xt>z}ee.event(Ae).zoom("mouse",r(ne(ee.that.__zoom,ee.mouse[0]=Ht(Ae,G),ee.mouse[1]),ee.extent,c))}function it(Ae){oe.on("mousemove.zoom mouseup.zoom",null),ro(Ae.view,ee.moved),Ir(Ae),ee.event(Ae).end()}}function Ve(D,...W){if(e.apply(this,arguments)){var G=this.__zoom,ee=Ht(D.changedTouches?D.changedTouches[0]:D,this),oe=G.invert(ee),Y=G.k*(D.shiftKey?.5:2),se=r(ne(X(G,Y),ee,oe),t.apply(this,W),c);Ir(D),h>0?rt(this).transition().duration(h).call(me,se,ee,D):rt(this).call(L.transform,se,ee,D)}}function Oe(D,...W){if(e.apply(this,arguments)){var G=D.touches,ee=G.length,oe=be(this,W,D.changedTouches.length===ee).event(D),Y,se,xe,ve;for(N0(D),se=0;se<ee;++se)xe=G[se],ve=Ht(xe,this),ve=[ve,this.__zoom.invert(ve),xe.identifier],oe.touch0?!oe.touch1&&oe.touch0[2]!==ve[2]&&(oe.touch1=ve,oe.taps=0):(oe.touch0=ve,Y=!0,oe.taps=1+!!y);y&&(y=clearTimeout(y)),Y&&(oe.taps<2&&(S=ve[0],y=setTimeout(function(){y=null},N)),Qt(this),oe.start())}}function Xe(D,...W){if(this.__zooming){var G=be(this,W).event(D),ee=D.changedTouches,oe=ee.length,Y,se,xe,ve;for(Ir(D),Y=0;Y<oe;++Y)se=ee[Y],xe=Ht(se,this),G.touch0&&G.touch0[2]===se.identifier?G.touch0[0]=xe:G.touch1&&G.touch1[2]===se.identifier&&(G.touch1[0]=xe);if(se=G.that.__zoom,G.touch1){var it=G.touch0[0],Ae=G.touch0[1],ot=G.touch1[0],xt=G.touch1[1],lt=(lt=ot[0]-it[0])*lt+(lt=ot[1]-it[1])*lt,yt=(yt=xt[0]-Ae[0])*yt+(yt=xt[1]-Ae[1])*yt;se=X(se,Math.sqrt(lt/yt)),xe=[(it[0]+ot[0])/2,(it[1]+ot[1])/2],ve=[(Ae[0]+xt[0])/2,(Ae[1]+xt[1])/2]}else if(G.touch0)xe=G.touch0[0],ve=G.touch0[1];else return;G.zoom("touch",r(ne(se,xe,ve),G.extent,c))}}function q(D,...W){if(this.__zooming){var G=be(this,W).event(D),ee=D.changedTouches,oe=ee.length,Y,se;for(N0(D),T&&clearTimeout(T),T=setTimeout(function(){T=null},N),Y=0;Y<oe;++Y)se=ee[Y],G.touch0&&G.touch0[2]===se.identifier?delete G.touch0:G.touch1&&G.touch1[2]===se.identifier&&delete G.touch1;if(G.touch1&&!G.touch0&&(G.touch0=G.touch1,delete G.touch1),G.touch0)G.touch0[1]=this.__zoom.invert(G.touch0[0]);else if(G.end(),G.taps===2&&(se=Ht(se,this),Math.hypot(S[0]-se[0],S[1]-se[1])<P)){var xe=rt(this).on("dblclick.zoom");xe&&xe.apply(this,arguments)}}}return L.wheelDelta=function(D){return arguments.length?(o=typeof D=="function"?D:xn(+D),L):o},L.filter=function(D){return arguments.length?(e=typeof D=="function"?D:xn(!!D),L):e},L.touchable=function(D){return arguments.length?(s=typeof D=="function"?D:xn(!!D),L):s},L.extent=function(D){return arguments.length?(t=typeof D=="function"?D:xn([[+D[0][0],+D[0][1]],[+D[1][0],+D[1][1]]]),L):t},L.scaleExtent=function(D){return arguments.length?(u[0]=+D[0],u[1]=+D[1],L):[u[0],u[1]]},L.translateExtent=function(D){return arguments.length?(c[0][0]=+D[0][0],c[1][0]=+D[1][0],c[0][1]=+D[0][1],c[1][1]=+D[1][1],L):[[c[0][0],c[0][1]],[c[1][0],c[1][1]]]},L.constrain=function(D){return arguments.length?(r=D,L):r},L.duration=function(D){return arguments.length?(h=+D,L):h},L.interpolate=function(D){return arguments.length?(g=D,L):g},L.on=function(){var D=b.on.apply(b,arguments);return D===b?L:D},L.clickDistance=function(D){return arguments.length?(z=(D=+D)*D,L):Math.sqrt(z)},L.tapDistance=function(D){return arguments.length?(P=+D,L):P},L}var Zc=1,Jc=2,Q5="http://www.w3.org/2000/svg",Mo="http://www.w3.org/1999/xlink",e3={show:Mo,actuate:Mo,href:Mo},t3=e=>typeof e=="string"||typeof e=="number",r3=e=>(e==null?void 0:e.vtype)===Zc,n3=e=>(e==null?void 0:e.vtype)===Jc;function E0(e,t){let r;if(typeof e=="string")r=Zc;else if(typeof e=="function")r=Jc;else throw new Error("Invalid VNode type");return{vtype:r,type:e,props:t}}function i3(e){return e.children}var o3={isSvg:!1};function jc(e,t){Array.isArray(t)||(t=[t]),t=t.filter(Boolean),t.length&&e.append(...t)}function l3(e,t,r){for(let o in t)if(!(o==="key"||o==="children"||o==="ref"))if(o==="dangerouslySetInnerHTML")e.innerHTML=t[o].__html;else if(o==="innerHTML"||o==="textContent"||o==="innerText"||o==="value"&&["textarea","select"].includes(e.tagName)){let s=t[o];s!=null&&(e[o]=s)}else o.startsWith("on")?e[o.toLowerCase()]=t[o]:a3(e,o,t[o],r.isSvg)}var s3={className:"class",labelFor:"for"};function a3(e,t,r,o){if(t=s3[t]||t,r===!0)e.setAttribute(t,"");else if(r===!1)e.removeAttribute(t);else{let s=o?e3[t]:void 0;s!==void 0?e.setAttributeNS(s,t,r):e.setAttribute(t,r)}}function u3(e){return e.reduce((t,r)=>t.concat(r),[])}function No(e,t){return Array.isArray(e)?u3(e.map(r=>No(r,t))):zo(e,t)}function zo(e,t=o3){if(e==null||typeof e=="boolean")return null;if(e instanceof Node)return e;if(n3(e)){let{type:r,props:o}=e;if(r===i3){let u=document.createDocumentFragment();if(o.children){let c=No(o.children,t);jc(u,c)}return u}let s=r(o);return zo(s,t)}if(t3(e))return document.createTextNode(`${e}`);if(r3(e)){let r,{type:o,props:s}=e;if(!t.isSvg&&o==="svg"&&(t=Object.assign({},t,{isSvg:!0})),t.isSvg?r=document.createElementNS(Q5,o):r=document.createElement(o),l3(r,s,t),s.children){let c=t;t.isSvg&&o==="foreignObject"&&(c=Object.assign({},c,{isSvg:!1}));let h=No(s.children,c);h!=null&&jc(r,h)}let{ref:u}=s;return typeof u=="function"&&u(r),r}throw new Error("mount: Invalid Vnode!")}function Co(e){return zo(e)}function c3(e){var t=0,r=e.children,o=r&&r.length;if(!o)t=1;else for(;--o>=0;)t+=r[o].value;e.value=t}function h3(){return this.eachAfter(c3)}function f3(e){var t=this,r,o=[t],s,u,c;do for(r=o.reverse(),o=[];t=r.pop();)if(e(t),s=t.children,s)for(u=0,c=s.length;u<c;++u)o.push(s[u]);while(o.length);return this}function p3(e){for(var t=this,r=[t],o,s;t=r.pop();)if(e(t),o=t.children,o)for(s=o.length-1;s>=0;--s)r.push(o[s]);return this}function d3(e){for(var t=this,r=[t],o=[],s,u,c;t=r.pop();)if(o.push(t),s=t.children,s)for(u=0,c=s.length;u<c;++u)r.push(s[u]);for(;t=o.pop();)e(t);return this}function m3(e){return this.eachAfter(function(t){for(var r=+e(t.data)||0,o=t.children,s=o&&o.length;--s>=0;)r+=o[s].value;t.value=r})}function g3(e){return this.eachBefore(function(t){t.children&&t.children.sort(e)})}function x3(e){for(var t=this,r=y3(t,e),o=[t];t!==r;)t=t.parent,o.push(t);for(var s=o.length;e!==r;)o.splice(s,0,e),e=e.parent;return o}function y3(e,t){if(e===t)return e;var r=e.ancestors(),o=t.ancestors(),s=null;for(e=r.pop(),t=o.pop();e===t;)s=e,e=r.pop(),t=o.pop();return s}function b3(){for(var e=this,t=[e];e=e.parent;)t.push(e);return t}function v3(){var e=[];return this.each(function(t){e.push(t)}),e}function w3(){var e=[];return this.eachBefore(function(t){t.children||e.push(t)}),e}function k3(){var e=this,t=[];return e.each(function(r){r!==e&&t.push({source:r.parent,target:r})}),t}function Io(e,t){var r=new z0(e),o=+e.value&&(r.value=e.value),s,u=[r],c,h,g,b;for(t==null&&(t=A3);s=u.pop();)if(o&&(s.value=+s.data.value),(h=t(s.data))&&(b=h.length))for(s.children=new Array(b),g=b-1;g>=0;--g)u.push(c=s.children[g]=new z0(h[g])),c.parent=s,c.depth=s.depth+1;return r.eachBefore(M3)}function S3(){return Io(this).eachBefore(_3)}function A3(e){return e.children}function _3(e){e.data=e.data.data}function M3(e){var t=0;do e.height=t;while((e=e.parent)&&e.height<++t)}function z0(e){this.data=e,this.depth=this.height=0,this.parent=null}z0.prototype=Io.prototype={constructor:z0,count:h3,each:f3,eachAfter:d3,eachBefore:p3,sum:m3,sort:g3,path:x3,ancestors:b3,descendants:v3,leaves:w3,links:k3,copy:S3};var C3="d3-flextree",T3="2.1.2",N3="build/d3-flextree.js",E3="index",z3={name:"Chris Maloney",url:"http://chrismaloney.org"},I3="Flexible tree layout algorithm that allows for variable node sizes.",D3=["d3","d3-module","layout","tree","hierarchy","d3-hierarchy","plugin","d3-plugin","infovis","visualization","2d"],B3="https://github.com/klortho/d3-flextree",O3="WTFPL",R3={type:"git",url:"https://github.com/klortho/d3-flextree.git"},F3={clean:"rm -rf build demo test","build:demo":"rollup -c --environment BUILD:demo","build:dev":"rollup -c --environment BUILD:dev","build:prod":"rollup -c --environment BUILD:prod","build:test":"rollup -c --environment BUILD:test",build:"rollup -c",lint:"eslint index.js src","test:main":"node test/bundle.js","test:browser":"node test/browser-tests.js",test:"npm-run-all test:*",prepare:"npm-run-all clean build lint test"},L3={"d3-hierarchy":"^1.1.5"},q3={"babel-plugin-external-helpers":"^6.22.0","babel-preset-es2015-rollup":"^3.0.0",d3:"^4.13.0","d3-selection-multi":"^1.0.1",eslint:"^4.19.1",jsdom:"^11.6.2","npm-run-all":"^4.1.2",rollup:"^0.55.3","rollup-plugin-babel":"^2.7.1","rollup-plugin-commonjs":"^8.0.2","rollup-plugin-copy":"^0.2.3","rollup-plugin-json":"^2.3.0","rollup-plugin-node-resolve":"^3.0.2","rollup-plugin-uglify":"^3.0.0","uglify-es":"^3.3.9"},P3={name:C3,version:T3,main:N3,module:E3,"jsnext:main":"index",author:z3,description:I3,keywords:D3,homepage:B3,license:O3,repository:R3,scripts:F3,dependencies:L3,devDependencies:q3},{version:H3}=P3,$3=Object.freeze({children:e=>e.children,nodeSize:e=>e.data.size,spacing:0});function eh(e){let t=Object.assign({},$3,e);function r(h){let g=t[h];return typeof g=="function"?g:()=>g}function o(h){let g=c(u(),h,b=>b.children);return g.update(),g.data}function s(){let h=r("nodeSize"),g=r("spacing");return class Qc extends Io.prototype.constructor{constructor(y){super(y)}copy(){let y=c(this.constructor,this,S=>S.children);return y.each(S=>S.data=S.data.data),y}get size(){return h(this)}spacing(y){return g(this,y)}get nodes(){return this.descendants()}get xSize(){return this.size[0]}get ySize(){return this.size[1]}get top(){return this.y}get bottom(){return this.y+this.ySize}get left(){return this.x-this.xSize/2}get right(){return this.x+this.xSize/2}get root(){let y=this.ancestors();return y[y.length-1]}get numChildren(){return this.hasChildren?this.children.length:0}get hasChildren(){return!this.noChildren}get noChildren(){return this.children===null}get firstChild(){return this.hasChildren?this.children[0]:null}get lastChild(){return this.hasChildren?this.children[this.numChildren-1]:null}get extents(){return(this.children||[]).reduce((y,S)=>Qc.maxExtents(y,S.extents),this.nodeExtents)}get nodeExtents(){return{top:this.top,bottom:this.bottom,left:this.left,right:this.right}}static maxExtents(y,S){return{top:Math.min(y.top,S.top),bottom:Math.max(y.bottom,S.bottom),left:Math.min(y.left,S.left),right:Math.max(y.right,S.right)}}}}function u(){let h=s(),g=r("nodeSize"),b=r("spacing");return class extends h{constructor(y){super(y),Object.assign(this,{x:0,y:0,relX:0,prelim:0,shift:0,change:0,lExt:this,lExtRelX:0,lThr:null,rExt:this,rExtRelX:0,rThr:null})}get size(){return g(this.data)}spacing(y){return b(this.data,y.data)}get x(){return this.data.x}set x(y){this.data.x=y}get y(){return this.data.y}set y(y){this.data.y=y}update(){return th(this),rh(this),this}}}function c(h,g,b){let y=(S,T)=>{let N=new h(S);Object.assign(N,{parent:T,depth:T===null?0:T.depth+1,height:0,length:1});let R=b(S)||[];return N.children=R.length===0?null:R.map(z=>y(z,N)),N.children&&Object.assign(N,N.children.reduce((z,P)=>({height:Math.max(z.height,P.height+1),length:z.length+P.length}),N)),N};return y(g,null)}return Object.assign(o,{nodeSize(h){return arguments.length?(t.nodeSize=h,o):t.nodeSize},spacing(h){return arguments.length?(t.spacing=h,o):t.spacing},children(h){return arguments.length?(t.children=h,o):t.children},hierarchy(h,g){let b=typeof g>"u"?t.children:g;return c(s(),h,b)},dump(h){let g=r("nodeSize"),b=y=>S=>{let T=y+"  ",N=y+"    ",{x:R,y:z}=S,P=g(S),L=S.children||[],X=L.length===0?" ":`,${T}children: [${N}${L.map(b(N)).join(N)}${T}],${y}`;return`{ size: [${P.join(", ")}],${T}x: ${R}, y: ${z}${X}},`};return b(`
`)(h)}}),o}eh.version=H3;var th=(e,t=0)=>(e.y=t,(e.children||[]).reduce((r,o)=>{let[s,u]=r;th(o,e.y+e.ySize);let c=(s===0?o.lExt:o.rExt).bottom;s!==0&&G3(e,s,u);let h=J3(c,s,u);return[s+1,h]},[0,null]),V3(e),Z3(e),e),rh=(e,t,r)=>{typeof t>"u"&&(t=-e.relX-e.prelim,r=0);let o=t+e.relX;return e.relX=o+e.prelim-r,e.prelim=0,e.x=r+e.relX,(e.children||[]).forEach(s=>rh(s,o,e.x)),e},V3=e=>{(e.children||[]).reduce((t,r)=>{let[o,s]=t,u=o+r.shift,c=s+u+r.change;return r.relX+=c,[u,c]},[0,0])},G3=(e,t,r)=>{let o=e.children[t-1],s=e.children[t],u=o,c=o.relX,h=s,g=s.relX,b=!0;for(;u&&h;){u.bottom>r.lowY&&(r=r.next);let y=c+u.prelim-(g+h.prelim)+u.xSize/2+h.xSize/2+u.spacing(h);(y>0||y<0&&b)&&(g+=y,Y3(s,y),U3(e,t,r.index,y)),b=!1;let S=u.bottom,T=h.bottom;S<=T&&(u=j3(u),u&&(c+=u.relX)),S>=T&&(h=X3(h),h&&(g+=h.relX))}!u&&h?W3(e,t,h,g):u&&!h&&K3(e,t,u,c)},Y3=(e,t)=>{e.relX+=t,e.lExtRelX+=t,e.rExtRelX+=t},U3=(e,t,r,o)=>{let s=e.children[t],u=t-r;if(u>1){let c=o/u;e.children[r+1].shift+=c,s.shift-=c,s.change-=o-c}},X3=e=>e.hasChildren?e.firstChild:e.lThr,j3=e=>e.hasChildren?e.lastChild:e.rThr,W3=(e,t,r,o)=>{let s=e.firstChild,u=s.lExt,c=e.children[t];u.lThr=r;let h=o-r.relX-s.lExtRelX;u.relX+=h,u.prelim-=h,s.lExt=c.lExt,s.lExtRelX=c.lExtRelX},K3=(e,t,r,o)=>{let s=e.children[t],u=s.rExt,c=e.children[t-1];u.rThr=r;let h=o-r.relX-s.rExtRelX;u.relX+=h,u.prelim-=h,s.rExt=c.rExt,s.rExtRelX=c.rExtRelX},Z3=e=>{if(e.hasChildren){let t=e.firstChild,r=e.lastChild,o=(t.prelim+t.relX-t.xSize/2+r.relX+r.prelim+r.xSize/2)/2;Object.assign(e,{prelim:o,lExt:t.lExt,lExtRelX:t.lExtRelX,rExt:r.rExt,rExtRelX:r.rExtRelX})}},J3=(e,t,r)=>{for(;r!==null&&e>=r.lowY;)r=r.next;return{lowY:e,index:t,next:r}},Q3=".markmap{font:300 16px/20px sans-serif}.markmap-link{fill:none}.markmap-node>circle{cursor:pointer}.markmap-foreign{display:inline-block}.markmap-foreign a{color:#0097e6}.markmap-foreign a:hover{color:#00a8ff}.markmap-foreign code{padding:.25em;font-size:calc(1em - 2px);color:#555;background-color:#f0f0f0;border-radius:2px}.markmap-foreign pre{margin:0}.markmap-foreign pre>code{display:block}.markmap-foreign del{text-decoration:line-through}.markmap-foreign em{font-style:italic}.markmap-foreign strong{font-weight:700}.markmap-foreign mark{background:#ffeaa7}",e6=".markmap-container{position:absolute;width:0;height:0;top:-100px;left:-100px;overflow:hidden}.markmap-container>.markmap-foreign{display:inline-block}.markmap-container>.markmap-foreign>div:last-child,.markmap-container>.markmap-foreign>div:last-child :not(pre){white-space:nowrap}.markmap-container>.markmap-foreign>div:last-child code{white-space:inherit}";function Wc(e){let t=e.data;return Math.max(4-2*t.depth,1.5)}function Kc(e,t){let r=e0(e,t);return e[r]}function To(e){e.stopPropagation()}function t6(){return{transformHtml:new vt}}var r6=new vt,n6=gn(vo),nh=typeof navigator<"u"&&navigator.userAgent.includes("Macintosh"),ih=class Eo{constructor(t,r){this.options=Eo.defaultOptions,this.revokers=[],this.handleZoom=o=>{let{transform:s}=o;this.g.attr("transform",s)},this.handlePan=o=>{o.preventDefault();let s=yr(this.svg.node()),u=s.translate(-o.deltaX/s.k,-o.deltaY/s.k);this.svg.call(this.zoom.transform,u)},this.handleClick=(o,s)=>{let u=this.options.toggleRecursively;(nh?o.metaKey:o.ctrlKey)&&(u=!u),this.toggleNode(s.data,u)},this.viewHooks=t6(),this.svg=t.datum?t:rt(t),this.styleNode=this.svg.append("style"),this.zoom=_o().filter(o=>this.options.scrollForPan&&o.type==="wheel"?o.ctrlKey&&!o.button:(!o.ctrlKey||o.type==="wheel")&&!o.button).on("zoom",this.handleZoom),this.setOptions(r),this.state={id:this.options.id||this.svg.attr("id")||Is(),minX:0,maxX:0,minY:0,maxY:0},this.g=this.svg.append("g"),this.revokers.push(r6.tap(()=>{this.setData()}))}getStyleContent(){let{style:t}=this.options,{id:r}=this.state,o=typeof t=="function"?t(r):"";return[this.options.embedGlobalCSS&&Q3,o].filter(Boolean).join(`
`)}updateStyle(){this.svg.attr("class",Ds(this.svg.attr("class"),"markmap",this.state.id));let t=this.getStyleContent();this.styleNode.text(t)}toggleNode(t,r=!1){var o,s;let u=(o=t.payload)!=null&&o.fold?0:1;r?Yn(t,(c,h)=>{c.payload={...c.payload,fold:u},h()}):t.payload={...t.payload,fold:(s=t.payload)!=null&&s.fold?0:1},this.renderData(t)}initializeData(t){let r=0,{color:o,nodeMinHeight:s,maxWidth:u,initialExpandLevel:c}=this.options,{id:h}=this.state,g=Co(E0("div",{className:`markmap-container markmap ${h}-g`})),b=Co(E0("style",{children:[this.getStyleContent(),e6].join(`
`)}));document.body.append(g,b);let y=u?`max-width: ${u}px`:"",S=0;Yn(t,(N,R,z)=>{var P,L,X;N.children=(P=N.children)==null?void 0:P.map(me=>({...me})),r+=1;let ne=Co(E0("div",{className:"markmap-foreign",style:y,children:E0("div",{dangerouslySetInnerHTML:{__html:N.content}})}));g.append(ne),N.state={...N.state,id:r,el:ne.firstChild},N.state.path=[(L=z==null?void 0:z.state)==null?void 0:L.path,N.state.id].filter(Boolean).join("."),o(N);let ue=((X=N.payload)==null?void 0:X.fold)===2;ue?S+=1:(S||c>=0&&N.depth>=c)&&(N.payload={...N.payload,fold:1}),R(),ue&&(S-=1)});let T=Array.from(g.childNodes).map(N=>N.firstChild);this.viewHooks.transformHtml.call(this,T),T.forEach(N=>{var R;(R=N.parentNode)==null||R.append(N.cloneNode(!0))}),Yn(t,(N,R,z)=>{var P;let L=N.state,X=L.el.getBoundingClientRect();N.content=L.el.innerHTML,L.size=[Math.ceil(X.width)+1,Math.max(Math.ceil(X.height),s)],L.key=[(P=z==null?void 0:z.state)==null?void 0:P.id,L.id].filter(Boolean).join(".")+N.content,R()}),g.remove(),b.remove()}setOptions(t){this.options={...this.options,...t},this.options.zoom?this.svg.call(this.zoom):this.svg.on(".zoom",null),this.options.pan?this.svg.on("wheel",this.handlePan):this.svg.on("wheel",null)}setData(t,r){r&&this.setOptions(r),t&&(this.state.data=t),this.state.data&&(this.initializeData(this.state.data),this.updateStyle(),this.renderData())}renderData(t){var Oe,Xe;if(!this.state.data)return;let{spacingHorizontal:r,paddingX:o,spacingVertical:s,autoFit:u,color:c}=this.options,h=eh({}).children(q=>{var D;if(!((D=q.payload)!=null&&D.fold))return q.children}).nodeSize(q=>{let[D,W]=q.data.state.size;return[W,D+(D?o*2:0)+r]}).spacing((q,D)=>q.parent===D.parent?s:s*2),g=h.hierarchy(this.state.data);h(g);let b=g.descendants().reverse(),y=g.links(),S=So(),T=Qr(b,q=>q.x-q.xSize/2),N=Jr(b,q=>q.x+q.xSize/2),R=Qr(b,q=>q.y),z=Jr(b,q=>q.y+q.ySize-r);Object.assign(this.state,{minX:T,maxX:N,minY:R,maxY:z}),u&&this.fit();let P=t&&b.find(q=>q.data===t)||g,L=(Oe=P.data.state.x0)!=null?Oe:P.x,X=(Xe=P.data.state.y0)!=null?Xe:P.y,ne=this.g.selectAll(ar("g")).data(b,q=>q.data.state.key),ue=ne.enter().append("g").attr("data-depth",q=>q.data.depth).attr("data-path",q=>q.data.state.path).attr("transform",q=>`translate(${X+P.ySize-q.ySize},${L+P.xSize/2-q.xSize})`),me=this.transition(ne.exit());me.select("line").attr("x1",q=>q.ySize-r).attr("x2",q=>q.ySize-r),me.select("foreignObject").style("opacity",0),me.attr("transform",q=>`translate(${P.y+P.ySize-q.ySize},${P.x+P.xSize/2-q.xSize})`).remove();let be=ne.merge(ue).attr("class",q=>{var D;return["markmap-node",((D=q.data.payload)==null?void 0:D.fold)&&"markmap-fold"].filter(Boolean).join(" ")});this.transition(be).attr("transform",q=>`translate(${q.y},${q.x-q.xSize/2})`);let Se=be.selectAll(ar("line")).data(q=>[q],q=>q.data.state.key).join(q=>q.append("line").attr("x1",D=>D.ySize-r).attr("x2",D=>D.ySize-r),q=>q,q=>q.remove());this.transition(Se).attr("x1",-1).attr("x2",q=>q.ySize-r+2).attr("y1",q=>q.xSize).attr("y2",q=>q.xSize).attr("stroke",q=>c(q.data)).attr("stroke-width",Wc);let $e=be.selectAll(ar("circle")).data(q=>{var D;return(D=q.data.children)!=null&&D.length?[q]:[]},q=>q.data.state.key).join(q=>q.append("circle").attr("stroke-width","1.5").attr("cx",D=>D.ySize-r).attr("cy",D=>D.xSize).attr("r",0).on("click",(D,W)=>this.handleClick(D,W)).on("mousedown",To),q=>q,q=>q.remove());this.transition($e).attr("r",6).attr("cx",q=>q.ySize-r).attr("cy",q=>q.xSize).attr("stroke",q=>c(q.data)).attr("fill",q=>{var D;return(D=q.data.payload)!=null&&D.fold&&q.data.children?c(q.data):"#fff"});let ze=be.selectAll(ar("foreignObject")).data(q=>[q],q=>q.data.state.key).join(q=>{let D=q.append("foreignObject").attr("class","markmap-foreign").attr("x",o).attr("y",0).style("opacity",0).on("mousedown",To).on("dblclick",To);return D.append("xhtml:div").select(function(W){let G=W.data.state.el.cloneNode(!0);return this.replaceWith(G),G}).attr("xmlns","http://www.w3.org/1999/xhtml"),D},q=>q,q=>q.remove()).attr("width",q=>Math.max(0,q.ySize-r-o*2)).attr("height",q=>q.xSize);this.transition(ze).style("opacity",1);let Ve=this.g.selectAll(ar("path")).data(y,q=>q.target.data.state.key).join(q=>{let D=[X+P.ySize-r,L+P.xSize/2];return q.insert("path","g").attr("class","markmap-link").attr("data-depth",W=>W.target.data.depth).attr("data-path",W=>W.target.data.state.path).attr("d",S({source:D,target:D}))},q=>q,q=>{let D=[P.y+P.ySize-r,P.x+P.xSize/2];return this.transition(q).attr("d",S({source:D,target:D})).remove()});this.transition(Ve).attr("stroke",q=>c(q.target.data)).attr("stroke-width",q=>Wc(q.target)).attr("d",q=>{let D=q.source,W=q.target,G=[D.y+D.ySize-r,D.x+D.xSize/2],ee=[W.y,W.x+W.xSize/2];return S({source:G,target:ee})}),b.forEach(q=>{q.data.state.x0=q.x,q.data.state.y0=q.y})}transition(t){let{duration:r}=this.options;return t.transition().duration(r)}async fit(){let t=this.svg.node(),{width:r,height:o}=t.getBoundingClientRect(),{fitRatio:s}=this.options,{minX:u,maxX:c,minY:h,maxY:g}=this.state,b=g-h,y=c-u,S=Math.min(r/b*s,o/y*s,2),T=xr.translate((r-b*S)/2-h*S,(o-y*S)/2-u*S).scale(S);return this.transition(this.svg).call(this.zoom.transform,T).end().catch(sr)}async ensureView(t,r){let o;if(this.g.selectAll(ar("g")).each(function(L){L.data===t&&(o=L)}),!o)return;let s=this.svg.node(),{spacingHorizontal:u}=this.options,c=s.getBoundingClientRect(),h=yr(s),[g,b]=[o.y,o.y+o.ySize-u+2].map(L=>L*h.k+h.x),[y,S]=[o.x-o.xSize/2,o.x+o.xSize/2].map(L=>L*h.k+h.y),T={left:0,right:0,top:0,bottom:0,...r},N=[T.left-g,c.width-T.right-b],R=[T.top-y,c.height-T.bottom-S],z=N[0]*N[1]>0?Kc(N,Math.abs)/h.k:0,P=R[0]*R[1]>0?Kc(R,Math.abs)/h.k:0;if(z||P){let L=h.translate(z,P);return this.transition(this.svg).call(this.zoom.transform,L).end().catch(sr)}}async rescale(t){let r=this.svg.node(),{width:o,height:s}=r.getBoundingClientRect(),u=o/2,c=s/2,h=yr(r),g=h.translate((u-h.x)*(1-t)/h.k,(c-h.y)*(1-t)/h.k).scale(t);return this.transition(this.svg).call(this.zoom.transform,g).end().catch(sr)}destroy(){this.svg.on(".zoom",null),this.svg.html(null),this.revokers.forEach(t=>{t()})}static create(t,r,o=null){let s=new Eo(t,r);return o&&(s.setData(o),s.fit()),s}};ih.defaultOptions={autoFit:!1,color:e=>{var t;return n6(`${((t=e.state)==null?void 0:t.path)||""}`)},duration:500,embedGlobalCSS:!0,fitRatio:.95,maxWidth:0,nodeMinHeight:16,paddingX:8,scrollForPan:nh,spacingHorizontal:80,spacingVertical:5,initialExpandLevel:-1,zoom:!0,pan:!0,toggleRecursively:!1};var oh=ih;var Dr="mindmap",I0=class extends D0.Plugin{constructor(){super(...arguments);this.mindmap=null;this.rootNode=null;this.currentNode=null;this.editingNode=null;this.selectedNode=null}async onload(){this.transformer=new Qn,this.registerView(Dr,r=>new er(r,this)),this.addCommand({id:"create-mindmap",name:"\u521B\u5EFA\u65B0\u7684\u601D\u7EF4\u5BFC\u56FE",callback:()=>this.createNewMindMap()}),this.addCommand({id:"select-parent-node",name:"\u9009\u62E9\u7236\u8282\u70B9",callback:()=>this.selectParentNode()}),this.addCommand({id:"select-first-child",name:"\u9009\u62E9\u7B2C\u4E00\u4E2A\u5B50\u8282\u70B9",callback:()=>this.selectFirstChild()}),this.addCommand({id:"select-next-sibling",name:"\u9009\u62E9\u4E0B\u4E00\u4E2A\u540C\u7EA7\u8282\u70B9",callback:()=>this.selectNextSibling()}),this.addCommand({id:"select-previous-sibling",name:"\u9009\u62E9\u4E0A\u4E00\u4E2A\u540C\u7EA7\u8282\u70B9",callback:()=>this.selectPreviousSibling()}),this.addCommand({id:"debug-mindmap",name:"\u8C03\u8BD5\u601D\u7EF4\u5BFC\u56FE\u72B6\u6001",callback:()=>this.debugMindMapState()}),this.addCommand({id:"force-select-root",name:"\u5F3A\u5236\u9009\u62E9\u6839\u8282\u70B9",callback:()=>{this.rootNode&&(console.log("Force selecting root node:",this.rootNode),this.selectNode(this.rootNode))}}),this.addCommand({id:"create-simple-test",name:"\u521B\u5EFA\u7B80\u5355\u6D4B\u8BD5\u89C6\u56FE",callback:()=>this.createSimpleTestView()}),this.addCommand({id:"test-edit-selected",name:"\u6D4B\u8BD5\u7F16\u8F91\u9009\u4E2D\u8282\u70B9",callback:()=>{if(this.selectedNode){console.log("Testing edit for selected node:",this.selectedNode.content);let r=document.querySelector(`[data-node-id="${this.selectedNode.id}"]`);r?this.startSimpleEditing(this.selectedNode,r):console.log("Node element not found")}else console.log("No node selected")}}),this.registerDomEvent(document,"keydown",r=>{if(!(!this.app.workspace.getActiveViewOfType(er)||!this.selectedNode))switch(r.key){case"Tab":r.preventDefault(),r.shiftKey?this.createSiblingNode(this.selectedNode):this.createChildNode(this.selectedNode);break;case"Enter":r.preventDefault(),this.editingNode?this.finishEditing():this.startEditing(this.selectedNode);break;case"Delete":case"Backspace":!this.editingNode&&r.ctrlKey&&(r.preventDefault(),this.deleteNode(this.selectedNode));break;case"ArrowUp":r.preventDefault(),this.selectPreviousSibling();break;case"ArrowDown":r.preventDefault(),this.selectNextSibling();break;case"ArrowLeft":r.preventDefault(),this.selectedNode&&this.selectedNode.isExpanded?this.collapseNode(this.selectedNode):this.selectParentNode();break;case"ArrowRight":r.preventDefault(),this.selectedNode&&!this.selectedNode.isExpanded?this.expandNode(this.selectedNode):this.selectFirstChild();break;case"Escape":this.editingNode&&(r.preventDefault(),this.cancelEditing());break}})}debugMindMapState(){console.log("=== \u601D\u7EF4\u5BFC\u56FE\u8C03\u8BD5\u4FE1\u606F ==="),console.log("Root node:",this.rootNode),console.log("Selected node:",this.selectedNode),console.log("Editing node:",this.editingNode),console.log("Mindmap instance:",this.mindmap);let r=this.app.workspace.getActiveViewOfType(er);if(console.log("Active view:",r),r){let o=r.containerEl.querySelector(".mindmap-container");if(console.log("Container:",o),o){let s=o.querySelector("svg");if(console.log("SVG element:",s),s){let u=s.querySelectorAll("g");console.log("Found groups:",u.length);let c=s.querySelectorAll("text");console.log("Found text elements:",c.length);for(let h=0;h<c.length;h++){let g=c[h];console.log(`Text ${h}:`,g.textContent,g)}}}}this.rootNode&&console.log("All nodes:",this.getAllNodes(this.rootNode)),console.log("=== \u8C03\u8BD5\u4FE1\u606F\u7ED3\u675F ===")}async createSimpleTestView(){this.rootNode={id:"root",content:"\u4E2D\u5FC3\u4E3B\u9898",children:[{id:"child1",content:"\u5B50\u8282\u70B91",children:[],parent:void 0,isExpanded:!0},{id:"child2",content:"\u5B50\u8282\u70B92",children:[],parent:void 0,isExpanded:!0}],isExpanded:!0},this.rootNode.children.forEach(o=>{o.parent=this.rootNode});let r=this.app.workspace.getLeaf(!0);await r.setViewState({type:Dr,state:{data:this.rootNode}}),this.app.workspace.revealLeaf(r),setTimeout(()=>{this.createSimpleHTMLMindMap()},200)}createSimpleHTMLMindMap(){let r=this.app.workspace.getActiveViewOfType(er);if(!r||!this.rootNode)return;let o=r.containerEl.querySelector(".mindmap-container");if(!o)return;o.innerHTML="",o.style.cssText=`
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            background: var(--background-primary);
        `;let s=this.createNodeElement(this.rootNode,0);o.appendChild(s);let u=o.createDiv("children-container");u.style.cssText=`
            display: flex;
            gap: 20px;
            margin-top: 20px;
        `,this.rootNode.children.forEach(c=>{let h=this.createNodeElement(c,1);u.appendChild(h)}),this.selectNode(this.rootNode)}createNodeElement(r,o){let s=document.createElement("div");s.className="simple-mindmap-node",s.setAttribute("data-node-id",r.id),s.setAttribute("tabindex","0");let u=this.selectedNode===r;return s.style.cssText=`
            padding: 10px 15px;
            border: 2px solid ${u?"var(--text-accent)":"var(--background-modifier-border)"};
            border-radius: 8px;
            background: var(--background-primary);
            cursor: pointer;
            user-select: none;
            font-weight: ${o===0?"600":"400"};
            font-size: ${o===0?"16px":"14px"};
            color: ${u?"var(--text-accent)":"var(--text-normal)"};
            transition: all 0.2s ease;
            min-width: 100px;
            text-align: center;
            outline: none;
        `,s.textContent=r.content,s.addEventListener("click",c=>{c.stopPropagation(),console.log("Simple node clicked:",r.content),this.selectNode(r),this.updateSimpleNodeStyles()}),s.addEventListener("dblclick",c=>{c.stopPropagation(),c.preventDefault(),console.log("Simple node double-clicked:",r.content),this.startSimpleEditing(r,s)}),s.addEventListener("keydown",c=>{c.key==="Enter"&&this.selectedNode===r&&(c.preventDefault(),console.log("Enter pressed on selected node:",r.content),this.startSimpleEditing(r,s))}),s}updateSimpleNodeStyles(){document.querySelectorAll(".simple-mindmap-node").forEach(o=>{var c;let s=o.getAttribute("data-node-id"),u=((c=this.selectedNode)==null?void 0:c.id)===s;o.style.borderColor=u?"var(--text-accent)":"var(--background-modifier-border)",o.style.color=u?"var(--text-accent)":"var(--text-normal)"})}startSimpleEditing(r,o){var h;console.log("Starting simple editing for node:",r.content);let s=document.createElement("input");s.type="text",s.value=r.content,s.className="simple-mindmap-input",s.style.cssText=`
            padding: 10px 15px;
            border: 2px solid var(--text-accent);
            border-radius: 8px;
            background: var(--background-primary);
            font-weight: ${r===this.rootNode?"600":"400"};
            font-size: ${r===this.rootNode?"16px":"14px"};
            color: var(--text-accent);
            min-width: 100px;
            text-align: center;
            outline: none;
            box-shadow: 0 0 0 2px var(--background-modifier-border-hover);
        `,console.log("Replacing node element with input"),(h=o.parentNode)==null||h.replaceChild(s,o),setTimeout(()=>{s.focus(),s.select()},10);let u=()=>{console.log("Finishing edit, new content:",s.value),r.content=s.value,this.createSimpleHTMLMindMap(),this.saveData()},c=()=>{console.log("Cancelling edit"),this.createSimpleHTMLMindMap()};s.addEventListener("blur",u),s.addEventListener("keydown",g=>{console.log("Key pressed in input:",g.key),g.key==="Enter"?(g.preventDefault(),u()):g.key==="Escape"&&(g.preventDefault(),c())})}selectNode(r){this.selectedNode&&(this.selectedNode.isSelected=!1),this.selectedNode=r,r&&(r.isSelected=!0,this.highlightSelectedNode())}selectParentNode(){var r;(r=this.selectedNode)!=null&&r.parent&&this.selectNode(this.selectedNode.parent)}selectFirstChild(){this.selectedNode&&this.selectedNode.children&&this.selectedNode.children.length>0&&this.selectNode(this.selectedNode.children[0])}selectNextSibling(){var s;if(!((s=this.selectedNode)!=null&&s.parent))return;let r=this.selectedNode.parent.children,o=r.indexOf(this.selectedNode);o<r.length-1&&this.selectNode(r[o+1])}selectPreviousSibling(){var s;if(!((s=this.selectedNode)!=null&&s.parent))return;let r=this.selectedNode.parent.children,o=r.indexOf(this.selectedNode);o>0&&this.selectNode(r[o-1])}expandNode(r){r.isExpanded=!0,this.renderMindMap()}collapseNode(r){r.isExpanded=!1,this.renderMindMap()}deleteNode(r){if(!r.parent||r===this.rootNode)return;let o=r.parent.children,s=o.indexOf(r);o.splice(s,1),o.length>0?this.selectNode(o[Math.min(s,o.length-1)]):this.selectNode(r.parent),this.saveData()}findNodeIdFromElement(r){var u;if(!r.getAttribute("transform"))return null;let s=this.getAllNodes(this.rootNode);for(let c of s)if((u=r.textContent)!=null&&u.includes(c.content))return c.id;return null}getAllNodes(r){let o=[r];for(let s of r.children)o.push(...this.getAllNodes(s));return o}startEditing(r){var b;if(this.editingNode===r)return;this.finishEditing(),this.editingNode=r;let o=document.querySelector(".mindmap-container svg");if(!o)return;let s=o.querySelectorAll("text"),u=null;for(let y=0;y<s.length;y++){let S=s[y];if(((b=S.textContent)==null?void 0:b.trim())===r.content){u=S;break}}if(!u)return;let c=document.createElement("input");c.value=r.content,c.className="mindmap-node-input";let h=u.getBBox(),g=this.getSVGElementPosition(u);Object.assign(c.style,{position:"absolute",left:`${g.x}px`,top:`${g.y-h.height/2}px`,width:`${Math.max(h.width+40,100)}px`,height:`${h.height+8}px`,zIndex:"1000",fontSize:"14px",fontFamily:"var(--font-text)"}),document.body.appendChild(c),c.focus(),c.select(),c.addEventListener("blur",()=>{this.finishEditing()}),c.addEventListener("keydown",y=>{y.key==="Enter"?(y.preventDefault(),this.finishEditing()):y.key==="Escape"&&(y.preventDefault(),this.cancelEditing())})}getSVGElementPosition(r){let s=r.ownerSVGElement.createSVGPoint(),u=r.getBBox();s.x=u.x,s.y=u.y+u.height;let c=r.getScreenCTM();if(c){let h=s.matrixTransform(c);return{x:h.x,y:h.y}}return{x:0,y:0}}finishEditing(){let r=document.querySelector(".mindmap-node-input");r&&this.editingNode&&(this.editingNode.content=r.value,r.remove(),this.editingNode=null,this.renderMindMap(),this.saveData())}cancelEditing(){let r=document.querySelector(".mindmap-node-input");r&&this.editingNode&&(r.remove(),this.editingNode=null)}async createNewMindMap(){this.rootNode={id:"root",content:"\u4E2D\u5FC3\u4E3B\u9898",children:[],isExpanded:!0};let r={id:"child1",content:"\u5B50\u8282\u70B91",children:[],parent:this.rootNode,isExpanded:!0},o={id:"child2",content:"\u5B50\u8282\u70B92",children:[],parent:this.rootNode,isExpanded:!0};this.rootNode.children.push(r,o);let s=this.app.workspace.getLeaf(!0);await s.setViewState({type:Dr,state:{data:this.rootNode}}),this.app.workspace.revealLeaf(s),this.selectNode(this.rootNode),this.saveData()}saveData(){if(!this.rootNode)return Promise.resolve();let r=this.app.workspace.getActiveViewOfType(er);return r?r.leaf.setViewState({type:Dr,state:{data:this.rootNode}}):Promise.resolve()}async loadData(r){r&&(this.rootNode=this.reconstructNode(r),this.currentNode=this.rootNode,this.renderMindMap())}getRootNode(){return this.rootNode}reconstructNode(r,o=void 0){let s={id:r.id,content:r.content,children:[],parent:o};for(let u of r.children)s.children.push(this.reconstructNode(u,s));return s}createSiblingNode(r){if(!r.parent)return;let o={id:Date.now().toString(),content:"\u65B0\u8282\u70B9",children:[],parent:r.parent,isExpanded:!0},s=r.parent.children.indexOf(r);r.parent.children.splice(s+1,0,o),this.renderMindMap(),this.selectNode(o),this.saveData()}createParentNode(r){let o={id:Date.now().toString(),content:"\u65B0\u7236\u8282\u70B9",children:[r]};if(r===this.rootNode)this.rootNode=o;else if(r.parent){let s=r.parent.children,u=s.indexOf(r);s[u]=o,o.parent=r.parent}r.parent=o,this.currentNode=o,this.renderMindMap(),this.saveData()}createChildNode(r){let o={id:Date.now().toString(),content:"\u65B0\u8282\u70B9",children:[],parent:r,isExpanded:!0};r.children.push(o),r.isExpanded=!0,this.renderMindMap(),this.selectNode(o),this.saveData()}findNode(r,o){if(r.id===o)return r;for(let s of r.children){let u=this.findNode(s,o);if(u)return u}return null}renderMindMap(){let r=this.app.workspace.getActiveViewOfType(er);if(!r||!this.rootNode)return;let o=r.containerEl.querySelector(".mindmap-container");if(!o)return;o.innerHTML="";let s=o.getBoundingClientRect(),u=s.width||800,c=s.height||600;console.log("Container dimensions:",u,c);let h=rt(o).append("svg").attr("width",u).attr("height",c).style("width",u+"px").style("height",c+"px");requestAnimationFrame(()=>{try{this.mindmap=oh.create(h.node(),{autoFit:!0,duration:300,maxWidth:u-100,initialExpandLevel:2});let g=this.mindmapNodeToMarkdown(this.rootNode),{root:b}=this.transformer.transform(g);console.log("Markmap data:",b),this.addCustomNodeIds(b,this.rootNode),this.mindmap.setData(b),this.setupEventListeners(o),this.selectedNode&&setTimeout(()=>this.highlightSelectedNode(),100)}catch(g){console.error("Error creating markmap:",g),this.createFallbackDisplay(o)}})}createFallbackDisplay(r){r.innerHTML="";let o=r.createDiv("mindmap-fallback");o.style.cssText=`
            padding: 20px;
            text-align: center;
            color: var(--text-muted);
            font-size: 14px;
        `,o.innerHTML=`
            <h3>\u601D\u7EF4\u5BFC\u56FE\u6E32\u67D3\u5931\u8D25</h3>
            <p>\u8BF7\u5C1D\u8BD5\u4EE5\u4E0B\u64CD\u4F5C\uFF1A</p>
            <ul style="text-align: left; display: inline-block;">
                <li>\u91CD\u65B0\u52A0\u8F7D\u63D2\u4EF6</li>
                <li>\u68C0\u67E5\u6D4F\u89C8\u5668\u63A7\u5236\u53F0\u7684\u9519\u8BEF\u4FE1\u606F</li>
                <li>\u4F7F\u7528\u8C03\u8BD5\u547D\u4EE4\u67E5\u770B\u8BE6\u7EC6\u4FE1\u606F</li>
            </ul>
            <button onclick="this.closest('.mindmap-container').dispatchEvent(new CustomEvent('retry-render'))">
                \u91CD\u8BD5\u6E32\u67D3
            </button>
        `,r.addEventListener("retry-render",()=>{setTimeout(()=>this.renderMindMap(),100)})}addCustomNodeIds(r,o){if(r.customId=o.id,r.children&&o.children)for(let s=0;s<r.children.length;s++)o.children[s]&&this.addCustomNodeIds(r.children[s],o.children[s])}setupEventListeners(r){let o=r.querySelector("svg");if(!o){console.log("No SVG found in container");return}console.log("Setting up event listeners on SVG:",o),o.addEventListener("click",s=>{console.log("SVG click captured"),this.handleSVGClick(s)},!0),o.addEventListener("dblclick",s=>{console.log("SVG double click captured"),this.handleSVGDoubleClick(s)},!0),r.addEventListener("click",s=>{console.log("Container click captured"),this.handleSVGClick(s)},!0),r.addEventListener("dblclick",s=>{console.log("Container double click captured"),this.handleSVGDoubleClick(s)},!0)}handleSVGClick(r){var u,c;console.log("SVG Click event triggered"),console.log("Event target:",r.target),console.log("Root node:",this.rootNode);let o=r.target;console.log("Target element:",o.tagName,o.className);let s=o.closest("g[data-depth]")||o.closest("g")||((u=o.parentElement)==null?void 0:u.closest("g"));if(console.log("Found node group:",s),s){let h=s.querySelector("text");if(console.log("Text element:",h),h){let g=(c=h.textContent)==null?void 0:c.trim();if(console.log("Clicked node content:",g),g){let b=this.findNodeByContent(this.rootNode,g);b?(console.log("Found node:",b),this.selectNode(b)):(console.log("Node not found for content:",g),console.log("Available nodes:",this.getAllNodes(this.rootNode)))}}else console.log("No text element found in node group")}else console.log("No node group found, checking if editing..."),this.editingNode?(console.log("Finishing editing"),this.finishEditing()):console.log("No editing in progress, clicked on empty area")}handleSVGDoubleClick(r){var u;let s=r.target.closest("g[data-depth]");if(s){let c=s.querySelector("text");if(c){let h=(u=c.textContent)==null?void 0:u.trim();if(h){let g=this.findNodeByContent(this.rootNode,h);g&&this.startEditing(g)}}}}findNodeByContent(r,o){if(r.content===o)return r;for(let s of r.children){let u=this.findNodeByContent(s,o);if(u)return u}return null}highlightSelectedNode(){var o;if(!this.selectedNode||!this.mindmap)return;let r=document.querySelector(".mindmap-container svg");if(r){let s=r.querySelectorAll(".selected-node");for(let c=0;c<s.length;c++)s[c].classList.remove("selected-node");let u=r.querySelectorAll("text");for(let c=0;c<u.length;c++){let h=u[c];if(((o=h.textContent)==null?void 0:o.trim())===this.selectedNode.content){let g=h.closest("g");g&&g.classList.add("selected-node")}}}}mindmapNodeToMarkdown(r,o=0){let u=`${"  ".repeat(o)}- ${r.content}
`;if(r.children)for(let c of r.children)u+=this.mindmapNodeToMarkdown(c,o+1);return u}onunload(){this.mindmap=null}},er=class extends D0.ItemView{constructor(r,o){super(r);this.plugin=o}getViewType(){return Dr}getDisplayText(){return"\u601D\u7EF4\u5BFC\u56FE"}getState(){return{type:Dr,data:this.plugin.getRootNode()}}async setState(r,o){let s=r.data;this.isMindMapNode(s)&&await this.plugin.loadData(s)}isMindMapNode(r){if(!r||typeof r!="object")return!1;let o=r;return typeof o.id=="string"&&typeof o.content=="string"&&Array.isArray(o.children)&&o.children.every(s=>this.isMindMapNode(s))}async onOpen(){let r=this.containerEl.children[1];r.empty();let o=r.createDiv("mindmap-container");o.style.width="100%",o.style.height="100%",o.style.minHeight="400px",o.style.position="relative",o.style.overflow="hidden",console.log("MindMap view opened, container created"),setTimeout(async()=>{let s=this.getState();s.data&&this.isMindMapNode(s.data)?await this.plugin.loadData(s.data):this.plugin.getRootNode()?this.plugin.renderMindMap():await this.plugin.createNewMindMap()},100)}async onClose(){}};
/*! Bundled license information:

markmap-common/dist/index.mjs:
  (*! @gera2ld/jsx-dom v2.2.2 | ISC License *)

js-yaml/dist/js-yaml.mjs:
  (*! js-yaml 4.1.0 https://github.com/nodeca/js-yaml @license MIT *)

markmap-view/dist/index.js:
  (*! @gera2ld/jsx-dom v2.2.2 | ISC License *)
*/
