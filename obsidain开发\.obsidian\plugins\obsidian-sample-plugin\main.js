/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var a=Object.defineProperty;var E=Object.getOwnPropertyDescriptor;var l=Object.getOwnPropertyNames;var _=Object.prototype.hasOwnProperty;var m=(t,o)=>{for(var e in o)a(t,e,{get:o[e],enumerable:!0})},d=(t,o,e,i)=>{if(o&&typeof o=="object"||typeof o=="function")for(let r of l(o))!_.call(t,r)&&r!==e&&a(t,r,{get:()=>o[r],enumerable:!(i=E(o,r))||i.enumerable});return t};var s=t=>d(a({},"__esModule",{value:!0}),t);var T={};m(T,{MIND_MAP_VIEW_TYPE:()=>p,default:()=>n});module.exports=s(T);var I=require("obsidian");var p="mindmap";var n=class extends I.Plugin{async onload(){console.log("MindMap plugin loaded - modular version")}onunload(){console.log("MindMap plugin unloaded")}};
