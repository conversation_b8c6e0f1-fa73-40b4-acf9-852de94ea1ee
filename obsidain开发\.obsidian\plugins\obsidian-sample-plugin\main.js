/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var z1=Object.create;var qn=Object.defineProperty;var D1=Object.getOwnPropertyDescriptor;var I1=Object.getOwnPropertyNames;var B1=Object.getPrototypeOf,O1=Object.prototype.hasOwnProperty;var os=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),F1=(e,t)=>{for(var r in t)qn(e,r,{get:t[r],enumerable:!0})},ls=(e,t,r,i)=>{if(t&&typeof t=="object"||typeof t=="function")for(let l of I1(t))!O1.call(e,l)&&l!==r&&qn(e,l,{get:()=>t[l],enumerable:!(i=D1(t,l))||i.enumerable});return e};var R1=(e,t,r)=>(r=e!=null?z1(B1(e)):{},ls(t||!e||!e.__esModule?qn(r,"default",{value:e,enumerable:!0}):r,e)),L1=e=>ls(qn({},"__esModule",{value:!0}),e);var Ls=os((Ur,Ii)=>{(function(t,r){typeof Ur=="object"&&typeof Ii=="object"?Ii.exports=r():typeof define=="function"&&define.amd?define([],r):typeof Ur=="object"?Ur.katex=r():t.katex=r()})(typeof self!="undefined"?self:Ur,function(){return function(){"use strict";var e={};(function(){e.d=function(o,n){for(var s in n)e.o(n,s)&&!e.o(o,s)&&Object.defineProperty(o,s,{enumerable:!0,get:n[s]})}})(),function(){e.o=function(o,n){return Object.prototype.hasOwnProperty.call(o,n)}}();var t={};e.d(t,{default:function(){return T1}});class r{constructor(n,s){this.name=void 0,this.position=void 0,this.length=void 0,this.rawMessage=void 0;let c="KaTeX parse error: "+n,f,m,x=s&&s.loc;if(x&&x.start<=x.end){let M=x.lexer.input;f=x.start,m=x.end,f===M.length?c+=" at end of input: ":c+=" at position "+(f+1)+": ";let N=M.slice(f,m).replace(/[^]/g,"$&\u0332"),D;f>15?D="\u2026"+M.slice(f-15,f):D=M.slice(0,f);let B;m+15<M.length?B=M.slice(m,m+15)+"\u2026":B=M.slice(m),c+=D+N+B}let w=new Error(c);return w.name="ParseError",w.__proto__=r.prototype,w.position=f,f!=null&&m!=null&&(w.length=m-f),w.rawMessage=n,w}}r.prototype.__proto__=Error.prototype;var i=r;let l=function(o,n){return o.indexOf(n)!==-1},a=function(o,n){return o===void 0?n:o},u=/([A-Z])/g,h=function(o){return o.replace(u,"-$1").toLowerCase()},p={"&":"&amp;",">":"&gt;","<":"&lt;",'"':"&quot;","'":"&#x27;"},b=/[&><"']/g;function y(o){return String(o).replace(b,n=>p[n])}let S=function(o){return o.type==="ordgroup"||o.type==="color"?o.body.length===1?S(o.body[0]):o:o.type==="font"?S(o.body):o},C=function(o){let n=S(o);return n.type==="mathord"||n.type==="textord"||n.type==="atom"},T=function(o){if(!o)throw new Error("Expected non-null, but got "+String(o));return o};var z={contains:l,deflt:a,escape:y,hyphenate:h,getBaseElem:S,isCharacterBox:C,protocolFromUrl:function(o){let n=/^[\x00-\x20]*([^\\/#?]*?)(:|&#0*58|&#x0*3a|&colon)/i.exec(o);return n?n[2]!==":"||!/^[a-zA-Z][a-zA-Z0-9+\-.]*$/.test(n[1])?null:n[1].toLowerCase():"_relative"}};let q={displayMode:{type:"boolean",description:"Render math in display mode, which puts the math in display style (so \\int and \\sum are large, for example), and centers the math on the page on its own line.",cli:"-d, --display-mode"},output:{type:{enum:["htmlAndMathml","html","mathml"]},description:"Determines the markup language of the output.",cli:"-F, --format <type>"},leqno:{type:"boolean",description:"Render display math in leqno style (left-justified tags)."},fleqn:{type:"boolean",description:"Render display math flush left."},throwOnError:{type:"boolean",default:!0,cli:"-t, --no-throw-on-error",cliDescription:"Render errors (in the color given by --error-color) instead of throwing a ParseError exception when encountering an error."},errorColor:{type:"string",default:"#cc0000",cli:"-c, --error-color <color>",cliDescription:"A color string given in the format 'rgb' or 'rrggbb' (no #). This option determines the color of errors rendered by the -t option.",cliProcessor:o=>"#"+o},macros:{type:"object",cli:"-m, --macro <def>",cliDescription:"Define custom macro of the form '\\foo:expansion' (use multiple -m arguments for multiple macros).",cliDefault:[],cliProcessor:(o,n)=>(n.push(o),n)},minRuleThickness:{type:"number",description:"Specifies a minimum thickness, in ems, for fraction lines, `\\sqrt` top lines, `{array}` vertical lines, `\\hline`, `\\hdashline`, `\\underline`, `\\overline`, and the borders of `\\fbox`, `\\boxed`, and `\\fcolorbox`.",processor:o=>Math.max(0,o),cli:"--min-rule-thickness <size>",cliProcessor:parseFloat},colorIsTextColor:{type:"boolean",description:"Makes \\color behave like LaTeX's 2-argument \\textcolor, instead of LaTeX's one-argument \\color mode change.",cli:"-b, --color-is-text-color"},strict:{type:[{enum:["warn","ignore","error"]},"boolean","function"],description:"Turn on strict / LaTeX faithfulness mode, which throws an error if the input uses features that are not supported by LaTeX.",cli:"-S, --strict",cliDefault:!1},trust:{type:["boolean","function"],description:"Trust the input, enabling all HTML features such as \\url.",cli:"-T, --trust"},maxSize:{type:"number",default:1/0,description:"If non-zero, all user-specified sizes, e.g. in \\rule{500em}{500em}, will be capped to maxSize ems. Otherwise, elements and spaces can be arbitrarily large",processor:o=>Math.max(0,o),cli:"-s, --max-size <n>",cliProcessor:parseInt},maxExpand:{type:"number",default:1e3,description:"Limit the number of macro expansions to the specified number, to prevent e.g. infinite macro loops. If set to Infinity, the macro expander will try to fully expand as in LaTeX.",processor:o=>Math.max(0,o),cli:"-e, --max-expand <n>",cliProcessor:o=>o==="Infinity"?1/0:parseInt(o)},globalGroup:{type:"boolean",cli:!1}};function L(o){if(o.default)return o.default;let n=o.type,s=Array.isArray(n)?n[0]:n;if(typeof s!="string")return s.enum[0];switch(s){case"boolean":return!1;case"string":return"";case"number":return 0;case"object":return{}}}class X{constructor(n){this.displayMode=void 0,this.output=void 0,this.leqno=void 0,this.fleqn=void 0,this.throwOnError=void 0,this.errorColor=void 0,this.macros=void 0,this.minRuleThickness=void 0,this.colorIsTextColor=void 0,this.strict=void 0,this.trust=void 0,this.maxSize=void 0,this.maxExpand=void 0,this.globalGroup=void 0,n=n||{};for(let s in q)if(q.hasOwnProperty(s)){let c=q[s];this[s]=n[s]!==void 0?c.processor?c.processor(n[s]):n[s]:L(c)}}reportNonstrict(n,s,c){let f=this.strict;if(typeof f=="function"&&(f=f(n,s,c)),!(!f||f==="ignore")){if(f===!0||f==="error")throw new i("LaTeX-incompatible input and strict mode is set to 'error': "+(s+" ["+n+"]"),c);f==="warn"?typeof console!="undefined"&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(s+" ["+n+"]")):typeof console!="undefined"&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+f+"': "+s+" ["+n+"]"))}}useStrictBehavior(n,s,c){let f=this.strict;if(typeof f=="function")try{f=f(n,s,c)}catch(m){f="error"}return!f||f==="ignore"?!1:f===!0||f==="error"?!0:f==="warn"?(typeof console!="undefined"&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(s+" ["+n+"]")),!1):(typeof console!="undefined"&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+f+"': "+s+" ["+n+"]")),!1)}isTrusted(n){if(n.url&&!n.protocol){let c=z.protocolFromUrl(n.url);if(c==null)return!1;n.protocol=c}let s=typeof this.trust=="function"?this.trust(n):this.trust;return Boolean(s)}}class re{constructor(n,s,c){this.id=void 0,this.size=void 0,this.cramped=void 0,this.id=n,this.size=s,this.cramped=c}sup(){return Xe[P[this.id]]}sub(){return Xe[I[this.id]]}fracNum(){return Xe[W[this.id]]}fracDen(){return Xe[G[this.id]]}cramp(){return Xe[ee[this.id]]}text(){return Xe[oe[this.id]]}isTight(){return this.size>=2}}let ce=0,me=1,be=2,Se=3,$e=4,ze=5,Ve=6,Oe=7,Xe=[new re(ce,0,!1),new re(me,0,!0),new re(be,1,!1),new re(Se,1,!0),new re($e,2,!1),new re(ze,2,!0),new re(Ve,3,!1),new re(Oe,3,!0)],P=[$e,ze,$e,ze,Ve,Oe,Ve,Oe],I=[ze,ze,ze,ze,Oe,Oe,Oe,Oe],W=[be,Se,$e,ze,Ve,Oe,Ve,Oe],G=[Se,Se,ze,ze,Oe,Oe,Oe,Oe],ee=[me,me,Se,Se,ze,ze,Oe,Oe],oe=[ce,me,be,Se,be,Se,be,Se];var Y={DISPLAY:Xe[ce],TEXT:Xe[be],SCRIPT:Xe[$e],SCRIPTSCRIPT:Xe[Ve]};let se=[{name:"latin",blocks:[[256,591],[768,879]]},{name:"cyrillic",blocks:[[1024,1279]]},{name:"armenian",blocks:[[1328,1423]]},{name:"brahmic",blocks:[[2304,4255]]},{name:"georgian",blocks:[[4256,4351]]},{name:"cjk",blocks:[[12288,12543],[19968,40879],[65280,65376]]},{name:"hangul",blocks:[[44032,55215]]}];function xe(o){for(let n=0;n<se.length;n++){let s=se[n];for(let c=0;c<s.blocks.length;c++){let f=s.blocks[c];if(o>=f[0]&&o<=f[1])return s.name}}return null}let ve=[];se.forEach(o=>o.blocks.forEach(n=>ve.push(...n)));function ot(o){for(let n=0;n<ve.length;n+=2)if(o>=ve[n]&&o<=ve[n+1])return!0;return!1}let Me=80,lt=function(o,n){return"M95,"+(622+o+n)+`
c-2.7,0,-7.17,-2.7,-13.5,-8c-5.8,-5.3,-9.5,-10,-9.5,-14
c0,-2,0.3,-3.3,1,-4c1.3,-2.7,23.83,-20.7,67.5,-54
c44.2,-33.3,65.8,-50.3,66.5,-51c1.3,-1.3,3,-2,5,-2c4.7,0,8.7,3.3,12,10
s173,378,173,378c0.7,0,35.3,-71,104,-213c68.7,-142,137.5,-285,206.5,-429
c69,-144,104.5,-217.7,106.5,-221
l`+o/2.075+" -"+o+`
c5.3,-9.3,12,-14,20,-14
H400000v`+(40+o)+`H845.2724
s-225.272,467,-225.272,467s-235,486,-235,486c-2.7,4.7,-9,7,-19,7
c-6,0,-10,-1,-12,-3s-194,-422,-194,-422s-65,47,-65,47z
M`+(834+o)+" "+n+"h400000v"+(40+o)+"h-400000z"},yt=function(o,n){return"M263,"+(601+o+n)+`c0.7,0,18,39.7,52,119
c34,79.3,68.167,158.7,102.5,238c34.3,79.3,51.8,119.3,52.5,120
c340,-704.7,510.7,-1060.3,512,-1067
l`+o/2.084+" -"+o+`
c4.7,-7.3,11,-11,19,-11
H40000v`+(40+o)+`H1012.3
s-271.3,567,-271.3,567c-38.7,80.7,-84,175,-136,283c-52,108,-89.167,185.3,-111.5,232
c-22.3,46.7,-33.8,70.3,-34.5,71c-4.7,4.7,-12.3,7,-23,7s-12,-1,-12,-1
s-109,-253,-109,-253c-72.7,-168,-109.3,-252,-110,-252c-10.7,8,-22,16.7,-34,26
c-22,17.3,-33.3,26,-34,26s-26,-26,-26,-26s76,-59,76,-59s76,-60,76,-60z
M`+(1001+o)+" "+n+"h400000v"+(40+o)+"h-400000z"},st=function(o,n){return"M983 "+(10+o+n)+`
l`+o/3.13+" -"+o+`
c4,-6.7,10,-10,18,-10 H400000v`+(40+o)+`
H1013.1s-83.4,268,-264.1,840c-180.7,572,-277,876.3,-289,913c-4.7,4.7,-12.7,7,-24,7
s-12,0,-12,0c-1.3,-3.3,-3.7,-11.7,-7,-25c-35.3,-125.3,-106.7,-373.3,-214,-744
c-10,12,-21,25,-33,39s-32,39,-32,39c-6,-5.3,-15,-14,-27,-26s25,-30,25,-30
c26.7,-32.7,52,-63,76,-91s52,-60,52,-60s208,722,208,722
c56,-175.3,126.3,-397.3,211,-666c84.7,-268.7,153.8,-488.2,207.5,-658.5
c53.7,-170.3,84.5,-266.8,92.5,-289.5z
M`+(1001+o)+" "+n+"h400000v"+(40+o)+"h-400000z"},bt=function(o,n){return"M424,"+(2398+o+n)+`
c-1.3,-0.7,-38.5,-172,-111.5,-514c-73,-342,-109.8,-513.3,-110.5,-514
c0,-2,-10.7,14.3,-32,49c-4.7,7.3,-9.8,15.7,-15.5,25c-5.7,9.3,-9.8,16,-12.5,20
s-5,7,-5,7c-4,-3.3,-8.3,-7.7,-13,-13s-13,-13,-13,-13s76,-122,76,-122s77,-121,77,-121
s209,968,209,968c0,-2,84.7,-361.7,254,-1079c169.3,-717.3,254.7,-1077.7,256,-1081
l`+o/4.223+" -"+o+`c4,-6.7,10,-10,18,-10 H400000
v`+(40+o)+`H1014.6
s-87.3,378.7,-272.6,1166c-185.3,787.3,-279.3,1182.3,-282,1185
c-2,6,-10,9,-24,9
c-8,0,-12,-0.7,-12,-2z M`+(1001+o)+" "+n+`
h400000v`+(40+o)+"h-400000z"},Or=function(o,n){return"M473,"+(2713+o+n)+`
c339.3,-1799.3,509.3,-2700,510,-2702 l`+o/5.298+" -"+o+`
c3.3,-7.3,9.3,-11,18,-11 H400000v`+(40+o)+`H1017.7
s-90.5,478,-276.2,1466c-185.7,988,-279.5,1483,-281.5,1485c-2,6,-10,9,-24,9
c-8,0,-12,-0.7,-12,-2c0,-1.3,-5.3,-32,-16,-92c-50.7,-293.3,-119.7,-693.3,-207,-1200
c0,-1.3,-5.3,8.7,-16,30c-10.7,21.3,-21.3,42.7,-32,64s-16,33,-16,33s-26,-26,-26,-26
s76,-153,76,-153s77,-151,77,-151c0.7,0.7,35.7,202,105,604c67.3,400.7,102,602.7,104,
606zM`+(1001+o)+" "+n+"h400000v"+(40+o)+"H1017.7z"},lh=function(o){let n=o/2;return"M400000 "+o+" H0 L"+n+" 0 l65 45 L145 "+(o-80)+" H400000z"},sh=function(o,n,s){let c=s-54-n-o;return"M702 "+(o+n)+"H400000"+(40+o)+`
H742v`+c+`l-4 4-4 4c-.667.7 -2 1.5-4 2.5s-4.167 1.833-6.5 2.5-5.5 1-9.5 1
h-12l-28-84c-16.667-52-96.667 -294.333-240-727l-212 -643 -85 170
c-4-3.333-8.333-7.667-13 -13l-13-13l77-155 77-156c66 199.333 139 419.667
219 661 l218 661zM702 `+n+"H400000v"+(40+o)+"H742z"},ah=function(o,n,s){n=1e3*n;let c="";switch(o){case"sqrtMain":c=lt(n,Me);break;case"sqrtSize1":c=yt(n,Me);break;case"sqrtSize2":c=st(n,Me);break;case"sqrtSize3":c=bt(n,Me);break;case"sqrtSize4":c=Or(n,Me);break;case"sqrtTall":c=sh(n,Me,s)}return c},ch=function(o,n){switch(o){case"\u239C":return"M291 0 H417 V"+n+" H291z M291 0 H417 V"+n+" H291z";case"\u2223":return"M145 0 H188 V"+n+" H145z M145 0 H188 V"+n+" H145z";case"\u2225":return"M145 0 H188 V"+n+" H145z M145 0 H188 V"+n+" H145z"+("M367 0 H410 V"+n+" H367z M367 0 H410 V"+n+" H367z");case"\u239F":return"M457 0 H583 V"+n+" H457z M457 0 H583 V"+n+" H457z";case"\u23A2":return"M319 0 H403 V"+n+" H319z M319 0 H403 V"+n+" H319z";case"\u23A5":return"M263 0 H347 V"+n+" H263z M263 0 H347 V"+n+" H263z";case"\u23AA":return"M384 0 H504 V"+n+" H384z M384 0 H504 V"+n+" H384z";case"\u23D0":return"M312 0 H355 V"+n+" H312z M312 0 H355 V"+n+" H312z";case"\u2016":return"M257 0 H300 V"+n+" H257z M257 0 H300 V"+n+" H257z"+("M478 0 H521 V"+n+" H478z M478 0 H521 V"+n+" H478z");default:return""}},Io={doubleleftarrow:`M262 157
l10-10c34-36 62.7-77 86-123 3.3-8 5-13.3 5-16 0-5.3-6.7-8-20-8-7.3
 0-12.2.5-14.5 1.5-2.3 1-4.8 4.5-7.5 10.5-49.3 97.3-121.7 169.3-217 216-28
 14-57.3 25-88 33-6.7 2-11 3.8-13 5.5-2 1.7-3 4.2-3 7.5s1 5.8 3 7.5
c2 1.7 6.3 3.5 13 5.5 68 17.3 128.2 47.8 180.5 91.5 52.3 43.7 93.8 96.2 124.5
 157.5 9.3 8 15.3 12.3 18 13h6c12-.7 18-4 18-10 0-2-1.7-7-5-15-23.3-46-52-87
-86-123l-10-10h399738v-40H218c328 0 0 0 0 0l-10-8c-26.7-20-65.7-43-117-69 2.7
-2 6-3.7 10-5 36.7-16 72.3-37.3 107-64l10-8h399782v-40z
m8 0v40h399730v-40zm0 194v40h399730v-40z`,doublerightarrow:`M399738 392l
-10 10c-34 36-62.7 77-86 123-3.3 8-5 13.3-5 16 0 5.3 6.7 8 20 8 7.3 0 12.2-.5
 14.5-1.5 2.3-1 4.8-4.5 7.5-10.5 49.3-97.3 121.7-169.3 217-216 28-14 57.3-25 88
-33 6.7-2 11-3.8 13-5.5 2-1.7 3-4.2 3-7.5s-1-5.8-3-7.5c-2-1.7-6.3-3.5-13-5.5-68
-17.3-128.2-47.8-180.5-91.5-52.3-43.7-93.8-96.2-124.5-157.5-9.3-8-15.3-12.3-18
-13h-6c-12 .7-18 4-18 10 0 2 1.7 7 5 15 23.3 46 52 87 86 123l10 10H0v40h399782
c-328 0 0 0 0 0l10 8c26.7 20 65.7 43 117 69-2.7 2-6 3.7-10 5-36.7 16-72.3 37.3
-107 64l-10 8H0v40zM0 157v40h399730v-40zm0 194v40h399730v-40z`,leftarrow:`M400000 241H110l3-3c68.7-52.7 113.7-120
 135-202 4-14.7 6-23 6-25 0-7.3-7-11-21-11-8 0-13.2.8-15.5 2.5-2.3 1.7-4.2 5.8
-5.5 12.5-1.3 4.7-2.7 10.3-4 17-12 48.7-34.8 92-68.5 130S65.3 228.3 18 247
c-10 4-16 7.7-18 11 0 8.7 6 14.3 18 17 47.3 18.7 87.8 47 121.5 85S196 441.3 208
 490c.7 2 1.3 5 2 9s1.2 6.7 1.5 8c.3 1.3 1 3.3 2 6s2.2 4.5 3.5 5.5c1.3 1 3.3
 1.8 6 2.5s6 1 10 1c14 0 21-3.7 21-11 0-2-2-10.3-6-25-20-79.3-65-146.7-135-202
 l-3-3h399890zM100 241v40h399900v-40z`,leftbrace:`M6 548l-6-6v-35l6-11c56-104 135.3-181.3 238-232 57.3-28.7 117
-45 179-50h399577v120H403c-43.3 7-81 15-113 26-100.7 33-179.7 91-237 174-2.7
 5-6 9-10 13-.7 1-7.3 1-20 1H6z`,leftbraceunder:`M0 6l6-6h17c12.688 0 19.313.3 20 1 4 4 7.313 8.3 10 13
 35.313 51.3 80.813 93.8 136.5 127.5 55.688 33.7 117.188 55.8 184.5 66.5.688
 0 2 .3 4 1 18.688 2.7 76 4.3 172 5h399450v120H429l-6-1c-124.688-8-235-61.7
-331-161C60.687 138.7 32.312 99.3 7 54L0 41V6z`,leftgroup:`M400000 80
H435C64 80 168.3 229.4 21 260c-5.9 1.2-18 0-18 0-2 0-3-1-3-3v-38C76 61 257 0
 435 0h399565z`,leftgroupunder:`M400000 262
H435C64 262 168.3 112.6 21 82c-5.9-1.2-18 0-18 0-2 0-3 1-3 3v38c76 158 257 219
 435 219h399565z`,leftharpoon:`M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3
-3.3 10.2-9.5 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5
-18.3 3-21-1.3-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7
-196 228-6.7 4.7-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40z`,leftharpoonplus:`M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3-3.3 10.2-9.5
 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5-18.3 3-21-1.3
-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7-196 228-6.7 4.7
-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40zM0 435v40h400000v-40z
m0 0v40h400000v-40z`,leftharpoondown:`M7 241c-4 4-6.333 8.667-7 14 0 5.333.667 9 2 11s5.333
 5.333 12 10c90.667 54 156 130 196 228 3.333 10.667 6.333 16.333 9 17 2 .667 5
 1 9 1h5c10.667 0 16.667-2 18-6 2-2.667 1-9.667-3-21-32-87.333-82.667-157.667
-152-211l-3-3h399907v-40zM93 281 H400000 v-40L7 241z`,leftharpoondownplus:`M7 435c-4 4-6.3 8.7-7 14 0 5.3.7 9 2 11s5.3 5.3 12
 10c90.7 54 156 130 196 228 3.3 10.7 6.3 16.3 9 17 2 .7 5 1 9 1h5c10.7 0 16.7
-2 18-6 2-2.7 1-9.7-3-21-32-87.3-82.7-157.7-152-211l-3-3h399907v-40H7zm93 0
v40h399900v-40zM0 241v40h399900v-40zm0 0v40h399900v-40z`,lefthook:`M400000 281 H103s-33-11.2-61-33.5S0 197.3 0 164s14.2-61.2 42.5
-83.5C70.8 58.2 104 47 142 47 c16.7 0 25 6.7 25 20 0 12-8.7 18.7-26 20-40 3.3
-68.7 15.7-86 37-10 12-15 25.3-15 40 0 22.7 9.8 40.7 29.5 54 19.7 13.3 43.5 21
 71.5 23h399859zM103 281v-40h399897v40z`,leftlinesegment:`M40 281 V428 H0 V94 H40 V241 H400000 v40z
M40 281 V428 H0 V94 H40 V241 H400000 v40z`,leftmapsto:`M40 281 V448H0V74H40V241H400000v40z
M40 281 V448H0V74H40V241H400000v40z`,leftToFrom:`M0 147h400000v40H0zm0 214c68 40 115.7 95.7 143 167h22c15.3 0 23
-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69-70-101l-7-8h399905v-40H95l7-8
c28.7-32 52-65.7 70-101 10.7-23.3 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 265.3
 68 321 0 361zm0-174v-40h399900v40zm100 154v40h399900v-40z`,longequal:`M0 50 h400000 v40H0z m0 194h40000v40H0z
M0 50 h400000 v40H0z m0 194h40000v40H0z`,midbrace:`M200428 334
c-100.7-8.3-195.3-44-280-108-55.3-42-101.7-93-139-153l-9-14c-2.7 4-5.7 8.7-9 14
-53.3 86.7-123.7 153-211 199-66.7 36-137.3 56.3-212 62H0V214h199568c178.3-11.7
 311.7-78.3 403-201 6-8 9.7-12 11-12 .7-.7 6.7-1 18-1s17.3.3 18 1c1.3 0 5 4 11
 12 44.7 59.3 101.3 106.3 170 141s145.3 54.3 229 60h199572v120z`,midbraceunder:`M199572 214
c100.7 8.3 195.3 44 280 108 55.3 42 101.7 93 139 153l9 14c2.7-4 5.7-8.7 9-14
 53.3-86.7 123.7-153 211-199 66.7-36 137.3-56.3 212-62h199568v120H200432c-178.3
 11.7-311.7 78.3-403 201-6 8-9.7 12-11 12-.7.7-6.7 1-18 1s-17.3-.3-18-1c-1.3 0
-5-4-11-12-44.7-59.3-101.3-106.3-170-141s-145.3-54.3-229-60H0V214z`,oiintSize1:`M512.6 71.6c272.6 0 320.3 106.8 320.3 178.2 0 70.8-47.7 177.6
-320.3 177.6S193.1 320.6 193.1 249.8c0-71.4 46.9-178.2 319.5-178.2z
m368.1 178.2c0-86.4-60.9-215.4-368.1-215.4-306.4 0-367.3 129-367.3 215.4 0 85.8
60.9 214.8 367.3 214.8 307.2 0 368.1-129 368.1-214.8z`,oiintSize2:`M757.8 100.1c384.7 0 451.1 137.6 451.1 230 0 91.3-66.4 228.8
-451.1 228.8-386.3 0-452.7-137.5-452.7-228.8 0-92.4 66.4-230 452.7-230z
m502.4 230c0-111.2-82.4-277.2-502.4-277.2s-504 166-504 277.2
c0 110 84 276 504 276s502.4-166 502.4-276z`,oiiintSize1:`M681.4 71.6c408.9 0 480.5 106.8 480.5 178.2 0 70.8-71.6 177.6
-480.5 177.6S202.1 320.6 202.1 249.8c0-71.4 70.5-178.2 479.3-178.2z
m525.8 178.2c0-86.4-86.8-215.4-525.7-215.4-437.9 0-524.7 129-524.7 215.4 0
85.8 86.8 214.8 524.7 214.8 438.9 0 525.7-129 525.7-214.8z`,oiiintSize2:`M1021.2 53c603.6 0 707.8 165.8 707.8 277.2 0 110-104.2 275.8
-707.8 275.8-606 0-710.2-165.8-710.2-275.8C311 218.8 415.2 53 1021.2 53z
m770.4 277.1c0-131.2-126.4-327.6-770.5-327.6S248.4 198.9 248.4 330.1
c0 130 128.8 326.4 772.7 326.4s770.5-196.4 770.5-326.4z`,rightarrow:`M0 241v40h399891c-47.3 35.3-84 78-110 128
-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20
 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7
 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85
-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5
-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67
 151.7 139 205zm0 0v40h399900v-40z`,rightbrace:`M400000 542l
-6 6h-17c-12.7 0-19.3-.3-20-1-4-4-7.3-8.3-10-13-35.3-51.3-80.8-93.8-136.5-127.5
s-117.2-55.8-184.5-66.5c-.7 0-2-.3-4-1-18.7-2.7-76-4.3-172-5H0V214h399571l6 1
c124.7 8 235 61.7 331 161 31.3 33.3 59.7 72.7 85 118l7 13v35z`,rightbraceunder:`M399994 0l6 6v35l-6 11c-56 104-135.3 181.3-238 232-57.3
 28.7-117 45-179 50H-300V214h399897c43.3-7 81-15 113-26 100.7-33 179.7-91 237
-174 2.7-5 6-9 10-13 .7-1 7.3-1 20-1h17z`,rightgroup:`M0 80h399565c371 0 266.7 149.4 414 180 5.9 1.2 18 0 18 0 2 0
 3-1 3-3v-38c-76-158-257-219-435-219H0z`,rightgroupunder:`M0 262h399565c371 0 266.7-149.4 414-180 5.9-1.2 18 0 18
 0 2 0 3 1 3 3v38c-76 158-257 219-435 219H0z`,rightharpoon:`M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3
-3.7-15.3-11-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2
-10.7 0-16.7 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58
 69.2 92 94.5zm0 0v40h399900v-40z`,rightharpoonplus:`M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3-3.7-15.3-11
-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2-10.7 0-16.7
 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58 69.2 92 94.5z
m0 0v40h399900v-40z m100 194v40h399900v-40zm0 0v40h399900v-40z`,rightharpoondown:`M399747 511c0 7.3 6.7 11 20 11 8 0 13-.8 15-2.5s4.7-6.8
 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3 8.5-5.8 9.5
-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3-64.7 57-92 95
-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 241v40h399900v-40z`,rightharpoondownplus:`M399747 705c0 7.3 6.7 11 20 11 8 0 13-.8
 15-2.5s4.7-6.8 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3
 8.5-5.8 9.5-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3
-64.7 57-92 95-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 435v40h399900v-40z
m0-194v40h400000v-40zm0 0v40h400000v-40z`,righthook:`M399859 241c-764 0 0 0 0 0 40-3.3 68.7-15.7 86-37 10-12 15-25.3
 15-40 0-22.7-9.8-40.7-29.5-54-19.7-13.3-43.5-21-71.5-23-17.3-1.3-26-8-26-20 0
-13.3 8.7-20 26-20 38 0 71 11.2 99 33.5 0 0 7 5.6 21 16.7 14 11.2 21 33.5 21
 66.8s-14 61.2-42 83.5c-28 22.3-61 33.5-99 33.5L0 241z M0 281v-40h399859v40z`,rightlinesegment:`M399960 241 V94 h40 V428 h-40 V281 H0 v-40z
M399960 241 V94 h40 V428 h-40 V281 H0 v-40z`,rightToFrom:`M400000 167c-70.7-42-118-97.7-142-167h-23c-15.3 0-23 .3-23
 1 0 1.3 5.3 13.7 16 37 18 35.3 41.3 69 70 101l7 8H0v40h399905l-7 8c-28.7 32
-52 65.7-70 101-10.7 23.3-16 35.7-16 37 0 .7 7.7 1 23 1h23c24-69.3 71.3-125 142
-167z M100 147v40h399900v-40zM0 341v40h399900v-40z`,twoheadleftarrow:`M0 167c68 40
 115.7 95.7 143 167h22c15.3 0 23-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69
-70-101l-7-8h125l9 7c50.7 39.3 85 86 103 140h46c0-4.7-6.3-18.7-19-42-18-35.3
-40-67.3-66-96l-9-9h399716v-40H284l9-9c26-28.7 48-60.7 66-96 12.7-23.333 19
-37.333 19-42h-46c-18 54-52.3 100.7-103 140l-9 7H95l7-8c28.7-32 52-65.7 70-101
 10.7-23.333 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 71.3 68 127 0 167z`,twoheadrightarrow:`M400000 167
c-68-40-115.7-95.7-143-167h-22c-15.3 0-23 .3-23 1 0 1.3 5.3 13.7 16 37 18 35.3
 41.3 69 70 101l7 8h-125l-9-7c-50.7-39.3-85-86-103-140h-46c0 4.7 6.3 18.7 19 42
 18 35.3 40 67.3 66 96l9 9H0v40h399716l-9 9c-26 28.7-48 60.7-66 96-12.7 23.333
-19 37.333-19 42h46c18-54 52.3-100.7 103-140l9-7h125l-7 8c-28.7 32-52 65.7-70
 101-10.7 23.333-16 35.7-16 37 0 .7 7.7 1 23 1h22c27.3-71.3 75-127 143-167z`,tilde1:`M200 55.538c-77 0-168 73.953-177 73.953-3 0-7
-2.175-9-5.437L2 97c-1-2-2-4-2-6 0-4 2-7 5-9l20-12C116 12 171 0 207 0c86 0
 114 68 191 68 78 0 168-68 177-68 4 0 7 2 9 5l12 19c1 2.175 2 4.35 2 6.525 0
 4.35-2 7.613-5 9.788l-19 13.05c-92 63.077-116.937 75.308-183 76.128
-68.267.847-113-73.952-191-73.952z`,tilde2:`M344 55.266c-142 0-300.638 81.316-311.5 86.418
-8.01 3.762-22.5 10.91-23.5 5.562L1 120c-1-2-1-3-1-4 0-5 3-9 8-10l18.4-9C160.9
 31.9 283 0 358 0c148 0 188 122 331 122s314-97 326-97c4 0 8 2 10 7l7 21.114
c1 2.14 1 3.21 1 4.28 0 5.347-3 9.626-7 10.696l-22.3 12.622C852.6 158.372 751
 181.476 676 181.476c-149 0-189-126.21-332-126.21z`,tilde3:`M786 59C457 59 32 175.242 13 175.242c-6 0-10-3.457
-11-10.37L.15 138c-1-7 3-12 10-13l19.2-6.4C378.4 40.7 634.3 0 804.3 0c337 0
 411.8 157 746.8 157 328 0 754-112 773-112 5 0 10 3 11 9l1 14.075c1 8.066-.697
 16.595-6.697 17.492l-21.052 7.31c-367.9 98.146-609.15 122.696-778.15 122.696
 -338 0-409-156.573-744-156.573z`,tilde4:`M786 58C457 58 32 177.487 13 177.487c-6 0-10-3.345
-11-10.035L.15 143c-1-7 3-12 10-13l22-6.7C381.2 35 637.15 0 807.15 0c337 0 409
 177 744 177 328 0 754-127 773-127 5 0 10 3 11 9l1 14.794c1 7.805-3 13.38-9
 14.495l-20.7 5.574c-366.85 99.79-607.3 139.372-776.3 139.372-338 0-409
 -175.236-744-175.236z`,vec:`M377 20c0-5.333 1.833-10 5.5-14S391 0 397 0c4.667 0 8.667 1.667 12 5
3.333 2.667 6.667 9 10 19 6.667 24.667 20.333 43.667 41 57 7.333 4.667 11
10.667 11 18 0 6-1 10-3 12s-6.667 5-14 9c-28.667 14.667-53.667 35.667-75 63
-1.333 1.333-3.167 3.5-5.5 6.5s-4 4.833-5 5.5c-1 .667-2.5 1.333-4.5 2s-4.333 1
-7 1c-4.667 0-9.167-1.833-13.5-5.5S337 184 337 178c0-12.667 15.667-32.333 47-59
H213l-171-1c-8.667-6-13-12.333-13-19 0-4.667 4.333-11.333 13-20h359
c-16-25.333-24-45-24-59z`,widehat1:`M529 0h5l519 115c5 1 9 5 9 10 0 1-1 2-1 3l-4 22
c-1 5-5 9-11 9h-2L532 67 19 159h-2c-5 0-9-4-11-9l-5-22c-1-6 2-12 8-13z`,widehat2:`M1181 0h2l1171 176c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 220h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widehat3:`M1181 0h2l1171 236c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 280h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widehat4:`M1181 0h2l1171 296c6 0 10 5 10 11l-2 23c-1 6-5 10
-11 10h-1L1182 67 15 340h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z`,widecheck1:`M529,159h5l519,-115c5,-1,9,-5,9,-10c0,-1,-1,-2,-1,-3l-4,-22c-1,
-5,-5,-9,-11,-9h-2l-512,92l-513,-92h-2c-5,0,-9,4,-11,9l-5,22c-1,6,2,12,8,13z`,widecheck2:`M1181,220h2l1171,-176c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,153l-1167,-153h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,widecheck3:`M1181,280h2l1171,-236c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,213l-1167,-213h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,widecheck4:`M1181,340h2l1171,-296c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,
-11,-10h-1l-1168,273l-1167,-273h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z`,baraboveleftarrow:`M400000 620h-399890l3 -3c68.7 -52.7 113.7 -120 135 -202
c4 -14.7 6 -23 6 -25c0 -7.3 -7 -11 -21 -11c-8 0 -13.2 0.8 -15.5 2.5
c-2.3 1.7 -4.2 5.8 -5.5 12.5c-1.3 4.7 -2.7 10.3 -4 17c-12 48.7 -34.8 92 -68.5 130
s-74.2 66.3 -121.5 85c-10 4 -16 7.7 -18 11c0 8.7 6 14.3 18 17c47.3 18.7 87.8 47
121.5 85s56.5 81.3 68.5 130c0.7 2 1.3 5 2 9s1.2 6.7 1.5 8c0.3 1.3 1 3.3 2 6
s2.2 4.5 3.5 5.5c1.3 1 3.3 1.8 6 2.5s6 1 10 1c14 0 21 -3.7 21 -11
c0 -2 -2 -10.3 -6 -25c-20 -79.3 -65 -146.7 -135 -202l-3 -3h399890z
M100 620v40h399900v-40z M0 241v40h399900v-40zM0 241v40h399900v-40z`,rightarrowabovebar:`M0 241v40h399891c-47.3 35.3-84 78-110 128-16.7 32
-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20 11 8 0
13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7 39
-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85-40.5
-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5
-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67
151.7 139 205zm96 379h399894v40H0zm0 0h399904v40H0z`,baraboveshortleftharpoon:`M507,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11
c1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17
c2,0.7,5,1,9,1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21
c-32,-87.3,-82.7,-157.7,-152,-211c0,0,-3,-3,-3,-3l399351,0l0,-40
c-398570,0,-399437,0,-399437,0z M593 435 v40 H399500 v-40z
M0 281 v-40 H399908 v40z M0 281 v-40 H399908 v40z`,rightharpoonaboveshortbar:`M0,241 l0,40c399126,0,399993,0,399993,0
c4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,
-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6
c-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z
M0 241 v40 H399908 v-40z M0 475 v-40 H399500 v40z M0 475 v-40 H399500 v40z`,shortbaraboveleftharpoon:`M7,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11
c1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17c2,0.7,5,1,9,
1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21c-32,-87.3,-82.7,-157.7,
-152,-211c0,0,-3,-3,-3,-3l399907,0l0,-40c-399126,0,-399993,0,-399993,0z
M93 435 v40 H400000 v-40z M500 241 v40 H400000 v-40z M500 241 v40 H400000 v-40z`,shortrightharpoonabovebar:`M53,241l0,40c398570,0,399437,0,399437,0
c4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,
-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6
c-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z
M500 241 v40 H399408 v-40z M500 435 v40 H400000 v-40z`},uh=function(o,n){switch(o){case"lbrack":return"M403 1759 V84 H666 V0 H319 V1759 v"+n+` v1759 h347 v-84
H403z M403 1759 V0 H319 V1759 v`+n+" v1759 h84z";case"rbrack":return"M347 1759 V0 H0 V84 H263 V1759 v"+n+` v1759 H0 v84 H347z
M347 1759 V0 H263 V1759 v`+n+" v1759 h84z";case"vert":return"M145 15 v585 v"+n+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-n+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M188 15 H145 v585 v`+n+" v585 h43z";case"doublevert":return"M145 15 v585 v"+n+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-n+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M188 15 H145 v585 v`+n+` v585 h43z
M367 15 v585 v`+n+` v585 c2.667,10,9.667,15,21,15
c10,0,16.667,-5,20,-15 v-585 v`+-n+` v-585 c-2.667,-10,-9.667,-15,-21,-15
c-10,0,-16.667,5,-20,15z M410 15 H367 v585 v`+n+" v585 h43z";case"lfloor":return"M319 602 V0 H403 V602 v"+n+` v1715 h263 v84 H319z
MM319 602 V0 H403 V602 v`+n+" v1715 H319z";case"rfloor":return"M319 602 V0 H403 V602 v"+n+` v1799 H0 v-84 H319z
MM319 602 V0 H403 V602 v`+n+" v1715 H319z";case"lceil":return"M403 1759 V84 H666 V0 H319 V1759 v"+n+` v602 h84z
M403 1759 V0 H319 V1759 v`+n+" v602 h84z";case"rceil":return"M347 1759 V0 H0 V84 H263 V1759 v"+n+` v602 h84z
M347 1759 V0 h-84 V1759 v`+n+" v602 h84z";case"lparen":return`M863,9c0,-2,-2,-5,-6,-9c0,0,-17,0,-17,0c-12.7,0,-19.3,0.3,-20,1
c-5.3,5.3,-10.3,11,-15,17c-242.7,294.7,-395.3,682,-458,1162c-21.3,163.3,-33.3,349,
-36,557 l0,`+(n+84)+`c0.2,6,0,26,0,60c2,159.3,10,310.7,24,454c53.3,528,210,
949.7,470,1265c4.7,6,9.7,11.7,15,17c0.7,0.7,7,1,19,1c0,0,18,0,18,0c4,-4,6,-7,6,-9
c0,-2.7,-3.3,-8.7,-10,-18c-135.3,-192.7,-235.5,-414.3,-300.5,-665c-65,-250.7,-102.5,
-544.7,-112.5,-882c-2,-104,-3,-167,-3,-189
l0,-`+(n+92)+`c0,-162.7,5.7,-314,17,-454c20.7,-272,63.7,-513,129,-723c65.3,
-210,155.3,-396.3,270,-559c6.7,-9.3,10,-15.3,10,-18z`;case"rparen":return`M76,0c-16.7,0,-25,3,-25,9c0,2,2,6.3,6,13c21.3,28.7,42.3,60.3,
63,95c96.7,156.7,172.8,332.5,228.5,527.5c55.7,195,92.8,416.5,111.5,664.5
c11.3,139.3,17,290.7,17,454c0,28,1.7,43,3.3,45l0,`+(n+9)+`
c-3,4,-3.3,16.7,-3.3,38c0,162,-5.7,313.7,-17,455c-18.7,248,-55.8,469.3,-111.5,664
c-55.7,194.7,-131.8,370.3,-228.5,527c-20.7,34.7,-41.7,66.3,-63,95c-2,3.3,-4,7,-6,11
c0,7.3,5.7,11,17,11c0,0,11,0,11,0c9.3,0,14.3,-0.3,15,-1c5.3,-5.3,10.3,-11,15,-17
c242.7,-294.7,395.3,-681.7,458,-1161c21.3,-164.7,33.3,-350.7,36,-558
l0,-`+(n+144)+`c-2,-159.3,-10,-310.7,-24,-454c-53.3,-528,-210,-949.7,
-470,-1265c-4.7,-6,-9.7,-11.7,-15,-17c-0.7,-0.7,-6.7,-1,-18,-1z`;default:throw new Error("Unknown stretchy delimiter.")}};class Fr{constructor(n){this.children=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.children=n,this.classes=[],this.height=0,this.depth=0,this.maxFontSize=0,this.style={}}hasClass(n){return z.contains(this.classes,n)}toNode(){let n=document.createDocumentFragment();for(let s=0;s<this.children.length;s++)n.appendChild(this.children[s].toNode());return n}toMarkup(){let n="";for(let s=0;s<this.children.length;s++)n+=this.children[s].toMarkup();return n}toText(){let n=s=>s.toText();return this.children.map(n).join("")}}var At={"AMS-Regular":{32:[0,0,0,0,.25],65:[0,.68889,0,0,.72222],66:[0,.68889,0,0,.66667],67:[0,.68889,0,0,.72222],68:[0,.68889,0,0,.72222],69:[0,.68889,0,0,.66667],70:[0,.68889,0,0,.61111],71:[0,.68889,0,0,.77778],72:[0,.68889,0,0,.77778],73:[0,.68889,0,0,.38889],74:[.16667,.68889,0,0,.5],75:[0,.68889,0,0,.77778],76:[0,.68889,0,0,.66667],77:[0,.68889,0,0,.94445],78:[0,.68889,0,0,.72222],79:[.16667,.68889,0,0,.77778],80:[0,.68889,0,0,.61111],81:[.16667,.68889,0,0,.77778],82:[0,.68889,0,0,.72222],83:[0,.68889,0,0,.55556],84:[0,.68889,0,0,.66667],85:[0,.68889,0,0,.72222],86:[0,.68889,0,0,.72222],87:[0,.68889,0,0,1],88:[0,.68889,0,0,.72222],89:[0,.68889,0,0,.72222],90:[0,.68889,0,0,.66667],107:[0,.68889,0,0,.55556],160:[0,0,0,0,.25],165:[0,.675,.025,0,.75],174:[.15559,.69224,0,0,.94666],240:[0,.68889,0,0,.55556],295:[0,.68889,0,0,.54028],710:[0,.825,0,0,2.33334],732:[0,.9,0,0,2.33334],770:[0,.825,0,0,2.33334],771:[0,.9,0,0,2.33334],989:[.08167,.58167,0,0,.77778],1008:[0,.43056,.04028,0,.66667],8245:[0,.54986,0,0,.275],8463:[0,.68889,0,0,.54028],8487:[0,.68889,0,0,.72222],8498:[0,.68889,0,0,.55556],8502:[0,.68889,0,0,.66667],8503:[0,.68889,0,0,.44445],8504:[0,.68889,0,0,.66667],8513:[0,.68889,0,0,.63889],8592:[-.03598,.46402,0,0,.5],8594:[-.03598,.46402,0,0,.5],8602:[-.13313,.36687,0,0,1],8603:[-.13313,.36687,0,0,1],8606:[.01354,.52239,0,0,1],8608:[.01354,.52239,0,0,1],8610:[.01354,.52239,0,0,1.11111],8611:[.01354,.52239,0,0,1.11111],8619:[0,.54986,0,0,1],8620:[0,.54986,0,0,1],8621:[-.13313,.37788,0,0,1.38889],8622:[-.13313,.36687,0,0,1],8624:[0,.69224,0,0,.5],8625:[0,.69224,0,0,.5],8630:[0,.43056,0,0,1],8631:[0,.43056,0,0,1],8634:[.08198,.58198,0,0,.77778],8635:[.08198,.58198,0,0,.77778],8638:[.19444,.69224,0,0,.41667],8639:[.19444,.69224,0,0,.41667],8642:[.19444,.69224,0,0,.41667],8643:[.19444,.69224,0,0,.41667],8644:[.1808,.675,0,0,1],8646:[.1808,.675,0,0,1],8647:[.1808,.675,0,0,1],8648:[.19444,.69224,0,0,.83334],8649:[.1808,.675,0,0,1],8650:[.19444,.69224,0,0,.83334],8651:[.01354,.52239,0,0,1],8652:[.01354,.52239,0,0,1],8653:[-.13313,.36687,0,0,1],8654:[-.13313,.36687,0,0,1],8655:[-.13313,.36687,0,0,1],8666:[.13667,.63667,0,0,1],8667:[.13667,.63667,0,0,1],8669:[-.13313,.37788,0,0,1],8672:[-.064,.437,0,0,1.334],8674:[-.064,.437,0,0,1.334],8705:[0,.825,0,0,.5],8708:[0,.68889,0,0,.55556],8709:[.08167,.58167,0,0,.77778],8717:[0,.43056,0,0,.42917],8722:[-.03598,.46402,0,0,.5],8724:[.08198,.69224,0,0,.77778],8726:[.08167,.58167,0,0,.77778],8733:[0,.69224,0,0,.77778],8736:[0,.69224,0,0,.72222],8737:[0,.69224,0,0,.72222],8738:[.03517,.52239,0,0,.72222],8739:[.08167,.58167,0,0,.22222],8740:[.25142,.74111,0,0,.27778],8741:[.08167,.58167,0,0,.38889],8742:[.25142,.74111,0,0,.5],8756:[0,.69224,0,0,.66667],8757:[0,.69224,0,0,.66667],8764:[-.13313,.36687,0,0,.77778],8765:[-.13313,.37788,0,0,.77778],8769:[-.13313,.36687,0,0,.77778],8770:[-.03625,.46375,0,0,.77778],8774:[.30274,.79383,0,0,.77778],8776:[-.01688,.48312,0,0,.77778],8778:[.08167,.58167,0,0,.77778],8782:[.06062,.54986,0,0,.77778],8783:[.06062,.54986,0,0,.77778],8785:[.08198,.58198,0,0,.77778],8786:[.08198,.58198,0,0,.77778],8787:[.08198,.58198,0,0,.77778],8790:[0,.69224,0,0,.77778],8791:[.22958,.72958,0,0,.77778],8796:[.08198,.91667,0,0,.77778],8806:[.25583,.75583,0,0,.77778],8807:[.25583,.75583,0,0,.77778],8808:[.25142,.75726,0,0,.77778],8809:[.25142,.75726,0,0,.77778],8812:[.25583,.75583,0,0,.5],8814:[.20576,.70576,0,0,.77778],8815:[.20576,.70576,0,0,.77778],8816:[.30274,.79383,0,0,.77778],8817:[.30274,.79383,0,0,.77778],8818:[.22958,.72958,0,0,.77778],8819:[.22958,.72958,0,0,.77778],8822:[.1808,.675,0,0,.77778],8823:[.1808,.675,0,0,.77778],8828:[.13667,.63667,0,0,.77778],8829:[.13667,.63667,0,0,.77778],8830:[.22958,.72958,0,0,.77778],8831:[.22958,.72958,0,0,.77778],8832:[.20576,.70576,0,0,.77778],8833:[.20576,.70576,0,0,.77778],8840:[.30274,.79383,0,0,.77778],8841:[.30274,.79383,0,0,.77778],8842:[.13597,.63597,0,0,.77778],8843:[.13597,.63597,0,0,.77778],8847:[.03517,.54986,0,0,.77778],8848:[.03517,.54986,0,0,.77778],8858:[.08198,.58198,0,0,.77778],8859:[.08198,.58198,0,0,.77778],8861:[.08198,.58198,0,0,.77778],8862:[0,.675,0,0,.77778],8863:[0,.675,0,0,.77778],8864:[0,.675,0,0,.77778],8865:[0,.675,0,0,.77778],8872:[0,.69224,0,0,.61111],8873:[0,.69224,0,0,.72222],8874:[0,.69224,0,0,.88889],8876:[0,.68889,0,0,.61111],8877:[0,.68889,0,0,.61111],8878:[0,.68889,0,0,.72222],8879:[0,.68889,0,0,.72222],8882:[.03517,.54986,0,0,.77778],8883:[.03517,.54986,0,0,.77778],8884:[.13667,.63667,0,0,.77778],8885:[.13667,.63667,0,0,.77778],8888:[0,.54986,0,0,1.11111],8890:[.19444,.43056,0,0,.55556],8891:[.19444,.69224,0,0,.61111],8892:[.19444,.69224,0,0,.61111],8901:[0,.54986,0,0,.27778],8903:[.08167,.58167,0,0,.77778],8905:[.08167,.58167,0,0,.77778],8906:[.08167,.58167,0,0,.77778],8907:[0,.69224,0,0,.77778],8908:[0,.69224,0,0,.77778],8909:[-.03598,.46402,0,0,.77778],8910:[0,.54986,0,0,.76042],8911:[0,.54986,0,0,.76042],8912:[.03517,.54986,0,0,.77778],8913:[.03517,.54986,0,0,.77778],8914:[0,.54986,0,0,.66667],8915:[0,.54986,0,0,.66667],8916:[0,.69224,0,0,.66667],8918:[.0391,.5391,0,0,.77778],8919:[.0391,.5391,0,0,.77778],8920:[.03517,.54986,0,0,1.33334],8921:[.03517,.54986,0,0,1.33334],8922:[.38569,.88569,0,0,.77778],8923:[.38569,.88569,0,0,.77778],8926:[.13667,.63667,0,0,.77778],8927:[.13667,.63667,0,0,.77778],8928:[.30274,.79383,0,0,.77778],8929:[.30274,.79383,0,0,.77778],8934:[.23222,.74111,0,0,.77778],8935:[.23222,.74111,0,0,.77778],8936:[.23222,.74111,0,0,.77778],8937:[.23222,.74111,0,0,.77778],8938:[.20576,.70576,0,0,.77778],8939:[.20576,.70576,0,0,.77778],8940:[.30274,.79383,0,0,.77778],8941:[.30274,.79383,0,0,.77778],8994:[.19444,.69224,0,0,.77778],8995:[.19444,.69224,0,0,.77778],9416:[.15559,.69224,0,0,.90222],9484:[0,.69224,0,0,.5],9488:[0,.69224,0,0,.5],9492:[0,.37788,0,0,.5],9496:[0,.37788,0,0,.5],9585:[.19444,.68889,0,0,.88889],9586:[.19444,.74111,0,0,.88889],9632:[0,.675,0,0,.77778],9633:[0,.675,0,0,.77778],9650:[0,.54986,0,0,.72222],9651:[0,.54986,0,0,.72222],9654:[.03517,.54986,0,0,.77778],9660:[0,.54986,0,0,.72222],9661:[0,.54986,0,0,.72222],9664:[.03517,.54986,0,0,.77778],9674:[.11111,.69224,0,0,.66667],9733:[.19444,.69224,0,0,.94445],10003:[0,.69224,0,0,.83334],10016:[0,.69224,0,0,.83334],10731:[.11111,.69224,0,0,.66667],10846:[.19444,.75583,0,0,.61111],10877:[.13667,.63667,0,0,.77778],10878:[.13667,.63667,0,0,.77778],10885:[.25583,.75583,0,0,.77778],10886:[.25583,.75583,0,0,.77778],10887:[.13597,.63597,0,0,.77778],10888:[.13597,.63597,0,0,.77778],10889:[.26167,.75726,0,0,.77778],10890:[.26167,.75726,0,0,.77778],10891:[.48256,.98256,0,0,.77778],10892:[.48256,.98256,0,0,.77778],10901:[.13667,.63667,0,0,.77778],10902:[.13667,.63667,0,0,.77778],10933:[.25142,.75726,0,0,.77778],10934:[.25142,.75726,0,0,.77778],10935:[.26167,.75726,0,0,.77778],10936:[.26167,.75726,0,0,.77778],10937:[.26167,.75726,0,0,.77778],10938:[.26167,.75726,0,0,.77778],10949:[.25583,.75583,0,0,.77778],10950:[.25583,.75583,0,0,.77778],10955:[.28481,.79383,0,0,.77778],10956:[.28481,.79383,0,0,.77778],57350:[.08167,.58167,0,0,.22222],57351:[.08167,.58167,0,0,.38889],57352:[.08167,.58167,0,0,.77778],57353:[0,.43056,.04028,0,.66667],57356:[.25142,.75726,0,0,.77778],57357:[.25142,.75726,0,0,.77778],57358:[.41951,.91951,0,0,.77778],57359:[.30274,.79383,0,0,.77778],57360:[.30274,.79383,0,0,.77778],57361:[.41951,.91951,0,0,.77778],57366:[.25142,.75726,0,0,.77778],57367:[.25142,.75726,0,0,.77778],57368:[.25142,.75726,0,0,.77778],57369:[.25142,.75726,0,0,.77778],57370:[.13597,.63597,0,0,.77778],57371:[.13597,.63597,0,0,.77778]},"Caligraphic-Regular":{32:[0,0,0,0,.25],65:[0,.68333,0,.19445,.79847],66:[0,.68333,.03041,.13889,.65681],67:[0,.68333,.05834,.13889,.52653],68:[0,.68333,.02778,.08334,.77139],69:[0,.68333,.08944,.11111,.52778],70:[0,.68333,.09931,.11111,.71875],71:[.09722,.68333,.0593,.11111,.59487],72:[0,.68333,.00965,.11111,.84452],73:[0,.68333,.07382,0,.54452],74:[.09722,.68333,.18472,.16667,.67778],75:[0,.68333,.01445,.05556,.76195],76:[0,.68333,0,.13889,.68972],77:[0,.68333,0,.13889,1.2009],78:[0,.68333,.14736,.08334,.82049],79:[0,.68333,.02778,.11111,.79611],80:[0,.68333,.08222,.08334,.69556],81:[.09722,.68333,0,.11111,.81667],82:[0,.68333,0,.08334,.8475],83:[0,.68333,.075,.13889,.60556],84:[0,.68333,.25417,0,.54464],85:[0,.68333,.09931,.08334,.62583],86:[0,.68333,.08222,0,.61278],87:[0,.68333,.08222,.08334,.98778],88:[0,.68333,.14643,.13889,.7133],89:[.09722,.68333,.08222,.08334,.66834],90:[0,.68333,.07944,.13889,.72473],160:[0,0,0,0,.25]},"Fraktur-Regular":{32:[0,0,0,0,.25],33:[0,.69141,0,0,.29574],34:[0,.69141,0,0,.21471],38:[0,.69141,0,0,.73786],39:[0,.69141,0,0,.21201],40:[.24982,.74947,0,0,.38865],41:[.24982,.74947,0,0,.38865],42:[0,.62119,0,0,.27764],43:[.08319,.58283,0,0,.75623],44:[0,.10803,0,0,.27764],45:[.08319,.58283,0,0,.75623],46:[0,.10803,0,0,.27764],47:[.24982,.74947,0,0,.50181],48:[0,.47534,0,0,.50181],49:[0,.47534,0,0,.50181],50:[0,.47534,0,0,.50181],51:[.18906,.47534,0,0,.50181],52:[.18906,.47534,0,0,.50181],53:[.18906,.47534,0,0,.50181],54:[0,.69141,0,0,.50181],55:[.18906,.47534,0,0,.50181],56:[0,.69141,0,0,.50181],57:[.18906,.47534,0,0,.50181],58:[0,.47534,0,0,.21606],59:[.12604,.47534,0,0,.21606],61:[-.13099,.36866,0,0,.75623],63:[0,.69141,0,0,.36245],65:[0,.69141,0,0,.7176],66:[0,.69141,0,0,.88397],67:[0,.69141,0,0,.61254],68:[0,.69141,0,0,.83158],69:[0,.69141,0,0,.66278],70:[.12604,.69141,0,0,.61119],71:[0,.69141,0,0,.78539],72:[.06302,.69141,0,0,.7203],73:[0,.69141,0,0,.55448],74:[.12604,.69141,0,0,.55231],75:[0,.69141,0,0,.66845],76:[0,.69141,0,0,.66602],77:[0,.69141,0,0,1.04953],78:[0,.69141,0,0,.83212],79:[0,.69141,0,0,.82699],80:[.18906,.69141,0,0,.82753],81:[.03781,.69141,0,0,.82699],82:[0,.69141,0,0,.82807],83:[0,.69141,0,0,.82861],84:[0,.69141,0,0,.66899],85:[0,.69141,0,0,.64576],86:[0,.69141,0,0,.83131],87:[0,.69141,0,0,1.04602],88:[0,.69141,0,0,.71922],89:[.18906,.69141,0,0,.83293],90:[.12604,.69141,0,0,.60201],91:[.24982,.74947,0,0,.27764],93:[.24982,.74947,0,0,.27764],94:[0,.69141,0,0,.49965],97:[0,.47534,0,0,.50046],98:[0,.69141,0,0,.51315],99:[0,.47534,0,0,.38946],100:[0,.62119,0,0,.49857],101:[0,.47534,0,0,.40053],102:[.18906,.69141,0,0,.32626],103:[.18906,.47534,0,0,.5037],104:[.18906,.69141,0,0,.52126],105:[0,.69141,0,0,.27899],106:[0,.69141,0,0,.28088],107:[0,.69141,0,0,.38946],108:[0,.69141,0,0,.27953],109:[0,.47534,0,0,.76676],110:[0,.47534,0,0,.52666],111:[0,.47534,0,0,.48885],112:[.18906,.52396,0,0,.50046],113:[.18906,.47534,0,0,.48912],114:[0,.47534,0,0,.38919],115:[0,.47534,0,0,.44266],116:[0,.62119,0,0,.33301],117:[0,.47534,0,0,.5172],118:[0,.52396,0,0,.5118],119:[0,.52396,0,0,.77351],120:[.18906,.47534,0,0,.38865],121:[.18906,.47534,0,0,.49884],122:[.18906,.47534,0,0,.39054],160:[0,0,0,0,.25],8216:[0,.69141,0,0,.21471],8217:[0,.69141,0,0,.21471],58112:[0,.62119,0,0,.49749],58113:[0,.62119,0,0,.4983],58114:[.18906,.69141,0,0,.33328],58115:[.18906,.69141,0,0,.32923],58116:[.18906,.47534,0,0,.50343],58117:[0,.69141,0,0,.33301],58118:[0,.62119,0,0,.33409],58119:[0,.47534,0,0,.50073]},"Main-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.35],34:[0,.69444,0,0,.60278],35:[.19444,.69444,0,0,.95833],36:[.05556,.75,0,0,.575],37:[.05556,.75,0,0,.95833],38:[0,.69444,0,0,.89444],39:[0,.69444,0,0,.31944],40:[.25,.75,0,0,.44722],41:[.25,.75,0,0,.44722],42:[0,.75,0,0,.575],43:[.13333,.63333,0,0,.89444],44:[.19444,.15556,0,0,.31944],45:[0,.44444,0,0,.38333],46:[0,.15556,0,0,.31944],47:[.25,.75,0,0,.575],48:[0,.64444,0,0,.575],49:[0,.64444,0,0,.575],50:[0,.64444,0,0,.575],51:[0,.64444,0,0,.575],52:[0,.64444,0,0,.575],53:[0,.64444,0,0,.575],54:[0,.64444,0,0,.575],55:[0,.64444,0,0,.575],56:[0,.64444,0,0,.575],57:[0,.64444,0,0,.575],58:[0,.44444,0,0,.31944],59:[.19444,.44444,0,0,.31944],60:[.08556,.58556,0,0,.89444],61:[-.10889,.39111,0,0,.89444],62:[.08556,.58556,0,0,.89444],63:[0,.69444,0,0,.54305],64:[0,.69444,0,0,.89444],65:[0,.68611,0,0,.86944],66:[0,.68611,0,0,.81805],67:[0,.68611,0,0,.83055],68:[0,.68611,0,0,.88194],69:[0,.68611,0,0,.75555],70:[0,.68611,0,0,.72361],71:[0,.68611,0,0,.90416],72:[0,.68611,0,0,.9],73:[0,.68611,0,0,.43611],74:[0,.68611,0,0,.59444],75:[0,.68611,0,0,.90138],76:[0,.68611,0,0,.69166],77:[0,.68611,0,0,1.09166],78:[0,.68611,0,0,.9],79:[0,.68611,0,0,.86388],80:[0,.68611,0,0,.78611],81:[.19444,.68611,0,0,.86388],82:[0,.68611,0,0,.8625],83:[0,.68611,0,0,.63889],84:[0,.68611,0,0,.8],85:[0,.68611,0,0,.88472],86:[0,.68611,.01597,0,.86944],87:[0,.68611,.01597,0,1.18888],88:[0,.68611,0,0,.86944],89:[0,.68611,.02875,0,.86944],90:[0,.68611,0,0,.70277],91:[.25,.75,0,0,.31944],92:[.25,.75,0,0,.575],93:[.25,.75,0,0,.31944],94:[0,.69444,0,0,.575],95:[.31,.13444,.03194,0,.575],97:[0,.44444,0,0,.55902],98:[0,.69444,0,0,.63889],99:[0,.44444,0,0,.51111],100:[0,.69444,0,0,.63889],101:[0,.44444,0,0,.52708],102:[0,.69444,.10903,0,.35139],103:[.19444,.44444,.01597,0,.575],104:[0,.69444,0,0,.63889],105:[0,.69444,0,0,.31944],106:[.19444,.69444,0,0,.35139],107:[0,.69444,0,0,.60694],108:[0,.69444,0,0,.31944],109:[0,.44444,0,0,.95833],110:[0,.44444,0,0,.63889],111:[0,.44444,0,0,.575],112:[.19444,.44444,0,0,.63889],113:[.19444,.44444,0,0,.60694],114:[0,.44444,0,0,.47361],115:[0,.44444,0,0,.45361],116:[0,.63492,0,0,.44722],117:[0,.44444,0,0,.63889],118:[0,.44444,.01597,0,.60694],119:[0,.44444,.01597,0,.83055],120:[0,.44444,0,0,.60694],121:[.19444,.44444,.01597,0,.60694],122:[0,.44444,0,0,.51111],123:[.25,.75,0,0,.575],124:[.25,.75,0,0,.31944],125:[.25,.75,0,0,.575],126:[.35,.34444,0,0,.575],160:[0,0,0,0,.25],163:[0,.69444,0,0,.86853],168:[0,.69444,0,0,.575],172:[0,.44444,0,0,.76666],176:[0,.69444,0,0,.86944],177:[.13333,.63333,0,0,.89444],184:[.17014,0,0,0,.51111],198:[0,.68611,0,0,1.04166],215:[.13333,.63333,0,0,.89444],216:[.04861,.73472,0,0,.89444],223:[0,.69444,0,0,.59722],230:[0,.44444,0,0,.83055],247:[.13333,.63333,0,0,.89444],248:[.09722,.54167,0,0,.575],305:[0,.44444,0,0,.31944],338:[0,.68611,0,0,1.16944],339:[0,.44444,0,0,.89444],567:[.19444,.44444,0,0,.35139],710:[0,.69444,0,0,.575],711:[0,.63194,0,0,.575],713:[0,.59611,0,0,.575],714:[0,.69444,0,0,.575],715:[0,.69444,0,0,.575],728:[0,.69444,0,0,.575],729:[0,.69444,0,0,.31944],730:[0,.69444,0,0,.86944],732:[0,.69444,0,0,.575],733:[0,.69444,0,0,.575],915:[0,.68611,0,0,.69166],916:[0,.68611,0,0,.95833],920:[0,.68611,0,0,.89444],923:[0,.68611,0,0,.80555],926:[0,.68611,0,0,.76666],928:[0,.68611,0,0,.9],931:[0,.68611,0,0,.83055],933:[0,.68611,0,0,.89444],934:[0,.68611,0,0,.83055],936:[0,.68611,0,0,.89444],937:[0,.68611,0,0,.83055],8211:[0,.44444,.03194,0,.575],8212:[0,.44444,.03194,0,1.14999],8216:[0,.69444,0,0,.31944],8217:[0,.69444,0,0,.31944],8220:[0,.69444,0,0,.60278],8221:[0,.69444,0,0,.60278],8224:[.19444,.69444,0,0,.51111],8225:[.19444,.69444,0,0,.51111],8242:[0,.55556,0,0,.34444],8407:[0,.72444,.15486,0,.575],8463:[0,.69444,0,0,.66759],8465:[0,.69444,0,0,.83055],8467:[0,.69444,0,0,.47361],8472:[.19444,.44444,0,0,.74027],8476:[0,.69444,0,0,.83055],8501:[0,.69444,0,0,.70277],8592:[-.10889,.39111,0,0,1.14999],8593:[.19444,.69444,0,0,.575],8594:[-.10889,.39111,0,0,1.14999],8595:[.19444,.69444,0,0,.575],8596:[-.10889,.39111,0,0,1.14999],8597:[.25,.75,0,0,.575],8598:[.19444,.69444,0,0,1.14999],8599:[.19444,.69444,0,0,1.14999],8600:[.19444,.69444,0,0,1.14999],8601:[.19444,.69444,0,0,1.14999],8636:[-.10889,.39111,0,0,1.14999],8637:[-.10889,.39111,0,0,1.14999],8640:[-.10889,.39111,0,0,1.14999],8641:[-.10889,.39111,0,0,1.14999],8656:[-.10889,.39111,0,0,1.14999],8657:[.19444,.69444,0,0,.70277],8658:[-.10889,.39111,0,0,1.14999],8659:[.19444,.69444,0,0,.70277],8660:[-.10889,.39111,0,0,1.14999],8661:[.25,.75,0,0,.70277],8704:[0,.69444,0,0,.63889],8706:[0,.69444,.06389,0,.62847],8707:[0,.69444,0,0,.63889],8709:[.05556,.75,0,0,.575],8711:[0,.68611,0,0,.95833],8712:[.08556,.58556,0,0,.76666],8715:[.08556,.58556,0,0,.76666],8722:[.13333,.63333,0,0,.89444],8723:[.13333,.63333,0,0,.89444],8725:[.25,.75,0,0,.575],8726:[.25,.75,0,0,.575],8727:[-.02778,.47222,0,0,.575],8728:[-.02639,.47361,0,0,.575],8729:[-.02639,.47361,0,0,.575],8730:[.18,.82,0,0,.95833],8733:[0,.44444,0,0,.89444],8734:[0,.44444,0,0,1.14999],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.31944],8741:[.25,.75,0,0,.575],8743:[0,.55556,0,0,.76666],8744:[0,.55556,0,0,.76666],8745:[0,.55556,0,0,.76666],8746:[0,.55556,0,0,.76666],8747:[.19444,.69444,.12778,0,.56875],8764:[-.10889,.39111,0,0,.89444],8768:[.19444,.69444,0,0,.31944],8771:[.00222,.50222,0,0,.89444],8773:[.027,.638,0,0,.894],8776:[.02444,.52444,0,0,.89444],8781:[.00222,.50222,0,0,.89444],8801:[.00222,.50222,0,0,.89444],8804:[.19667,.69667,0,0,.89444],8805:[.19667,.69667,0,0,.89444],8810:[.08556,.58556,0,0,1.14999],8811:[.08556,.58556,0,0,1.14999],8826:[.08556,.58556,0,0,.89444],8827:[.08556,.58556,0,0,.89444],8834:[.08556,.58556,0,0,.89444],8835:[.08556,.58556,0,0,.89444],8838:[.19667,.69667,0,0,.89444],8839:[.19667,.69667,0,0,.89444],8846:[0,.55556,0,0,.76666],8849:[.19667,.69667,0,0,.89444],8850:[.19667,.69667,0,0,.89444],8851:[0,.55556,0,0,.76666],8852:[0,.55556,0,0,.76666],8853:[.13333,.63333,0,0,.89444],8854:[.13333,.63333,0,0,.89444],8855:[.13333,.63333,0,0,.89444],8856:[.13333,.63333,0,0,.89444],8857:[.13333,.63333,0,0,.89444],8866:[0,.69444,0,0,.70277],8867:[0,.69444,0,0,.70277],8868:[0,.69444,0,0,.89444],8869:[0,.69444,0,0,.89444],8900:[-.02639,.47361,0,0,.575],8901:[-.02639,.47361,0,0,.31944],8902:[-.02778,.47222,0,0,.575],8968:[.25,.75,0,0,.51111],8969:[.25,.75,0,0,.51111],8970:[.25,.75,0,0,.51111],8971:[.25,.75,0,0,.51111],8994:[-.13889,.36111,0,0,1.14999],8995:[-.13889,.36111,0,0,1.14999],9651:[.19444,.69444,0,0,1.02222],9657:[-.02778,.47222,0,0,.575],9661:[.19444,.69444,0,0,1.02222],9667:[-.02778,.47222,0,0,.575],9711:[.19444,.69444,0,0,1.14999],9824:[.12963,.69444,0,0,.89444],9825:[.12963,.69444,0,0,.89444],9826:[.12963,.69444,0,0,.89444],9827:[.12963,.69444,0,0,.89444],9837:[0,.75,0,0,.44722],9838:[.19444,.69444,0,0,.44722],9839:[.19444,.69444,0,0,.44722],10216:[.25,.75,0,0,.44722],10217:[.25,.75,0,0,.44722],10815:[0,.68611,0,0,.9],10927:[.19667,.69667,0,0,.89444],10928:[.19667,.69667,0,0,.89444],57376:[.19444,.69444,0,0,0]},"Main-BoldItalic":{32:[0,0,0,0,.25],33:[0,.69444,.11417,0,.38611],34:[0,.69444,.07939,0,.62055],35:[.19444,.69444,.06833,0,.94444],37:[.05556,.75,.12861,0,.94444],38:[0,.69444,.08528,0,.88555],39:[0,.69444,.12945,0,.35555],40:[.25,.75,.15806,0,.47333],41:[.25,.75,.03306,0,.47333],42:[0,.75,.14333,0,.59111],43:[.10333,.60333,.03306,0,.88555],44:[.19444,.14722,0,0,.35555],45:[0,.44444,.02611,0,.41444],46:[0,.14722,0,0,.35555],47:[.25,.75,.15806,0,.59111],48:[0,.64444,.13167,0,.59111],49:[0,.64444,.13167,0,.59111],50:[0,.64444,.13167,0,.59111],51:[0,.64444,.13167,0,.59111],52:[.19444,.64444,.13167,0,.59111],53:[0,.64444,.13167,0,.59111],54:[0,.64444,.13167,0,.59111],55:[.19444,.64444,.13167,0,.59111],56:[0,.64444,.13167,0,.59111],57:[0,.64444,.13167,0,.59111],58:[0,.44444,.06695,0,.35555],59:[.19444,.44444,.06695,0,.35555],61:[-.10889,.39111,.06833,0,.88555],63:[0,.69444,.11472,0,.59111],64:[0,.69444,.09208,0,.88555],65:[0,.68611,0,0,.86555],66:[0,.68611,.0992,0,.81666],67:[0,.68611,.14208,0,.82666],68:[0,.68611,.09062,0,.87555],69:[0,.68611,.11431,0,.75666],70:[0,.68611,.12903,0,.72722],71:[0,.68611,.07347,0,.89527],72:[0,.68611,.17208,0,.8961],73:[0,.68611,.15681,0,.47166],74:[0,.68611,.145,0,.61055],75:[0,.68611,.14208,0,.89499],76:[0,.68611,0,0,.69777],77:[0,.68611,.17208,0,1.07277],78:[0,.68611,.17208,0,.8961],79:[0,.68611,.09062,0,.85499],80:[0,.68611,.0992,0,.78721],81:[.19444,.68611,.09062,0,.85499],82:[0,.68611,.02559,0,.85944],83:[0,.68611,.11264,0,.64999],84:[0,.68611,.12903,0,.7961],85:[0,.68611,.17208,0,.88083],86:[0,.68611,.18625,0,.86555],87:[0,.68611,.18625,0,1.15999],88:[0,.68611,.15681,0,.86555],89:[0,.68611,.19803,0,.86555],90:[0,.68611,.14208,0,.70888],91:[.25,.75,.1875,0,.35611],93:[.25,.75,.09972,0,.35611],94:[0,.69444,.06709,0,.59111],95:[.31,.13444,.09811,0,.59111],97:[0,.44444,.09426,0,.59111],98:[0,.69444,.07861,0,.53222],99:[0,.44444,.05222,0,.53222],100:[0,.69444,.10861,0,.59111],101:[0,.44444,.085,0,.53222],102:[.19444,.69444,.21778,0,.4],103:[.19444,.44444,.105,0,.53222],104:[0,.69444,.09426,0,.59111],105:[0,.69326,.11387,0,.35555],106:[.19444,.69326,.1672,0,.35555],107:[0,.69444,.11111,0,.53222],108:[0,.69444,.10861,0,.29666],109:[0,.44444,.09426,0,.94444],110:[0,.44444,.09426,0,.64999],111:[0,.44444,.07861,0,.59111],112:[.19444,.44444,.07861,0,.59111],113:[.19444,.44444,.105,0,.53222],114:[0,.44444,.11111,0,.50167],115:[0,.44444,.08167,0,.48694],116:[0,.63492,.09639,0,.385],117:[0,.44444,.09426,0,.62055],118:[0,.44444,.11111,0,.53222],119:[0,.44444,.11111,0,.76777],120:[0,.44444,.12583,0,.56055],121:[.19444,.44444,.105,0,.56166],122:[0,.44444,.13889,0,.49055],126:[.35,.34444,.11472,0,.59111],160:[0,0,0,0,.25],168:[0,.69444,.11473,0,.59111],176:[0,.69444,0,0,.94888],184:[.17014,0,0,0,.53222],198:[0,.68611,.11431,0,1.02277],216:[.04861,.73472,.09062,0,.88555],223:[.19444,.69444,.09736,0,.665],230:[0,.44444,.085,0,.82666],248:[.09722,.54167,.09458,0,.59111],305:[0,.44444,.09426,0,.35555],338:[0,.68611,.11431,0,1.14054],339:[0,.44444,.085,0,.82666],567:[.19444,.44444,.04611,0,.385],710:[0,.69444,.06709,0,.59111],711:[0,.63194,.08271,0,.59111],713:[0,.59444,.10444,0,.59111],714:[0,.69444,.08528,0,.59111],715:[0,.69444,0,0,.59111],728:[0,.69444,.10333,0,.59111],729:[0,.69444,.12945,0,.35555],730:[0,.69444,0,0,.94888],732:[0,.69444,.11472,0,.59111],733:[0,.69444,.11472,0,.59111],915:[0,.68611,.12903,0,.69777],916:[0,.68611,0,0,.94444],920:[0,.68611,.09062,0,.88555],923:[0,.68611,0,0,.80666],926:[0,.68611,.15092,0,.76777],928:[0,.68611,.17208,0,.8961],931:[0,.68611,.11431,0,.82666],933:[0,.68611,.10778,0,.88555],934:[0,.68611,.05632,0,.82666],936:[0,.68611,.10778,0,.88555],937:[0,.68611,.0992,0,.82666],8211:[0,.44444,.09811,0,.59111],8212:[0,.44444,.09811,0,1.18221],8216:[0,.69444,.12945,0,.35555],8217:[0,.69444,.12945,0,.35555],8220:[0,.69444,.16772,0,.62055],8221:[0,.69444,.07939,0,.62055]},"Main-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.12417,0,.30667],34:[0,.69444,.06961,0,.51444],35:[.19444,.69444,.06616,0,.81777],37:[.05556,.75,.13639,0,.81777],38:[0,.69444,.09694,0,.76666],39:[0,.69444,.12417,0,.30667],40:[.25,.75,.16194,0,.40889],41:[.25,.75,.03694,0,.40889],42:[0,.75,.14917,0,.51111],43:[.05667,.56167,.03694,0,.76666],44:[.19444,.10556,0,0,.30667],45:[0,.43056,.02826,0,.35778],46:[0,.10556,0,0,.30667],47:[.25,.75,.16194,0,.51111],48:[0,.64444,.13556,0,.51111],49:[0,.64444,.13556,0,.51111],50:[0,.64444,.13556,0,.51111],51:[0,.64444,.13556,0,.51111],52:[.19444,.64444,.13556,0,.51111],53:[0,.64444,.13556,0,.51111],54:[0,.64444,.13556,0,.51111],55:[.19444,.64444,.13556,0,.51111],56:[0,.64444,.13556,0,.51111],57:[0,.64444,.13556,0,.51111],58:[0,.43056,.0582,0,.30667],59:[.19444,.43056,.0582,0,.30667],61:[-.13313,.36687,.06616,0,.76666],63:[0,.69444,.1225,0,.51111],64:[0,.69444,.09597,0,.76666],65:[0,.68333,0,0,.74333],66:[0,.68333,.10257,0,.70389],67:[0,.68333,.14528,0,.71555],68:[0,.68333,.09403,0,.755],69:[0,.68333,.12028,0,.67833],70:[0,.68333,.13305,0,.65277],71:[0,.68333,.08722,0,.77361],72:[0,.68333,.16389,0,.74333],73:[0,.68333,.15806,0,.38555],74:[0,.68333,.14028,0,.525],75:[0,.68333,.14528,0,.76888],76:[0,.68333,0,0,.62722],77:[0,.68333,.16389,0,.89666],78:[0,.68333,.16389,0,.74333],79:[0,.68333,.09403,0,.76666],80:[0,.68333,.10257,0,.67833],81:[.19444,.68333,.09403,0,.76666],82:[0,.68333,.03868,0,.72944],83:[0,.68333,.11972,0,.56222],84:[0,.68333,.13305,0,.71555],85:[0,.68333,.16389,0,.74333],86:[0,.68333,.18361,0,.74333],87:[0,.68333,.18361,0,.99888],88:[0,.68333,.15806,0,.74333],89:[0,.68333,.19383,0,.74333],90:[0,.68333,.14528,0,.61333],91:[.25,.75,.1875,0,.30667],93:[.25,.75,.10528,0,.30667],94:[0,.69444,.06646,0,.51111],95:[.31,.12056,.09208,0,.51111],97:[0,.43056,.07671,0,.51111],98:[0,.69444,.06312,0,.46],99:[0,.43056,.05653,0,.46],100:[0,.69444,.10333,0,.51111],101:[0,.43056,.07514,0,.46],102:[.19444,.69444,.21194,0,.30667],103:[.19444,.43056,.08847,0,.46],104:[0,.69444,.07671,0,.51111],105:[0,.65536,.1019,0,.30667],106:[.19444,.65536,.14467,0,.30667],107:[0,.69444,.10764,0,.46],108:[0,.69444,.10333,0,.25555],109:[0,.43056,.07671,0,.81777],110:[0,.43056,.07671,0,.56222],111:[0,.43056,.06312,0,.51111],112:[.19444,.43056,.06312,0,.51111],113:[.19444,.43056,.08847,0,.46],114:[0,.43056,.10764,0,.42166],115:[0,.43056,.08208,0,.40889],116:[0,.61508,.09486,0,.33222],117:[0,.43056,.07671,0,.53666],118:[0,.43056,.10764,0,.46],119:[0,.43056,.10764,0,.66444],120:[0,.43056,.12042,0,.46389],121:[.19444,.43056,.08847,0,.48555],122:[0,.43056,.12292,0,.40889],126:[.35,.31786,.11585,0,.51111],160:[0,0,0,0,.25],168:[0,.66786,.10474,0,.51111],176:[0,.69444,0,0,.83129],184:[.17014,0,0,0,.46],198:[0,.68333,.12028,0,.88277],216:[.04861,.73194,.09403,0,.76666],223:[.19444,.69444,.10514,0,.53666],230:[0,.43056,.07514,0,.71555],248:[.09722,.52778,.09194,0,.51111],338:[0,.68333,.12028,0,.98499],339:[0,.43056,.07514,0,.71555],710:[0,.69444,.06646,0,.51111],711:[0,.62847,.08295,0,.51111],713:[0,.56167,.10333,0,.51111],714:[0,.69444,.09694,0,.51111],715:[0,.69444,0,0,.51111],728:[0,.69444,.10806,0,.51111],729:[0,.66786,.11752,0,.30667],730:[0,.69444,0,0,.83129],732:[0,.66786,.11585,0,.51111],733:[0,.69444,.1225,0,.51111],915:[0,.68333,.13305,0,.62722],916:[0,.68333,0,0,.81777],920:[0,.68333,.09403,0,.76666],923:[0,.68333,0,0,.69222],926:[0,.68333,.15294,0,.66444],928:[0,.68333,.16389,0,.74333],931:[0,.68333,.12028,0,.71555],933:[0,.68333,.11111,0,.76666],934:[0,.68333,.05986,0,.71555],936:[0,.68333,.11111,0,.76666],937:[0,.68333,.10257,0,.71555],8211:[0,.43056,.09208,0,.51111],8212:[0,.43056,.09208,0,1.02222],8216:[0,.69444,.12417,0,.30667],8217:[0,.69444,.12417,0,.30667],8220:[0,.69444,.1685,0,.51444],8221:[0,.69444,.06961,0,.51444],8463:[0,.68889,0,0,.54028]},"Main-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.27778],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.77778],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.19444,.10556,0,0,.27778],45:[0,.43056,0,0,.33333],46:[0,.10556,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.64444,0,0,.5],49:[0,.64444,0,0,.5],50:[0,.64444,0,0,.5],51:[0,.64444,0,0,.5],52:[0,.64444,0,0,.5],53:[0,.64444,0,0,.5],54:[0,.64444,0,0,.5],55:[0,.64444,0,0,.5],56:[0,.64444,0,0,.5],57:[0,.64444,0,0,.5],58:[0,.43056,0,0,.27778],59:[.19444,.43056,0,0,.27778],60:[.0391,.5391,0,0,.77778],61:[-.13313,.36687,0,0,.77778],62:[.0391,.5391,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.77778],65:[0,.68333,0,0,.75],66:[0,.68333,0,0,.70834],67:[0,.68333,0,0,.72222],68:[0,.68333,0,0,.76389],69:[0,.68333,0,0,.68056],70:[0,.68333,0,0,.65278],71:[0,.68333,0,0,.78472],72:[0,.68333,0,0,.75],73:[0,.68333,0,0,.36111],74:[0,.68333,0,0,.51389],75:[0,.68333,0,0,.77778],76:[0,.68333,0,0,.625],77:[0,.68333,0,0,.91667],78:[0,.68333,0,0,.75],79:[0,.68333,0,0,.77778],80:[0,.68333,0,0,.68056],81:[.19444,.68333,0,0,.77778],82:[0,.68333,0,0,.73611],83:[0,.68333,0,0,.55556],84:[0,.68333,0,0,.72222],85:[0,.68333,0,0,.75],86:[0,.68333,.01389,0,.75],87:[0,.68333,.01389,0,1.02778],88:[0,.68333,0,0,.75],89:[0,.68333,.025,0,.75],90:[0,.68333,0,0,.61111],91:[.25,.75,0,0,.27778],92:[.25,.75,0,0,.5],93:[.25,.75,0,0,.27778],94:[0,.69444,0,0,.5],95:[.31,.12056,.02778,0,.5],97:[0,.43056,0,0,.5],98:[0,.69444,0,0,.55556],99:[0,.43056,0,0,.44445],100:[0,.69444,0,0,.55556],101:[0,.43056,0,0,.44445],102:[0,.69444,.07778,0,.30556],103:[.19444,.43056,.01389,0,.5],104:[0,.69444,0,0,.55556],105:[0,.66786,0,0,.27778],106:[.19444,.66786,0,0,.30556],107:[0,.69444,0,0,.52778],108:[0,.69444,0,0,.27778],109:[0,.43056,0,0,.83334],110:[0,.43056,0,0,.55556],111:[0,.43056,0,0,.5],112:[.19444,.43056,0,0,.55556],113:[.19444,.43056,0,0,.52778],114:[0,.43056,0,0,.39167],115:[0,.43056,0,0,.39445],116:[0,.61508,0,0,.38889],117:[0,.43056,0,0,.55556],118:[0,.43056,.01389,0,.52778],119:[0,.43056,.01389,0,.72222],120:[0,.43056,0,0,.52778],121:[.19444,.43056,.01389,0,.52778],122:[0,.43056,0,0,.44445],123:[.25,.75,0,0,.5],124:[.25,.75,0,0,.27778],125:[.25,.75,0,0,.5],126:[.35,.31786,0,0,.5],160:[0,0,0,0,.25],163:[0,.69444,0,0,.76909],167:[.19444,.69444,0,0,.44445],168:[0,.66786,0,0,.5],172:[0,.43056,0,0,.66667],176:[0,.69444,0,0,.75],177:[.08333,.58333,0,0,.77778],182:[.19444,.69444,0,0,.61111],184:[.17014,0,0,0,.44445],198:[0,.68333,0,0,.90278],215:[.08333,.58333,0,0,.77778],216:[.04861,.73194,0,0,.77778],223:[0,.69444,0,0,.5],230:[0,.43056,0,0,.72222],247:[.08333,.58333,0,0,.77778],248:[.09722,.52778,0,0,.5],305:[0,.43056,0,0,.27778],338:[0,.68333,0,0,1.01389],339:[0,.43056,0,0,.77778],567:[.19444,.43056,0,0,.30556],710:[0,.69444,0,0,.5],711:[0,.62847,0,0,.5],713:[0,.56778,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.66786,0,0,.27778],730:[0,.69444,0,0,.75],732:[0,.66786,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.68333,0,0,.625],916:[0,.68333,0,0,.83334],920:[0,.68333,0,0,.77778],923:[0,.68333,0,0,.69445],926:[0,.68333,0,0,.66667],928:[0,.68333,0,0,.75],931:[0,.68333,0,0,.72222],933:[0,.68333,0,0,.77778],934:[0,.68333,0,0,.72222],936:[0,.68333,0,0,.77778],937:[0,.68333,0,0,.72222],8211:[0,.43056,.02778,0,.5],8212:[0,.43056,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5],8224:[.19444,.69444,0,0,.44445],8225:[.19444,.69444,0,0,.44445],8230:[0,.123,0,0,1.172],8242:[0,.55556,0,0,.275],8407:[0,.71444,.15382,0,.5],8463:[0,.68889,0,0,.54028],8465:[0,.69444,0,0,.72222],8467:[0,.69444,0,.11111,.41667],8472:[.19444,.43056,0,.11111,.63646],8476:[0,.69444,0,0,.72222],8501:[0,.69444,0,0,.61111],8592:[-.13313,.36687,0,0,1],8593:[.19444,.69444,0,0,.5],8594:[-.13313,.36687,0,0,1],8595:[.19444,.69444,0,0,.5],8596:[-.13313,.36687,0,0,1],8597:[.25,.75,0,0,.5],8598:[.19444,.69444,0,0,1],8599:[.19444,.69444,0,0,1],8600:[.19444,.69444,0,0,1],8601:[.19444,.69444,0,0,1],8614:[.011,.511,0,0,1],8617:[.011,.511,0,0,1.126],8618:[.011,.511,0,0,1.126],8636:[-.13313,.36687,0,0,1],8637:[-.13313,.36687,0,0,1],8640:[-.13313,.36687,0,0,1],8641:[-.13313,.36687,0,0,1],8652:[.011,.671,0,0,1],8656:[-.13313,.36687,0,0,1],8657:[.19444,.69444,0,0,.61111],8658:[-.13313,.36687,0,0,1],8659:[.19444,.69444,0,0,.61111],8660:[-.13313,.36687,0,0,1],8661:[.25,.75,0,0,.61111],8704:[0,.69444,0,0,.55556],8706:[0,.69444,.05556,.08334,.5309],8707:[0,.69444,0,0,.55556],8709:[.05556,.75,0,0,.5],8711:[0,.68333,0,0,.83334],8712:[.0391,.5391,0,0,.66667],8715:[.0391,.5391,0,0,.66667],8722:[.08333,.58333,0,0,.77778],8723:[.08333,.58333,0,0,.77778],8725:[.25,.75,0,0,.5],8726:[.25,.75,0,0,.5],8727:[-.03472,.46528,0,0,.5],8728:[-.05555,.44445,0,0,.5],8729:[-.05555,.44445,0,0,.5],8730:[.2,.8,0,0,.83334],8733:[0,.43056,0,0,.77778],8734:[0,.43056,0,0,1],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.27778],8741:[.25,.75,0,0,.5],8743:[0,.55556,0,0,.66667],8744:[0,.55556,0,0,.66667],8745:[0,.55556,0,0,.66667],8746:[0,.55556,0,0,.66667],8747:[.19444,.69444,.11111,0,.41667],8764:[-.13313,.36687,0,0,.77778],8768:[.19444,.69444,0,0,.27778],8771:[-.03625,.46375,0,0,.77778],8773:[-.022,.589,0,0,.778],8776:[-.01688,.48312,0,0,.77778],8781:[-.03625,.46375,0,0,.77778],8784:[-.133,.673,0,0,.778],8801:[-.03625,.46375,0,0,.77778],8804:[.13597,.63597,0,0,.77778],8805:[.13597,.63597,0,0,.77778],8810:[.0391,.5391,0,0,1],8811:[.0391,.5391,0,0,1],8826:[.0391,.5391,0,0,.77778],8827:[.0391,.5391,0,0,.77778],8834:[.0391,.5391,0,0,.77778],8835:[.0391,.5391,0,0,.77778],8838:[.13597,.63597,0,0,.77778],8839:[.13597,.63597,0,0,.77778],8846:[0,.55556,0,0,.66667],8849:[.13597,.63597,0,0,.77778],8850:[.13597,.63597,0,0,.77778],8851:[0,.55556,0,0,.66667],8852:[0,.55556,0,0,.66667],8853:[.08333,.58333,0,0,.77778],8854:[.08333,.58333,0,0,.77778],8855:[.08333,.58333,0,0,.77778],8856:[.08333,.58333,0,0,.77778],8857:[.08333,.58333,0,0,.77778],8866:[0,.69444,0,0,.61111],8867:[0,.69444,0,0,.61111],8868:[0,.69444,0,0,.77778],8869:[0,.69444,0,0,.77778],8872:[.249,.75,0,0,.867],8900:[-.05555,.44445,0,0,.5],8901:[-.05555,.44445,0,0,.27778],8902:[-.03472,.46528,0,0,.5],8904:[.005,.505,0,0,.9],8942:[.03,.903,0,0,.278],8943:[-.19,.313,0,0,1.172],8945:[-.1,.823,0,0,1.282],8968:[.25,.75,0,0,.44445],8969:[.25,.75,0,0,.44445],8970:[.25,.75,0,0,.44445],8971:[.25,.75,0,0,.44445],8994:[-.14236,.35764,0,0,1],8995:[-.14236,.35764,0,0,1],9136:[.244,.744,0,0,.412],9137:[.244,.745,0,0,.412],9651:[.19444,.69444,0,0,.88889],9657:[-.03472,.46528,0,0,.5],9661:[.19444,.69444,0,0,.88889],9667:[-.03472,.46528,0,0,.5],9711:[.19444,.69444,0,0,1],9824:[.12963,.69444,0,0,.77778],9825:[.12963,.69444,0,0,.77778],9826:[.12963,.69444,0,0,.77778],9827:[.12963,.69444,0,0,.77778],9837:[0,.75,0,0,.38889],9838:[.19444,.69444,0,0,.38889],9839:[.19444,.69444,0,0,.38889],10216:[.25,.75,0,0,.38889],10217:[.25,.75,0,0,.38889],10222:[.244,.744,0,0,.412],10223:[.244,.745,0,0,.412],10229:[.011,.511,0,0,1.609],10230:[.011,.511,0,0,1.638],10231:[.011,.511,0,0,1.859],10232:[.024,.525,0,0,1.609],10233:[.024,.525,0,0,1.638],10234:[.024,.525,0,0,1.858],10236:[.011,.511,0,0,1.638],10815:[0,.68333,0,0,.75],10927:[.13597,.63597,0,0,.77778],10928:[.13597,.63597,0,0,.77778],57376:[.19444,.69444,0,0,0]},"Math-BoldItalic":{32:[0,0,0,0,.25],48:[0,.44444,0,0,.575],49:[0,.44444,0,0,.575],50:[0,.44444,0,0,.575],51:[.19444,.44444,0,0,.575],52:[.19444,.44444,0,0,.575],53:[.19444,.44444,0,0,.575],54:[0,.64444,0,0,.575],55:[.19444,.44444,0,0,.575],56:[0,.64444,0,0,.575],57:[.19444,.44444,0,0,.575],65:[0,.68611,0,0,.86944],66:[0,.68611,.04835,0,.8664],67:[0,.68611,.06979,0,.81694],68:[0,.68611,.03194,0,.93812],69:[0,.68611,.05451,0,.81007],70:[0,.68611,.15972,0,.68889],71:[0,.68611,0,0,.88673],72:[0,.68611,.08229,0,.98229],73:[0,.68611,.07778,0,.51111],74:[0,.68611,.10069,0,.63125],75:[0,.68611,.06979,0,.97118],76:[0,.68611,0,0,.75555],77:[0,.68611,.11424,0,1.14201],78:[0,.68611,.11424,0,.95034],79:[0,.68611,.03194,0,.83666],80:[0,.68611,.15972,0,.72309],81:[.19444,.68611,0,0,.86861],82:[0,.68611,.00421,0,.87235],83:[0,.68611,.05382,0,.69271],84:[0,.68611,.15972,0,.63663],85:[0,.68611,.11424,0,.80027],86:[0,.68611,.25555,0,.67778],87:[0,.68611,.15972,0,1.09305],88:[0,.68611,.07778,0,.94722],89:[0,.68611,.25555,0,.67458],90:[0,.68611,.06979,0,.77257],97:[0,.44444,0,0,.63287],98:[0,.69444,0,0,.52083],99:[0,.44444,0,0,.51342],100:[0,.69444,0,0,.60972],101:[0,.44444,0,0,.55361],102:[.19444,.69444,.11042,0,.56806],103:[.19444,.44444,.03704,0,.5449],104:[0,.69444,0,0,.66759],105:[0,.69326,0,0,.4048],106:[.19444,.69326,.0622,0,.47083],107:[0,.69444,.01852,0,.6037],108:[0,.69444,.0088,0,.34815],109:[0,.44444,0,0,1.0324],110:[0,.44444,0,0,.71296],111:[0,.44444,0,0,.58472],112:[.19444,.44444,0,0,.60092],113:[.19444,.44444,.03704,0,.54213],114:[0,.44444,.03194,0,.5287],115:[0,.44444,0,0,.53125],116:[0,.63492,0,0,.41528],117:[0,.44444,0,0,.68102],118:[0,.44444,.03704,0,.56666],119:[0,.44444,.02778,0,.83148],120:[0,.44444,0,0,.65903],121:[.19444,.44444,.03704,0,.59028],122:[0,.44444,.04213,0,.55509],160:[0,0,0,0,.25],915:[0,.68611,.15972,0,.65694],916:[0,.68611,0,0,.95833],920:[0,.68611,.03194,0,.86722],923:[0,.68611,0,0,.80555],926:[0,.68611,.07458,0,.84125],928:[0,.68611,.08229,0,.98229],931:[0,.68611,.05451,0,.88507],933:[0,.68611,.15972,0,.67083],934:[0,.68611,0,0,.76666],936:[0,.68611,.11653,0,.71402],937:[0,.68611,.04835,0,.8789],945:[0,.44444,0,0,.76064],946:[.19444,.69444,.03403,0,.65972],947:[.19444,.44444,.06389,0,.59003],948:[0,.69444,.03819,0,.52222],949:[0,.44444,0,0,.52882],950:[.19444,.69444,.06215,0,.50833],951:[.19444,.44444,.03704,0,.6],952:[0,.69444,.03194,0,.5618],953:[0,.44444,0,0,.41204],954:[0,.44444,0,0,.66759],955:[0,.69444,0,0,.67083],956:[.19444,.44444,0,0,.70787],957:[0,.44444,.06898,0,.57685],958:[.19444,.69444,.03021,0,.50833],959:[0,.44444,0,0,.58472],960:[0,.44444,.03704,0,.68241],961:[.19444,.44444,0,0,.6118],962:[.09722,.44444,.07917,0,.42361],963:[0,.44444,.03704,0,.68588],964:[0,.44444,.13472,0,.52083],965:[0,.44444,.03704,0,.63055],966:[.19444,.44444,0,0,.74722],967:[.19444,.44444,0,0,.71805],968:[.19444,.69444,.03704,0,.75833],969:[0,.44444,.03704,0,.71782],977:[0,.69444,0,0,.69155],981:[.19444,.69444,0,0,.7125],982:[0,.44444,.03194,0,.975],1009:[.19444,.44444,0,0,.6118],1013:[0,.44444,0,0,.48333],57649:[0,.44444,0,0,.39352],57911:[.19444,.44444,0,0,.43889]},"Math-Italic":{32:[0,0,0,0,.25],48:[0,.43056,0,0,.5],49:[0,.43056,0,0,.5],50:[0,.43056,0,0,.5],51:[.19444,.43056,0,0,.5],52:[.19444,.43056,0,0,.5],53:[.19444,.43056,0,0,.5],54:[0,.64444,0,0,.5],55:[.19444,.43056,0,0,.5],56:[0,.64444,0,0,.5],57:[.19444,.43056,0,0,.5],65:[0,.68333,0,.13889,.75],66:[0,.68333,.05017,.08334,.75851],67:[0,.68333,.07153,.08334,.71472],68:[0,.68333,.02778,.05556,.82792],69:[0,.68333,.05764,.08334,.7382],70:[0,.68333,.13889,.08334,.64306],71:[0,.68333,0,.08334,.78625],72:[0,.68333,.08125,.05556,.83125],73:[0,.68333,.07847,.11111,.43958],74:[0,.68333,.09618,.16667,.55451],75:[0,.68333,.07153,.05556,.84931],76:[0,.68333,0,.02778,.68056],77:[0,.68333,.10903,.08334,.97014],78:[0,.68333,.10903,.08334,.80347],79:[0,.68333,.02778,.08334,.76278],80:[0,.68333,.13889,.08334,.64201],81:[.19444,.68333,0,.08334,.79056],82:[0,.68333,.00773,.08334,.75929],83:[0,.68333,.05764,.08334,.6132],84:[0,.68333,.13889,.08334,.58438],85:[0,.68333,.10903,.02778,.68278],86:[0,.68333,.22222,0,.58333],87:[0,.68333,.13889,0,.94445],88:[0,.68333,.07847,.08334,.82847],89:[0,.68333,.22222,0,.58056],90:[0,.68333,.07153,.08334,.68264],97:[0,.43056,0,0,.52859],98:[0,.69444,0,0,.42917],99:[0,.43056,0,.05556,.43276],100:[0,.69444,0,.16667,.52049],101:[0,.43056,0,.05556,.46563],102:[.19444,.69444,.10764,.16667,.48959],103:[.19444,.43056,.03588,.02778,.47697],104:[0,.69444,0,0,.57616],105:[0,.65952,0,0,.34451],106:[.19444,.65952,.05724,0,.41181],107:[0,.69444,.03148,0,.5206],108:[0,.69444,.01968,.08334,.29838],109:[0,.43056,0,0,.87801],110:[0,.43056,0,0,.60023],111:[0,.43056,0,.05556,.48472],112:[.19444,.43056,0,.08334,.50313],113:[.19444,.43056,.03588,.08334,.44641],114:[0,.43056,.02778,.05556,.45116],115:[0,.43056,0,.05556,.46875],116:[0,.61508,0,.08334,.36111],117:[0,.43056,0,.02778,.57246],118:[0,.43056,.03588,.02778,.48472],119:[0,.43056,.02691,.08334,.71592],120:[0,.43056,0,.02778,.57153],121:[.19444,.43056,.03588,.05556,.49028],122:[0,.43056,.04398,.05556,.46505],160:[0,0,0,0,.25],915:[0,.68333,.13889,.08334,.61528],916:[0,.68333,0,.16667,.83334],920:[0,.68333,.02778,.08334,.76278],923:[0,.68333,0,.16667,.69445],926:[0,.68333,.07569,.08334,.74236],928:[0,.68333,.08125,.05556,.83125],931:[0,.68333,.05764,.08334,.77986],933:[0,.68333,.13889,.05556,.58333],934:[0,.68333,0,.08334,.66667],936:[0,.68333,.11,.05556,.61222],937:[0,.68333,.05017,.08334,.7724],945:[0,.43056,.0037,.02778,.6397],946:[.19444,.69444,.05278,.08334,.56563],947:[.19444,.43056,.05556,0,.51773],948:[0,.69444,.03785,.05556,.44444],949:[0,.43056,0,.08334,.46632],950:[.19444,.69444,.07378,.08334,.4375],951:[.19444,.43056,.03588,.05556,.49653],952:[0,.69444,.02778,.08334,.46944],953:[0,.43056,0,.05556,.35394],954:[0,.43056,0,0,.57616],955:[0,.69444,0,0,.58334],956:[.19444,.43056,0,.02778,.60255],957:[0,.43056,.06366,.02778,.49398],958:[.19444,.69444,.04601,.11111,.4375],959:[0,.43056,0,.05556,.48472],960:[0,.43056,.03588,0,.57003],961:[.19444,.43056,0,.08334,.51702],962:[.09722,.43056,.07986,.08334,.36285],963:[0,.43056,.03588,0,.57141],964:[0,.43056,.1132,.02778,.43715],965:[0,.43056,.03588,.02778,.54028],966:[.19444,.43056,0,.08334,.65417],967:[.19444,.43056,0,.05556,.62569],968:[.19444,.69444,.03588,.11111,.65139],969:[0,.43056,.03588,0,.62245],977:[0,.69444,0,.08334,.59144],981:[.19444,.69444,0,.08334,.59583],982:[0,.43056,.02778,0,.82813],1009:[.19444,.43056,0,.08334,.51702],1013:[0,.43056,0,.05556,.4059],57649:[0,.43056,0,.02778,.32246],57911:[.19444,.43056,0,.08334,.38403]},"SansSerif-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.36667],34:[0,.69444,0,0,.55834],35:[.19444,.69444,0,0,.91667],36:[.05556,.75,0,0,.55],37:[.05556,.75,0,0,1.02912],38:[0,.69444,0,0,.83056],39:[0,.69444,0,0,.30556],40:[.25,.75,0,0,.42778],41:[.25,.75,0,0,.42778],42:[0,.75,0,0,.55],43:[.11667,.61667,0,0,.85556],44:[.10556,.13056,0,0,.30556],45:[0,.45833,0,0,.36667],46:[0,.13056,0,0,.30556],47:[.25,.75,0,0,.55],48:[0,.69444,0,0,.55],49:[0,.69444,0,0,.55],50:[0,.69444,0,0,.55],51:[0,.69444,0,0,.55],52:[0,.69444,0,0,.55],53:[0,.69444,0,0,.55],54:[0,.69444,0,0,.55],55:[0,.69444,0,0,.55],56:[0,.69444,0,0,.55],57:[0,.69444,0,0,.55],58:[0,.45833,0,0,.30556],59:[.10556,.45833,0,0,.30556],61:[-.09375,.40625,0,0,.85556],63:[0,.69444,0,0,.51945],64:[0,.69444,0,0,.73334],65:[0,.69444,0,0,.73334],66:[0,.69444,0,0,.73334],67:[0,.69444,0,0,.70278],68:[0,.69444,0,0,.79445],69:[0,.69444,0,0,.64167],70:[0,.69444,0,0,.61111],71:[0,.69444,0,0,.73334],72:[0,.69444,0,0,.79445],73:[0,.69444,0,0,.33056],74:[0,.69444,0,0,.51945],75:[0,.69444,0,0,.76389],76:[0,.69444,0,0,.58056],77:[0,.69444,0,0,.97778],78:[0,.69444,0,0,.79445],79:[0,.69444,0,0,.79445],80:[0,.69444,0,0,.70278],81:[.10556,.69444,0,0,.79445],82:[0,.69444,0,0,.70278],83:[0,.69444,0,0,.61111],84:[0,.69444,0,0,.73334],85:[0,.69444,0,0,.76389],86:[0,.69444,.01528,0,.73334],87:[0,.69444,.01528,0,1.03889],88:[0,.69444,0,0,.73334],89:[0,.69444,.0275,0,.73334],90:[0,.69444,0,0,.67223],91:[.25,.75,0,0,.34306],93:[.25,.75,0,0,.34306],94:[0,.69444,0,0,.55],95:[.35,.10833,.03056,0,.55],97:[0,.45833,0,0,.525],98:[0,.69444,0,0,.56111],99:[0,.45833,0,0,.48889],100:[0,.69444,0,0,.56111],101:[0,.45833,0,0,.51111],102:[0,.69444,.07639,0,.33611],103:[.19444,.45833,.01528,0,.55],104:[0,.69444,0,0,.56111],105:[0,.69444,0,0,.25556],106:[.19444,.69444,0,0,.28611],107:[0,.69444,0,0,.53056],108:[0,.69444,0,0,.25556],109:[0,.45833,0,0,.86667],110:[0,.45833,0,0,.56111],111:[0,.45833,0,0,.55],112:[.19444,.45833,0,0,.56111],113:[.19444,.45833,0,0,.56111],114:[0,.45833,.01528,0,.37222],115:[0,.45833,0,0,.42167],116:[0,.58929,0,0,.40417],117:[0,.45833,0,0,.56111],118:[0,.45833,.01528,0,.5],119:[0,.45833,.01528,0,.74445],120:[0,.45833,0,0,.5],121:[.19444,.45833,.01528,0,.5],122:[0,.45833,0,0,.47639],126:[.35,.34444,0,0,.55],160:[0,0,0,0,.25],168:[0,.69444,0,0,.55],176:[0,.69444,0,0,.73334],180:[0,.69444,0,0,.55],184:[.17014,0,0,0,.48889],305:[0,.45833,0,0,.25556],567:[.19444,.45833,0,0,.28611],710:[0,.69444,0,0,.55],711:[0,.63542,0,0,.55],713:[0,.63778,0,0,.55],728:[0,.69444,0,0,.55],729:[0,.69444,0,0,.30556],730:[0,.69444,0,0,.73334],732:[0,.69444,0,0,.55],733:[0,.69444,0,0,.55],915:[0,.69444,0,0,.58056],916:[0,.69444,0,0,.91667],920:[0,.69444,0,0,.85556],923:[0,.69444,0,0,.67223],926:[0,.69444,0,0,.73334],928:[0,.69444,0,0,.79445],931:[0,.69444,0,0,.79445],933:[0,.69444,0,0,.85556],934:[0,.69444,0,0,.79445],936:[0,.69444,0,0,.85556],937:[0,.69444,0,0,.79445],8211:[0,.45833,.03056,0,.55],8212:[0,.45833,.03056,0,1.10001],8216:[0,.69444,0,0,.30556],8217:[0,.69444,0,0,.30556],8220:[0,.69444,0,0,.55834],8221:[0,.69444,0,0,.55834]},"SansSerif-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.05733,0,.31945],34:[0,.69444,.00316,0,.5],35:[.19444,.69444,.05087,0,.83334],36:[.05556,.75,.11156,0,.5],37:[.05556,.75,.03126,0,.83334],38:[0,.69444,.03058,0,.75834],39:[0,.69444,.07816,0,.27778],40:[.25,.75,.13164,0,.38889],41:[.25,.75,.02536,0,.38889],42:[0,.75,.11775,0,.5],43:[.08333,.58333,.02536,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,.01946,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,.13164,0,.5],48:[0,.65556,.11156,0,.5],49:[0,.65556,.11156,0,.5],50:[0,.65556,.11156,0,.5],51:[0,.65556,.11156,0,.5],52:[0,.65556,.11156,0,.5],53:[0,.65556,.11156,0,.5],54:[0,.65556,.11156,0,.5],55:[0,.65556,.11156,0,.5],56:[0,.65556,.11156,0,.5],57:[0,.65556,.11156,0,.5],58:[0,.44444,.02502,0,.27778],59:[.125,.44444,.02502,0,.27778],61:[-.13,.37,.05087,0,.77778],63:[0,.69444,.11809,0,.47222],64:[0,.69444,.07555,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,.08293,0,.66667],67:[0,.69444,.11983,0,.63889],68:[0,.69444,.07555,0,.72223],69:[0,.69444,.11983,0,.59722],70:[0,.69444,.13372,0,.56945],71:[0,.69444,.11983,0,.66667],72:[0,.69444,.08094,0,.70834],73:[0,.69444,.13372,0,.27778],74:[0,.69444,.08094,0,.47222],75:[0,.69444,.11983,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,.08094,0,.875],78:[0,.69444,.08094,0,.70834],79:[0,.69444,.07555,0,.73611],80:[0,.69444,.08293,0,.63889],81:[.125,.69444,.07555,0,.73611],82:[0,.69444,.08293,0,.64584],83:[0,.69444,.09205,0,.55556],84:[0,.69444,.13372,0,.68056],85:[0,.69444,.08094,0,.6875],86:[0,.69444,.1615,0,.66667],87:[0,.69444,.1615,0,.94445],88:[0,.69444,.13372,0,.66667],89:[0,.69444,.17261,0,.66667],90:[0,.69444,.11983,0,.61111],91:[.25,.75,.15942,0,.28889],93:[.25,.75,.08719,0,.28889],94:[0,.69444,.0799,0,.5],95:[.35,.09444,.08616,0,.5],97:[0,.44444,.00981,0,.48056],98:[0,.69444,.03057,0,.51667],99:[0,.44444,.08336,0,.44445],100:[0,.69444,.09483,0,.51667],101:[0,.44444,.06778,0,.44445],102:[0,.69444,.21705,0,.30556],103:[.19444,.44444,.10836,0,.5],104:[0,.69444,.01778,0,.51667],105:[0,.67937,.09718,0,.23889],106:[.19444,.67937,.09162,0,.26667],107:[0,.69444,.08336,0,.48889],108:[0,.69444,.09483,0,.23889],109:[0,.44444,.01778,0,.79445],110:[0,.44444,.01778,0,.51667],111:[0,.44444,.06613,0,.5],112:[.19444,.44444,.0389,0,.51667],113:[.19444,.44444,.04169,0,.51667],114:[0,.44444,.10836,0,.34167],115:[0,.44444,.0778,0,.38333],116:[0,.57143,.07225,0,.36111],117:[0,.44444,.04169,0,.51667],118:[0,.44444,.10836,0,.46111],119:[0,.44444,.10836,0,.68334],120:[0,.44444,.09169,0,.46111],121:[.19444,.44444,.10836,0,.46111],122:[0,.44444,.08752,0,.43472],126:[.35,.32659,.08826,0,.5],160:[0,0,0,0,.25],168:[0,.67937,.06385,0,.5],176:[0,.69444,0,0,.73752],184:[.17014,0,0,0,.44445],305:[0,.44444,.04169,0,.23889],567:[.19444,.44444,.04169,0,.26667],710:[0,.69444,.0799,0,.5],711:[0,.63194,.08432,0,.5],713:[0,.60889,.08776,0,.5],714:[0,.69444,.09205,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,.09483,0,.5],729:[0,.67937,.07774,0,.27778],730:[0,.69444,0,0,.73752],732:[0,.67659,.08826,0,.5],733:[0,.69444,.09205,0,.5],915:[0,.69444,.13372,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,.07555,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,.12816,0,.66667],928:[0,.69444,.08094,0,.70834],931:[0,.69444,.11983,0,.72222],933:[0,.69444,.09031,0,.77778],934:[0,.69444,.04603,0,.72222],936:[0,.69444,.09031,0,.77778],937:[0,.69444,.08293,0,.72222],8211:[0,.44444,.08616,0,.5],8212:[0,.44444,.08616,0,1],8216:[0,.69444,.07816,0,.27778],8217:[0,.69444,.07816,0,.27778],8220:[0,.69444,.14205,0,.5],8221:[0,.69444,.00316,0,.5]},"SansSerif-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.31945],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.75834],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,0,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.65556,0,0,.5],49:[0,.65556,0,0,.5],50:[0,.65556,0,0,.5],51:[0,.65556,0,0,.5],52:[0,.65556,0,0,.5],53:[0,.65556,0,0,.5],54:[0,.65556,0,0,.5],55:[0,.65556,0,0,.5],56:[0,.65556,0,0,.5],57:[0,.65556,0,0,.5],58:[0,.44444,0,0,.27778],59:[.125,.44444,0,0,.27778],61:[-.13,.37,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,0,0,.66667],67:[0,.69444,0,0,.63889],68:[0,.69444,0,0,.72223],69:[0,.69444,0,0,.59722],70:[0,.69444,0,0,.56945],71:[0,.69444,0,0,.66667],72:[0,.69444,0,0,.70834],73:[0,.69444,0,0,.27778],74:[0,.69444,0,0,.47222],75:[0,.69444,0,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,0,0,.875],78:[0,.69444,0,0,.70834],79:[0,.69444,0,0,.73611],80:[0,.69444,0,0,.63889],81:[.125,.69444,0,0,.73611],82:[0,.69444,0,0,.64584],83:[0,.69444,0,0,.55556],84:[0,.69444,0,0,.68056],85:[0,.69444,0,0,.6875],86:[0,.69444,.01389,0,.66667],87:[0,.69444,.01389,0,.94445],88:[0,.69444,0,0,.66667],89:[0,.69444,.025,0,.66667],90:[0,.69444,0,0,.61111],91:[.25,.75,0,0,.28889],93:[.25,.75,0,0,.28889],94:[0,.69444,0,0,.5],95:[.35,.09444,.02778,0,.5],97:[0,.44444,0,0,.48056],98:[0,.69444,0,0,.51667],99:[0,.44444,0,0,.44445],100:[0,.69444,0,0,.51667],101:[0,.44444,0,0,.44445],102:[0,.69444,.06944,0,.30556],103:[.19444,.44444,.01389,0,.5],104:[0,.69444,0,0,.51667],105:[0,.67937,0,0,.23889],106:[.19444,.67937,0,0,.26667],107:[0,.69444,0,0,.48889],108:[0,.69444,0,0,.23889],109:[0,.44444,0,0,.79445],110:[0,.44444,0,0,.51667],111:[0,.44444,0,0,.5],112:[.19444,.44444,0,0,.51667],113:[.19444,.44444,0,0,.51667],114:[0,.44444,.01389,0,.34167],115:[0,.44444,0,0,.38333],116:[0,.57143,0,0,.36111],117:[0,.44444,0,0,.51667],118:[0,.44444,.01389,0,.46111],119:[0,.44444,.01389,0,.68334],120:[0,.44444,0,0,.46111],121:[.19444,.44444,.01389,0,.46111],122:[0,.44444,0,0,.43472],126:[.35,.32659,0,0,.5],160:[0,0,0,0,.25],168:[0,.67937,0,0,.5],176:[0,.69444,0,0,.66667],184:[.17014,0,0,0,.44445],305:[0,.44444,0,0,.23889],567:[.19444,.44444,0,0,.26667],710:[0,.69444,0,0,.5],711:[0,.63194,0,0,.5],713:[0,.60889,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.67937,0,0,.27778],730:[0,.69444,0,0,.66667],732:[0,.67659,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.69444,0,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,0,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,0,0,.66667],928:[0,.69444,0,0,.70834],931:[0,.69444,0,0,.72222],933:[0,.69444,0,0,.77778],934:[0,.69444,0,0,.72222],936:[0,.69444,0,0,.77778],937:[0,.69444,0,0,.72222],8211:[0,.44444,.02778,0,.5],8212:[0,.44444,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5]},"Script-Regular":{32:[0,0,0,0,.25],65:[0,.7,.22925,0,.80253],66:[0,.7,.04087,0,.90757],67:[0,.7,.1689,0,.66619],68:[0,.7,.09371,0,.77443],69:[0,.7,.18583,0,.56162],70:[0,.7,.13634,0,.89544],71:[0,.7,.17322,0,.60961],72:[0,.7,.29694,0,.96919],73:[0,.7,.19189,0,.80907],74:[.27778,.7,.19189,0,1.05159],75:[0,.7,.31259,0,.91364],76:[0,.7,.19189,0,.87373],77:[0,.7,.15981,0,1.08031],78:[0,.7,.3525,0,.9015],79:[0,.7,.08078,0,.73787],80:[0,.7,.08078,0,1.01262],81:[0,.7,.03305,0,.88282],82:[0,.7,.06259,0,.85],83:[0,.7,.19189,0,.86767],84:[0,.7,.29087,0,.74697],85:[0,.7,.25815,0,.79996],86:[0,.7,.27523,0,.62204],87:[0,.7,.27523,0,.80532],88:[0,.7,.26006,0,.94445],89:[0,.7,.2939,0,.70961],90:[0,.7,.24037,0,.8212],160:[0,0,0,0,.25]},"Size1-Regular":{32:[0,0,0,0,.25],40:[.35001,.85,0,0,.45834],41:[.35001,.85,0,0,.45834],47:[.35001,.85,0,0,.57778],91:[.35001,.85,0,0,.41667],92:[.35001,.85,0,0,.57778],93:[.35001,.85,0,0,.41667],123:[.35001,.85,0,0,.58334],125:[.35001,.85,0,0,.58334],160:[0,0,0,0,.25],710:[0,.72222,0,0,.55556],732:[0,.72222,0,0,.55556],770:[0,.72222,0,0,.55556],771:[0,.72222,0,0,.55556],8214:[-99e-5,.601,0,0,.77778],8593:[1e-5,.6,0,0,.66667],8595:[1e-5,.6,0,0,.66667],8657:[1e-5,.6,0,0,.77778],8659:[1e-5,.6,0,0,.77778],8719:[.25001,.75,0,0,.94445],8720:[.25001,.75,0,0,.94445],8721:[.25001,.75,0,0,1.05556],8730:[.35001,.85,0,0,1],8739:[-.00599,.606,0,0,.33333],8741:[-.00599,.606,0,0,.55556],8747:[.30612,.805,.19445,0,.47222],8748:[.306,.805,.19445,0,.47222],8749:[.306,.805,.19445,0,.47222],8750:[.30612,.805,.19445,0,.47222],8896:[.25001,.75,0,0,.83334],8897:[.25001,.75,0,0,.83334],8898:[.25001,.75,0,0,.83334],8899:[.25001,.75,0,0,.83334],8968:[.35001,.85,0,0,.47222],8969:[.35001,.85,0,0,.47222],8970:[.35001,.85,0,0,.47222],8971:[.35001,.85,0,0,.47222],9168:[-99e-5,.601,0,0,.66667],10216:[.35001,.85,0,0,.47222],10217:[.35001,.85,0,0,.47222],10752:[.25001,.75,0,0,1.11111],10753:[.25001,.75,0,0,1.11111],10754:[.25001,.75,0,0,1.11111],10756:[.25001,.75,0,0,.83334],10758:[.25001,.75,0,0,.83334]},"Size2-Regular":{32:[0,0,0,0,.25],40:[.65002,1.15,0,0,.59722],41:[.65002,1.15,0,0,.59722],47:[.65002,1.15,0,0,.81111],91:[.65002,1.15,0,0,.47222],92:[.65002,1.15,0,0,.81111],93:[.65002,1.15,0,0,.47222],123:[.65002,1.15,0,0,.66667],125:[.65002,1.15,0,0,.66667],160:[0,0,0,0,.25],710:[0,.75,0,0,1],732:[0,.75,0,0,1],770:[0,.75,0,0,1],771:[0,.75,0,0,1],8719:[.55001,1.05,0,0,1.27778],8720:[.55001,1.05,0,0,1.27778],8721:[.55001,1.05,0,0,1.44445],8730:[.65002,1.15,0,0,1],8747:[.86225,1.36,.44445,0,.55556],8748:[.862,1.36,.44445,0,.55556],8749:[.862,1.36,.44445,0,.55556],8750:[.86225,1.36,.44445,0,.55556],8896:[.55001,1.05,0,0,1.11111],8897:[.55001,1.05,0,0,1.11111],8898:[.55001,1.05,0,0,1.11111],8899:[.55001,1.05,0,0,1.11111],8968:[.65002,1.15,0,0,.52778],8969:[.65002,1.15,0,0,.52778],8970:[.65002,1.15,0,0,.52778],8971:[.65002,1.15,0,0,.52778],10216:[.65002,1.15,0,0,.61111],10217:[.65002,1.15,0,0,.61111],10752:[.55001,1.05,0,0,1.51112],10753:[.55001,1.05,0,0,1.51112],10754:[.55001,1.05,0,0,1.51112],10756:[.55001,1.05,0,0,1.11111],10758:[.55001,1.05,0,0,1.11111]},"Size3-Regular":{32:[0,0,0,0,.25],40:[.95003,1.45,0,0,.73611],41:[.95003,1.45,0,0,.73611],47:[.95003,1.45,0,0,1.04445],91:[.95003,1.45,0,0,.52778],92:[.95003,1.45,0,0,1.04445],93:[.95003,1.45,0,0,.52778],123:[.95003,1.45,0,0,.75],125:[.95003,1.45,0,0,.75],160:[0,0,0,0,.25],710:[0,.75,0,0,1.44445],732:[0,.75,0,0,1.44445],770:[0,.75,0,0,1.44445],771:[0,.75,0,0,1.44445],8730:[.95003,1.45,0,0,1],8968:[.95003,1.45,0,0,.58334],8969:[.95003,1.45,0,0,.58334],8970:[.95003,1.45,0,0,.58334],8971:[.95003,1.45,0,0,.58334],10216:[.95003,1.45,0,0,.75],10217:[.95003,1.45,0,0,.75]},"Size4-Regular":{32:[0,0,0,0,.25],40:[1.25003,1.75,0,0,.79167],41:[1.25003,1.75,0,0,.79167],47:[1.25003,1.75,0,0,1.27778],91:[1.25003,1.75,0,0,.58334],92:[1.25003,1.75,0,0,1.27778],93:[1.25003,1.75,0,0,.58334],123:[1.25003,1.75,0,0,.80556],125:[1.25003,1.75,0,0,.80556],160:[0,0,0,0,.25],710:[0,.825,0,0,1.8889],732:[0,.825,0,0,1.8889],770:[0,.825,0,0,1.8889],771:[0,.825,0,0,1.8889],8730:[1.25003,1.75,0,0,1],8968:[1.25003,1.75,0,0,.63889],8969:[1.25003,1.75,0,0,.63889],8970:[1.25003,1.75,0,0,.63889],8971:[1.25003,1.75,0,0,.63889],9115:[.64502,1.155,0,0,.875],9116:[1e-5,.6,0,0,.875],9117:[.64502,1.155,0,0,.875],9118:[.64502,1.155,0,0,.875],9119:[1e-5,.6,0,0,.875],9120:[.64502,1.155,0,0,.875],9121:[.64502,1.155,0,0,.66667],9122:[-99e-5,.601,0,0,.66667],9123:[.64502,1.155,0,0,.66667],9124:[.64502,1.155,0,0,.66667],9125:[-99e-5,.601,0,0,.66667],9126:[.64502,1.155,0,0,.66667],9127:[1e-5,.9,0,0,.88889],9128:[.65002,1.15,0,0,.88889],9129:[.90001,0,0,0,.88889],9130:[0,.3,0,0,.88889],9131:[1e-5,.9,0,0,.88889],9132:[.65002,1.15,0,0,.88889],9133:[.90001,0,0,0,.88889],9143:[.88502,.915,0,0,1.05556],10216:[1.25003,1.75,0,0,.80556],10217:[1.25003,1.75,0,0,.80556],57344:[-.00499,.605,0,0,1.05556],57345:[-.00499,.605,0,0,1.05556],57680:[0,.12,0,0,.45],57681:[0,.12,0,0,.45],57682:[0,.12,0,0,.45],57683:[0,.12,0,0,.45]},"Typewriter-Regular":{32:[0,0,0,0,.525],33:[0,.61111,0,0,.525],34:[0,.61111,0,0,.525],35:[0,.61111,0,0,.525],36:[.08333,.69444,0,0,.525],37:[.08333,.69444,0,0,.525],38:[0,.61111,0,0,.525],39:[0,.61111,0,0,.525],40:[.08333,.69444,0,0,.525],41:[.08333,.69444,0,0,.525],42:[0,.52083,0,0,.525],43:[-.08056,.53055,0,0,.525],44:[.13889,.125,0,0,.525],45:[-.08056,.53055,0,0,.525],46:[0,.125,0,0,.525],47:[.08333,.69444,0,0,.525],48:[0,.61111,0,0,.525],49:[0,.61111,0,0,.525],50:[0,.61111,0,0,.525],51:[0,.61111,0,0,.525],52:[0,.61111,0,0,.525],53:[0,.61111,0,0,.525],54:[0,.61111,0,0,.525],55:[0,.61111,0,0,.525],56:[0,.61111,0,0,.525],57:[0,.61111,0,0,.525],58:[0,.43056,0,0,.525],59:[.13889,.43056,0,0,.525],60:[-.05556,.55556,0,0,.525],61:[-.19549,.41562,0,0,.525],62:[-.05556,.55556,0,0,.525],63:[0,.61111,0,0,.525],64:[0,.61111,0,0,.525],65:[0,.61111,0,0,.525],66:[0,.61111,0,0,.525],67:[0,.61111,0,0,.525],68:[0,.61111,0,0,.525],69:[0,.61111,0,0,.525],70:[0,.61111,0,0,.525],71:[0,.61111,0,0,.525],72:[0,.61111,0,0,.525],73:[0,.61111,0,0,.525],74:[0,.61111,0,0,.525],75:[0,.61111,0,0,.525],76:[0,.61111,0,0,.525],77:[0,.61111,0,0,.525],78:[0,.61111,0,0,.525],79:[0,.61111,0,0,.525],80:[0,.61111,0,0,.525],81:[.13889,.61111,0,0,.525],82:[0,.61111,0,0,.525],83:[0,.61111,0,0,.525],84:[0,.61111,0,0,.525],85:[0,.61111,0,0,.525],86:[0,.61111,0,0,.525],87:[0,.61111,0,0,.525],88:[0,.61111,0,0,.525],89:[0,.61111,0,0,.525],90:[0,.61111,0,0,.525],91:[.08333,.69444,0,0,.525],92:[.08333,.69444,0,0,.525],93:[.08333,.69444,0,0,.525],94:[0,.61111,0,0,.525],95:[.09514,0,0,0,.525],96:[0,.61111,0,0,.525],97:[0,.43056,0,0,.525],98:[0,.61111,0,0,.525],99:[0,.43056,0,0,.525],100:[0,.61111,0,0,.525],101:[0,.43056,0,0,.525],102:[0,.61111,0,0,.525],103:[.22222,.43056,0,0,.525],104:[0,.61111,0,0,.525],105:[0,.61111,0,0,.525],106:[.22222,.61111,0,0,.525],107:[0,.61111,0,0,.525],108:[0,.61111,0,0,.525],109:[0,.43056,0,0,.525],110:[0,.43056,0,0,.525],111:[0,.43056,0,0,.525],112:[.22222,.43056,0,0,.525],113:[.22222,.43056,0,0,.525],114:[0,.43056,0,0,.525],115:[0,.43056,0,0,.525],116:[0,.55358,0,0,.525],117:[0,.43056,0,0,.525],118:[0,.43056,0,0,.525],119:[0,.43056,0,0,.525],120:[0,.43056,0,0,.525],121:[.22222,.43056,0,0,.525],122:[0,.43056,0,0,.525],123:[.08333,.69444,0,0,.525],124:[.08333,.69444,0,0,.525],125:[.08333,.69444,0,0,.525],126:[0,.61111,0,0,.525],127:[0,.61111,0,0,.525],160:[0,0,0,0,.525],176:[0,.61111,0,0,.525],184:[.19445,0,0,0,.525],305:[0,.43056,0,0,.525],567:[.22222,.43056,0,0,.525],711:[0,.56597,0,0,.525],713:[0,.56555,0,0,.525],714:[0,.61111,0,0,.525],715:[0,.61111,0,0,.525],728:[0,.61111,0,0,.525],730:[0,.61111,0,0,.525],770:[0,.61111,0,0,.525],771:[0,.61111,0,0,.525],776:[0,.61111,0,0,.525],915:[0,.61111,0,0,.525],916:[0,.61111,0,0,.525],920:[0,.61111,0,0,.525],923:[0,.61111,0,0,.525],926:[0,.61111,0,0,.525],928:[0,.61111,0,0,.525],931:[0,.61111,0,0,.525],933:[0,.61111,0,0,.525],934:[0,.61111,0,0,.525],936:[0,.61111,0,0,.525],937:[0,.61111,0,0,.525],8216:[0,.61111,0,0,.525],8217:[0,.61111,0,0,.525],8242:[0,.61111,0,0,.525],9251:[.11111,.21944,0,0,.525]}};let bn={slant:[.25,.25,.25],space:[0,0,0],stretch:[0,0,0],shrink:[0,0,0],xHeight:[.431,.431,.431],quad:[1,1.171,1.472],extraSpace:[0,0,0],num1:[.677,.732,.925],num2:[.394,.384,.387],num3:[.444,.471,.504],denom1:[.686,.752,1.025],denom2:[.345,.344,.532],sup1:[.413,.503,.504],sup2:[.363,.431,.404],sup3:[.289,.286,.294],sub1:[.15,.143,.2],sub2:[.247,.286,.4],supDrop:[.386,.353,.494],subDrop:[.05,.071,.1],delim1:[2.39,1.7,1.98],delim2:[1.01,1.157,1.42],axisHeight:[.25,.25,.25],defaultRuleThickness:[.04,.049,.049],bigOpSpacing1:[.111,.111,.111],bigOpSpacing2:[.166,.166,.166],bigOpSpacing3:[.2,.2,.2],bigOpSpacing4:[.6,.611,.611],bigOpSpacing5:[.1,.143,.143],sqrtRuleThickness:[.04,.04,.04],ptPerEm:[10,10,10],doubleRuleSep:[.2,.2,.2],arrayRuleWidth:[.04,.04,.04],fboxsep:[.3,.3,.3],fboxrule:[.04,.04,.04]},Bo={\u00C5:"A",\u00D0:"D",\u00DE:"o",\u00E5:"a",\u00F0:"d",\u00FE:"o",\u0410:"A",\u0411:"B",\u0412:"B",\u0413:"F",\u0414:"A",\u0415:"E",\u0416:"K",\u0417:"3",\u0418:"N",\u0419:"N",\u041A:"K",\u041B:"N",\u041C:"M",\u041D:"H",\u041E:"O",\u041F:"N",\u0420:"P",\u0421:"C",\u0422:"T",\u0423:"y",\u0424:"O",\u0425:"X",\u0426:"U",\u0427:"h",\u0428:"W",\u0429:"W",\u042A:"B",\u042B:"X",\u042C:"B",\u042D:"3",\u042E:"X",\u042F:"R",\u0430:"a",\u0431:"b",\u0432:"a",\u0433:"r",\u0434:"y",\u0435:"e",\u0436:"m",\u0437:"e",\u0438:"n",\u0439:"n",\u043A:"n",\u043B:"n",\u043C:"m",\u043D:"n",\u043E:"o",\u043F:"n",\u0440:"p",\u0441:"c",\u0442:"o",\u0443:"y",\u0444:"b",\u0445:"x",\u0446:"n",\u0447:"n",\u0448:"w",\u0449:"w",\u044A:"a",\u044B:"m",\u044C:"a",\u044D:"e",\u044E:"m",\u044F:"r"};function hh(o,n){At[o]=n}function B0(o,n,s){if(!At[n])throw new Error("Font metrics not found for font: "+n+".");let c=o.charCodeAt(0),f=At[n][c];if(!f&&o[0]in Bo&&(c=Bo[o[0]].charCodeAt(0),f=At[n][c]),!f&&s==="text"&&ot(c)&&(f=At[n][77]),f)return{depth:f[0],height:f[1],italic:f[2],skew:f[3],width:f[4]}}let O0={};function fh(o){let n;if(o>=5?n=0:o>=3?n=1:n=2,!O0[n]){let s=O0[n]={cssEmPerMu:bn.quad[n]/18};for(let c in bn)bn.hasOwnProperty(c)&&(s[c]=bn[c][n])}return O0[n]}let dh=[[1,1,1],[2,1,1],[3,1,1],[4,2,1],[5,2,1],[6,3,1],[7,4,2],[8,6,3],[9,7,6],[10,8,7],[11,10,9]],Oo=[.5,.6,.7,.8,.9,1,1.2,1.44,1.728,2.074,2.488],Fo=function(o,n){return n.size<2?o:dh[o-1][n.size-1]};class Dt{constructor(n){this.style=void 0,this.color=void 0,this.size=void 0,this.textSize=void 0,this.phantom=void 0,this.font=void 0,this.fontFamily=void 0,this.fontWeight=void 0,this.fontShape=void 0,this.sizeMultiplier=void 0,this.maxSize=void 0,this.minRuleThickness=void 0,this._fontMetrics=void 0,this.style=n.style,this.color=n.color,this.size=n.size||Dt.BASESIZE,this.textSize=n.textSize||this.size,this.phantom=!!n.phantom,this.font=n.font||"",this.fontFamily=n.fontFamily||"",this.fontWeight=n.fontWeight||"",this.fontShape=n.fontShape||"",this.sizeMultiplier=Oo[this.size-1],this.maxSize=n.maxSize,this.minRuleThickness=n.minRuleThickness,this._fontMetrics=void 0}extend(n){let s={style:this.style,size:this.size,textSize:this.textSize,color:this.color,phantom:this.phantom,font:this.font,fontFamily:this.fontFamily,fontWeight:this.fontWeight,fontShape:this.fontShape,maxSize:this.maxSize,minRuleThickness:this.minRuleThickness};for(let c in n)n.hasOwnProperty(c)&&(s[c]=n[c]);return new Dt(s)}havingStyle(n){return this.style===n?this:this.extend({style:n,size:Fo(this.textSize,n)})}havingCrampedStyle(){return this.havingStyle(this.style.cramp())}havingSize(n){return this.size===n&&this.textSize===n?this:this.extend({style:this.style.text(),size:n,textSize:n,sizeMultiplier:Oo[n-1]})}havingBaseStyle(n){n=n||this.style.text();let s=Fo(Dt.BASESIZE,n);return this.size===s&&this.textSize===Dt.BASESIZE&&this.style===n?this:this.extend({style:n,size:s})}havingBaseSizing(){let n;switch(this.style.id){case 4:case 5:n=3;break;case 6:case 7:n=1;break;default:n=6}return this.extend({style:this.style.text(),size:n})}withColor(n){return this.extend({color:n})}withPhantom(){return this.extend({phantom:!0})}withFont(n){return this.extend({font:n})}withTextFontFamily(n){return this.extend({fontFamily:n,font:""})}withTextFontWeight(n){return this.extend({fontWeight:n,font:""})}withTextFontShape(n){return this.extend({fontShape:n,font:""})}sizingClasses(n){return n.size!==this.size?["sizing","reset-size"+n.size,"size"+this.size]:[]}baseSizingClasses(){return this.size!==Dt.BASESIZE?["sizing","reset-size"+this.size,"size"+Dt.BASESIZE]:[]}fontMetrics(){return this._fontMetrics||(this._fontMetrics=fh(this.size)),this._fontMetrics}getColor(){return this.phantom?"transparent":this.color}}Dt.BASESIZE=6;var ph=Dt;let F0={pt:1,mm:7227/2540,cm:7227/254,in:72.27,bp:803/800,pc:12,dd:1238/1157,cc:14856/1157,nd:685/642,nc:1370/107,sp:1/65536,px:803/800},mh={ex:!0,em:!0,mu:!0},Ro=function(o){return typeof o!="string"&&(o=o.unit),o in F0||o in mh||o==="ex"},Ce=function(o,n){let s;if(o.unit in F0)s=F0[o.unit]/n.fontMetrics().ptPerEm/n.sizeMultiplier;else if(o.unit==="mu")s=n.fontMetrics().cssEmPerMu;else{let c;if(n.style.isTight()?c=n.havingStyle(n.style.text()):c=n,o.unit==="ex")s=c.fontMetrics().xHeight;else if(o.unit==="em")s=c.fontMetrics().quad;else throw new i("Invalid unit: '"+o.unit+"'");c!==n&&(s*=c.sizeMultiplier/n.sizeMultiplier)}return Math.min(o.number*s,n.maxSize)},U=function(o){return+o.toFixed(4)+"em"},Ut=function(o){return o.filter(n=>n).join(" ")},Lo=function(o,n,s){if(this.classes=o||[],this.attributes={},this.height=0,this.depth=0,this.maxFontSize=0,this.style=s||{},n){n.style.isTight()&&this.classes.push("mtight");let c=n.getColor();c&&(this.style.color=c)}},qo=function(o){let n=document.createElement(o);n.className=Ut(this.classes);for(let s in this.style)this.style.hasOwnProperty(s)&&(n.style[s]=this.style[s]);for(let s in this.attributes)this.attributes.hasOwnProperty(s)&&n.setAttribute(s,this.attributes[s]);for(let s=0;s<this.children.length;s++)n.appendChild(this.children[s].toNode());return n},gh=/[\s"'>/=\x00-\x1f]/,Po=function(o){let n="<"+o;this.classes.length&&(n+=' class="'+z.escape(Ut(this.classes))+'"');let s="";for(let c in this.style)this.style.hasOwnProperty(c)&&(s+=z.hyphenate(c)+":"+this.style[c]+";");s&&(n+=' style="'+z.escape(s)+'"');for(let c in this.attributes)if(this.attributes.hasOwnProperty(c)){if(gh.test(c))throw new i("Invalid attribute name '"+c+"'");n+=" "+c+'="'+z.escape(this.attributes[c])+'"'}n+=">";for(let c=0;c<this.children.length;c++)n+=this.children[c].toMarkup();return n+="</"+o+">",n};class Rr{constructor(n,s,c,f){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.width=void 0,this.maxFontSize=void 0,this.style=void 0,Lo.call(this,n,c,f),this.children=s||[]}setAttribute(n,s){this.attributes[n]=s}hasClass(n){return z.contains(this.classes,n)}toNode(){return qo.call(this,"span")}toMarkup(){return Po.call(this,"span")}}class R0{constructor(n,s,c,f){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,Lo.call(this,s,f),this.children=c||[],this.setAttribute("href",n)}setAttribute(n,s){this.attributes[n]=s}hasClass(n){return z.contains(this.classes,n)}toNode(){return qo.call(this,"a")}toMarkup(){return Po.call(this,"a")}}class xh{constructor(n,s,c){this.src=void 0,this.alt=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.alt=s,this.src=n,this.classes=["mord"],this.style=c}hasClass(n){return z.contains(this.classes,n)}toNode(){let n=document.createElement("img");n.src=this.src,n.alt=this.alt,n.className="mord";for(let s in this.style)this.style.hasOwnProperty(s)&&(n.style[s]=this.style[s]);return n}toMarkup(){let n='<img src="'+z.escape(this.src)+'"'+(' alt="'+z.escape(this.alt)+'"'),s="";for(let c in this.style)this.style.hasOwnProperty(c)&&(s+=z.hyphenate(c)+":"+this.style[c]+";");return s&&(n+=' style="'+z.escape(s)+'"'),n+="'/>",n}}let yh={\u00EE:"\u0131\u0302",\u00EF:"\u0131\u0308",\u00ED:"\u0131\u0301",\u00EC:"\u0131\u0300"};class ft{constructor(n,s,c,f,m,x,w,M){this.text=void 0,this.height=void 0,this.depth=void 0,this.italic=void 0,this.skew=void 0,this.width=void 0,this.maxFontSize=void 0,this.classes=void 0,this.style=void 0,this.text=n,this.height=s||0,this.depth=c||0,this.italic=f||0,this.skew=m||0,this.width=x||0,this.classes=w||[],this.style=M||{},this.maxFontSize=0;let N=xe(this.text.charCodeAt(0));N&&this.classes.push(N+"_fallback"),/[îïíì]/.test(this.text)&&(this.text=yh[this.text])}hasClass(n){return z.contains(this.classes,n)}toNode(){let n=document.createTextNode(this.text),s=null;this.italic>0&&(s=document.createElement("span"),s.style.marginRight=U(this.italic)),this.classes.length>0&&(s=s||document.createElement("span"),s.className=Ut(this.classes));for(let c in this.style)this.style.hasOwnProperty(c)&&(s=s||document.createElement("span"),s.style[c]=this.style[c]);return s?(s.appendChild(n),s):n}toMarkup(){let n=!1,s="<span";this.classes.length&&(n=!0,s+=' class="',s+=z.escape(Ut(this.classes)),s+='"');let c="";this.italic>0&&(c+="margin-right:"+this.italic+"em;");for(let m in this.style)this.style.hasOwnProperty(m)&&(c+=z.hyphenate(m)+":"+this.style[m]+";");c&&(n=!0,s+=' style="'+z.escape(c)+'"');let f=z.escape(this.text);return n?(s+=">",s+=f,s+="</span>",s):f}}class It{constructor(n,s){this.children=void 0,this.attributes=void 0,this.children=n||[],this.attributes=s||{}}toNode(){let n="http://www.w3.org/2000/svg",s=document.createElementNS(n,"svg");for(let c in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,c)&&s.setAttribute(c,this.attributes[c]);for(let c=0;c<this.children.length;c++)s.appendChild(this.children[c].toNode());return s}toMarkup(){let n='<svg xmlns="http://www.w3.org/2000/svg"';for(let s in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,s)&&(n+=" "+s+'="'+z.escape(this.attributes[s])+'"');n+=">";for(let s=0;s<this.children.length;s++)n+=this.children[s].toMarkup();return n+="</svg>",n}}class Xt{constructor(n,s){this.pathName=void 0,this.alternate=void 0,this.pathName=n,this.alternate=s}toNode(){let n="http://www.w3.org/2000/svg",s=document.createElementNS(n,"path");return this.alternate?s.setAttribute("d",this.alternate):s.setAttribute("d",Io[this.pathName]),s}toMarkup(){return this.alternate?'<path d="'+z.escape(this.alternate)+'"/>':'<path d="'+z.escape(Io[this.pathName])+'"/>'}}class L0{constructor(n){this.attributes=void 0,this.attributes=n||{}}toNode(){let n="http://www.w3.org/2000/svg",s=document.createElementNS(n,"line");for(let c in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,c)&&s.setAttribute(c,this.attributes[c]);return s}toMarkup(){let n="<line";for(let s in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,s)&&(n+=" "+s+'="'+z.escape(this.attributes[s])+'"');return n+="/>",n}}function Ho(o){if(o instanceof ft)return o;throw new Error("Expected symbolNode but got "+String(o)+".")}function bh(o){if(o instanceof Rr)return o;throw new Error("Expected span<HtmlDomNode> but got "+String(o)+".")}let vh={bin:1,close:1,inner:1,open:1,punct:1,rel:1},wh={"accent-token":1,mathord:1,"op-token":1,spacing:1,textord:1},vn={math:{},text:{}};var Te=vn;function d(o,n,s,c,f,m){vn[o][f]={font:n,group:s,replace:c},m&&c&&(vn[o][c]=vn[o][f])}let g="math",$="text",v="main",A="ams",Ae="accent-token",K="bin",je="close",wr="inner",ne="mathord",Fe="op-token",at="open",wn="punct",_="rel",Bt="spacing",E="textord";d(g,v,_,"\u2261","\\equiv",!0),d(g,v,_,"\u227A","\\prec",!0),d(g,v,_,"\u227B","\\succ",!0),d(g,v,_,"\u223C","\\sim",!0),d(g,v,_,"\u22A5","\\perp"),d(g,v,_,"\u2AAF","\\preceq",!0),d(g,v,_,"\u2AB0","\\succeq",!0),d(g,v,_,"\u2243","\\simeq",!0),d(g,v,_,"\u2223","\\mid",!0),d(g,v,_,"\u226A","\\ll",!0),d(g,v,_,"\u226B","\\gg",!0),d(g,v,_,"\u224D","\\asymp",!0),d(g,v,_,"\u2225","\\parallel"),d(g,v,_,"\u22C8","\\bowtie",!0),d(g,v,_,"\u2323","\\smile",!0),d(g,v,_,"\u2291","\\sqsubseteq",!0),d(g,v,_,"\u2292","\\sqsupseteq",!0),d(g,v,_,"\u2250","\\doteq",!0),d(g,v,_,"\u2322","\\frown",!0),d(g,v,_,"\u220B","\\ni",!0),d(g,v,_,"\u221D","\\propto",!0),d(g,v,_,"\u22A2","\\vdash",!0),d(g,v,_,"\u22A3","\\dashv",!0),d(g,v,_,"\u220B","\\owns"),d(g,v,wn,".","\\ldotp"),d(g,v,wn,"\u22C5","\\cdotp"),d(g,v,E,"#","\\#"),d($,v,E,"#","\\#"),d(g,v,E,"&","\\&"),d($,v,E,"&","\\&"),d(g,v,E,"\u2135","\\aleph",!0),d(g,v,E,"\u2200","\\forall",!0),d(g,v,E,"\u210F","\\hbar",!0),d(g,v,E,"\u2203","\\exists",!0),d(g,v,E,"\u2207","\\nabla",!0),d(g,v,E,"\u266D","\\flat",!0),d(g,v,E,"\u2113","\\ell",!0),d(g,v,E,"\u266E","\\natural",!0),d(g,v,E,"\u2663","\\clubsuit",!0),d(g,v,E,"\u2118","\\wp",!0),d(g,v,E,"\u266F","\\sharp",!0),d(g,v,E,"\u2662","\\diamondsuit",!0),d(g,v,E,"\u211C","\\Re",!0),d(g,v,E,"\u2661","\\heartsuit",!0),d(g,v,E,"\u2111","\\Im",!0),d(g,v,E,"\u2660","\\spadesuit",!0),d(g,v,E,"\xA7","\\S",!0),d($,v,E,"\xA7","\\S"),d(g,v,E,"\xB6","\\P",!0),d($,v,E,"\xB6","\\P"),d(g,v,E,"\u2020","\\dag"),d($,v,E,"\u2020","\\dag"),d($,v,E,"\u2020","\\textdagger"),d(g,v,E,"\u2021","\\ddag"),d($,v,E,"\u2021","\\ddag"),d($,v,E,"\u2021","\\textdaggerdbl"),d(g,v,je,"\u23B1","\\rmoustache",!0),d(g,v,at,"\u23B0","\\lmoustache",!0),d(g,v,je,"\u27EF","\\rgroup",!0),d(g,v,at,"\u27EE","\\lgroup",!0),d(g,v,K,"\u2213","\\mp",!0),d(g,v,K,"\u2296","\\ominus",!0),d(g,v,K,"\u228E","\\uplus",!0),d(g,v,K,"\u2293","\\sqcap",!0),d(g,v,K,"\u2217","\\ast"),d(g,v,K,"\u2294","\\sqcup",!0),d(g,v,K,"\u25EF","\\bigcirc",!0),d(g,v,K,"\u2219","\\bullet",!0),d(g,v,K,"\u2021","\\ddagger"),d(g,v,K,"\u2240","\\wr",!0),d(g,v,K,"\u2A3F","\\amalg"),d(g,v,K,"&","\\And"),d(g,v,_,"\u27F5","\\longleftarrow",!0),d(g,v,_,"\u21D0","\\Leftarrow",!0),d(g,v,_,"\u27F8","\\Longleftarrow",!0),d(g,v,_,"\u27F6","\\longrightarrow",!0),d(g,v,_,"\u21D2","\\Rightarrow",!0),d(g,v,_,"\u27F9","\\Longrightarrow",!0),d(g,v,_,"\u2194","\\leftrightarrow",!0),d(g,v,_,"\u27F7","\\longleftrightarrow",!0),d(g,v,_,"\u21D4","\\Leftrightarrow",!0),d(g,v,_,"\u27FA","\\Longleftrightarrow",!0),d(g,v,_,"\u21A6","\\mapsto",!0),d(g,v,_,"\u27FC","\\longmapsto",!0),d(g,v,_,"\u2197","\\nearrow",!0),d(g,v,_,"\u21A9","\\hookleftarrow",!0),d(g,v,_,"\u21AA","\\hookrightarrow",!0),d(g,v,_,"\u2198","\\searrow",!0),d(g,v,_,"\u21BC","\\leftharpoonup",!0),d(g,v,_,"\u21C0","\\rightharpoonup",!0),d(g,v,_,"\u2199","\\swarrow",!0),d(g,v,_,"\u21BD","\\leftharpoondown",!0),d(g,v,_,"\u21C1","\\rightharpoondown",!0),d(g,v,_,"\u2196","\\nwarrow",!0),d(g,v,_,"\u21CC","\\rightleftharpoons",!0),d(g,A,_,"\u226E","\\nless",!0),d(g,A,_,"\uE010","\\@nleqslant"),d(g,A,_,"\uE011","\\@nleqq"),d(g,A,_,"\u2A87","\\lneq",!0),d(g,A,_,"\u2268","\\lneqq",!0),d(g,A,_,"\uE00C","\\@lvertneqq"),d(g,A,_,"\u22E6","\\lnsim",!0),d(g,A,_,"\u2A89","\\lnapprox",!0),d(g,A,_,"\u2280","\\nprec",!0),d(g,A,_,"\u22E0","\\npreceq",!0),d(g,A,_,"\u22E8","\\precnsim",!0),d(g,A,_,"\u2AB9","\\precnapprox",!0),d(g,A,_,"\u2241","\\nsim",!0),d(g,A,_,"\uE006","\\@nshortmid"),d(g,A,_,"\u2224","\\nmid",!0),d(g,A,_,"\u22AC","\\nvdash",!0),d(g,A,_,"\u22AD","\\nvDash",!0),d(g,A,_,"\u22EA","\\ntriangleleft"),d(g,A,_,"\u22EC","\\ntrianglelefteq",!0),d(g,A,_,"\u228A","\\subsetneq",!0),d(g,A,_,"\uE01A","\\@varsubsetneq"),d(g,A,_,"\u2ACB","\\subsetneqq",!0),d(g,A,_,"\uE017","\\@varsubsetneqq"),d(g,A,_,"\u226F","\\ngtr",!0),d(g,A,_,"\uE00F","\\@ngeqslant"),d(g,A,_,"\uE00E","\\@ngeqq"),d(g,A,_,"\u2A88","\\gneq",!0),d(g,A,_,"\u2269","\\gneqq",!0),d(g,A,_,"\uE00D","\\@gvertneqq"),d(g,A,_,"\u22E7","\\gnsim",!0),d(g,A,_,"\u2A8A","\\gnapprox",!0),d(g,A,_,"\u2281","\\nsucc",!0),d(g,A,_,"\u22E1","\\nsucceq",!0),d(g,A,_,"\u22E9","\\succnsim",!0),d(g,A,_,"\u2ABA","\\succnapprox",!0),d(g,A,_,"\u2246","\\ncong",!0),d(g,A,_,"\uE007","\\@nshortparallel"),d(g,A,_,"\u2226","\\nparallel",!0),d(g,A,_,"\u22AF","\\nVDash",!0),d(g,A,_,"\u22EB","\\ntriangleright"),d(g,A,_,"\u22ED","\\ntrianglerighteq",!0),d(g,A,_,"\uE018","\\@nsupseteqq"),d(g,A,_,"\u228B","\\supsetneq",!0),d(g,A,_,"\uE01B","\\@varsupsetneq"),d(g,A,_,"\u2ACC","\\supsetneqq",!0),d(g,A,_,"\uE019","\\@varsupsetneqq"),d(g,A,_,"\u22AE","\\nVdash",!0),d(g,A,_,"\u2AB5","\\precneqq",!0),d(g,A,_,"\u2AB6","\\succneqq",!0),d(g,A,_,"\uE016","\\@nsubseteqq"),d(g,A,K,"\u22B4","\\unlhd"),d(g,A,K,"\u22B5","\\unrhd"),d(g,A,_,"\u219A","\\nleftarrow",!0),d(g,A,_,"\u219B","\\nrightarrow",!0),d(g,A,_,"\u21CD","\\nLeftarrow",!0),d(g,A,_,"\u21CF","\\nRightarrow",!0),d(g,A,_,"\u21AE","\\nleftrightarrow",!0),d(g,A,_,"\u21CE","\\nLeftrightarrow",!0),d(g,A,_,"\u25B3","\\vartriangle"),d(g,A,E,"\u210F","\\hslash"),d(g,A,E,"\u25BD","\\triangledown"),d(g,A,E,"\u25CA","\\lozenge"),d(g,A,E,"\u24C8","\\circledS"),d(g,A,E,"\xAE","\\circledR"),d($,A,E,"\xAE","\\circledR"),d(g,A,E,"\u2221","\\measuredangle",!0),d(g,A,E,"\u2204","\\nexists"),d(g,A,E,"\u2127","\\mho"),d(g,A,E,"\u2132","\\Finv",!0),d(g,A,E,"\u2141","\\Game",!0),d(g,A,E,"\u2035","\\backprime"),d(g,A,E,"\u25B2","\\blacktriangle"),d(g,A,E,"\u25BC","\\blacktriangledown"),d(g,A,E,"\u25A0","\\blacksquare"),d(g,A,E,"\u29EB","\\blacklozenge"),d(g,A,E,"\u2605","\\bigstar"),d(g,A,E,"\u2222","\\sphericalangle",!0),d(g,A,E,"\u2201","\\complement",!0),d(g,A,E,"\xF0","\\eth",!0),d($,v,E,"\xF0","\xF0"),d(g,A,E,"\u2571","\\diagup"),d(g,A,E,"\u2572","\\diagdown"),d(g,A,E,"\u25A1","\\square"),d(g,A,E,"\u25A1","\\Box"),d(g,A,E,"\u25CA","\\Diamond"),d(g,A,E,"\xA5","\\yen",!0),d($,A,E,"\xA5","\\yen",!0),d(g,A,E,"\u2713","\\checkmark",!0),d($,A,E,"\u2713","\\checkmark"),d(g,A,E,"\u2136","\\beth",!0),d(g,A,E,"\u2138","\\daleth",!0),d(g,A,E,"\u2137","\\gimel",!0),d(g,A,E,"\u03DD","\\digamma",!0),d(g,A,E,"\u03F0","\\varkappa"),d(g,A,at,"\u250C","\\@ulcorner",!0),d(g,A,je,"\u2510","\\@urcorner",!0),d(g,A,at,"\u2514","\\@llcorner",!0),d(g,A,je,"\u2518","\\@lrcorner",!0),d(g,A,_,"\u2266","\\leqq",!0),d(g,A,_,"\u2A7D","\\leqslant",!0),d(g,A,_,"\u2A95","\\eqslantless",!0),d(g,A,_,"\u2272","\\lesssim",!0),d(g,A,_,"\u2A85","\\lessapprox",!0),d(g,A,_,"\u224A","\\approxeq",!0),d(g,A,K,"\u22D6","\\lessdot"),d(g,A,_,"\u22D8","\\lll",!0),d(g,A,_,"\u2276","\\lessgtr",!0),d(g,A,_,"\u22DA","\\lesseqgtr",!0),d(g,A,_,"\u2A8B","\\lesseqqgtr",!0),d(g,A,_,"\u2251","\\doteqdot"),d(g,A,_,"\u2253","\\risingdotseq",!0),d(g,A,_,"\u2252","\\fallingdotseq",!0),d(g,A,_,"\u223D","\\backsim",!0),d(g,A,_,"\u22CD","\\backsimeq",!0),d(g,A,_,"\u2AC5","\\subseteqq",!0),d(g,A,_,"\u22D0","\\Subset",!0),d(g,A,_,"\u228F","\\sqsubset",!0),d(g,A,_,"\u227C","\\preccurlyeq",!0),d(g,A,_,"\u22DE","\\curlyeqprec",!0),d(g,A,_,"\u227E","\\precsim",!0),d(g,A,_,"\u2AB7","\\precapprox",!0),d(g,A,_,"\u22B2","\\vartriangleleft"),d(g,A,_,"\u22B4","\\trianglelefteq"),d(g,A,_,"\u22A8","\\vDash",!0),d(g,A,_,"\u22AA","\\Vvdash",!0),d(g,A,_,"\u2323","\\smallsmile"),d(g,A,_,"\u2322","\\smallfrown"),d(g,A,_,"\u224F","\\bumpeq",!0),d(g,A,_,"\u224E","\\Bumpeq",!0),d(g,A,_,"\u2267","\\geqq",!0),d(g,A,_,"\u2A7E","\\geqslant",!0),d(g,A,_,"\u2A96","\\eqslantgtr",!0),d(g,A,_,"\u2273","\\gtrsim",!0),d(g,A,_,"\u2A86","\\gtrapprox",!0),d(g,A,K,"\u22D7","\\gtrdot"),d(g,A,_,"\u22D9","\\ggg",!0),d(g,A,_,"\u2277","\\gtrless",!0),d(g,A,_,"\u22DB","\\gtreqless",!0),d(g,A,_,"\u2A8C","\\gtreqqless",!0),d(g,A,_,"\u2256","\\eqcirc",!0),d(g,A,_,"\u2257","\\circeq",!0),d(g,A,_,"\u225C","\\triangleq",!0),d(g,A,_,"\u223C","\\thicksim"),d(g,A,_,"\u2248","\\thickapprox"),d(g,A,_,"\u2AC6","\\supseteqq",!0),d(g,A,_,"\u22D1","\\Supset",!0),d(g,A,_,"\u2290","\\sqsupset",!0),d(g,A,_,"\u227D","\\succcurlyeq",!0),d(g,A,_,"\u22DF","\\curlyeqsucc",!0),d(g,A,_,"\u227F","\\succsim",!0),d(g,A,_,"\u2AB8","\\succapprox",!0),d(g,A,_,"\u22B3","\\vartriangleright"),d(g,A,_,"\u22B5","\\trianglerighteq"),d(g,A,_,"\u22A9","\\Vdash",!0),d(g,A,_,"\u2223","\\shortmid"),d(g,A,_,"\u2225","\\shortparallel"),d(g,A,_,"\u226C","\\between",!0),d(g,A,_,"\u22D4","\\pitchfork",!0),d(g,A,_,"\u221D","\\varpropto"),d(g,A,_,"\u25C0","\\blacktriangleleft"),d(g,A,_,"\u2234","\\therefore",!0),d(g,A,_,"\u220D","\\backepsilon"),d(g,A,_,"\u25B6","\\blacktriangleright"),d(g,A,_,"\u2235","\\because",!0),d(g,A,_,"\u22D8","\\llless"),d(g,A,_,"\u22D9","\\gggtr"),d(g,A,K,"\u22B2","\\lhd"),d(g,A,K,"\u22B3","\\rhd"),d(g,A,_,"\u2242","\\eqsim",!0),d(g,v,_,"\u22C8","\\Join"),d(g,A,_,"\u2251","\\Doteq",!0),d(g,A,K,"\u2214","\\dotplus",!0),d(g,A,K,"\u2216","\\smallsetminus"),d(g,A,K,"\u22D2","\\Cap",!0),d(g,A,K,"\u22D3","\\Cup",!0),d(g,A,K,"\u2A5E","\\doublebarwedge",!0),d(g,A,K,"\u229F","\\boxminus",!0),d(g,A,K,"\u229E","\\boxplus",!0),d(g,A,K,"\u22C7","\\divideontimes",!0),d(g,A,K,"\u22C9","\\ltimes",!0),d(g,A,K,"\u22CA","\\rtimes",!0),d(g,A,K,"\u22CB","\\leftthreetimes",!0),d(g,A,K,"\u22CC","\\rightthreetimes",!0),d(g,A,K,"\u22CF","\\curlywedge",!0),d(g,A,K,"\u22CE","\\curlyvee",!0),d(g,A,K,"\u229D","\\circleddash",!0),d(g,A,K,"\u229B","\\circledast",!0),d(g,A,K,"\u22C5","\\centerdot"),d(g,A,K,"\u22BA","\\intercal",!0),d(g,A,K,"\u22D2","\\doublecap"),d(g,A,K,"\u22D3","\\doublecup"),d(g,A,K,"\u22A0","\\boxtimes",!0),d(g,A,_,"\u21E2","\\dashrightarrow",!0),d(g,A,_,"\u21E0","\\dashleftarrow",!0),d(g,A,_,"\u21C7","\\leftleftarrows",!0),d(g,A,_,"\u21C6","\\leftrightarrows",!0),d(g,A,_,"\u21DA","\\Lleftarrow",!0),d(g,A,_,"\u219E","\\twoheadleftarrow",!0),d(g,A,_,"\u21A2","\\leftarrowtail",!0),d(g,A,_,"\u21AB","\\looparrowleft",!0),d(g,A,_,"\u21CB","\\leftrightharpoons",!0),d(g,A,_,"\u21B6","\\curvearrowleft",!0),d(g,A,_,"\u21BA","\\circlearrowleft",!0),d(g,A,_,"\u21B0","\\Lsh",!0),d(g,A,_,"\u21C8","\\upuparrows",!0),d(g,A,_,"\u21BF","\\upharpoonleft",!0),d(g,A,_,"\u21C3","\\downharpoonleft",!0),d(g,v,_,"\u22B6","\\origof",!0),d(g,v,_,"\u22B7","\\imageof",!0),d(g,A,_,"\u22B8","\\multimap",!0),d(g,A,_,"\u21AD","\\leftrightsquigarrow",!0),d(g,A,_,"\u21C9","\\rightrightarrows",!0),d(g,A,_,"\u21C4","\\rightleftarrows",!0),d(g,A,_,"\u21A0","\\twoheadrightarrow",!0),d(g,A,_,"\u21A3","\\rightarrowtail",!0),d(g,A,_,"\u21AC","\\looparrowright",!0),d(g,A,_,"\u21B7","\\curvearrowright",!0),d(g,A,_,"\u21BB","\\circlearrowright",!0),d(g,A,_,"\u21B1","\\Rsh",!0),d(g,A,_,"\u21CA","\\downdownarrows",!0),d(g,A,_,"\u21BE","\\upharpoonright",!0),d(g,A,_,"\u21C2","\\downharpoonright",!0),d(g,A,_,"\u21DD","\\rightsquigarrow",!0),d(g,A,_,"\u21DD","\\leadsto"),d(g,A,_,"\u21DB","\\Rrightarrow",!0),d(g,A,_,"\u21BE","\\restriction"),d(g,v,E,"\u2018","`"),d(g,v,E,"$","\\$"),d($,v,E,"$","\\$"),d($,v,E,"$","\\textdollar"),d(g,v,E,"%","\\%"),d($,v,E,"%","\\%"),d(g,v,E,"_","\\_"),d($,v,E,"_","\\_"),d($,v,E,"_","\\textunderscore"),d(g,v,E,"\u2220","\\angle",!0),d(g,v,E,"\u221E","\\infty",!0),d(g,v,E,"\u2032","\\prime"),d(g,v,E,"\u25B3","\\triangle"),d(g,v,E,"\u0393","\\Gamma",!0),d(g,v,E,"\u0394","\\Delta",!0),d(g,v,E,"\u0398","\\Theta",!0),d(g,v,E,"\u039B","\\Lambda",!0),d(g,v,E,"\u039E","\\Xi",!0),d(g,v,E,"\u03A0","\\Pi",!0),d(g,v,E,"\u03A3","\\Sigma",!0),d(g,v,E,"\u03A5","\\Upsilon",!0),d(g,v,E,"\u03A6","\\Phi",!0),d(g,v,E,"\u03A8","\\Psi",!0),d(g,v,E,"\u03A9","\\Omega",!0),d(g,v,E,"A","\u0391"),d(g,v,E,"B","\u0392"),d(g,v,E,"E","\u0395"),d(g,v,E,"Z","\u0396"),d(g,v,E,"H","\u0397"),d(g,v,E,"I","\u0399"),d(g,v,E,"K","\u039A"),d(g,v,E,"M","\u039C"),d(g,v,E,"N","\u039D"),d(g,v,E,"O","\u039F"),d(g,v,E,"P","\u03A1"),d(g,v,E,"T","\u03A4"),d(g,v,E,"X","\u03A7"),d(g,v,E,"\xAC","\\neg",!0),d(g,v,E,"\xAC","\\lnot"),d(g,v,E,"\u22A4","\\top"),d(g,v,E,"\u22A5","\\bot"),d(g,v,E,"\u2205","\\emptyset"),d(g,A,E,"\u2205","\\varnothing"),d(g,v,ne,"\u03B1","\\alpha",!0),d(g,v,ne,"\u03B2","\\beta",!0),d(g,v,ne,"\u03B3","\\gamma",!0),d(g,v,ne,"\u03B4","\\delta",!0),d(g,v,ne,"\u03F5","\\epsilon",!0),d(g,v,ne,"\u03B6","\\zeta",!0),d(g,v,ne,"\u03B7","\\eta",!0),d(g,v,ne,"\u03B8","\\theta",!0),d(g,v,ne,"\u03B9","\\iota",!0),d(g,v,ne,"\u03BA","\\kappa",!0),d(g,v,ne,"\u03BB","\\lambda",!0),d(g,v,ne,"\u03BC","\\mu",!0),d(g,v,ne,"\u03BD","\\nu",!0),d(g,v,ne,"\u03BE","\\xi",!0),d(g,v,ne,"\u03BF","\\omicron",!0),d(g,v,ne,"\u03C0","\\pi",!0),d(g,v,ne,"\u03C1","\\rho",!0),d(g,v,ne,"\u03C3","\\sigma",!0),d(g,v,ne,"\u03C4","\\tau",!0),d(g,v,ne,"\u03C5","\\upsilon",!0),d(g,v,ne,"\u03D5","\\phi",!0),d(g,v,ne,"\u03C7","\\chi",!0),d(g,v,ne,"\u03C8","\\psi",!0),d(g,v,ne,"\u03C9","\\omega",!0),d(g,v,ne,"\u03B5","\\varepsilon",!0),d(g,v,ne,"\u03D1","\\vartheta",!0),d(g,v,ne,"\u03D6","\\varpi",!0),d(g,v,ne,"\u03F1","\\varrho",!0),d(g,v,ne,"\u03C2","\\varsigma",!0),d(g,v,ne,"\u03C6","\\varphi",!0),d(g,v,K,"\u2217","*",!0),d(g,v,K,"+","+"),d(g,v,K,"\u2212","-",!0),d(g,v,K,"\u22C5","\\cdot",!0),d(g,v,K,"\u2218","\\circ",!0),d(g,v,K,"\xF7","\\div",!0),d(g,v,K,"\xB1","\\pm",!0),d(g,v,K,"\xD7","\\times",!0),d(g,v,K,"\u2229","\\cap",!0),d(g,v,K,"\u222A","\\cup",!0),d(g,v,K,"\u2216","\\setminus",!0),d(g,v,K,"\u2227","\\land"),d(g,v,K,"\u2228","\\lor"),d(g,v,K,"\u2227","\\wedge",!0),d(g,v,K,"\u2228","\\vee",!0),d(g,v,E,"\u221A","\\surd"),d(g,v,at,"\u27E8","\\langle",!0),d(g,v,at,"\u2223","\\lvert"),d(g,v,at,"\u2225","\\lVert"),d(g,v,je,"?","?"),d(g,v,je,"!","!"),d(g,v,je,"\u27E9","\\rangle",!0),d(g,v,je,"\u2223","\\rvert"),d(g,v,je,"\u2225","\\rVert"),d(g,v,_,"=","="),d(g,v,_,":",":"),d(g,v,_,"\u2248","\\approx",!0),d(g,v,_,"\u2245","\\cong",!0),d(g,v,_,"\u2265","\\ge"),d(g,v,_,"\u2265","\\geq",!0),d(g,v,_,"\u2190","\\gets"),d(g,v,_,">","\\gt",!0),d(g,v,_,"\u2208","\\in",!0),d(g,v,_,"\uE020","\\@not"),d(g,v,_,"\u2282","\\subset",!0),d(g,v,_,"\u2283","\\supset",!0),d(g,v,_,"\u2286","\\subseteq",!0),d(g,v,_,"\u2287","\\supseteq",!0),d(g,A,_,"\u2288","\\nsubseteq",!0),d(g,A,_,"\u2289","\\nsupseteq",!0),d(g,v,_,"\u22A8","\\models"),d(g,v,_,"\u2190","\\leftarrow",!0),d(g,v,_,"\u2264","\\le"),d(g,v,_,"\u2264","\\leq",!0),d(g,v,_,"<","\\lt",!0),d(g,v,_,"\u2192","\\rightarrow",!0),d(g,v,_,"\u2192","\\to"),d(g,A,_,"\u2271","\\ngeq",!0),d(g,A,_,"\u2270","\\nleq",!0),d(g,v,Bt,"\xA0","\\ "),d(g,v,Bt,"\xA0","\\space"),d(g,v,Bt,"\xA0","\\nobreakspace"),d($,v,Bt,"\xA0","\\ "),d($,v,Bt,"\xA0"," "),d($,v,Bt,"\xA0","\\space"),d($,v,Bt,"\xA0","\\nobreakspace"),d(g,v,Bt,null,"\\nobreak"),d(g,v,Bt,null,"\\allowbreak"),d(g,v,wn,",",","),d(g,v,wn,";",";"),d(g,A,K,"\u22BC","\\barwedge",!0),d(g,A,K,"\u22BB","\\veebar",!0),d(g,v,K,"\u2299","\\odot",!0),d(g,v,K,"\u2295","\\oplus",!0),d(g,v,K,"\u2297","\\otimes",!0),d(g,v,E,"\u2202","\\partial",!0),d(g,v,K,"\u2298","\\oslash",!0),d(g,A,K,"\u229A","\\circledcirc",!0),d(g,A,K,"\u22A1","\\boxdot",!0),d(g,v,K,"\u25B3","\\bigtriangleup"),d(g,v,K,"\u25BD","\\bigtriangledown"),d(g,v,K,"\u2020","\\dagger"),d(g,v,K,"\u22C4","\\diamond"),d(g,v,K,"\u22C6","\\star"),d(g,v,K,"\u25C3","\\triangleleft"),d(g,v,K,"\u25B9","\\triangleright"),d(g,v,at,"{","\\{"),d($,v,E,"{","\\{"),d($,v,E,"{","\\textbraceleft"),d(g,v,je,"}","\\}"),d($,v,E,"}","\\}"),d($,v,E,"}","\\textbraceright"),d(g,v,at,"{","\\lbrace"),d(g,v,je,"}","\\rbrace"),d(g,v,at,"[","\\lbrack",!0),d($,v,E,"[","\\lbrack",!0),d(g,v,je,"]","\\rbrack",!0),d($,v,E,"]","\\rbrack",!0),d(g,v,at,"(","\\lparen",!0),d(g,v,je,")","\\rparen",!0),d($,v,E,"<","\\textless",!0),d($,v,E,">","\\textgreater",!0),d(g,v,at,"\u230A","\\lfloor",!0),d(g,v,je,"\u230B","\\rfloor",!0),d(g,v,at,"\u2308","\\lceil",!0),d(g,v,je,"\u2309","\\rceil",!0),d(g,v,E,"\\","\\backslash"),d(g,v,E,"\u2223","|"),d(g,v,E,"\u2223","\\vert"),d($,v,E,"|","\\textbar",!0),d(g,v,E,"\u2225","\\|"),d(g,v,E,"\u2225","\\Vert"),d($,v,E,"\u2225","\\textbardbl"),d($,v,E,"~","\\textasciitilde"),d($,v,E,"\\","\\textbackslash"),d($,v,E,"^","\\textasciicircum"),d(g,v,_,"\u2191","\\uparrow",!0),d(g,v,_,"\u21D1","\\Uparrow",!0),d(g,v,_,"\u2193","\\downarrow",!0),d(g,v,_,"\u21D3","\\Downarrow",!0),d(g,v,_,"\u2195","\\updownarrow",!0),d(g,v,_,"\u21D5","\\Updownarrow",!0),d(g,v,Fe,"\u2210","\\coprod"),d(g,v,Fe,"\u22C1","\\bigvee"),d(g,v,Fe,"\u22C0","\\bigwedge"),d(g,v,Fe,"\u2A04","\\biguplus"),d(g,v,Fe,"\u22C2","\\bigcap"),d(g,v,Fe,"\u22C3","\\bigcup"),d(g,v,Fe,"\u222B","\\int"),d(g,v,Fe,"\u222B","\\intop"),d(g,v,Fe,"\u222C","\\iint"),d(g,v,Fe,"\u222D","\\iiint"),d(g,v,Fe,"\u220F","\\prod"),d(g,v,Fe,"\u2211","\\sum"),d(g,v,Fe,"\u2A02","\\bigotimes"),d(g,v,Fe,"\u2A01","\\bigoplus"),d(g,v,Fe,"\u2A00","\\bigodot"),d(g,v,Fe,"\u222E","\\oint"),d(g,v,Fe,"\u222F","\\oiint"),d(g,v,Fe,"\u2230","\\oiiint"),d(g,v,Fe,"\u2A06","\\bigsqcup"),d(g,v,Fe,"\u222B","\\smallint"),d($,v,wr,"\u2026","\\textellipsis"),d(g,v,wr,"\u2026","\\mathellipsis"),d($,v,wr,"\u2026","\\ldots",!0),d(g,v,wr,"\u2026","\\ldots",!0),d(g,v,wr,"\u22EF","\\@cdots",!0),d(g,v,wr,"\u22F1","\\ddots",!0),d(g,v,E,"\u22EE","\\varvdots"),d($,v,E,"\u22EE","\\varvdots"),d(g,v,Ae,"\u02CA","\\acute"),d(g,v,Ae,"\u02CB","\\grave"),d(g,v,Ae,"\xA8","\\ddot"),d(g,v,Ae,"~","\\tilde"),d(g,v,Ae,"\u02C9","\\bar"),d(g,v,Ae,"\u02D8","\\breve"),d(g,v,Ae,"\u02C7","\\check"),d(g,v,Ae,"^","\\hat"),d(g,v,Ae,"\u20D7","\\vec"),d(g,v,Ae,"\u02D9","\\dot"),d(g,v,Ae,"\u02DA","\\mathring"),d(g,v,ne,"\uE131","\\@imath"),d(g,v,ne,"\uE237","\\@jmath"),d(g,v,E,"\u0131","\u0131"),d(g,v,E,"\u0237","\u0237"),d($,v,E,"\u0131","\\i",!0),d($,v,E,"\u0237","\\j",!0),d($,v,E,"\xDF","\\ss",!0),d($,v,E,"\xE6","\\ae",!0),d($,v,E,"\u0153","\\oe",!0),d($,v,E,"\xF8","\\o",!0),d($,v,E,"\xC6","\\AE",!0),d($,v,E,"\u0152","\\OE",!0),d($,v,E,"\xD8","\\O",!0),d($,v,Ae,"\u02CA","\\'"),d($,v,Ae,"\u02CB","\\`"),d($,v,Ae,"\u02C6","\\^"),d($,v,Ae,"\u02DC","\\~"),d($,v,Ae,"\u02C9","\\="),d($,v,Ae,"\u02D8","\\u"),d($,v,Ae,"\u02D9","\\."),d($,v,Ae,"\xB8","\\c"),d($,v,Ae,"\u02DA","\\r"),d($,v,Ae,"\u02C7","\\v"),d($,v,Ae,"\xA8",'\\"'),d($,v,Ae,"\u02DD","\\H"),d($,v,Ae,"\u25EF","\\textcircled");let $o={"--":!0,"---":!0,"``":!0,"''":!0};d($,v,E,"\u2013","--",!0),d($,v,E,"\u2013","\\textendash"),d($,v,E,"\u2014","---",!0),d($,v,E,"\u2014","\\textemdash"),d($,v,E,"\u2018","`",!0),d($,v,E,"\u2018","\\textquoteleft"),d($,v,E,"\u2019","'",!0),d($,v,E,"\u2019","\\textquoteright"),d($,v,E,"\u201C","``",!0),d($,v,E,"\u201C","\\textquotedblleft"),d($,v,E,"\u201D","''",!0),d($,v,E,"\u201D","\\textquotedblright"),d(g,v,E,"\xB0","\\degree",!0),d($,v,E,"\xB0","\\degree"),d($,v,E,"\xB0","\\textdegree",!0),d(g,v,E,"\xA3","\\pounds"),d(g,v,E,"\xA3","\\mathsterling",!0),d($,v,E,"\xA3","\\pounds"),d($,v,E,"\xA3","\\textsterling",!0),d(g,A,E,"\u2720","\\maltese"),d($,A,E,"\u2720","\\maltese");let Vo='0123456789/@."';for(let o=0;o<Vo.length;o++){let n=Vo.charAt(o);d(g,v,E,n,n)}let Go='0123456789!@*()-=+";:?/.,';for(let o=0;o<Go.length;o++){let n=Go.charAt(o);d($,v,E,n,n)}let kn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";for(let o=0;o<kn.length;o++){let n=kn.charAt(o);d(g,v,ne,n,n),d($,v,E,n,n)}d(g,A,E,"C","\u2102"),d($,A,E,"C","\u2102"),d(g,A,E,"H","\u210D"),d($,A,E,"H","\u210D"),d(g,A,E,"N","\u2115"),d($,A,E,"N","\u2115"),d(g,A,E,"P","\u2119"),d($,A,E,"P","\u2119"),d(g,A,E,"Q","\u211A"),d($,A,E,"Q","\u211A"),d(g,A,E,"R","\u211D"),d($,A,E,"R","\u211D"),d(g,A,E,"Z","\u2124"),d($,A,E,"Z","\u2124"),d(g,v,ne,"h","\u210E"),d($,v,ne,"h","\u210E");let ie="";for(let o=0;o<kn.length;o++){let n=kn.charAt(o);ie=String.fromCharCode(55349,56320+o),d(g,v,ne,n,ie),d($,v,E,n,ie),ie=String.fromCharCode(55349,56372+o),d(g,v,ne,n,ie),d($,v,E,n,ie),ie=String.fromCharCode(55349,56424+o),d(g,v,ne,n,ie),d($,v,E,n,ie),ie=String.fromCharCode(55349,56580+o),d(g,v,ne,n,ie),d($,v,E,n,ie),ie=String.fromCharCode(55349,56684+o),d(g,v,ne,n,ie),d($,v,E,n,ie),ie=String.fromCharCode(55349,56736+o),d(g,v,ne,n,ie),d($,v,E,n,ie),ie=String.fromCharCode(55349,56788+o),d(g,v,ne,n,ie),d($,v,E,n,ie),ie=String.fromCharCode(55349,56840+o),d(g,v,ne,n,ie),d($,v,E,n,ie),ie=String.fromCharCode(55349,56944+o),d(g,v,ne,n,ie),d($,v,E,n,ie),o<26&&(ie=String.fromCharCode(55349,56632+o),d(g,v,ne,n,ie),d($,v,E,n,ie),ie=String.fromCharCode(55349,56476+o),d(g,v,ne,n,ie),d($,v,E,n,ie))}ie=String.fromCharCode(55349,56668),d(g,v,ne,"k",ie),d($,v,E,"k",ie);for(let o=0;o<10;o++){let n=o.toString();ie=String.fromCharCode(55349,57294+o),d(g,v,ne,n,ie),d($,v,E,n,ie),ie=String.fromCharCode(55349,57314+o),d(g,v,ne,n,ie),d($,v,E,n,ie),ie=String.fromCharCode(55349,57324+o),d(g,v,ne,n,ie),d($,v,E,n,ie),ie=String.fromCharCode(55349,57334+o),d(g,v,ne,n,ie),d($,v,E,n,ie)}let q0="\xD0\xDE\xFE";for(let o=0;o<q0.length;o++){let n=q0.charAt(o);d(g,v,ne,n,n),d($,v,E,n,n)}let Sn=[["mathbf","textbf","Main-Bold"],["mathbf","textbf","Main-Bold"],["mathnormal","textit","Math-Italic"],["mathnormal","textit","Math-Italic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["mathscr","textscr","Script-Regular"],["","",""],["","",""],["","",""],["mathfrak","textfrak","Fraktur-Regular"],["mathfrak","textfrak","Fraktur-Regular"],["mathbb","textbb","AMS-Regular"],["mathbb","textbb","AMS-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathitsf","textitsf","SansSerif-Italic"],["mathitsf","textitsf","SansSerif-Italic"],["","",""],["","",""],["mathtt","texttt","Typewriter-Regular"],["mathtt","texttt","Typewriter-Regular"]],Yo=[["mathbf","textbf","Main-Bold"],["","",""],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathtt","texttt","Typewriter-Regular"]],kh=function(o,n){let s=o.charCodeAt(0),c=o.charCodeAt(1),f=(s-55296)*1024+(c-56320)+65536,m=n==="math"?0:1;if(119808<=f&&f<120484){let x=Math.floor((f-119808)/26);return[Sn[x][2],Sn[x][m]]}else if(120782<=f&&f<=120831){let x=Math.floor((f-120782)/10);return[Yo[x][2],Yo[x][m]]}else{if(f===120485||f===120486)return[Sn[0][2],Sn[0][m]];if(120486<f&&f<120782)return["",""];throw new i("Unsupported character: "+o)}},Mn=function(o,n,s){return Te[s][o]&&Te[s][o].replace&&(o=Te[s][o].replace),{value:o,metrics:B0(o,n,s)}},vt=function(o,n,s,c,f){let m=Mn(o,n,s),x=m.metrics;o=m.value;let w;if(x){let M=x.italic;(s==="text"||c&&c.font==="mathit")&&(M=0),w=new ft(o,x.height,x.depth,M,x.skew,x.width,f)}else typeof console!="undefined"&&console.warn("No character metrics "+("for '"+o+"' in style '"+n+"' and mode '"+s+"'")),w=new ft(o,0,0,0,0,0,f);if(c){w.maxFontSize=c.sizeMultiplier,c.style.isTight()&&w.classes.push("mtight");let M=c.getColor();M&&(w.style.color=M)}return w},Sh=function(o,n,s,c){return c===void 0&&(c=[]),s.font==="boldsymbol"&&Mn(o,"Main-Bold",n).metrics?vt(o,"Main-Bold",n,s,c.concat(["mathbf"])):o==="\\"||Te[n][o].font==="main"?vt(o,"Main-Regular",n,s,c):vt(o,"AMS-Regular",n,s,c.concat(["amsrm"]))},Mh=function(o,n,s,c,f){return f!=="textord"&&Mn(o,"Math-BoldItalic",n).metrics?{fontName:"Math-BoldItalic",fontClass:"boldsymbol"}:{fontName:"Main-Bold",fontClass:"mathbf"}},Ah=function(o,n,s){let c=o.mode,f=o.text,m=["mord"],x=c==="math"||c==="text"&&n.font,w=x?n.font:n.fontFamily,M="",N="";if(f.charCodeAt(0)===55349&&([M,N]=kh(f,c)),M.length>0)return vt(f,M,c,n,m.concat(N));if(w){let D,B;if(w==="boldsymbol"){let R=Mh(f,c,n,m,s);D=R.fontName,B=[R.fontClass]}else x?(D=jo[w].fontName,B=[w]):(D=An(w,n.fontWeight,n.fontShape),B=[w,n.fontWeight,n.fontShape]);if(Mn(f,D,c).metrics)return vt(f,D,c,n,m.concat(B));if($o.hasOwnProperty(f)&&D.slice(0,10)==="Typewriter"){let R=[];for(let H=0;H<f.length;H++)R.push(vt(f[H],D,c,n,m.concat(B)));return Xo(R)}}if(s==="mathord")return vt(f,"Math-Italic",c,n,m.concat(["mathnormal"]));if(s==="textord"){let D=Te[c][f]&&Te[c][f].font;if(D==="ams"){let B=An("amsrm",n.fontWeight,n.fontShape);return vt(f,B,c,n,m.concat("amsrm",n.fontWeight,n.fontShape))}else if(D==="main"||!D){let B=An("textrm",n.fontWeight,n.fontShape);return vt(f,B,c,n,m.concat(n.fontWeight,n.fontShape))}else{let B=An(D,n.fontWeight,n.fontShape);return vt(f,B,c,n,m.concat(B,n.fontWeight,n.fontShape))}}else throw new Error("unexpected type: "+s+" in makeOrd")},_h=(o,n)=>{if(Ut(o.classes)!==Ut(n.classes)||o.skew!==n.skew||o.maxFontSize!==n.maxFontSize)return!1;if(o.classes.length===1){let s=o.classes[0];if(s==="mbin"||s==="mord")return!1}for(let s in o.style)if(o.style.hasOwnProperty(s)&&o.style[s]!==n.style[s])return!1;for(let s in n.style)if(n.style.hasOwnProperty(s)&&o.style[s]!==n.style[s])return!1;return!0},Ch=o=>{for(let n=0;n<o.length-1;n++){let s=o[n],c=o[n+1];s instanceof ft&&c instanceof ft&&_h(s,c)&&(s.text+=c.text,s.height=Math.max(s.height,c.height),s.depth=Math.max(s.depth,c.depth),s.italic=c.italic,o.splice(n+1,1),n--)}return o},P0=function(o){let n=0,s=0,c=0;for(let f=0;f<o.children.length;f++){let m=o.children[f];m.height>n&&(n=m.height),m.depth>s&&(s=m.depth),m.maxFontSize>c&&(c=m.maxFontSize)}o.height=n,o.depth=s,o.maxFontSize=c},Qe=function(o,n,s,c){let f=new Rr(o,n,s,c);return P0(f),f},Uo=(o,n,s,c)=>new Rr(o,n,s,c),Th=function(o,n,s){let c=Qe([o],[],n);return c.height=Math.max(s||n.fontMetrics().defaultRuleThickness,n.minRuleThickness),c.style.borderBottomWidth=U(c.height),c.maxFontSize=1,c},Nh=function(o,n,s,c){let f=new R0(o,n,s,c);return P0(f),f},Xo=function(o){let n=new Fr(o);return P0(n),n},Eh=function(o,n){return o instanceof Fr?Qe([],[o],n):o},zh=function(o){if(o.positionType==="individualShift"){let s=o.children,c=[s[0]],f=-s[0].shift-s[0].elem.depth,m=f;for(let x=1;x<s.length;x++){let w=-s[x].shift-m-s[x].elem.depth,M=w-(s[x-1].elem.height+s[x-1].elem.depth);m=m+w,c.push({type:"kern",size:M}),c.push(s[x])}return{children:c,depth:f}}let n;if(o.positionType==="top"){let s=o.positionData;for(let c=0;c<o.children.length;c++){let f=o.children[c];s-=f.type==="kern"?f.size:f.elem.height+f.elem.depth}n=s}else if(o.positionType==="bottom")n=-o.positionData;else{let s=o.children[0];if(s.type!=="elem")throw new Error('First child must have type "elem".');if(o.positionType==="shift")n=-s.elem.depth-o.positionData;else if(o.positionType==="firstBaseline")n=-s.elem.depth;else throw new Error("Invalid positionType "+o.positionType+".")}return{children:o.children,depth:n}},Dh=function(o,n){let{children:s,depth:c}=zh(o),f=0;for(let H=0;H<s.length;H++){let Z=s[H];if(Z.type==="elem"){let Q=Z.elem;f=Math.max(f,Q.maxFontSize,Q.height)}}f+=2;let m=Qe(["pstrut"],[]);m.style.height=U(f);let x=[],w=c,M=c,N=c;for(let H=0;H<s.length;H++){let Z=s[H];if(Z.type==="kern")N+=Z.size;else{let Q=Z.elem,ue=Z.wrapperClasses||[],ae=Z.wrapperStyle||{},he=Qe(ue,[m,Q],void 0,ae);he.style.top=U(-f-N-Q.depth),Z.marginLeft&&(he.style.marginLeft=Z.marginLeft),Z.marginRight&&(he.style.marginRight=Z.marginRight),x.push(he),N+=Q.height+Q.depth}w=Math.min(w,N),M=Math.max(M,N)}let D=Qe(["vlist"],x);D.style.height=U(M);let B;if(w<0){let H=Qe([],[]),Z=Qe(["vlist"],[H]);Z.style.height=U(-w);let Q=Qe(["vlist-s"],[new ft("\u200B")]);B=[Qe(["vlist-r"],[D,Q]),Qe(["vlist-r"],[Z])]}else B=[Qe(["vlist-r"],[D])];let R=Qe(["vlist-t"],B);return B.length===2&&R.classes.push("vlist-t2"),R.height=M,R.depth=-w,R},Ih=(o,n)=>{let s=Qe(["mspace"],[],n),c=Ce(o,n);return s.style.marginRight=U(c),s},An=function(o,n,s){let c="";switch(o){case"amsrm":c="AMS";break;case"textrm":c="Main";break;case"textsf":c="SansSerif";break;case"texttt":c="Typewriter";break;default:c=o}let f;return n==="textbf"&&s==="textit"?f="BoldItalic":n==="textbf"?f="Bold":n==="textit"?f="Italic":f="Regular",c+"-"+f},jo={mathbf:{variant:"bold",fontName:"Main-Bold"},mathrm:{variant:"normal",fontName:"Main-Regular"},textit:{variant:"italic",fontName:"Main-Italic"},mathit:{variant:"italic",fontName:"Main-Italic"},mathnormal:{variant:"italic",fontName:"Math-Italic"},mathsfit:{variant:"sans-serif-italic",fontName:"SansSerif-Italic"},mathbb:{variant:"double-struck",fontName:"AMS-Regular"},mathcal:{variant:"script",fontName:"Caligraphic-Regular"},mathfrak:{variant:"fraktur",fontName:"Fraktur-Regular"},mathscr:{variant:"script",fontName:"Script-Regular"},mathsf:{variant:"sans-serif",fontName:"SansSerif-Regular"},mathtt:{variant:"monospace",fontName:"Typewriter-Regular"}},Wo={vec:["vec",.471,.714],oiintSize1:["oiintSize1",.957,.499],oiintSize2:["oiintSize2",1.472,.659],oiiintSize1:["oiiintSize1",1.304,.499],oiiintSize2:["oiiintSize2",1.98,.659]};var O={fontMap:jo,makeSymbol:vt,mathsym:Sh,makeSpan:Qe,makeSvgSpan:Uo,makeLineSpan:Th,makeAnchor:Nh,makeFragment:Xo,wrapFragment:Eh,makeVList:Dh,makeOrd:Ah,makeGlue:Ih,staticSvg:function(o,n){let[s,c,f]=Wo[o],m=new Xt(s),x=new It([m],{width:U(c),height:U(f),style:"width:"+U(c),viewBox:"0 0 "+1e3*c+" "+1e3*f,preserveAspectRatio:"xMinYMin"}),w=Uo(["overlay"],[x],n);return w.height=f,w.style.height=U(f),w.style.width=U(c),w},svgData:Wo,tryCombineChars:Ch};let Ne={number:3,unit:"mu"},nr={number:4,unit:"mu"},Ot={number:5,unit:"mu"},Bh={mord:{mop:Ne,mbin:nr,mrel:Ot,minner:Ne},mop:{mord:Ne,mop:Ne,mrel:Ot,minner:Ne},mbin:{mord:nr,mop:nr,mopen:nr,minner:nr},mrel:{mord:Ot,mop:Ot,mopen:Ot,minner:Ot},mopen:{},mclose:{mop:Ne,mbin:nr,mrel:Ot,minner:Ne},mpunct:{mord:Ne,mop:Ne,mrel:Ot,mopen:Ne,mclose:Ne,mpunct:Ne,minner:Ne},minner:{mord:Ne,mop:Ne,mbin:nr,mrel:Ot,mopen:Ne,mpunct:Ne,minner:Ne}},Oh={mord:{mop:Ne},mop:{mord:Ne,mop:Ne},mbin:{},mrel:{},mopen:{},mclose:{mop:Ne},mpunct:{},minner:{mop:Ne}},Ko={},_n={},Cn={};function j(o){let{type:n,names:s,props:c,handler:f,htmlBuilder:m,mathmlBuilder:x}=o,w={type:n,numArgs:c.numArgs,argTypes:c.argTypes,allowedInArgument:!!c.allowedInArgument,allowedInText:!!c.allowedInText,allowedInMath:c.allowedInMath===void 0?!0:c.allowedInMath,numOptionalArgs:c.numOptionalArgs||0,infix:!!c.infix,primitive:!!c.primitive,handler:f};for(let M=0;M<s.length;++M)Ko[s[M]]=w;n&&(m&&(_n[n]=m),x&&(Cn[n]=x))}function ir(o){let{type:n,htmlBuilder:s,mathmlBuilder:c}=o;j({type:n,names:[],props:{numArgs:0},handler(){throw new Error("Should never be called.")},htmlBuilder:s,mathmlBuilder:c})}let Tn=function(o){return o.type==="ordgroup"&&o.body.length===1?o.body[0]:o},Be=function(o){return o.type==="ordgroup"?o.body:[o]},Ft=O.makeSpan,Fh=["leftmost","mbin","mopen","mrel","mop","mpunct"],Rh=["rightmost","mrel","mclose","mpunct"],Lh={display:Y.DISPLAY,text:Y.TEXT,script:Y.SCRIPT,scriptscript:Y.SCRIPTSCRIPT},qh={mord:"mord",mop:"mop",mbin:"mbin",mrel:"mrel",mopen:"mopen",mclose:"mclose",mpunct:"mpunct",minner:"minner"},Le=function(o,n,s,c){c===void 0&&(c=[null,null]);let f=[];for(let N=0;N<o.length;N++){let D=fe(o[N],n);if(D instanceof Fr){let B=D.children;f.push(...B)}else f.push(D)}if(O.tryCombineChars(f),!s)return f;let m=n;if(o.length===1){let N=o[0];N.type==="sizing"?m=n.havingSize(N.size):N.type==="styling"&&(m=n.havingStyle(Lh[N.style]))}let x=Ft([c[0]||"leftmost"],[],n),w=Ft([c[1]||"rightmost"],[],n),M=s==="root";return H0(f,(N,D)=>{let B=D.classes[0],R=N.classes[0];B==="mbin"&&z.contains(Rh,R)?D.classes[0]="mord":R==="mbin"&&z.contains(Fh,B)&&(N.classes[0]="mord")},{node:x},w,M),H0(f,(N,D)=>{let B=V0(D),R=V0(N),H=B&&R?N.hasClass("mtight")?Oh[B][R]:Bh[B][R]:null;if(H)return O.makeGlue(H,m)},{node:x},w,M),f},H0=function(o,n,s,c,f){c&&o.push(c);let m=0;for(;m<o.length;m++){let x=o[m],w=Zo(x);if(w){H0(w.children,n,s,null,f);continue}let M=!x.hasClass("mspace");if(M){let N=n(x,s.node);N&&(s.insertAfter?s.insertAfter(N):(o.unshift(N),m++))}M?s.node=x:f&&x.hasClass("newline")&&(s.node=Ft(["leftmost"])),s.insertAfter=(N=>D=>{o.splice(N+1,0,D),m++})(m)}c&&o.pop()},Zo=function(o){return o instanceof Fr||o instanceof R0||o instanceof Rr&&o.hasClass("enclosing")?o:null},$0=function(o,n){let s=Zo(o);if(s){let c=s.children;if(c.length){if(n==="right")return $0(c[c.length-1],"right");if(n==="left")return $0(c[0],"left")}}return o},V0=function(o,n){return o?(n&&(o=$0(o,n)),qh[o.classes[0]]||null):null},Lr=function(o,n){let s=["nulldelimiter"].concat(o.baseSizingClasses());return Ft(n.concat(s))},fe=function(o,n,s){if(!o)return Ft();if(_n[o.type]){let c=_n[o.type](o,n);if(s&&n.size!==s.size){c=Ft(n.sizingClasses(s),[c],n);let f=n.sizeMultiplier/s.sizeMultiplier;c.height*=f,c.depth*=f}return c}else throw new i("Got group of unknown type: '"+o.type+"'")};function Nn(o,n){let s=Ft(["base"],o,n),c=Ft(["strut"]);return c.style.height=U(s.height+s.depth),s.depth&&(c.style.verticalAlign=U(-s.depth)),s.children.unshift(c),s}function G0(o,n){let s=null;o.length===1&&o[0].type==="tag"&&(s=o[0].tag,o=o[0].body);let c=Le(o,n,"root"),f;c.length===2&&c[1].hasClass("tag")&&(f=c.pop());let m=[],x=[];for(let N=0;N<c.length;N++)if(x.push(c[N]),c[N].hasClass("mbin")||c[N].hasClass("mrel")||c[N].hasClass("allowbreak")){let D=!1;for(;N<c.length-1&&c[N+1].hasClass("mspace")&&!c[N+1].hasClass("newline");)N++,x.push(c[N]),c[N].hasClass("nobreak")&&(D=!0);D||(m.push(Nn(x,n)),x=[])}else c[N].hasClass("newline")&&(x.pop(),x.length>0&&(m.push(Nn(x,n)),x=[]),m.push(c[N]));x.length>0&&m.push(Nn(x,n));let w;s?(w=Nn(Le(s,n,!0)),w.classes=["tag"],m.push(w)):f&&m.push(f);let M=Ft(["katex-html"],m);if(M.setAttribute("aria-hidden","true"),w){let N=w.children[0];N.style.height=U(M.height+M.depth),M.depth&&(N.style.verticalAlign=U(-M.depth))}return M}function Jo(o){return new Fr(o)}class ct{constructor(n,s,c){this.type=void 0,this.attributes=void 0,this.children=void 0,this.classes=void 0,this.type=n,this.attributes={},this.children=s||[],this.classes=c||[]}setAttribute(n,s){this.attributes[n]=s}getAttribute(n){return this.attributes[n]}toNode(){let n=document.createElementNS("http://www.w3.org/1998/Math/MathML",this.type);for(let s in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,s)&&n.setAttribute(s,this.attributes[s]);this.classes.length>0&&(n.className=Ut(this.classes));for(let s=0;s<this.children.length;s++)if(this.children[s]instanceof _t&&this.children[s+1]instanceof _t){let c=this.children[s].toText()+this.children[++s].toText();for(;this.children[s+1]instanceof _t;)c+=this.children[++s].toText();n.appendChild(new _t(c).toNode())}else n.appendChild(this.children[s].toNode());return n}toMarkup(){let n="<"+this.type;for(let s in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,s)&&(n+=" "+s+'="',n+=z.escape(this.attributes[s]),n+='"');this.classes.length>0&&(n+=' class ="'+z.escape(Ut(this.classes))+'"'),n+=">";for(let s=0;s<this.children.length;s++)n+=this.children[s].toMarkup();return n+="</"+this.type+">",n}toText(){return this.children.map(n=>n.toText()).join("")}}class _t{constructor(n){this.text=void 0,this.text=n}toNode(){return document.createTextNode(this.text)}toMarkup(){return z.escape(this.toText())}toText(){return this.text}}class Ph{constructor(n){this.width=void 0,this.character=void 0,this.width=n,n>=.05555&&n<=.05556?this.character="\u200A":n>=.1666&&n<=.1667?this.character="\u2009":n>=.2222&&n<=.2223?this.character="\u2005":n>=.2777&&n<=.2778?this.character="\u2005\u200A":n>=-.05556&&n<=-.05555?this.character="\u200A\u2063":n>=-.1667&&n<=-.1666?this.character="\u2009\u2063":n>=-.2223&&n<=-.2222?this.character="\u205F\u2063":n>=-.2778&&n<=-.2777?this.character="\u2005\u2063":this.character=null}toNode(){if(this.character)return document.createTextNode(this.character);{let n=document.createElementNS("http://www.w3.org/1998/Math/MathML","mspace");return n.setAttribute("width",U(this.width)),n}}toMarkup(){return this.character?"<mtext>"+this.character+"</mtext>":'<mspace width="'+U(this.width)+'"/>'}toText(){return this.character?this.character:" "}}var V={MathNode:ct,TextNode:_t,SpaceNode:Ph,newDocumentFragment:Jo};let dt=function(o,n,s){return Te[n][o]&&Te[n][o].replace&&o.charCodeAt(0)!==55349&&!($o.hasOwnProperty(o)&&s&&(s.fontFamily&&s.fontFamily.slice(4,6)==="tt"||s.font&&s.font.slice(4,6)==="tt"))&&(o=Te[n][o].replace),new V.TextNode(o)},Y0=function(o){return o.length===1?o[0]:new V.MathNode("mrow",o)},U0=function(o,n){if(n.fontFamily==="texttt")return"monospace";if(n.fontFamily==="textsf")return n.fontShape==="textit"&&n.fontWeight==="textbf"?"sans-serif-bold-italic":n.fontShape==="textit"?"sans-serif-italic":n.fontWeight==="textbf"?"bold-sans-serif":"sans-serif";if(n.fontShape==="textit"&&n.fontWeight==="textbf")return"bold-italic";if(n.fontShape==="textit")return"italic";if(n.fontWeight==="textbf")return"bold";let s=n.font;if(!s||s==="mathnormal")return null;let c=o.mode;if(s==="mathit")return"italic";if(s==="boldsymbol")return o.type==="textord"?"bold":"bold-italic";if(s==="mathbf")return"bold";if(s==="mathbb")return"double-struck";if(s==="mathsfit")return"sans-serif-italic";if(s==="mathfrak")return"fraktur";if(s==="mathscr"||s==="mathcal")return"script";if(s==="mathsf")return"sans-serif";if(s==="mathtt")return"monospace";let f=o.text;if(z.contains(["\\imath","\\jmath"],f))return null;Te[c][f]&&Te[c][f].replace&&(f=Te[c][f].replace);let m=O.fontMap[s].fontName;return B0(f,m,c)?O.fontMap[s].variant:null};function X0(o){if(!o)return!1;if(o.type==="mi"&&o.children.length===1){let n=o.children[0];return n instanceof _t&&n.text==="."}else if(o.type==="mo"&&o.children.length===1&&o.getAttribute("separator")==="true"&&o.getAttribute("lspace")==="0em"&&o.getAttribute("rspace")==="0em"){let n=o.children[0];return n instanceof _t&&n.text===","}else return!1}let et=function(o,n,s){if(o.length===1){let m=ye(o[0],n);return s&&m instanceof ct&&m.type==="mo"&&(m.setAttribute("lspace","0em"),m.setAttribute("rspace","0em")),[m]}let c=[],f;for(let m=0;m<o.length;m++){let x=ye(o[m],n);if(x instanceof ct&&f instanceof ct){if(x.type==="mtext"&&f.type==="mtext"&&x.getAttribute("mathvariant")===f.getAttribute("mathvariant")){f.children.push(...x.children);continue}else if(x.type==="mn"&&f.type==="mn"){f.children.push(...x.children);continue}else if(X0(x)&&f.type==="mn"){f.children.push(...x.children);continue}else if(x.type==="mn"&&X0(f))x.children=[...f.children,...x.children],c.pop();else if((x.type==="msup"||x.type==="msub")&&x.children.length>=1&&(f.type==="mn"||X0(f))){let w=x.children[0];w instanceof ct&&w.type==="mn"&&(w.children=[...f.children,...w.children],c.pop())}else if(f.type==="mi"&&f.children.length===1){let w=f.children[0];if(w instanceof _t&&w.text==="\u0338"&&(x.type==="mo"||x.type==="mi"||x.type==="mn")){let M=x.children[0];M instanceof _t&&M.text.length>0&&(M.text=M.text.slice(0,1)+"\u0338"+M.text.slice(1),c.pop())}}}c.push(x),f=x}return c},jt=function(o,n,s){return Y0(et(o,n,s))},ye=function(o,n){if(!o)return new V.MathNode("mrow");if(Cn[o.type])return Cn[o.type](o,n);throw new i("Got group of unknown type: '"+o.type+"'")};function Qo(o,n,s,c,f){let m=et(o,s),x;m.length===1&&m[0]instanceof ct&&z.contains(["mrow","mtable"],m[0].type)?x=m[0]:x=new V.MathNode("mrow",m);let w=new V.MathNode("annotation",[new V.TextNode(n)]);w.setAttribute("encoding","application/x-tex");let M=new V.MathNode("semantics",[x,w]),N=new V.MathNode("math",[M]);N.setAttribute("xmlns","http://www.w3.org/1998/Math/MathML"),c&&N.setAttribute("display","block");let D=f?"katex":"katex-mathml";return O.makeSpan([D],[N])}let el=function(o){return new ph({style:o.displayMode?Y.DISPLAY:Y.TEXT,maxSize:o.maxSize,minRuleThickness:o.minRuleThickness})},tl=function(o,n){if(n.displayMode){let s=["katex-display"];n.leqno&&s.push("leqno"),n.fleqn&&s.push("fleqn"),o=O.makeSpan(s,[o])}return o},Hh=function(o,n,s){let c=el(s),f;if(s.output==="mathml")return Qo(o,n,c,s.displayMode,!0);if(s.output==="html"){let m=G0(o,c);f=O.makeSpan(["katex"],[m])}else{let m=Qo(o,n,c,s.displayMode,!1),x=G0(o,c);f=O.makeSpan(["katex"],[m,x])}return tl(f,s)},$h=function(o,n,s){let c=el(s),f=G0(o,c),m=O.makeSpan(["katex"],[f]);return tl(m,s)};var l6=null;let Vh={widehat:"^",widecheck:"\u02C7",widetilde:"~",utilde:"~",overleftarrow:"\u2190",underleftarrow:"\u2190",xleftarrow:"\u2190",overrightarrow:"\u2192",underrightarrow:"\u2192",xrightarrow:"\u2192",underbrace:"\u23DF",overbrace:"\u23DE",overgroup:"\u23E0",undergroup:"\u23E1",overleftrightarrow:"\u2194",underleftrightarrow:"\u2194",xleftrightarrow:"\u2194",Overrightarrow:"\u21D2",xRightarrow:"\u21D2",overleftharpoon:"\u21BC",xleftharpoonup:"\u21BC",overrightharpoon:"\u21C0",xrightharpoonup:"\u21C0",xLeftarrow:"\u21D0",xLeftrightarrow:"\u21D4",xhookleftarrow:"\u21A9",xhookrightarrow:"\u21AA",xmapsto:"\u21A6",xrightharpoondown:"\u21C1",xleftharpoondown:"\u21BD",xrightleftharpoons:"\u21CC",xleftrightharpoons:"\u21CB",xtwoheadleftarrow:"\u219E",xtwoheadrightarrow:"\u21A0",xlongequal:"=",xtofrom:"\u21C4",xrightleftarrows:"\u21C4",xrightequilibrium:"\u21CC",xleftequilibrium:"\u21CB","\\cdrightarrow":"\u2192","\\cdleftarrow":"\u2190","\\cdlongequal":"="},Gh=function(o){let n=new V.MathNode("mo",[new V.TextNode(Vh[o.replace(/^\\/,"")])]);return n.setAttribute("stretchy","true"),n},Yh={overrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],overleftarrow:[["leftarrow"],.888,522,"xMinYMin"],underrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],underleftarrow:[["leftarrow"],.888,522,"xMinYMin"],xrightarrow:[["rightarrow"],1.469,522,"xMaxYMin"],"\\cdrightarrow":[["rightarrow"],3,522,"xMaxYMin"],xleftarrow:[["leftarrow"],1.469,522,"xMinYMin"],"\\cdleftarrow":[["leftarrow"],3,522,"xMinYMin"],Overrightarrow:[["doublerightarrow"],.888,560,"xMaxYMin"],xRightarrow:[["doublerightarrow"],1.526,560,"xMaxYMin"],xLeftarrow:[["doubleleftarrow"],1.526,560,"xMinYMin"],overleftharpoon:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoonup:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoondown:[["leftharpoondown"],.888,522,"xMinYMin"],overrightharpoon:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoonup:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoondown:[["rightharpoondown"],.888,522,"xMaxYMin"],xlongequal:[["longequal"],.888,334,"xMinYMin"],"\\cdlongequal":[["longequal"],3,334,"xMinYMin"],xtwoheadleftarrow:[["twoheadleftarrow"],.888,334,"xMinYMin"],xtwoheadrightarrow:[["twoheadrightarrow"],.888,334,"xMaxYMin"],overleftrightarrow:[["leftarrow","rightarrow"],.888,522],overbrace:[["leftbrace","midbrace","rightbrace"],1.6,548],underbrace:[["leftbraceunder","midbraceunder","rightbraceunder"],1.6,548],underleftrightarrow:[["leftarrow","rightarrow"],.888,522],xleftrightarrow:[["leftarrow","rightarrow"],1.75,522],xLeftrightarrow:[["doubleleftarrow","doublerightarrow"],1.75,560],xrightleftharpoons:[["leftharpoondownplus","rightharpoonplus"],1.75,716],xleftrightharpoons:[["leftharpoonplus","rightharpoondownplus"],1.75,716],xhookleftarrow:[["leftarrow","righthook"],1.08,522],xhookrightarrow:[["lefthook","rightarrow"],1.08,522],overlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],underlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],overgroup:[["leftgroup","rightgroup"],.888,342],undergroup:[["leftgroupunder","rightgroupunder"],.888,342],xmapsto:[["leftmapsto","rightarrow"],1.5,522],xtofrom:[["leftToFrom","rightToFrom"],1.75,528],xrightleftarrows:[["baraboveleftarrow","rightarrowabovebar"],1.75,901],xrightequilibrium:[["baraboveshortleftharpoon","rightharpoonaboveshortbar"],1.75,716],xleftequilibrium:[["shortbaraboveleftharpoon","shortrightharpoonabovebar"],1.75,716]},Uh=function(o){return o.type==="ordgroup"?o.body.length:1};var Rt={encloseSpan:function(o,n,s,c,f){let m,x=o.height+o.depth+s+c;if(/fbox|color|angl/.test(n)){if(m=O.makeSpan(["stretchy",n],[],f),n==="fbox"){let w=f.color&&f.getColor();w&&(m.style.borderColor=w)}}else{let w=[];/^[bx]cancel$/.test(n)&&w.push(new L0({x1:"0",y1:"0",x2:"100%",y2:"100%","stroke-width":"0.046em"})),/^x?cancel$/.test(n)&&w.push(new L0({x1:"0",y1:"100%",x2:"100%",y2:"0","stroke-width":"0.046em"}));let M=new It(w,{width:"100%",height:U(x)});m=O.makeSvgSpan([],[M],f)}return m.height=x,m.style.height=U(x),m},mathMLnode:Gh,svgSpan:function(o,n){function s(){let x=4e5,w=o.label.slice(1);if(z.contains(["widehat","widecheck","widetilde","utilde"],w)){let N=Uh(o.base),D,B,R;if(N>5)w==="widehat"||w==="widecheck"?(D=420,x=2364,R=.42,B=w+"4"):(D=312,x=2340,R=.34,B="tilde4");else{let Q=[1,1,2,2,3,3][N];w==="widehat"||w==="widecheck"?(x=[0,1062,2364,2364,2364][Q],D=[0,239,300,360,420][Q],R=[0,.24,.3,.3,.36,.42][Q],B=w+Q):(x=[0,600,1033,2339,2340][Q],D=[0,260,286,306,312][Q],R=[0,.26,.286,.3,.306,.34][Q],B="tilde"+Q)}let H=new Xt(B),Z=new It([H],{width:"100%",height:U(R),viewBox:"0 0 "+x+" "+D,preserveAspectRatio:"none"});return{span:O.makeSvgSpan([],[Z],n),minWidth:0,height:R}}else{let M=[],N=Yh[w],[D,B,R]=N,H=R/1e3,Z=D.length,Q,ue;if(Z===1){let ae=N[3];Q=["hide-tail"],ue=[ae]}else if(Z===2)Q=["halfarrow-left","halfarrow-right"],ue=["xMinYMin","xMaxYMin"];else if(Z===3)Q=["brace-left","brace-center","brace-right"],ue=["xMinYMin","xMidYMin","xMaxYMin"];else throw new Error(`Correct katexImagesData or update code here to support
                    `+Z+" children.");for(let ae=0;ae<Z;ae++){let he=new Xt(D[ae]),pe=new It([he],{width:"400em",height:U(H),viewBox:"0 0 "+x+" "+R,preserveAspectRatio:ue[ae]+" slice"}),we=O.makeSvgSpan([Q[ae]],[pe],n);if(Z===1)return{span:we,minWidth:B,height:H};we.style.height=U(H),M.push(we)}return{span:O.makeSpan(["stretchy"],M,n),minWidth:B,height:H}}}let{span:c,minWidth:f,height:m}=s();return c.height=m,c.style.height=U(m),f>0&&(c.style.minWidth=U(f)),c}};function le(o,n){if(!o||o.type!==n)throw new Error("Expected node of type "+n+", but got "+(o?"node of type "+o.type:String(o)));return o}function j0(o){let n=En(o);if(!n)throw new Error("Expected node of symbol group type, but got "+(o?"node of type "+o.type:String(o)));return n}function En(o){return o&&(o.type==="atom"||wh.hasOwnProperty(o.type))?o:null}let W0=(o,n)=>{let s,c,f;o&&o.type==="supsub"?(c=le(o.base,"accent"),s=c.base,o.base=s,f=bh(fe(o,n)),o.base=c):(c=le(o,"accent"),s=c.base);let m=fe(s,n.havingCrampedStyle()),x=c.isShifty&&z.isCharacterBox(s),w=0;if(x){let R=z.getBaseElem(s),H=fe(R,n.havingCrampedStyle());w=Ho(H).skew}let M=c.label==="\\c",N=M?m.height+m.depth:Math.min(m.height,n.fontMetrics().xHeight),D;if(c.isStretchy)D=Rt.svgSpan(c,n),D=O.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:m},{type:"elem",elem:D,wrapperClasses:["svg-align"],wrapperStyle:w>0?{width:"calc(100% - "+U(2*w)+")",marginLeft:U(2*w)}:void 0}]},n);else{let R,H;c.label==="\\vec"?(R=O.staticSvg("vec",n),H=O.svgData.vec[1]):(R=O.makeOrd({mode:c.mode,text:c.label},n,"textord"),R=Ho(R),R.italic=0,H=R.width,M&&(N+=R.depth)),D=O.makeSpan(["accent-body"],[R]);let Z=c.label==="\\textcircled";Z&&(D.classes.push("accent-full"),N=m.height);let Q=w;Z||(Q-=H/2),D.style.left=U(Q),c.label==="\\textcircled"&&(D.style.top=".2em"),D=O.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:m},{type:"kern",size:-N},{type:"elem",elem:D}]},n)}let B=O.makeSpan(["mord","accent"],[D],n);return f?(f.children[0]=B,f.height=Math.max(B.height,f.height),f.classes[0]="mord",f):B},rl=(o,n)=>{let s=o.isStretchy?Rt.mathMLnode(o.label):new V.MathNode("mo",[dt(o.label,o.mode)]),c=new V.MathNode("mover",[ye(o.base,n),s]);return c.setAttribute("accent","true"),c},Xh=new RegExp(["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring"].map(o=>"\\"+o).join("|"));j({type:"accent",names:["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring","\\widecheck","\\widehat","\\widetilde","\\overrightarrow","\\overleftarrow","\\Overrightarrow","\\overleftrightarrow","\\overgroup","\\overlinesegment","\\overleftharpoon","\\overrightharpoon"],props:{numArgs:1},handler:(o,n)=>{let s=Tn(n[0]),c=!Xh.test(o.funcName),f=!c||o.funcName==="\\widehat"||o.funcName==="\\widetilde"||o.funcName==="\\widecheck";return{type:"accent",mode:o.parser.mode,label:o.funcName,isStretchy:c,isShifty:f,base:s}},htmlBuilder:W0,mathmlBuilder:rl}),j({type:"accent",names:["\\'","\\`","\\^","\\~","\\=","\\u","\\.",'\\"',"\\c","\\r","\\H","\\v","\\textcircled"],props:{numArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["primitive"]},handler:(o,n)=>{let s=n[0],c=o.parser.mode;return c==="math"&&(o.parser.settings.reportNonstrict("mathVsTextAccents","LaTeX's accent "+o.funcName+" works only in text mode"),c="text"),{type:"accent",mode:c,label:o.funcName,isStretchy:!1,isShifty:!0,base:s}},htmlBuilder:W0,mathmlBuilder:rl}),j({type:"accentUnder",names:["\\underleftarrow","\\underrightarrow","\\underleftrightarrow","\\undergroup","\\underlinesegment","\\utilde"],props:{numArgs:1},handler:(o,n)=>{let{parser:s,funcName:c}=o,f=n[0];return{type:"accentUnder",mode:s.mode,label:c,base:f}},htmlBuilder:(o,n)=>{let s=fe(o.base,n),c=Rt.svgSpan(o,n),f=o.label==="\\utilde"?.12:0,m=O.makeVList({positionType:"top",positionData:s.height,children:[{type:"elem",elem:c,wrapperClasses:["svg-align"]},{type:"kern",size:f},{type:"elem",elem:s}]},n);return O.makeSpan(["mord","accentunder"],[m],n)},mathmlBuilder:(o,n)=>{let s=Rt.mathMLnode(o.label),c=new V.MathNode("munder",[ye(o.base,n),s]);return c.setAttribute("accentunder","true"),c}});let zn=o=>{let n=new V.MathNode("mpadded",o?[o]:[]);return n.setAttribute("width","+0.6em"),n.setAttribute("lspace","0.3em"),n};j({type:"xArrow",names:["\\xleftarrow","\\xrightarrow","\\xLeftarrow","\\xRightarrow","\\xleftrightarrow","\\xLeftrightarrow","\\xhookleftarrow","\\xhookrightarrow","\\xmapsto","\\xrightharpoondown","\\xrightharpoonup","\\xleftharpoondown","\\xleftharpoonup","\\xrightleftharpoons","\\xleftrightharpoons","\\xlongequal","\\xtwoheadrightarrow","\\xtwoheadleftarrow","\\xtofrom","\\xrightleftarrows","\\xrightequilibrium","\\xleftequilibrium","\\\\cdrightarrow","\\\\cdleftarrow","\\\\cdlongequal"],props:{numArgs:1,numOptionalArgs:1},handler(o,n,s){let{parser:c,funcName:f}=o;return{type:"xArrow",mode:c.mode,label:f,body:n[0],below:s[0]}},htmlBuilder(o,n){let s=n.style,c=n.havingStyle(s.sup()),f=O.wrapFragment(fe(o.body,c,n),n),m=o.label.slice(0,2)==="\\x"?"x":"cd";f.classes.push(m+"-arrow-pad");let x;o.below&&(c=n.havingStyle(s.sub()),x=O.wrapFragment(fe(o.below,c,n),n),x.classes.push(m+"-arrow-pad"));let w=Rt.svgSpan(o,n),M=-n.fontMetrics().axisHeight+.5*w.height,N=-n.fontMetrics().axisHeight-.5*w.height-.111;(f.depth>.25||o.label==="\\xleftequilibrium")&&(N-=f.depth);let D;if(x){let B=-n.fontMetrics().axisHeight+x.height+.5*w.height+.111;D=O.makeVList({positionType:"individualShift",children:[{type:"elem",elem:f,shift:N},{type:"elem",elem:w,shift:M},{type:"elem",elem:x,shift:B}]},n)}else D=O.makeVList({positionType:"individualShift",children:[{type:"elem",elem:f,shift:N},{type:"elem",elem:w,shift:M}]},n);return D.children[0].children[0].children[1].classes.push("svg-align"),O.makeSpan(["mrel","x-arrow"],[D],n)},mathmlBuilder(o,n){let s=Rt.mathMLnode(o.label);s.setAttribute("minsize",o.label.charAt(0)==="x"?"1.75em":"3.0em");let c;if(o.body){let f=zn(ye(o.body,n));if(o.below){let m=zn(ye(o.below,n));c=new V.MathNode("munderover",[s,m,f])}else c=new V.MathNode("mover",[s,f])}else if(o.below){let f=zn(ye(o.below,n));c=new V.MathNode("munder",[s,f])}else c=zn(),c=new V.MathNode("mover",[s,c]);return c}});let jh=O.makeSpan;function nl(o,n){let s=Le(o.body,n,!0);return jh([o.mclass],s,n)}function il(o,n){let s,c=et(o.body,n);return o.mclass==="minner"?s=new V.MathNode("mpadded",c):o.mclass==="mord"?o.isCharacterBox?(s=c[0],s.type="mi"):s=new V.MathNode("mi",c):(o.isCharacterBox?(s=c[0],s.type="mo"):s=new V.MathNode("mo",c),o.mclass==="mbin"?(s.attributes.lspace="0.22em",s.attributes.rspace="0.22em"):o.mclass==="mpunct"?(s.attributes.lspace="0em",s.attributes.rspace="0.17em"):o.mclass==="mopen"||o.mclass==="mclose"?(s.attributes.lspace="0em",s.attributes.rspace="0em"):o.mclass==="minner"&&(s.attributes.lspace="0.0556em",s.attributes.width="+0.1111em")),s}j({type:"mclass",names:["\\mathord","\\mathbin","\\mathrel","\\mathopen","\\mathclose","\\mathpunct","\\mathinner"],props:{numArgs:1,primitive:!0},handler(o,n){let{parser:s,funcName:c}=o,f=n[0];return{type:"mclass",mode:s.mode,mclass:"m"+c.slice(5),body:Be(f),isCharacterBox:z.isCharacterBox(f)}},htmlBuilder:nl,mathmlBuilder:il});let Dn=o=>{let n=o.type==="ordgroup"&&o.body.length?o.body[0]:o;return n.type==="atom"&&(n.family==="bin"||n.family==="rel")?"m"+n.family:"mord"};j({type:"mclass",names:["\\@binrel"],props:{numArgs:2},handler(o,n){let{parser:s}=o;return{type:"mclass",mode:s.mode,mclass:Dn(n[0]),body:Be(n[1]),isCharacterBox:z.isCharacterBox(n[1])}}}),j({type:"mclass",names:["\\stackrel","\\overset","\\underset"],props:{numArgs:2},handler(o,n){let{parser:s,funcName:c}=o,f=n[1],m=n[0],x;c!=="\\stackrel"?x=Dn(f):x="mrel";let w={type:"op",mode:f.mode,limits:!0,alwaysHandleSupSub:!0,parentIsSupSub:!1,symbol:!1,suppressBaseShift:c!=="\\stackrel",body:Be(f)},M={type:"supsub",mode:m.mode,base:w,sup:c==="\\underset"?null:m,sub:c==="\\underset"?m:null};return{type:"mclass",mode:s.mode,mclass:x,body:[M],isCharacterBox:z.isCharacterBox(M)}},htmlBuilder:nl,mathmlBuilder:il}),j({type:"pmb",names:["\\pmb"],props:{numArgs:1,allowedInText:!0},handler(o,n){let{parser:s}=o;return{type:"pmb",mode:s.mode,mclass:Dn(n[0]),body:Be(n[0])}},htmlBuilder(o,n){let s=Le(o.body,n,!0),c=O.makeSpan([o.mclass],s,n);return c.style.textShadow="0.02em 0.01em 0.04px",c},mathmlBuilder(o,n){let s=et(o.body,n),c=new V.MathNode("mstyle",s);return c.setAttribute("style","text-shadow: 0.02em 0.01em 0.04px"),c}});let Wh={">":"\\\\cdrightarrow","<":"\\\\cdleftarrow","=":"\\\\cdlongequal",A:"\\uparrow",V:"\\downarrow","|":"\\Vert",".":"no arrow"},ol=()=>({type:"styling",body:[],mode:"math",style:"display"}),ll=o=>o.type==="textord"&&o.text==="@",Kh=(o,n)=>(o.type==="mathord"||o.type==="atom")&&o.text===n;function Zh(o,n,s){let c=Wh[o];switch(c){case"\\\\cdrightarrow":case"\\\\cdleftarrow":return s.callFunction(c,[n[0]],[n[1]]);case"\\uparrow":case"\\downarrow":{let f=s.callFunction("\\\\cdleft",[n[0]],[]),m={type:"atom",text:c,mode:"math",family:"rel"},x=s.callFunction("\\Big",[m],[]),w=s.callFunction("\\\\cdright",[n[1]],[]),M={type:"ordgroup",mode:"math",body:[f,x,w]};return s.callFunction("\\\\cdparent",[M],[])}case"\\\\cdlongequal":return s.callFunction("\\\\cdlongequal",[],[]);case"\\Vert":{let f={type:"textord",text:"\\Vert",mode:"math"};return s.callFunction("\\Big",[f],[])}default:return{type:"textord",text:" ",mode:"math"}}}function Jh(o){let n=[];for(o.gullet.beginGroup(),o.gullet.macros.set("\\cr","\\\\\\relax"),o.gullet.beginGroup();;){n.push(o.parseExpression(!1,"\\\\")),o.gullet.endGroup(),o.gullet.beginGroup();let m=o.fetch().text;if(m==="&"||m==="\\\\")o.consume();else if(m==="\\end"){n[n.length-1].length===0&&n.pop();break}else throw new i("Expected \\\\ or \\cr or \\end",o.nextToken)}let s=[],c=[s];for(let m=0;m<n.length;m++){let x=n[m],w=ol();for(let M=0;M<x.length;M++)if(!ll(x[M]))w.body.push(x[M]);else{s.push(w),M+=1;let N=j0(x[M]).text,D=new Array(2);if(D[0]={type:"ordgroup",mode:"math",body:[]},D[1]={type:"ordgroup",mode:"math",body:[]},!("=|.".indexOf(N)>-1))if("<>AV".indexOf(N)>-1)for(let H=0;H<2;H++){let Z=!0;for(let Q=M+1;Q<x.length;Q++){if(Kh(x[Q],N)){Z=!1,M=Q;break}if(ll(x[Q]))throw new i("Missing a "+N+" character to complete a CD arrow.",x[Q]);D[H].body.push(x[Q])}if(Z)throw new i("Missing a "+N+" character to complete a CD arrow.",x[M])}else throw new i('Expected one of "<>AV=|." after @',x[M]);let R={type:"styling",body:[Zh(N,D,o)],mode:"math",style:"display"};s.push(R),w=ol()}m%2===0?s.push(w):s.shift(),s=[],c.push(s)}o.gullet.endGroup(),o.gullet.endGroup();let f=new Array(c[0].length).fill({type:"align",align:"c",pregap:.25,postgap:.25});return{type:"array",mode:"math",body:c,arraystretch:1,addJot:!0,rowGaps:[null],cols:f,colSeparationType:"CD",hLinesBeforeRow:new Array(c.length+1).fill([])}}j({type:"cdlabel",names:["\\\\cdleft","\\\\cdright"],props:{numArgs:1},handler(o,n){let{parser:s,funcName:c}=o;return{type:"cdlabel",mode:s.mode,side:c.slice(4),label:n[0]}},htmlBuilder(o,n){let s=n.havingStyle(n.style.sup()),c=O.wrapFragment(fe(o.label,s,n),n);return c.classes.push("cd-label-"+o.side),c.style.bottom=U(.8-c.depth),c.height=0,c.depth=0,c},mathmlBuilder(o,n){let s=new V.MathNode("mrow",[ye(o.label,n)]);return s=new V.MathNode("mpadded",[s]),s.setAttribute("width","0"),o.side==="left"&&s.setAttribute("lspace","-1width"),s.setAttribute("voffset","0.7em"),s=new V.MathNode("mstyle",[s]),s.setAttribute("displaystyle","false"),s.setAttribute("scriptlevel","1"),s}}),j({type:"cdlabelparent",names:["\\\\cdparent"],props:{numArgs:1},handler(o,n){let{parser:s}=o;return{type:"cdlabelparent",mode:s.mode,fragment:n[0]}},htmlBuilder(o,n){let s=O.wrapFragment(fe(o.fragment,n),n);return s.classes.push("cd-vert-arrow"),s},mathmlBuilder(o,n){return new V.MathNode("mrow",[ye(o.fragment,n)])}}),j({type:"textord",names:["\\@char"],props:{numArgs:1,allowedInText:!0},handler(o,n){let{parser:s}=o,f=le(n[0],"ordgroup").body,m="";for(let M=0;M<f.length;M++){let N=le(f[M],"textord");m+=N.text}let x=parseInt(m),w;if(isNaN(x))throw new i("\\@char has non-numeric argument "+m);if(x<0||x>=1114111)throw new i("\\@char with invalid code point "+m);return x<=65535?w=String.fromCharCode(x):(x-=65536,w=String.fromCharCode((x>>10)+55296,(x&1023)+56320)),{type:"textord",mode:s.mode,text:w}}});let sl=(o,n)=>{let s=Le(o.body,n.withColor(o.color),!1);return O.makeFragment(s)},al=(o,n)=>{let s=et(o.body,n.withColor(o.color)),c=new V.MathNode("mstyle",s);return c.setAttribute("mathcolor",o.color),c};j({type:"color",names:["\\textcolor"],props:{numArgs:2,allowedInText:!0,argTypes:["color","original"]},handler(o,n){let{parser:s}=o,c=le(n[0],"color-token").color,f=n[1];return{type:"color",mode:s.mode,color:c,body:Be(f)}},htmlBuilder:sl,mathmlBuilder:al}),j({type:"color",names:["\\color"],props:{numArgs:1,allowedInText:!0,argTypes:["color"]},handler(o,n){let{parser:s,breakOnTokenText:c}=o,f=le(n[0],"color-token").color;s.gullet.macros.set("\\current@color",f);let m=s.parseExpression(!0,c);return{type:"color",mode:s.mode,color:f,body:m}},htmlBuilder:sl,mathmlBuilder:al}),j({type:"cr",names:["\\\\"],props:{numArgs:0,numOptionalArgs:0,allowedInText:!0},handler(o,n,s){let{parser:c}=o,f=c.gullet.future().text==="["?c.parseSizeGroup(!0):null,m=!c.settings.displayMode||!c.settings.useStrictBehavior("newLineInDisplayMode","In LaTeX, \\\\ or \\newline does nothing in display mode");return{type:"cr",mode:c.mode,newLine:m,size:f&&le(f,"size").value}},htmlBuilder(o,n){let s=O.makeSpan(["mspace"],[],n);return o.newLine&&(s.classes.push("newline"),o.size&&(s.style.marginTop=U(Ce(o.size,n)))),s},mathmlBuilder(o,n){let s=new V.MathNode("mspace");return o.newLine&&(s.setAttribute("linebreak","newline"),o.size&&s.setAttribute("height",U(Ce(o.size,n)))),s}});let K0={"\\global":"\\global","\\long":"\\\\globallong","\\\\globallong":"\\\\globallong","\\def":"\\gdef","\\gdef":"\\gdef","\\edef":"\\xdef","\\xdef":"\\xdef","\\let":"\\\\globallet","\\futurelet":"\\\\globalfuture"},cl=o=>{let n=o.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(n))throw new i("Expected a control sequence",o);return n},Qh=o=>{let n=o.gullet.popToken();return n.text==="="&&(n=o.gullet.popToken(),n.text===" "&&(n=o.gullet.popToken())),n},ul=(o,n,s,c)=>{let f=o.gullet.macros.get(s.text);f==null&&(s.noexpand=!0,f={tokens:[s],numArgs:0,unexpandable:!o.gullet.isExpandable(s.text)}),o.gullet.macros.set(n,f,c)};j({type:"internal",names:["\\global","\\long","\\\\globallong"],props:{numArgs:0,allowedInText:!0},handler(o){let{parser:n,funcName:s}=o;n.consumeSpaces();let c=n.fetch();if(K0[c.text])return(s==="\\global"||s==="\\\\globallong")&&(c.text=K0[c.text]),le(n.parseFunction(),"internal");throw new i("Invalid token after macro prefix",c)}}),j({type:"internal",names:["\\def","\\gdef","\\edef","\\xdef"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(o){let{parser:n,funcName:s}=o,c=n.gullet.popToken(),f=c.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(f))throw new i("Expected a control sequence",c);let m=0,x,w=[[]];for(;n.gullet.future().text!=="{";)if(c=n.gullet.popToken(),c.text==="#"){if(n.gullet.future().text==="{"){x=n.gullet.future(),w[m].push("{");break}if(c=n.gullet.popToken(),!/^[1-9]$/.test(c.text))throw new i('Invalid argument number "'+c.text+'"');if(parseInt(c.text)!==m+1)throw new i('Argument number "'+c.text+'" out of order');m++,w.push([])}else{if(c.text==="EOF")throw new i("Expected a macro definition");w[m].push(c.text)}let{tokens:M}=n.gullet.consumeArg();return x&&M.unshift(x),(s==="\\edef"||s==="\\xdef")&&(M=n.gullet.expandTokens(M),M.reverse()),n.gullet.macros.set(f,{tokens:M,numArgs:m,delimiters:w},s===K0[s]),{type:"internal",mode:n.mode}}}),j({type:"internal",names:["\\let","\\\\globallet"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(o){let{parser:n,funcName:s}=o,c=cl(n.gullet.popToken());n.gullet.consumeSpaces();let f=Qh(n);return ul(n,c,f,s==="\\\\globallet"),{type:"internal",mode:n.mode}}}),j({type:"internal",names:["\\futurelet","\\\\globalfuture"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(o){let{parser:n,funcName:s}=o,c=cl(n.gullet.popToken()),f=n.gullet.popToken(),m=n.gullet.popToken();return ul(n,c,m,s==="\\\\globalfuture"),n.gullet.pushToken(m),n.gullet.pushToken(f),{type:"internal",mode:n.mode}}});let qr=function(o,n,s){let c=Te.math[o]&&Te.math[o].replace,f=B0(c||o,n,s);if(!f)throw new Error("Unsupported symbol "+o+" and font size "+n+".");return f},Z0=function(o,n,s,c){let f=s.havingBaseStyle(n),m=O.makeSpan(c.concat(f.sizingClasses(s)),[o],s),x=f.sizeMultiplier/s.sizeMultiplier;return m.height*=x,m.depth*=x,m.maxFontSize=f.sizeMultiplier,m},hl=function(o,n,s){let c=n.havingBaseStyle(s),f=(1-n.sizeMultiplier/c.sizeMultiplier)*n.fontMetrics().axisHeight;o.classes.push("delimcenter"),o.style.top=U(f),o.height-=f,o.depth+=f},e1=function(o,n,s,c,f,m){let x=O.makeSymbol(o,"Main-Regular",f,c),w=Z0(x,n,c,m);return s&&hl(w,c,n),w},t1=function(o,n,s,c){return O.makeSymbol(o,"Size"+n+"-Regular",s,c)},fl=function(o,n,s,c,f,m){let x=t1(o,n,f,c),w=Z0(O.makeSpan(["delimsizing","size"+n],[x],c),Y.TEXT,c,m);return s&&hl(w,c,Y.TEXT),w},J0=function(o,n,s){let c;return n==="Size1-Regular"?c="delim-size1":c="delim-size4",{type:"elem",elem:O.makeSpan(["delimsizinginner",c],[O.makeSpan([],[O.makeSymbol(o,n,s)])])}},Q0=function(o,n,s){let c=At["Size4-Regular"][o.charCodeAt(0)]?At["Size4-Regular"][o.charCodeAt(0)][4]:At["Size1-Regular"][o.charCodeAt(0)][4],f=new Xt("inner",ch(o,Math.round(1e3*n))),m=new It([f],{width:U(c),height:U(n),style:"width:"+U(c),viewBox:"0 0 "+1e3*c+" "+Math.round(1e3*n),preserveAspectRatio:"xMinYMin"}),x=O.makeSvgSpan([],[m],s);return x.height=n,x.style.height=U(n),x.style.width=U(c),{type:"elem",elem:x}},ei=.008,In={type:"kern",size:-1*ei},r1=["|","\\lvert","\\rvert","\\vert"],n1=["\\|","\\lVert","\\rVert","\\Vert"],dl=function(o,n,s,c,f,m){let x,w,M,N,D="",B=0;x=M=N=o,w=null;let R="Size1-Regular";o==="\\uparrow"?M=N="\u23D0":o==="\\Uparrow"?M=N="\u2016":o==="\\downarrow"?x=M="\u23D0":o==="\\Downarrow"?x=M="\u2016":o==="\\updownarrow"?(x="\\uparrow",M="\u23D0",N="\\downarrow"):o==="\\Updownarrow"?(x="\\Uparrow",M="\u2016",N="\\Downarrow"):z.contains(r1,o)?(M="\u2223",D="vert",B=333):z.contains(n1,o)?(M="\u2225",D="doublevert",B=556):o==="["||o==="\\lbrack"?(x="\u23A1",M="\u23A2",N="\u23A3",R="Size4-Regular",D="lbrack",B=667):o==="]"||o==="\\rbrack"?(x="\u23A4",M="\u23A5",N="\u23A6",R="Size4-Regular",D="rbrack",B=667):o==="\\lfloor"||o==="\u230A"?(M=x="\u23A2",N="\u23A3",R="Size4-Regular",D="lfloor",B=667):o==="\\lceil"||o==="\u2308"?(x="\u23A1",M=N="\u23A2",R="Size4-Regular",D="lceil",B=667):o==="\\rfloor"||o==="\u230B"?(M=x="\u23A5",N="\u23A6",R="Size4-Regular",D="rfloor",B=667):o==="\\rceil"||o==="\u2309"?(x="\u23A4",M=N="\u23A5",R="Size4-Regular",D="rceil",B=667):o==="("||o==="\\lparen"?(x="\u239B",M="\u239C",N="\u239D",R="Size4-Regular",D="lparen",B=875):o===")"||o==="\\rparen"?(x="\u239E",M="\u239F",N="\u23A0",R="Size4-Regular",D="rparen",B=875):o==="\\{"||o==="\\lbrace"?(x="\u23A7",w="\u23A8",N="\u23A9",M="\u23AA",R="Size4-Regular"):o==="\\}"||o==="\\rbrace"?(x="\u23AB",w="\u23AC",N="\u23AD",M="\u23AA",R="Size4-Regular"):o==="\\lgroup"||o==="\u27EE"?(x="\u23A7",N="\u23A9",M="\u23AA",R="Size4-Regular"):o==="\\rgroup"||o==="\u27EF"?(x="\u23AB",N="\u23AD",M="\u23AA",R="Size4-Regular"):o==="\\lmoustache"||o==="\u23B0"?(x="\u23A7",N="\u23AD",M="\u23AA",R="Size4-Regular"):(o==="\\rmoustache"||o==="\u23B1")&&(x="\u23AB",N="\u23A9",M="\u23AA",R="Size4-Regular");let H=qr(x,R,f),Z=H.height+H.depth,Q=qr(M,R,f),ue=Q.height+Q.depth,ae=qr(N,R,f),he=ae.height+ae.depth,pe=0,we=1;if(w!==null){let De=qr(w,R,f);pe=De.height+De.depth,we=2}let We=Z+he+pe,qe=Math.max(0,Math.ceil((n-We)/(we*ue))),mt=We+qe*we*ue,Sr=c.fontMetrics().axisHeight;s&&(Sr*=c.sizeMultiplier);let de=mt/2-Sr,ge=[];if(D.length>0){let De=mt-Z-he,Ee=Math.round(mt*1e3),gt=uh(D,Math.round(De*1e3)),N1=new Xt(D,gt),ns=(B/1e3).toFixed(3)+"em",is=(Ee/1e3).toFixed(3)+"em",E1=new It([N1],{width:ns,height:is,viewBox:"0 0 "+B+" "+Ee}),Ln=O.makeSvgSpan([],[E1],c);Ln.height=Ee/1e3,Ln.style.width=ns,Ln.style.height=is,ge.push({type:"elem",elem:Ln})}else{if(ge.push(J0(N,R,f)),ge.push(In),w===null){let De=mt-Z-he+2*ei;ge.push(Q0(M,De,c))}else{let De=(mt-Z-he-pe)/2+2*ei;ge.push(Q0(M,De,c)),ge.push(In),ge.push(J0(w,R,f)),ge.push(In),ge.push(Q0(M,De,c))}ge.push(In),ge.push(J0(x,R,f))}let ke=c.havingBaseStyle(Y.TEXT),_e=O.makeVList({positionType:"bottom",positionData:de,children:ge},ke);return Z0(O.makeSpan(["delimsizing","mult"],[_e],ke),Y.TEXT,c,m)},ti=80,ri=.08,ni=function(o,n,s,c,f){let m=ah(o,c,s),x=new Xt(o,m),w=new It([x],{width:"400em",height:U(n),viewBox:"0 0 400000 "+s,preserveAspectRatio:"xMinYMin slice"});return O.makeSvgSpan(["hide-tail"],[w],f)},i1=function(o,n){let s=n.havingBaseSizing(),c=xl("\\surd",o*s.sizeMultiplier,gl,s),f=s.sizeMultiplier,m=Math.max(0,n.minRuleThickness-n.fontMetrics().sqrtRuleThickness),x,w=0,M=0,N=0,D;return c.type==="small"?(N=1e3+1e3*m+ti,o<1?f=1:o<1.4&&(f=.7),w=(1+m+ri)/f,M=(1+m)/f,x=ni("sqrtMain",w,N,m,n),x.style.minWidth="0.853em",D=.833/f):c.type==="large"?(N=(1e3+ti)*Pr[c.size],M=(Pr[c.size]+m)/f,w=(Pr[c.size]+m+ri)/f,x=ni("sqrtSize"+c.size,w,N,m,n),x.style.minWidth="1.02em",D=1/f):(w=o+m+ri,M=o+m,N=Math.floor(1e3*o+m)+ti,x=ni("sqrtTall",w,N,m,n),x.style.minWidth="0.742em",D=1.056),x.height=M,x.style.height=U(w),{span:x,advanceWidth:D,ruleWidth:(n.fontMetrics().sqrtRuleThickness+m)*f}},pl=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","\u230A","\u230B","\\lceil","\\rceil","\u2308","\u2309","\\surd"],o1=["\\uparrow","\\downarrow","\\updownarrow","\\Uparrow","\\Downarrow","\\Updownarrow","|","\\|","\\vert","\\Vert","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","\u27EE","\u27EF","\\lmoustache","\\rmoustache","\u23B0","\u23B1"],ml=["<",">","\\langle","\\rangle","/","\\backslash","\\lt","\\gt"],Pr=[0,1.2,1.8,2.4,3],l1=function(o,n,s,c,f){if(o==="<"||o==="\\lt"||o==="\u27E8"?o="\\langle":(o===">"||o==="\\gt"||o==="\u27E9")&&(o="\\rangle"),z.contains(pl,o)||z.contains(ml,o))return fl(o,n,!1,s,c,f);if(z.contains(o1,o))return dl(o,Pr[n],!1,s,c,f);throw new i("Illegal delimiter: '"+o+"'")},s1=[{type:"small",style:Y.SCRIPTSCRIPT},{type:"small",style:Y.SCRIPT},{type:"small",style:Y.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4}],a1=[{type:"small",style:Y.SCRIPTSCRIPT},{type:"small",style:Y.SCRIPT},{type:"small",style:Y.TEXT},{type:"stack"}],gl=[{type:"small",style:Y.SCRIPTSCRIPT},{type:"small",style:Y.SCRIPT},{type:"small",style:Y.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4},{type:"stack"}],c1=function(o){if(o.type==="small")return"Main-Regular";if(o.type==="large")return"Size"+o.size+"-Regular";if(o.type==="stack")return"Size4-Regular";throw new Error("Add support for delim type '"+o.type+"' here.")},xl=function(o,n,s,c){let f=Math.min(2,3-c.style.size);for(let m=f;m<s.length&&s[m].type!=="stack";m++){let x=qr(o,c1(s[m]),"math"),w=x.height+x.depth;if(s[m].type==="small"){let M=c.havingBaseStyle(s[m].style);w*=M.sizeMultiplier}if(w>n)return s[m]}return s[s.length-1]},yl=function(o,n,s,c,f,m){o==="<"||o==="\\lt"||o==="\u27E8"?o="\\langle":(o===">"||o==="\\gt"||o==="\u27E9")&&(o="\\rangle");let x;z.contains(ml,o)?x=s1:z.contains(pl,o)?x=gl:x=a1;let w=xl(o,n,x,c);return w.type==="small"?e1(o,w.style,s,c,f,m):w.type==="large"?fl(o,w.size,s,c,f,m):dl(o,n,s,c,f,m)};var Lt={sqrtImage:i1,sizedDelim:l1,sizeToMaxHeight:Pr,customSizedDelim:yl,leftRightDelim:function(o,n,s,c,f,m){let x=c.fontMetrics().axisHeight*c.sizeMultiplier,w=901,M=5/c.fontMetrics().ptPerEm,N=Math.max(n-x,s+x),D=Math.max(N/500*w,2*N-M);return yl(o,D,!0,c,f,m)}};let bl={"\\bigl":{mclass:"mopen",size:1},"\\Bigl":{mclass:"mopen",size:2},"\\biggl":{mclass:"mopen",size:3},"\\Biggl":{mclass:"mopen",size:4},"\\bigr":{mclass:"mclose",size:1},"\\Bigr":{mclass:"mclose",size:2},"\\biggr":{mclass:"mclose",size:3},"\\Biggr":{mclass:"mclose",size:4},"\\bigm":{mclass:"mrel",size:1},"\\Bigm":{mclass:"mrel",size:2},"\\biggm":{mclass:"mrel",size:3},"\\Biggm":{mclass:"mrel",size:4},"\\big":{mclass:"mord",size:1},"\\Big":{mclass:"mord",size:2},"\\bigg":{mclass:"mord",size:3},"\\Bigg":{mclass:"mord",size:4}},u1=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","\u230A","\u230B","\\lceil","\\rceil","\u2308","\u2309","<",">","\\langle","\u27E8","\\rangle","\u27E9","\\lt","\\gt","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","\u27EE","\u27EF","\\lmoustache","\\rmoustache","\u23B0","\u23B1","/","\\backslash","|","\\vert","\\|","\\Vert","\\uparrow","\\Uparrow","\\downarrow","\\Downarrow","\\updownarrow","\\Updownarrow","."];function Bn(o,n){let s=En(o);if(s&&z.contains(u1,s.text))return s;throw s?new i("Invalid delimiter '"+s.text+"' after '"+n.funcName+"'",o):new i("Invalid delimiter type '"+o.type+"'",o)}j({type:"delimsizing",names:["\\bigl","\\Bigl","\\biggl","\\Biggl","\\bigr","\\Bigr","\\biggr","\\Biggr","\\bigm","\\Bigm","\\biggm","\\Biggm","\\big","\\Big","\\bigg","\\Bigg"],props:{numArgs:1,argTypes:["primitive"]},handler:(o,n)=>{let s=Bn(n[0],o);return{type:"delimsizing",mode:o.parser.mode,size:bl[o.funcName].size,mclass:bl[o.funcName].mclass,delim:s.text}},htmlBuilder:(o,n)=>o.delim==="."?O.makeSpan([o.mclass]):Lt.sizedDelim(o.delim,o.size,n,o.mode,[o.mclass]),mathmlBuilder:o=>{let n=[];o.delim!=="."&&n.push(dt(o.delim,o.mode));let s=new V.MathNode("mo",n);o.mclass==="mopen"||o.mclass==="mclose"?s.setAttribute("fence","true"):s.setAttribute("fence","false"),s.setAttribute("stretchy","true");let c=U(Lt.sizeToMaxHeight[o.size]);return s.setAttribute("minsize",c),s.setAttribute("maxsize",c),s}});function vl(o){if(!o.body)throw new Error("Bug: The leftright ParseNode wasn't fully parsed.")}j({type:"leftright-right",names:["\\right"],props:{numArgs:1,primitive:!0},handler:(o,n)=>{let s=o.parser.gullet.macros.get("\\current@color");if(s&&typeof s!="string")throw new i("\\current@color set to non-string in \\right");return{type:"leftright-right",mode:o.parser.mode,delim:Bn(n[0],o).text,color:s}}}),j({type:"leftright",names:["\\left"],props:{numArgs:1,primitive:!0},handler:(o,n)=>{let s=Bn(n[0],o),c=o.parser;++c.leftrightDepth;let f=c.parseExpression(!1);--c.leftrightDepth,c.expect("\\right",!1);let m=le(c.parseFunction(),"leftright-right");return{type:"leftright",mode:c.mode,body:f,left:s.text,right:m.delim,rightColor:m.color}},htmlBuilder:(o,n)=>{vl(o);let s=Le(o.body,n,!0,["mopen","mclose"]),c=0,f=0,m=!1;for(let M=0;M<s.length;M++)s[M].isMiddle?m=!0:(c=Math.max(s[M].height,c),f=Math.max(s[M].depth,f));c*=n.sizeMultiplier,f*=n.sizeMultiplier;let x;if(o.left==="."?x=Lr(n,["mopen"]):x=Lt.leftRightDelim(o.left,c,f,n,o.mode,["mopen"]),s.unshift(x),m)for(let M=1;M<s.length;M++){let D=s[M].isMiddle;D&&(s[M]=Lt.leftRightDelim(D.delim,c,f,D.options,o.mode,[]))}let w;if(o.right===".")w=Lr(n,["mclose"]);else{let M=o.rightColor?n.withColor(o.rightColor):n;w=Lt.leftRightDelim(o.right,c,f,M,o.mode,["mclose"])}return s.push(w),O.makeSpan(["minner"],s,n)},mathmlBuilder:(o,n)=>{vl(o);let s=et(o.body,n);if(o.left!=="."){let c=new V.MathNode("mo",[dt(o.left,o.mode)]);c.setAttribute("fence","true"),s.unshift(c)}if(o.right!=="."){let c=new V.MathNode("mo",[dt(o.right,o.mode)]);c.setAttribute("fence","true"),o.rightColor&&c.setAttribute("mathcolor",o.rightColor),s.push(c)}return Y0(s)}}),j({type:"middle",names:["\\middle"],props:{numArgs:1,primitive:!0},handler:(o,n)=>{let s=Bn(n[0],o);if(!o.parser.leftrightDepth)throw new i("\\middle without preceding \\left",s);return{type:"middle",mode:o.parser.mode,delim:s.text}},htmlBuilder:(o,n)=>{let s;if(o.delim===".")s=Lr(n,[]);else{s=Lt.sizedDelim(o.delim,1,n,o.mode,[]);let c={delim:o.delim,options:n};s.isMiddle=c}return s},mathmlBuilder:(o,n)=>{let s=o.delim==="\\vert"||o.delim==="|"?dt("|","text"):dt(o.delim,o.mode),c=new V.MathNode("mo",[s]);return c.setAttribute("fence","true"),c.setAttribute("lspace","0.05em"),c.setAttribute("rspace","0.05em"),c}});let ii=(o,n)=>{let s=O.wrapFragment(fe(o.body,n),n),c=o.label.slice(1),f=n.sizeMultiplier,m,x=0,w=z.isCharacterBox(o.body);if(c==="sout")m=O.makeSpan(["stretchy","sout"]),m.height=n.fontMetrics().defaultRuleThickness/f,x=-.5*n.fontMetrics().xHeight;else if(c==="phase"){let N=Ce({number:.6,unit:"pt"},n),D=Ce({number:.35,unit:"ex"},n),B=n.havingBaseSizing();f=f/B.sizeMultiplier;let R=s.height+s.depth+N+D;s.style.paddingLeft=U(R/2+N);let H=Math.floor(1e3*R*f),Z=lh(H),Q=new It([new Xt("phase",Z)],{width:"400em",height:U(H/1e3),viewBox:"0 0 400000 "+H,preserveAspectRatio:"xMinYMin slice"});m=O.makeSvgSpan(["hide-tail"],[Q],n),m.style.height=U(R),x=s.depth+N+D}else{/cancel/.test(c)?w||s.classes.push("cancel-pad"):c==="angl"?s.classes.push("anglpad"):s.classes.push("boxpad");let N=0,D=0,B=0;/box/.test(c)?(B=Math.max(n.fontMetrics().fboxrule,n.minRuleThickness),N=n.fontMetrics().fboxsep+(c==="colorbox"?0:B),D=N):c==="angl"?(B=Math.max(n.fontMetrics().defaultRuleThickness,n.minRuleThickness),N=4*B,D=Math.max(0,.25-s.depth)):(N=w?.2:0,D=N),m=Rt.encloseSpan(s,c,N,D,n),/fbox|boxed|fcolorbox/.test(c)?(m.style.borderStyle="solid",m.style.borderWidth=U(B)):c==="angl"&&B!==.049&&(m.style.borderTopWidth=U(B),m.style.borderRightWidth=U(B)),x=s.depth+D,o.backgroundColor&&(m.style.backgroundColor=o.backgroundColor,o.borderColor&&(m.style.borderColor=o.borderColor))}let M;if(o.backgroundColor)M=O.makeVList({positionType:"individualShift",children:[{type:"elem",elem:m,shift:x},{type:"elem",elem:s,shift:0}]},n);else{let N=/cancel|phase/.test(c)?["svg-align"]:[];M=O.makeVList({positionType:"individualShift",children:[{type:"elem",elem:s,shift:0},{type:"elem",elem:m,shift:x,wrapperClasses:N}]},n)}return/cancel/.test(c)&&(M.height=s.height,M.depth=s.depth),/cancel/.test(c)&&!w?O.makeSpan(["mord","cancel-lap"],[M],n):O.makeSpan(["mord"],[M],n)},oi=(o,n)=>{let s=0,c=new V.MathNode(o.label.indexOf("colorbox")>-1?"mpadded":"menclose",[ye(o.body,n)]);switch(o.label){case"\\cancel":c.setAttribute("notation","updiagonalstrike");break;case"\\bcancel":c.setAttribute("notation","downdiagonalstrike");break;case"\\phase":c.setAttribute("notation","phasorangle");break;case"\\sout":c.setAttribute("notation","horizontalstrike");break;case"\\fbox":c.setAttribute("notation","box");break;case"\\angl":c.setAttribute("notation","actuarial");break;case"\\fcolorbox":case"\\colorbox":if(s=n.fontMetrics().fboxsep*n.fontMetrics().ptPerEm,c.setAttribute("width","+"+2*s+"pt"),c.setAttribute("height","+"+2*s+"pt"),c.setAttribute("lspace",s+"pt"),c.setAttribute("voffset",s+"pt"),o.label==="\\fcolorbox"){let f=Math.max(n.fontMetrics().fboxrule,n.minRuleThickness);c.setAttribute("style","border: "+f+"em solid "+String(o.borderColor))}break;case"\\xcancel":c.setAttribute("notation","updiagonalstrike downdiagonalstrike");break}return o.backgroundColor&&c.setAttribute("mathbackground",o.backgroundColor),c};j({type:"enclose",names:["\\colorbox"],props:{numArgs:2,allowedInText:!0,argTypes:["color","text"]},handler(o,n,s){let{parser:c,funcName:f}=o,m=le(n[0],"color-token").color,x=n[1];return{type:"enclose",mode:c.mode,label:f,backgroundColor:m,body:x}},htmlBuilder:ii,mathmlBuilder:oi}),j({type:"enclose",names:["\\fcolorbox"],props:{numArgs:3,allowedInText:!0,argTypes:["color","color","text"]},handler(o,n,s){let{parser:c,funcName:f}=o,m=le(n[0],"color-token").color,x=le(n[1],"color-token").color,w=n[2];return{type:"enclose",mode:c.mode,label:f,backgroundColor:x,borderColor:m,body:w}},htmlBuilder:ii,mathmlBuilder:oi}),j({type:"enclose",names:["\\fbox"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!0},handler(o,n){let{parser:s}=o;return{type:"enclose",mode:s.mode,label:"\\fbox",body:n[0]}}}),j({type:"enclose",names:["\\cancel","\\bcancel","\\xcancel","\\sout","\\phase"],props:{numArgs:1},handler(o,n){let{parser:s,funcName:c}=o,f=n[0];return{type:"enclose",mode:s.mode,label:c,body:f}},htmlBuilder:ii,mathmlBuilder:oi}),j({type:"enclose",names:["\\angl"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!1},handler(o,n){let{parser:s}=o;return{type:"enclose",mode:s.mode,label:"\\angl",body:n[0]}}});let wl={};function Ct(o){let{type:n,names:s,props:c,handler:f,htmlBuilder:m,mathmlBuilder:x}=o,w={type:n,numArgs:c.numArgs||0,allowedInText:!1,numOptionalArgs:0,handler:f};for(let M=0;M<s.length;++M)wl[s[M]]=w;m&&(_n[n]=m),x&&(Cn[n]=x)}let kl={};function k(o,n){kl[o]=n}class ut{constructor(n,s,c){this.lexer=void 0,this.start=void 0,this.end=void 0,this.lexer=n,this.start=s,this.end=c}static range(n,s){return s?!n||!n.loc||!s.loc||n.loc.lexer!==s.loc.lexer?null:new ut(n.loc.lexer,n.loc.start,s.loc.end):n&&n.loc}}class pt{constructor(n,s){this.text=void 0,this.loc=void 0,this.noexpand=void 0,this.treatAsRelax=void 0,this.text=n,this.loc=s}range(n,s){return new pt(s,ut.range(this,n))}}function Sl(o){let n=[];o.consumeSpaces();let s=o.fetch().text;for(s==="\\relax"&&(o.consume(),o.consumeSpaces(),s=o.fetch().text);s==="\\hline"||s==="\\hdashline";)o.consume(),n.push(s==="\\hdashline"),o.consumeSpaces(),s=o.fetch().text;return n}let On=o=>{if(!o.parser.settings.displayMode)throw new i("{"+o.envName+"} can be used only in display mode.")};function li(o){if(o.indexOf("ed")===-1)return o.indexOf("*")===-1}function Wt(o,n,s){let{hskipBeforeAndAfter:c,addJot:f,cols:m,arraystretch:x,colSeparationType:w,autoTag:M,singleRow:N,emptySingleRow:D,maxNumCols:B,leqno:R}=n;if(o.gullet.beginGroup(),N||o.gullet.macros.set("\\cr","\\\\\\relax"),!x){let we=o.gullet.expandMacroAsText("\\arraystretch");if(we==null)x=1;else if(x=parseFloat(we),!x||x<0)throw new i("Invalid \\arraystretch: "+we)}o.gullet.beginGroup();let H=[],Z=[H],Q=[],ue=[],ae=M!=null?[]:void 0;function he(){M&&o.gullet.macros.set("\\@eqnsw","1",!0)}function pe(){ae&&(o.gullet.macros.get("\\df@tag")?(ae.push(o.subparse([new pt("\\df@tag")])),o.gullet.macros.set("\\df@tag",void 0,!0)):ae.push(Boolean(M)&&o.gullet.macros.get("\\@eqnsw")==="1"))}for(he(),ue.push(Sl(o));;){let we=o.parseExpression(!1,N?"\\end":"\\\\");o.gullet.endGroup(),o.gullet.beginGroup(),we={type:"ordgroup",mode:o.mode,body:we},s&&(we={type:"styling",mode:o.mode,style:s,body:[we]}),H.push(we);let We=o.fetch().text;if(We==="&"){if(B&&H.length===B){if(N||w)throw new i("Too many tab characters: &",o.nextToken);o.settings.reportNonstrict("textEnv","Too few columns specified in the {array} column argument.")}o.consume()}else if(We==="\\end"){pe(),H.length===1&&we.type==="styling"&&we.body[0].body.length===0&&(Z.length>1||!D)&&Z.pop(),ue.length<Z.length+1&&ue.push([]);break}else if(We==="\\\\"){o.consume();let qe;o.gullet.future().text!==" "&&(qe=o.parseSizeGroup(!0)),Q.push(qe?qe.value:null),pe(),ue.push(Sl(o)),H=[],Z.push(H),he()}else throw new i("Expected & or \\\\ or \\cr or \\end",o.nextToken)}return o.gullet.endGroup(),o.gullet.endGroup(),{type:"array",mode:o.mode,addJot:f,arraystretch:x,body:Z,cols:m,rowGaps:Q,hskipBeforeAndAfter:c,hLinesBeforeRow:ue,colSeparationType:w,tags:ae,leqno:R}}function si(o){return o.slice(0,1)==="d"?"display":"text"}let Tt=function(o,n){let s,c,f=o.body.length,m=o.hLinesBeforeRow,x=0,w=new Array(f),M=[],N=Math.max(n.fontMetrics().arrayRuleWidth,n.minRuleThickness),D=1/n.fontMetrics().ptPerEm,B=5*D;o.colSeparationType&&o.colSeparationType==="small"&&(B=.2778*(n.havingStyle(Y.SCRIPT).sizeMultiplier/n.sizeMultiplier));let R=o.colSeparationType==="CD"?Ce({number:3,unit:"ex"},n):12*D,H=3*D,Z=o.arraystretch*R,Q=.7*Z,ue=.3*Z,ae=0;function he(de){for(let ge=0;ge<de.length;++ge)ge>0&&(ae+=.25),M.push({pos:ae,isDashed:de[ge]})}for(he(m[0]),s=0;s<o.body.length;++s){let de=o.body[s],ge=Q,ke=ue;x<de.length&&(x=de.length);let _e=new Array(de.length);for(c=0;c<de.length;++c){let gt=fe(de[c],n);ke<gt.depth&&(ke=gt.depth),ge<gt.height&&(ge=gt.height),_e[c]=gt}let De=o.rowGaps[s],Ee=0;De&&(Ee=Ce(De,n),Ee>0&&(Ee+=ue,ke<Ee&&(ke=Ee),Ee=0)),o.addJot&&(ke+=H),_e.height=ge,_e.depth=ke,ae+=ge,_e.pos=ae,ae+=ke+Ee,w[s]=_e,he(m[s+1])}let pe=ae/2+n.fontMetrics().axisHeight,we=o.cols||[],We=[],qe,mt,Sr=[];if(o.tags&&o.tags.some(de=>de))for(s=0;s<f;++s){let de=w[s],ge=de.pos-pe,ke=o.tags[s],_e;ke===!0?_e=O.makeSpan(["eqn-num"],[],n):ke===!1?_e=O.makeSpan([],[],n):_e=O.makeSpan([],Le(ke,n,!0),n),_e.depth=de.depth,_e.height=de.height,Sr.push({type:"elem",elem:_e,shift:ge})}for(c=0,mt=0;c<x||mt<we.length;++c,++mt){let de=we[mt]||{},ge=!0;for(;de.type==="separator";){if(ge||(qe=O.makeSpan(["arraycolsep"],[]),qe.style.width=U(n.fontMetrics().doubleRuleSep),We.push(qe)),de.separator==="|"||de.separator===":"){let De=de.separator==="|"?"solid":"dashed",Ee=O.makeSpan(["vertical-separator"],[],n);Ee.style.height=U(ae),Ee.style.borderRightWidth=U(N),Ee.style.borderRightStyle=De,Ee.style.margin="0 "+U(-N/2);let gt=ae-pe;gt&&(Ee.style.verticalAlign=U(-gt)),We.push(Ee)}else throw new i("Invalid separator type: "+de.separator);mt++,de=we[mt]||{},ge=!1}if(c>=x)continue;let ke;(c>0||o.hskipBeforeAndAfter)&&(ke=z.deflt(de.pregap,B),ke!==0&&(qe=O.makeSpan(["arraycolsep"],[]),qe.style.width=U(ke),We.push(qe)));let _e=[];for(s=0;s<f;++s){let De=w[s],Ee=De[c];if(!Ee)continue;let gt=De.pos-pe;Ee.depth=De.depth,Ee.height=De.height,_e.push({type:"elem",elem:Ee,shift:gt})}_e=O.makeVList({positionType:"individualShift",children:_e},n),_e=O.makeSpan(["col-align-"+(de.align||"c")],[_e]),We.push(_e),(c<x-1||o.hskipBeforeAndAfter)&&(ke=z.deflt(de.postgap,B),ke!==0&&(qe=O.makeSpan(["arraycolsep"],[]),qe.style.width=U(ke),We.push(qe)))}if(w=O.makeSpan(["mtable"],We),M.length>0){let de=O.makeLineSpan("hline",n,N),ge=O.makeLineSpan("hdashline",n,N),ke=[{type:"elem",elem:w,shift:0}];for(;M.length>0;){let _e=M.pop(),De=_e.pos-pe;_e.isDashed?ke.push({type:"elem",elem:ge,shift:De}):ke.push({type:"elem",elem:de,shift:De})}w=O.makeVList({positionType:"individualShift",children:ke},n)}if(Sr.length===0)return O.makeSpan(["mord"],[w],n);{let de=O.makeVList({positionType:"individualShift",children:Sr},n);return de=O.makeSpan(["tag"],[de],n),O.makeFragment([w,de])}},h1={c:"center ",l:"left ",r:"right "},Nt=function(o,n){let s=[],c=new V.MathNode("mtd",[],["mtr-glue"]),f=new V.MathNode("mtd",[],["mml-eqn-num"]);for(let B=0;B<o.body.length;B++){let R=o.body[B],H=[];for(let Z=0;Z<R.length;Z++)H.push(new V.MathNode("mtd",[ye(R[Z],n)]));o.tags&&o.tags[B]&&(H.unshift(c),H.push(c),o.leqno?H.unshift(f):H.push(f)),s.push(new V.MathNode("mtr",H))}let m=new V.MathNode("mtable",s),x=o.arraystretch===.5?.1:.16+o.arraystretch-1+(o.addJot?.09:0);m.setAttribute("rowspacing",U(x));let w="",M="";if(o.cols&&o.cols.length>0){let B=o.cols,R="",H=!1,Z=0,Q=B.length;B[0].type==="separator"&&(w+="top ",Z=1),B[B.length-1].type==="separator"&&(w+="bottom ",Q-=1);for(let ue=Z;ue<Q;ue++)B[ue].type==="align"?(M+=h1[B[ue].align],H&&(R+="none "),H=!0):B[ue].type==="separator"&&H&&(R+=B[ue].separator==="|"?"solid ":"dashed ",H=!1);m.setAttribute("columnalign",M.trim()),/[sd]/.test(R)&&m.setAttribute("columnlines",R.trim())}if(o.colSeparationType==="align"){let B=o.cols||[],R="";for(let H=1;H<B.length;H++)R+=H%2?"0em ":"1em ";m.setAttribute("columnspacing",R.trim())}else o.colSeparationType==="alignat"||o.colSeparationType==="gather"?m.setAttribute("columnspacing","0em"):o.colSeparationType==="small"?m.setAttribute("columnspacing","0.2778em"):o.colSeparationType==="CD"?m.setAttribute("columnspacing","0.5em"):m.setAttribute("columnspacing","1em");let N="",D=o.hLinesBeforeRow;w+=D[0].length>0?"left ":"",w+=D[D.length-1].length>0?"right ":"";for(let B=1;B<D.length-1;B++)N+=D[B].length===0?"none ":D[B][0]?"dashed ":"solid ";return/[sd]/.test(N)&&m.setAttribute("rowlines",N.trim()),w!==""&&(m=new V.MathNode("menclose",[m]),m.setAttribute("notation",w.trim())),o.arraystretch&&o.arraystretch<1&&(m=new V.MathNode("mstyle",[m]),m.setAttribute("scriptlevel","1")),m},Ml=function(o,n){o.envName.indexOf("ed")===-1&&On(o);let s=[],c=o.envName.indexOf("at")>-1?"alignat":"align",f=o.envName==="split",m=Wt(o.parser,{cols:s,addJot:!0,autoTag:f?void 0:li(o.envName),emptySingleRow:!0,colSeparationType:c,maxNumCols:f?2:void 0,leqno:o.parser.settings.leqno},"display"),x,w=0,M={type:"ordgroup",mode:o.mode,body:[]};if(n[0]&&n[0].type==="ordgroup"){let D="";for(let B=0;B<n[0].body.length;B++){let R=le(n[0].body[B],"textord");D+=R.text}x=Number(D),w=x*2}let N=!w;m.body.forEach(function(D){for(let B=1;B<D.length;B+=2){let R=le(D[B],"styling");le(R.body[0],"ordgroup").body.unshift(M)}if(N)w<D.length&&(w=D.length);else{let B=D.length/2;if(x<B)throw new i("Too many math in a row: "+("expected "+x+", but got "+B),D[0])}});for(let D=0;D<w;++D){let B="r",R=0;D%2===1?B="l":D>0&&N&&(R=1),s[D]={type:"align",align:B,pregap:R,postgap:0}}return m.colSeparationType=N?"align":"alignat",m};Ct({type:"array",names:["array","darray"],props:{numArgs:1},handler(o,n){let f=(En(n[0])?[n[0]]:le(n[0],"ordgroup").body).map(function(x){let M=j0(x).text;if("lcr".indexOf(M)!==-1)return{type:"align",align:M};if(M==="|")return{type:"separator",separator:"|"};if(M===":")return{type:"separator",separator:":"};throw new i("Unknown column alignment: "+M,x)}),m={cols:f,hskipBeforeAndAfter:!0,maxNumCols:f.length};return Wt(o.parser,m,si(o.envName))},htmlBuilder:Tt,mathmlBuilder:Nt}),Ct({type:"array",names:["matrix","pmatrix","bmatrix","Bmatrix","vmatrix","Vmatrix","matrix*","pmatrix*","bmatrix*","Bmatrix*","vmatrix*","Vmatrix*"],props:{numArgs:0},handler(o){let n={matrix:null,pmatrix:["(",")"],bmatrix:["[","]"],Bmatrix:["\\{","\\}"],vmatrix:["|","|"],Vmatrix:["\\Vert","\\Vert"]}[o.envName.replace("*","")],s="c",c={hskipBeforeAndAfter:!1,cols:[{type:"align",align:s}]};if(o.envName.charAt(o.envName.length-1)==="*"){let x=o.parser;if(x.consumeSpaces(),x.fetch().text==="["){if(x.consume(),x.consumeSpaces(),s=x.fetch().text,"lcr".indexOf(s)===-1)throw new i("Expected l or c or r",x.nextToken);x.consume(),x.consumeSpaces(),x.expect("]"),x.consume(),c.cols=[{type:"align",align:s}]}}let f=Wt(o.parser,c,si(o.envName)),m=Math.max(0,...f.body.map(x=>x.length));return f.cols=new Array(m).fill({type:"align",align:s}),n?{type:"leftright",mode:o.mode,body:[f],left:n[0],right:n[1],rightColor:void 0}:f},htmlBuilder:Tt,mathmlBuilder:Nt}),Ct({type:"array",names:["smallmatrix"],props:{numArgs:0},handler(o){let n={arraystretch:.5},s=Wt(o.parser,n,"script");return s.colSeparationType="small",s},htmlBuilder:Tt,mathmlBuilder:Nt}),Ct({type:"array",names:["subarray"],props:{numArgs:1},handler(o,n){let f=(En(n[0])?[n[0]]:le(n[0],"ordgroup").body).map(function(x){let M=j0(x).text;if("lc".indexOf(M)!==-1)return{type:"align",align:M};throw new i("Unknown column alignment: "+M,x)});if(f.length>1)throw new i("{subarray} can contain only one column");let m={cols:f,hskipBeforeAndAfter:!1,arraystretch:.5};if(m=Wt(o.parser,m,"script"),m.body.length>0&&m.body[0].length>1)throw new i("{subarray} can contain only one column");return m},htmlBuilder:Tt,mathmlBuilder:Nt}),Ct({type:"array",names:["cases","dcases","rcases","drcases"],props:{numArgs:0},handler(o){let n={arraystretch:1.2,cols:[{type:"align",align:"l",pregap:0,postgap:1},{type:"align",align:"l",pregap:0,postgap:0}]},s=Wt(o.parser,n,si(o.envName));return{type:"leftright",mode:o.mode,body:[s],left:o.envName.indexOf("r")>-1?".":"\\{",right:o.envName.indexOf("r")>-1?"\\}":".",rightColor:void 0}},htmlBuilder:Tt,mathmlBuilder:Nt}),Ct({type:"array",names:["align","align*","aligned","split"],props:{numArgs:0},handler:Ml,htmlBuilder:Tt,mathmlBuilder:Nt}),Ct({type:"array",names:["gathered","gather","gather*"],props:{numArgs:0},handler(o){z.contains(["gather","gather*"],o.envName)&&On(o);let n={cols:[{type:"align",align:"c"}],addJot:!0,colSeparationType:"gather",autoTag:li(o.envName),emptySingleRow:!0,leqno:o.parser.settings.leqno};return Wt(o.parser,n,"display")},htmlBuilder:Tt,mathmlBuilder:Nt}),Ct({type:"array",names:["alignat","alignat*","alignedat"],props:{numArgs:1},handler:Ml,htmlBuilder:Tt,mathmlBuilder:Nt}),Ct({type:"array",names:["equation","equation*"],props:{numArgs:0},handler(o){On(o);let n={autoTag:li(o.envName),emptySingleRow:!0,singleRow:!0,maxNumCols:1,leqno:o.parser.settings.leqno};return Wt(o.parser,n,"display")},htmlBuilder:Tt,mathmlBuilder:Nt}),Ct({type:"array",names:["CD"],props:{numArgs:0},handler(o){return On(o),Jh(o.parser)},htmlBuilder:Tt,mathmlBuilder:Nt}),k("\\nonumber","\\gdef\\@eqnsw{0}"),k("\\notag","\\nonumber"),j({type:"text",names:["\\hline","\\hdashline"],props:{numArgs:0,allowedInText:!0,allowedInMath:!0},handler(o,n){throw new i(o.funcName+" valid only within array environment")}});var Al=wl;j({type:"environment",names:["\\begin","\\end"],props:{numArgs:1,argTypes:["text"]},handler(o,n){let{parser:s,funcName:c}=o,f=n[0];if(f.type!=="ordgroup")throw new i("Invalid environment name",f);let m="";for(let x=0;x<f.body.length;++x)m+=le(f.body[x],"textord").text;if(c==="\\begin"){if(!Al.hasOwnProperty(m))throw new i("No such environment: "+m,f);let x=Al[m],{args:w,optArgs:M}=s.parseArguments("\\begin{"+m+"}",x),N={mode:s.mode,envName:m,parser:s},D=x.handler(N,w,M);s.expect("\\end",!1);let B=s.nextToken,R=le(s.parseFunction(),"environment");if(R.name!==m)throw new i("Mismatch: \\begin{"+m+"} matched by \\end{"+R.name+"}",B);return D}return{type:"environment",mode:s.mode,name:m,nameGroup:f}}});let _l=(o,n)=>{let s=o.font,c=n.withFont(s);return fe(o.body,c)},Cl=(o,n)=>{let s=o.font,c=n.withFont(s);return ye(o.body,c)},Tl={"\\Bbb":"\\mathbb","\\bold":"\\mathbf","\\frak":"\\mathfrak","\\bm":"\\boldsymbol"};j({type:"font",names:["\\mathrm","\\mathit","\\mathbf","\\mathnormal","\\mathsfit","\\mathbb","\\mathcal","\\mathfrak","\\mathscr","\\mathsf","\\mathtt","\\Bbb","\\bold","\\frak"],props:{numArgs:1,allowedInArgument:!0},handler:(o,n)=>{let{parser:s,funcName:c}=o,f=Tn(n[0]),m=c;return m in Tl&&(m=Tl[m]),{type:"font",mode:s.mode,font:m.slice(1),body:f}},htmlBuilder:_l,mathmlBuilder:Cl}),j({type:"mclass",names:["\\boldsymbol","\\bm"],props:{numArgs:1},handler:(o,n)=>{let{parser:s}=o,c=n[0],f=z.isCharacterBox(c);return{type:"mclass",mode:s.mode,mclass:Dn(c),body:[{type:"font",mode:s.mode,font:"boldsymbol",body:c}],isCharacterBox:f}}}),j({type:"font",names:["\\rm","\\sf","\\tt","\\bf","\\it","\\cal"],props:{numArgs:0,allowedInText:!0},handler:(o,n)=>{let{parser:s,funcName:c,breakOnTokenText:f}=o,{mode:m}=s,x=s.parseExpression(!0,f),w="math"+c.slice(1);return{type:"font",mode:m,font:w,body:{type:"ordgroup",mode:s.mode,body:x}}},htmlBuilder:_l,mathmlBuilder:Cl});let Nl=(o,n)=>{let s=n;return o==="display"?s=s.id>=Y.SCRIPT.id?s.text():Y.DISPLAY:o==="text"&&s.size===Y.DISPLAY.size?s=Y.TEXT:o==="script"?s=Y.SCRIPT:o==="scriptscript"&&(s=Y.SCRIPTSCRIPT),s},ai=(o,n)=>{let s=Nl(o.size,n.style),c=s.fracNum(),f=s.fracDen(),m;m=n.havingStyle(c);let x=fe(o.numer,m,n);if(o.continued){let he=8.5/n.fontMetrics().ptPerEm,pe=3.5/n.fontMetrics().ptPerEm;x.height=x.height<he?he:x.height,x.depth=x.depth<pe?pe:x.depth}m=n.havingStyle(f);let w=fe(o.denom,m,n),M,N,D;o.hasBarLine?(o.barSize?(N=Ce(o.barSize,n),M=O.makeLineSpan("frac-line",n,N)):M=O.makeLineSpan("frac-line",n),N=M.height,D=M.height):(M=null,N=0,D=n.fontMetrics().defaultRuleThickness);let B,R,H;s.size===Y.DISPLAY.size||o.size==="display"?(B=n.fontMetrics().num1,N>0?R=3*D:R=7*D,H=n.fontMetrics().denom1):(N>0?(B=n.fontMetrics().num2,R=D):(B=n.fontMetrics().num3,R=3*D),H=n.fontMetrics().denom2);let Z;if(M){let he=n.fontMetrics().axisHeight;B-x.depth-(he+.5*N)<R&&(B+=R-(B-x.depth-(he+.5*N))),he-.5*N-(w.height-H)<R&&(H+=R-(he-.5*N-(w.height-H)));let pe=-(he-.5*N);Z=O.makeVList({positionType:"individualShift",children:[{type:"elem",elem:w,shift:H},{type:"elem",elem:M,shift:pe},{type:"elem",elem:x,shift:-B}]},n)}else{let he=B-x.depth-(w.height-H);he<R&&(B+=.5*(R-he),H+=.5*(R-he)),Z=O.makeVList({positionType:"individualShift",children:[{type:"elem",elem:w,shift:H},{type:"elem",elem:x,shift:-B}]},n)}m=n.havingStyle(s),Z.height*=m.sizeMultiplier/n.sizeMultiplier,Z.depth*=m.sizeMultiplier/n.sizeMultiplier;let Q;s.size===Y.DISPLAY.size?Q=n.fontMetrics().delim1:s.size===Y.SCRIPTSCRIPT.size?Q=n.havingStyle(Y.SCRIPT).fontMetrics().delim2:Q=n.fontMetrics().delim2;let ue,ae;return o.leftDelim==null?ue=Lr(n,["mopen"]):ue=Lt.customSizedDelim(o.leftDelim,Q,!0,n.havingStyle(s),o.mode,["mopen"]),o.continued?ae=O.makeSpan([]):o.rightDelim==null?ae=Lr(n,["mclose"]):ae=Lt.customSizedDelim(o.rightDelim,Q,!0,n.havingStyle(s),o.mode,["mclose"]),O.makeSpan(["mord"].concat(m.sizingClasses(n)),[ue,O.makeSpan(["mfrac"],[Z]),ae],n)},ci=(o,n)=>{let s=new V.MathNode("mfrac",[ye(o.numer,n),ye(o.denom,n)]);if(!o.hasBarLine)s.setAttribute("linethickness","0px");else if(o.barSize){let f=Ce(o.barSize,n);s.setAttribute("linethickness",U(f))}let c=Nl(o.size,n.style);if(c.size!==n.style.size){s=new V.MathNode("mstyle",[s]);let f=c.size===Y.DISPLAY.size?"true":"false";s.setAttribute("displaystyle",f),s.setAttribute("scriptlevel","0")}if(o.leftDelim!=null||o.rightDelim!=null){let f=[];if(o.leftDelim!=null){let m=new V.MathNode("mo",[new V.TextNode(o.leftDelim.replace("\\",""))]);m.setAttribute("fence","true"),f.push(m)}if(f.push(s),o.rightDelim!=null){let m=new V.MathNode("mo",[new V.TextNode(o.rightDelim.replace("\\",""))]);m.setAttribute("fence","true"),f.push(m)}return Y0(f)}return s};j({type:"genfrac",names:["\\dfrac","\\frac","\\tfrac","\\dbinom","\\binom","\\tbinom","\\\\atopfrac","\\\\bracefrac","\\\\brackfrac"],props:{numArgs:2,allowedInArgument:!0},handler:(o,n)=>{let{parser:s,funcName:c}=o,f=n[0],m=n[1],x,w=null,M=null,N="auto";switch(c){case"\\dfrac":case"\\frac":case"\\tfrac":x=!0;break;case"\\\\atopfrac":x=!1;break;case"\\dbinom":case"\\binom":case"\\tbinom":x=!1,w="(",M=")";break;case"\\\\bracefrac":x=!1,w="\\{",M="\\}";break;case"\\\\brackfrac":x=!1,w="[",M="]";break;default:throw new Error("Unrecognized genfrac command")}switch(c){case"\\dfrac":case"\\dbinom":N="display";break;case"\\tfrac":case"\\tbinom":N="text";break}return{type:"genfrac",mode:s.mode,continued:!1,numer:f,denom:m,hasBarLine:x,leftDelim:w,rightDelim:M,size:N,barSize:null}},htmlBuilder:ai,mathmlBuilder:ci}),j({type:"genfrac",names:["\\cfrac"],props:{numArgs:2},handler:(o,n)=>{let{parser:s,funcName:c}=o,f=n[0],m=n[1];return{type:"genfrac",mode:s.mode,continued:!0,numer:f,denom:m,hasBarLine:!0,leftDelim:null,rightDelim:null,size:"display",barSize:null}}}),j({type:"infix",names:["\\over","\\choose","\\atop","\\brace","\\brack"],props:{numArgs:0,infix:!0},handler(o){let{parser:n,funcName:s,token:c}=o,f;switch(s){case"\\over":f="\\frac";break;case"\\choose":f="\\binom";break;case"\\atop":f="\\\\atopfrac";break;case"\\brace":f="\\\\bracefrac";break;case"\\brack":f="\\\\brackfrac";break;default:throw new Error("Unrecognized infix genfrac command")}return{type:"infix",mode:n.mode,replaceWith:f,token:c}}});let El=["display","text","script","scriptscript"],zl=function(o){let n=null;return o.length>0&&(n=o,n=n==="."?null:n),n};j({type:"genfrac",names:["\\genfrac"],props:{numArgs:6,allowedInArgument:!0,argTypes:["math","math","size","text","math","math"]},handler(o,n){let{parser:s}=o,c=n[4],f=n[5],m=Tn(n[0]),x=m.type==="atom"&&m.family==="open"?zl(m.text):null,w=Tn(n[1]),M=w.type==="atom"&&w.family==="close"?zl(w.text):null,N=le(n[2],"size"),D,B=null;N.isBlank?D=!0:(B=N.value,D=B.number>0);let R="auto",H=n[3];if(H.type==="ordgroup"){if(H.body.length>0){let Z=le(H.body[0],"textord");R=El[Number(Z.text)]}}else H=le(H,"textord"),R=El[Number(H.text)];return{type:"genfrac",mode:s.mode,numer:c,denom:f,continued:!1,hasBarLine:D,barSize:B,leftDelim:x,rightDelim:M,size:R}},htmlBuilder:ai,mathmlBuilder:ci}),j({type:"infix",names:["\\above"],props:{numArgs:1,argTypes:["size"],infix:!0},handler(o,n){let{parser:s,funcName:c,token:f}=o;return{type:"infix",mode:s.mode,replaceWith:"\\\\abovefrac",size:le(n[0],"size").value,token:f}}}),j({type:"genfrac",names:["\\\\abovefrac"],props:{numArgs:3,argTypes:["math","size","math"]},handler:(o,n)=>{let{parser:s,funcName:c}=o,f=n[0],m=T(le(n[1],"infix").size),x=n[2],w=m.number>0;return{type:"genfrac",mode:s.mode,numer:f,denom:x,continued:!1,hasBarLine:w,barSize:m,leftDelim:null,rightDelim:null,size:"auto"}},htmlBuilder:ai,mathmlBuilder:ci});let Dl=(o,n)=>{let s=n.style,c,f;o.type==="supsub"?(c=o.sup?fe(o.sup,n.havingStyle(s.sup()),n):fe(o.sub,n.havingStyle(s.sub()),n),f=le(o.base,"horizBrace")):f=le(o,"horizBrace");let m=fe(f.base,n.havingBaseStyle(Y.DISPLAY)),x=Rt.svgSpan(f,n),w;if(f.isOver?(w=O.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:m},{type:"kern",size:.1},{type:"elem",elem:x}]},n),w.children[0].children[0].children[1].classes.push("svg-align")):(w=O.makeVList({positionType:"bottom",positionData:m.depth+.1+x.height,children:[{type:"elem",elem:x},{type:"kern",size:.1},{type:"elem",elem:m}]},n),w.children[0].children[0].children[0].classes.push("svg-align")),c){let M=O.makeSpan(["mord",f.isOver?"mover":"munder"],[w],n);f.isOver?w=O.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:M},{type:"kern",size:.2},{type:"elem",elem:c}]},n):w=O.makeVList({positionType:"bottom",positionData:M.depth+.2+c.height+c.depth,children:[{type:"elem",elem:c},{type:"kern",size:.2},{type:"elem",elem:M}]},n)}return O.makeSpan(["mord",f.isOver?"mover":"munder"],[w],n)};j({type:"horizBrace",names:["\\overbrace","\\underbrace"],props:{numArgs:1},handler(o,n){let{parser:s,funcName:c}=o;return{type:"horizBrace",mode:s.mode,label:c,isOver:/^\\over/.test(c),base:n[0]}},htmlBuilder:Dl,mathmlBuilder:(o,n)=>{let s=Rt.mathMLnode(o.label);return new V.MathNode(o.isOver?"mover":"munder",[ye(o.base,n),s])}}),j({type:"href",names:["\\href"],props:{numArgs:2,argTypes:["url","original"],allowedInText:!0},handler:(o,n)=>{let{parser:s}=o,c=n[1],f=le(n[0],"url").url;return s.settings.isTrusted({command:"\\href",url:f})?{type:"href",mode:s.mode,href:f,body:Be(c)}:s.formatUnsupportedCmd("\\href")},htmlBuilder:(o,n)=>{let s=Le(o.body,n,!1);return O.makeAnchor(o.href,[],s,n)},mathmlBuilder:(o,n)=>{let s=jt(o.body,n);return s instanceof ct||(s=new ct("mrow",[s])),s.setAttribute("href",o.href),s}}),j({type:"href",names:["\\url"],props:{numArgs:1,argTypes:["url"],allowedInText:!0},handler:(o,n)=>{let{parser:s}=o,c=le(n[0],"url").url;if(!s.settings.isTrusted({command:"\\url",url:c}))return s.formatUnsupportedCmd("\\url");let f=[];for(let x=0;x<c.length;x++){let w=c[x];w==="~"&&(w="\\textasciitilde"),f.push({type:"textord",mode:"text",text:w})}let m={type:"text",mode:s.mode,font:"\\texttt",body:f};return{type:"href",mode:s.mode,href:c,body:Be(m)}}}),j({type:"hbox",names:["\\hbox"],props:{numArgs:1,argTypes:["text"],allowedInText:!0,primitive:!0},handler(o,n){let{parser:s}=o;return{type:"hbox",mode:s.mode,body:Be(n[0])}},htmlBuilder(o,n){let s=Le(o.body,n,!1);return O.makeFragment(s)},mathmlBuilder(o,n){return new V.MathNode("mrow",et(o.body,n))}}),j({type:"html",names:["\\htmlClass","\\htmlId","\\htmlStyle","\\htmlData"],props:{numArgs:2,argTypes:["raw","original"],allowedInText:!0},handler:(o,n)=>{let{parser:s,funcName:c,token:f}=o,m=le(n[0],"raw").string,x=n[1];s.settings.strict&&s.settings.reportNonstrict("htmlExtension","HTML extension is disabled on strict mode");let w,M={};switch(c){case"\\htmlClass":M.class=m,w={command:"\\htmlClass",class:m};break;case"\\htmlId":M.id=m,w={command:"\\htmlId",id:m};break;case"\\htmlStyle":M.style=m,w={command:"\\htmlStyle",style:m};break;case"\\htmlData":{let N=m.split(",");for(let D=0;D<N.length;D++){let B=N[D].split("=");if(B.length!==2)throw new i("Error parsing key-value for \\htmlData");M["data-"+B[0].trim()]=B[1].trim()}w={command:"\\htmlData",attributes:M};break}default:throw new Error("Unrecognized html command")}return s.settings.isTrusted(w)?{type:"html",mode:s.mode,attributes:M,body:Be(x)}:s.formatUnsupportedCmd(c)},htmlBuilder:(o,n)=>{let s=Le(o.body,n,!1),c=["enclosing"];o.attributes.class&&c.push(...o.attributes.class.trim().split(/\s+/));let f=O.makeSpan(c,s,n);for(let m in o.attributes)m!=="class"&&o.attributes.hasOwnProperty(m)&&f.setAttribute(m,o.attributes[m]);return f},mathmlBuilder:(o,n)=>jt(o.body,n)}),j({type:"htmlmathml",names:["\\html@mathml"],props:{numArgs:2,allowedInText:!0},handler:(o,n)=>{let{parser:s}=o;return{type:"htmlmathml",mode:s.mode,html:Be(n[0]),mathml:Be(n[1])}},htmlBuilder:(o,n)=>{let s=Le(o.html,n,!1);return O.makeFragment(s)},mathmlBuilder:(o,n)=>jt(o.mathml,n)});let ui=function(o){if(/^[-+]? *(\d+(\.\d*)?|\.\d+)$/.test(o))return{number:+o,unit:"bp"};{let n=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(o);if(!n)throw new i("Invalid size: '"+o+"' in \\includegraphics");let s={number:+(n[1]+n[2]),unit:n[3]};if(!Ro(s))throw new i("Invalid unit: '"+s.unit+"' in \\includegraphics.");return s}};j({type:"includegraphics",names:["\\includegraphics"],props:{numArgs:1,numOptionalArgs:1,argTypes:["raw","url"],allowedInText:!1},handler:(o,n,s)=>{let{parser:c}=o,f={number:0,unit:"em"},m={number:.9,unit:"em"},x={number:0,unit:"em"},w="";if(s[0]){let D=le(s[0],"raw").string.split(",");for(let B=0;B<D.length;B++){let R=D[B].split("=");if(R.length===2){let H=R[1].trim();switch(R[0].trim()){case"alt":w=H;break;case"width":f=ui(H);break;case"height":m=ui(H);break;case"totalheight":x=ui(H);break;default:throw new i("Invalid key: '"+R[0]+"' in \\includegraphics.")}}}}let M=le(n[0],"url").url;return w===""&&(w=M,w=w.replace(/^.*[\\/]/,""),w=w.substring(0,w.lastIndexOf("."))),c.settings.isTrusted({command:"\\includegraphics",url:M})?{type:"includegraphics",mode:c.mode,alt:w,width:f,height:m,totalheight:x,src:M}:c.formatUnsupportedCmd("\\includegraphics")},htmlBuilder:(o,n)=>{let s=Ce(o.height,n),c=0;o.totalheight.number>0&&(c=Ce(o.totalheight,n)-s);let f=0;o.width.number>0&&(f=Ce(o.width,n));let m={height:U(s+c)};f>0&&(m.width=U(f)),c>0&&(m.verticalAlign=U(-c));let x=new xh(o.src,o.alt,m);return x.height=s,x.depth=c,x},mathmlBuilder:(o,n)=>{let s=new V.MathNode("mglyph",[]);s.setAttribute("alt",o.alt);let c=Ce(o.height,n),f=0;if(o.totalheight.number>0&&(f=Ce(o.totalheight,n)-c,s.setAttribute("valign",U(-f))),s.setAttribute("height",U(c+f)),o.width.number>0){let m=Ce(o.width,n);s.setAttribute("width",U(m))}return s.setAttribute("src",o.src),s}}),j({type:"kern",names:["\\kern","\\mkern","\\hskip","\\mskip"],props:{numArgs:1,argTypes:["size"],primitive:!0,allowedInText:!0},handler(o,n){let{parser:s,funcName:c}=o,f=le(n[0],"size");if(s.settings.strict){let m=c[1]==="m",x=f.value.unit==="mu";m?(x||s.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+c+" supports only mu units, "+("not "+f.value.unit+" units")),s.mode!=="math"&&s.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+c+" works only in math mode")):x&&s.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+c+" doesn't support mu units")}return{type:"kern",mode:s.mode,dimension:f.value}},htmlBuilder(o,n){return O.makeGlue(o.dimension,n)},mathmlBuilder(o,n){let s=Ce(o.dimension,n);return new V.SpaceNode(s)}}),j({type:"lap",names:["\\mathllap","\\mathrlap","\\mathclap"],props:{numArgs:1,allowedInText:!0},handler:(o,n)=>{let{parser:s,funcName:c}=o,f=n[0];return{type:"lap",mode:s.mode,alignment:c.slice(5),body:f}},htmlBuilder:(o,n)=>{let s;o.alignment==="clap"?(s=O.makeSpan([],[fe(o.body,n)]),s=O.makeSpan(["inner"],[s],n)):s=O.makeSpan(["inner"],[fe(o.body,n)]);let c=O.makeSpan(["fix"],[]),f=O.makeSpan([o.alignment],[s,c],n),m=O.makeSpan(["strut"]);return m.style.height=U(f.height+f.depth),f.depth&&(m.style.verticalAlign=U(-f.depth)),f.children.unshift(m),f=O.makeSpan(["thinbox"],[f],n),O.makeSpan(["mord","vbox"],[f],n)},mathmlBuilder:(o,n)=>{let s=new V.MathNode("mpadded",[ye(o.body,n)]);if(o.alignment!=="rlap"){let c=o.alignment==="llap"?"-1":"-0.5";s.setAttribute("lspace",c+"width")}return s.setAttribute("width","0px"),s}}),j({type:"styling",names:["\\(","$"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(o,n){let{funcName:s,parser:c}=o,f=c.mode;c.switchMode("math");let m=s==="\\("?"\\)":"$",x=c.parseExpression(!1,m);return c.expect(m),c.switchMode(f),{type:"styling",mode:c.mode,style:"text",body:x}}}),j({type:"text",names:["\\)","\\]"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(o,n){throw new i("Mismatched "+o.funcName)}});let Il=(o,n)=>{switch(n.style.size){case Y.DISPLAY.size:return o.display;case Y.TEXT.size:return o.text;case Y.SCRIPT.size:return o.script;case Y.SCRIPTSCRIPT.size:return o.scriptscript;default:return o.text}};j({type:"mathchoice",names:["\\mathchoice"],props:{numArgs:4,primitive:!0},handler:(o,n)=>{let{parser:s}=o;return{type:"mathchoice",mode:s.mode,display:Be(n[0]),text:Be(n[1]),script:Be(n[2]),scriptscript:Be(n[3])}},htmlBuilder:(o,n)=>{let s=Il(o,n),c=Le(s,n,!1);return O.makeFragment(c)},mathmlBuilder:(o,n)=>{let s=Il(o,n);return jt(s,n)}});let Bl=(o,n,s,c,f,m,x)=>{o=O.makeSpan([],[o]);let w=s&&z.isCharacterBox(s),M,N;if(n){let R=fe(n,c.havingStyle(f.sup()),c);N={elem:R,kern:Math.max(c.fontMetrics().bigOpSpacing1,c.fontMetrics().bigOpSpacing3-R.depth)}}if(s){let R=fe(s,c.havingStyle(f.sub()),c);M={elem:R,kern:Math.max(c.fontMetrics().bigOpSpacing2,c.fontMetrics().bigOpSpacing4-R.height)}}let D;if(N&&M){let R=c.fontMetrics().bigOpSpacing5+M.elem.height+M.elem.depth+M.kern+o.depth+x;D=O.makeVList({positionType:"bottom",positionData:R,children:[{type:"kern",size:c.fontMetrics().bigOpSpacing5},{type:"elem",elem:M.elem,marginLeft:U(-m)},{type:"kern",size:M.kern},{type:"elem",elem:o},{type:"kern",size:N.kern},{type:"elem",elem:N.elem,marginLeft:U(m)},{type:"kern",size:c.fontMetrics().bigOpSpacing5}]},c)}else if(M){let R=o.height-x;D=O.makeVList({positionType:"top",positionData:R,children:[{type:"kern",size:c.fontMetrics().bigOpSpacing5},{type:"elem",elem:M.elem,marginLeft:U(-m)},{type:"kern",size:M.kern},{type:"elem",elem:o}]},c)}else if(N){let R=o.depth+x;D=O.makeVList({positionType:"bottom",positionData:R,children:[{type:"elem",elem:o},{type:"kern",size:N.kern},{type:"elem",elem:N.elem,marginLeft:U(m)},{type:"kern",size:c.fontMetrics().bigOpSpacing5}]},c)}else return o;let B=[D];if(M&&m!==0&&!w){let R=O.makeSpan(["mspace"],[],c);R.style.marginRight=U(m),B.unshift(R)}return O.makeSpan(["mop","op-limits"],B,c)},Ol=["\\smallint"],kr=(o,n)=>{let s,c,f=!1,m;o.type==="supsub"?(s=o.sup,c=o.sub,m=le(o.base,"op"),f=!0):m=le(o,"op");let x=n.style,w=!1;x.size===Y.DISPLAY.size&&m.symbol&&!z.contains(Ol,m.name)&&(w=!0);let M;if(m.symbol){let B=w?"Size2-Regular":"Size1-Regular",R="";if((m.name==="\\oiint"||m.name==="\\oiiint")&&(R=m.name.slice(1),m.name=R==="oiint"?"\\iint":"\\iiint"),M=O.makeSymbol(m.name,B,"math",n,["mop","op-symbol",w?"large-op":"small-op"]),R.length>0){let H=M.italic,Z=O.staticSvg(R+"Size"+(w?"2":"1"),n);M=O.makeVList({positionType:"individualShift",children:[{type:"elem",elem:M,shift:0},{type:"elem",elem:Z,shift:w?.08:0}]},n),m.name="\\"+R,M.classes.unshift("mop"),M.italic=H}}else if(m.body){let B=Le(m.body,n,!0);B.length===1&&B[0]instanceof ft?(M=B[0],M.classes[0]="mop"):M=O.makeSpan(["mop"],B,n)}else{let B=[];for(let R=1;R<m.name.length;R++)B.push(O.mathsym(m.name[R],m.mode,n));M=O.makeSpan(["mop"],B,n)}let N=0,D=0;return(M instanceof ft||m.name==="\\oiint"||m.name==="\\oiiint")&&!m.suppressBaseShift&&(N=(M.height-M.depth)/2-n.fontMetrics().axisHeight,D=M.italic),f?Bl(M,s,c,n,x,D,N):(N&&(M.style.position="relative",M.style.top=U(N)),M)},Hr=(o,n)=>{let s;if(o.symbol)s=new ct("mo",[dt(o.name,o.mode)]),z.contains(Ol,o.name)&&s.setAttribute("largeop","false");else if(o.body)s=new ct("mo",et(o.body,n));else{s=new ct("mi",[new _t(o.name.slice(1))]);let c=new ct("mo",[dt("\u2061","text")]);o.parentIsSupSub?s=new ct("mrow",[s,c]):s=Jo([s,c])}return s},f1={"\u220F":"\\prod","\u2210":"\\coprod","\u2211":"\\sum","\u22C0":"\\bigwedge","\u22C1":"\\bigvee","\u22C2":"\\bigcap","\u22C3":"\\bigcup","\u2A00":"\\bigodot","\u2A01":"\\bigoplus","\u2A02":"\\bigotimes","\u2A04":"\\biguplus","\u2A06":"\\bigsqcup"};j({type:"op",names:["\\coprod","\\bigvee","\\bigwedge","\\biguplus","\\bigcap","\\bigcup","\\intop","\\prod","\\sum","\\bigotimes","\\bigoplus","\\bigodot","\\bigsqcup","\\smallint","\u220F","\u2210","\u2211","\u22C0","\u22C1","\u22C2","\u22C3","\u2A00","\u2A01","\u2A02","\u2A04","\u2A06"],props:{numArgs:0},handler:(o,n)=>{let{parser:s,funcName:c}=o,f=c;return f.length===1&&(f=f1[f]),{type:"op",mode:s.mode,limits:!0,parentIsSupSub:!1,symbol:!0,name:f}},htmlBuilder:kr,mathmlBuilder:Hr}),j({type:"op",names:["\\mathop"],props:{numArgs:1,primitive:!0},handler:(o,n)=>{let{parser:s}=o,c=n[0];return{type:"op",mode:s.mode,limits:!1,parentIsSupSub:!1,symbol:!1,body:Be(c)}},htmlBuilder:kr,mathmlBuilder:Hr});let d1={"\u222B":"\\int","\u222C":"\\iint","\u222D":"\\iiint","\u222E":"\\oint","\u222F":"\\oiint","\u2230":"\\oiiint"};j({type:"op",names:["\\arcsin","\\arccos","\\arctan","\\arctg","\\arcctg","\\arg","\\ch","\\cos","\\cosec","\\cosh","\\cot","\\cotg","\\coth","\\csc","\\ctg","\\cth","\\deg","\\dim","\\exp","\\hom","\\ker","\\lg","\\ln","\\log","\\sec","\\sin","\\sinh","\\sh","\\tan","\\tanh","\\tg","\\th"],props:{numArgs:0},handler(o){let{parser:n,funcName:s}=o;return{type:"op",mode:n.mode,limits:!1,parentIsSupSub:!1,symbol:!1,name:s}},htmlBuilder:kr,mathmlBuilder:Hr}),j({type:"op",names:["\\det","\\gcd","\\inf","\\lim","\\max","\\min","\\Pr","\\sup"],props:{numArgs:0},handler(o){let{parser:n,funcName:s}=o;return{type:"op",mode:n.mode,limits:!0,parentIsSupSub:!1,symbol:!1,name:s}},htmlBuilder:kr,mathmlBuilder:Hr}),j({type:"op",names:["\\int","\\iint","\\iiint","\\oint","\\oiint","\\oiiint","\u222B","\u222C","\u222D","\u222E","\u222F","\u2230"],props:{numArgs:0},handler(o){let{parser:n,funcName:s}=o,c=s;return c.length===1&&(c=d1[c]),{type:"op",mode:n.mode,limits:!1,parentIsSupSub:!1,symbol:!0,name:c}},htmlBuilder:kr,mathmlBuilder:Hr});let Fl=(o,n)=>{let s,c,f=!1,m;o.type==="supsub"?(s=o.sup,c=o.sub,m=le(o.base,"operatorname"),f=!0):m=le(o,"operatorname");let x;if(m.body.length>0){let w=m.body.map(N=>{let D=N.text;return typeof D=="string"?{type:"textord",mode:N.mode,text:D}:N}),M=Le(w,n.withFont("mathrm"),!0);for(let N=0;N<M.length;N++){let D=M[N];D instanceof ft&&(D.text=D.text.replace(/\u2212/,"-").replace(/\u2217/,"*"))}x=O.makeSpan(["mop"],M,n)}else x=O.makeSpan(["mop"],[],n);return f?Bl(x,s,c,n,n.style,0,0):x};j({type:"operatorname",names:["\\operatorname@","\\operatornamewithlimits"],props:{numArgs:1},handler:(o,n)=>{let{parser:s,funcName:c}=o,f=n[0];return{type:"operatorname",mode:s.mode,body:Be(f),alwaysHandleSupSub:c==="\\operatornamewithlimits",limits:!1,parentIsSupSub:!1}},htmlBuilder:Fl,mathmlBuilder:(o,n)=>{let s=et(o.body,n.withFont("mathrm")),c=!0;for(let x=0;x<s.length;x++){let w=s[x];if(!(w instanceof V.SpaceNode))if(w instanceof V.MathNode)switch(w.type){case"mi":case"mn":case"ms":case"mspace":case"mtext":break;case"mo":{let M=w.children[0];w.children.length===1&&M instanceof V.TextNode?M.text=M.text.replace(/\u2212/,"-").replace(/\u2217/,"*"):c=!1;break}default:c=!1}else c=!1}if(c){let x=s.map(w=>w.toText()).join("");s=[new V.TextNode(x)]}let f=new V.MathNode("mi",s);f.setAttribute("mathvariant","normal");let m=new V.MathNode("mo",[dt("\u2061","text")]);return o.parentIsSupSub?new V.MathNode("mrow",[f,m]):V.newDocumentFragment([f,m])}}),k("\\operatorname","\\@ifstar\\operatornamewithlimits\\operatorname@"),ir({type:"ordgroup",htmlBuilder(o,n){return o.semisimple?O.makeFragment(Le(o.body,n,!1)):O.makeSpan(["mord"],Le(o.body,n,!0),n)},mathmlBuilder(o,n){return jt(o.body,n,!0)}}),j({type:"overline",names:["\\overline"],props:{numArgs:1},handler(o,n){let{parser:s}=o,c=n[0];return{type:"overline",mode:s.mode,body:c}},htmlBuilder(o,n){let s=fe(o.body,n.havingCrampedStyle()),c=O.makeLineSpan("overline-line",n),f=n.fontMetrics().defaultRuleThickness,m=O.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:s},{type:"kern",size:3*f},{type:"elem",elem:c},{type:"kern",size:f}]},n);return O.makeSpan(["mord","overline"],[m],n)},mathmlBuilder(o,n){let s=new V.MathNode("mo",[new V.TextNode("\u203E")]);s.setAttribute("stretchy","true");let c=new V.MathNode("mover",[ye(o.body,n),s]);return c.setAttribute("accent","true"),c}}),j({type:"phantom",names:["\\phantom"],props:{numArgs:1,allowedInText:!0},handler:(o,n)=>{let{parser:s}=o,c=n[0];return{type:"phantom",mode:s.mode,body:Be(c)}},htmlBuilder:(o,n)=>{let s=Le(o.body,n.withPhantom(),!1);return O.makeFragment(s)},mathmlBuilder:(o,n)=>{let s=et(o.body,n);return new V.MathNode("mphantom",s)}}),j({type:"hphantom",names:["\\hphantom"],props:{numArgs:1,allowedInText:!0},handler:(o,n)=>{let{parser:s}=o,c=n[0];return{type:"hphantom",mode:s.mode,body:c}},htmlBuilder:(o,n)=>{let s=O.makeSpan([],[fe(o.body,n.withPhantom())]);if(s.height=0,s.depth=0,s.children)for(let c=0;c<s.children.length;c++)s.children[c].height=0,s.children[c].depth=0;return s=O.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:s}]},n),O.makeSpan(["mord"],[s],n)},mathmlBuilder:(o,n)=>{let s=et(Be(o.body),n),c=new V.MathNode("mphantom",s),f=new V.MathNode("mpadded",[c]);return f.setAttribute("height","0px"),f.setAttribute("depth","0px"),f}}),j({type:"vphantom",names:["\\vphantom"],props:{numArgs:1,allowedInText:!0},handler:(o,n)=>{let{parser:s}=o,c=n[0];return{type:"vphantom",mode:s.mode,body:c}},htmlBuilder:(o,n)=>{let s=O.makeSpan(["inner"],[fe(o.body,n.withPhantom())]),c=O.makeSpan(["fix"],[]);return O.makeSpan(["mord","rlap"],[s,c],n)},mathmlBuilder:(o,n)=>{let s=et(Be(o.body),n),c=new V.MathNode("mphantom",s),f=new V.MathNode("mpadded",[c]);return f.setAttribute("width","0px"),f}}),j({type:"raisebox",names:["\\raisebox"],props:{numArgs:2,argTypes:["size","hbox"],allowedInText:!0},handler(o,n){let{parser:s}=o,c=le(n[0],"size").value,f=n[1];return{type:"raisebox",mode:s.mode,dy:c,body:f}},htmlBuilder(o,n){let s=fe(o.body,n),c=Ce(o.dy,n);return O.makeVList({positionType:"shift",positionData:-c,children:[{type:"elem",elem:s}]},n)},mathmlBuilder(o,n){let s=new V.MathNode("mpadded",[ye(o.body,n)]),c=o.dy.number+o.dy.unit;return s.setAttribute("voffset",c),s}}),j({type:"internal",names:["\\relax"],props:{numArgs:0,allowedInText:!0,allowedInArgument:!0},handler(o){let{parser:n}=o;return{type:"internal",mode:n.mode}}}),j({type:"rule",names:["\\rule"],props:{numArgs:2,numOptionalArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["size","size","size"]},handler(o,n,s){let{parser:c}=o,f=s[0],m=le(n[0],"size"),x=le(n[1],"size");return{type:"rule",mode:c.mode,shift:f&&le(f,"size").value,width:m.value,height:x.value}},htmlBuilder(o,n){let s=O.makeSpan(["mord","rule"],[],n),c=Ce(o.width,n),f=Ce(o.height,n),m=o.shift?Ce(o.shift,n):0;return s.style.borderRightWidth=U(c),s.style.borderTopWidth=U(f),s.style.bottom=U(m),s.width=c,s.height=f+m,s.depth=-m,s.maxFontSize=f*1.125*n.sizeMultiplier,s},mathmlBuilder(o,n){let s=Ce(o.width,n),c=Ce(o.height,n),f=o.shift?Ce(o.shift,n):0,m=n.color&&n.getColor()||"black",x=new V.MathNode("mspace");x.setAttribute("mathbackground",m),x.setAttribute("width",U(s)),x.setAttribute("height",U(c));let w=new V.MathNode("mpadded",[x]);return f>=0?w.setAttribute("height",U(f)):(w.setAttribute("height",U(f)),w.setAttribute("depth",U(-f))),w.setAttribute("voffset",U(f)),w}});function Rl(o,n,s){let c=Le(o,n,!1),f=n.sizeMultiplier/s.sizeMultiplier;for(let m=0;m<c.length;m++){let x=c[m].classes.indexOf("sizing");x<0?Array.prototype.push.apply(c[m].classes,n.sizingClasses(s)):c[m].classes[x+1]==="reset-size"+n.size&&(c[m].classes[x+1]="reset-size"+s.size),c[m].height*=f,c[m].depth*=f}return O.makeFragment(c)}let Ll=["\\tiny","\\sixptsize","\\scriptsize","\\footnotesize","\\small","\\normalsize","\\large","\\Large","\\LARGE","\\huge","\\Huge"];j({type:"sizing",names:Ll,props:{numArgs:0,allowedInText:!0},handler:(o,n)=>{let{breakOnTokenText:s,funcName:c,parser:f}=o,m=f.parseExpression(!1,s);return{type:"sizing",mode:f.mode,size:Ll.indexOf(c)+1,body:m}},htmlBuilder:(o,n)=>{let s=n.havingSize(o.size);return Rl(o.body,s,n)},mathmlBuilder:(o,n)=>{let s=n.havingSize(o.size),c=et(o.body,s),f=new V.MathNode("mstyle",c);return f.setAttribute("mathsize",U(s.sizeMultiplier)),f}}),j({type:"smash",names:["\\smash"],props:{numArgs:1,numOptionalArgs:1,allowedInText:!0},handler:(o,n,s)=>{let{parser:c}=o,f=!1,m=!1,x=s[0]&&le(s[0],"ordgroup");if(x){let M="";for(let N=0;N<x.body.length;++N)if(M=x.body[N].text,M==="t")f=!0;else if(M==="b")m=!0;else{f=!1,m=!1;break}}else f=!0,m=!0;let w=n[0];return{type:"smash",mode:c.mode,body:w,smashHeight:f,smashDepth:m}},htmlBuilder:(o,n)=>{let s=O.makeSpan([],[fe(o.body,n)]);if(!o.smashHeight&&!o.smashDepth)return s;if(o.smashHeight&&(s.height=0,s.children))for(let f=0;f<s.children.length;f++)s.children[f].height=0;if(o.smashDepth&&(s.depth=0,s.children))for(let f=0;f<s.children.length;f++)s.children[f].depth=0;let c=O.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:s}]},n);return O.makeSpan(["mord"],[c],n)},mathmlBuilder:(o,n)=>{let s=new V.MathNode("mpadded",[ye(o.body,n)]);return o.smashHeight&&s.setAttribute("height","0px"),o.smashDepth&&s.setAttribute("depth","0px"),s}}),j({type:"sqrt",names:["\\sqrt"],props:{numArgs:1,numOptionalArgs:1},handler(o,n,s){let{parser:c}=o,f=s[0],m=n[0];return{type:"sqrt",mode:c.mode,body:m,index:f}},htmlBuilder(o,n){let s=fe(o.body,n.havingCrampedStyle());s.height===0&&(s.height=n.fontMetrics().xHeight),s=O.wrapFragment(s,n);let f=n.fontMetrics().defaultRuleThickness,m=f;n.style.id<Y.TEXT.id&&(m=n.fontMetrics().xHeight);let x=f+m/4,w=s.height+s.depth+x+f,{span:M,ruleWidth:N,advanceWidth:D}=Lt.sqrtImage(w,n),B=M.height-N;B>s.height+s.depth+x&&(x=(x+B-s.height-s.depth)/2);let R=M.height-s.height-x-N;s.style.paddingLeft=U(D);let H=O.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:s,wrapperClasses:["svg-align"]},{type:"kern",size:-(s.height+R)},{type:"elem",elem:M},{type:"kern",size:N}]},n);if(o.index){let Z=n.havingStyle(Y.SCRIPTSCRIPT),Q=fe(o.index,Z,n),ue=.6*(H.height-H.depth),ae=O.makeVList({positionType:"shift",positionData:-ue,children:[{type:"elem",elem:Q}]},n),he=O.makeSpan(["root"],[ae]);return O.makeSpan(["mord","sqrt"],[he,H],n)}else return O.makeSpan(["mord","sqrt"],[H],n)},mathmlBuilder(o,n){let{body:s,index:c}=o;return c?new V.MathNode("mroot",[ye(s,n),ye(c,n)]):new V.MathNode("msqrt",[ye(s,n)])}});let ql={display:Y.DISPLAY,text:Y.TEXT,script:Y.SCRIPT,scriptscript:Y.SCRIPTSCRIPT};j({type:"styling",names:["\\displaystyle","\\textstyle","\\scriptstyle","\\scriptscriptstyle"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(o,n){let{breakOnTokenText:s,funcName:c,parser:f}=o,m=f.parseExpression(!0,s),x=c.slice(1,c.length-5);return{type:"styling",mode:f.mode,style:x,body:m}},htmlBuilder(o,n){let s=ql[o.style],c=n.havingStyle(s).withFont("");return Rl(o.body,c,n)},mathmlBuilder(o,n){let s=ql[o.style],c=n.havingStyle(s),f=et(o.body,c),m=new V.MathNode("mstyle",f),w={display:["0","true"],text:["0","false"],script:["1","false"],scriptscript:["2","false"]}[o.style];return m.setAttribute("scriptlevel",w[0]),m.setAttribute("displaystyle",w[1]),m}});let p1=function(o,n){let s=o.base;return s?s.type==="op"?s.limits&&(n.style.size===Y.DISPLAY.size||s.alwaysHandleSupSub)?kr:null:s.type==="operatorname"?s.alwaysHandleSupSub&&(n.style.size===Y.DISPLAY.size||s.limits)?Fl:null:s.type==="accent"?z.isCharacterBox(s.base)?W0:null:s.type==="horizBrace"&&!o.sub===s.isOver?Dl:null:null};ir({type:"supsub",htmlBuilder(o,n){let s=p1(o,n);if(s)return s(o,n);let{base:c,sup:f,sub:m}=o,x=fe(c,n),w,M,N=n.fontMetrics(),D=0,B=0,R=c&&z.isCharacterBox(c);if(f){let pe=n.havingStyle(n.style.sup());w=fe(f,pe,n),R||(D=x.height-pe.fontMetrics().supDrop*pe.sizeMultiplier/n.sizeMultiplier)}if(m){let pe=n.havingStyle(n.style.sub());M=fe(m,pe,n),R||(B=x.depth+pe.fontMetrics().subDrop*pe.sizeMultiplier/n.sizeMultiplier)}let H;n.style===Y.DISPLAY?H=N.sup1:n.style.cramped?H=N.sup3:H=N.sup2;let Z=n.sizeMultiplier,Q=U(.5/N.ptPerEm/Z),ue=null;if(M){let pe=o.base&&o.base.type==="op"&&o.base.name&&(o.base.name==="\\oiint"||o.base.name==="\\oiiint");(x instanceof ft||pe)&&(ue=U(-x.italic))}let ae;if(w&&M){D=Math.max(D,H,w.depth+.25*N.xHeight),B=Math.max(B,N.sub2);let we=4*N.defaultRuleThickness;if(D-w.depth-(M.height-B)<we){B=we-(D-w.depth)+M.height;let qe=.8*N.xHeight-(D-w.depth);qe>0&&(D+=qe,B-=qe)}let We=[{type:"elem",elem:M,shift:B,marginRight:Q,marginLeft:ue},{type:"elem",elem:w,shift:-D,marginRight:Q}];ae=O.makeVList({positionType:"individualShift",children:We},n)}else if(M){B=Math.max(B,N.sub1,M.height-.8*N.xHeight);let pe=[{type:"elem",elem:M,marginLeft:ue,marginRight:Q}];ae=O.makeVList({positionType:"shift",positionData:B,children:pe},n)}else if(w)D=Math.max(D,H,w.depth+.25*N.xHeight),ae=O.makeVList({positionType:"shift",positionData:-D,children:[{type:"elem",elem:w,marginRight:Q}]},n);else throw new Error("supsub must have either sup or sub.");let he=V0(x,"right")||"mord";return O.makeSpan([he],[x,O.makeSpan(["msupsub"],[ae])],n)},mathmlBuilder(o,n){let s=!1,c,f;o.base&&o.base.type==="horizBrace"&&(f=!!o.sup,f===o.base.isOver&&(s=!0,c=o.base.isOver)),o.base&&(o.base.type==="op"||o.base.type==="operatorname")&&(o.base.parentIsSupSub=!0);let m=[ye(o.base,n)];o.sub&&m.push(ye(o.sub,n)),o.sup&&m.push(ye(o.sup,n));let x;if(s)x=c?"mover":"munder";else if(o.sub)if(o.sup){let w=o.base;w&&w.type==="op"&&w.limits&&n.style===Y.DISPLAY||w&&w.type==="operatorname"&&w.alwaysHandleSupSub&&(n.style===Y.DISPLAY||w.limits)?x="munderover":x="msubsup"}else{let w=o.base;w&&w.type==="op"&&w.limits&&(n.style===Y.DISPLAY||w.alwaysHandleSupSub)||w&&w.type==="operatorname"&&w.alwaysHandleSupSub&&(w.limits||n.style===Y.DISPLAY)?x="munder":x="msub"}else{let w=o.base;w&&w.type==="op"&&w.limits&&(n.style===Y.DISPLAY||w.alwaysHandleSupSub)||w&&w.type==="operatorname"&&w.alwaysHandleSupSub&&(w.limits||n.style===Y.DISPLAY)?x="mover":x="msup"}return new V.MathNode(x,m)}}),ir({type:"atom",htmlBuilder(o,n){return O.mathsym(o.text,o.mode,n,["m"+o.family])},mathmlBuilder(o,n){let s=new V.MathNode("mo",[dt(o.text,o.mode)]);if(o.family==="bin"){let c=U0(o,n);c==="bold-italic"&&s.setAttribute("mathvariant",c)}else o.family==="punct"?s.setAttribute("separator","true"):(o.family==="open"||o.family==="close")&&s.setAttribute("stretchy","false");return s}});let Pl={mi:"italic",mn:"normal",mtext:"normal"};ir({type:"mathord",htmlBuilder(o,n){return O.makeOrd(o,n,"mathord")},mathmlBuilder(o,n){let s=new V.MathNode("mi",[dt(o.text,o.mode,n)]),c=U0(o,n)||"italic";return c!==Pl[s.type]&&s.setAttribute("mathvariant",c),s}}),ir({type:"textord",htmlBuilder(o,n){return O.makeOrd(o,n,"textord")},mathmlBuilder(o,n){let s=dt(o.text,o.mode,n),c=U0(o,n)||"normal",f;return o.mode==="text"?f=new V.MathNode("mtext",[s]):/[0-9]/.test(o.text)?f=new V.MathNode("mn",[s]):o.text==="\\prime"?f=new V.MathNode("mo",[s]):f=new V.MathNode("mi",[s]),c!==Pl[f.type]&&f.setAttribute("mathvariant",c),f}});let hi={"\\nobreak":"nobreak","\\allowbreak":"allowbreak"},fi={" ":{},"\\ ":{},"~":{className:"nobreak"},"\\space":{},"\\nobreakspace":{className:"nobreak"}};ir({type:"spacing",htmlBuilder(o,n){if(fi.hasOwnProperty(o.text)){let s=fi[o.text].className||"";if(o.mode==="text"){let c=O.makeOrd(o,n,"textord");return c.classes.push(s),c}else return O.makeSpan(["mspace",s],[O.mathsym(o.text,o.mode,n)],n)}else{if(hi.hasOwnProperty(o.text))return O.makeSpan(["mspace",hi[o.text]],[],n);throw new i('Unknown type of space "'+o.text+'"')}},mathmlBuilder(o,n){let s;if(fi.hasOwnProperty(o.text))s=new V.MathNode("mtext",[new V.TextNode("\xA0")]);else{if(hi.hasOwnProperty(o.text))return new V.MathNode("mspace");throw new i('Unknown type of space "'+o.text+'"')}return s}});let Hl=()=>{let o=new V.MathNode("mtd",[]);return o.setAttribute("width","50%"),o};ir({type:"tag",mathmlBuilder(o,n){let s=new V.MathNode("mtable",[new V.MathNode("mtr",[Hl(),new V.MathNode("mtd",[jt(o.body,n)]),Hl(),new V.MathNode("mtd",[jt(o.tag,n)])])]);return s.setAttribute("width","100%"),s}});let $l={"\\text":void 0,"\\textrm":"textrm","\\textsf":"textsf","\\texttt":"texttt","\\textnormal":"textrm"},Vl={"\\textbf":"textbf","\\textmd":"textmd"},m1={"\\textit":"textit","\\textup":"textup"},Gl=(o,n)=>{let s=o.font;if(s){if($l[s])return n.withTextFontFamily($l[s]);if(Vl[s])return n.withTextFontWeight(Vl[s]);if(s==="\\emph")return n.fontShape==="textit"?n.withTextFontShape("textup"):n.withTextFontShape("textit")}else return n;return n.withTextFontShape(m1[s])};j({type:"text",names:["\\text","\\textrm","\\textsf","\\texttt","\\textnormal","\\textbf","\\textmd","\\textit","\\textup","\\emph"],props:{numArgs:1,argTypes:["text"],allowedInArgument:!0,allowedInText:!0},handler(o,n){let{parser:s,funcName:c}=o,f=n[0];return{type:"text",mode:s.mode,body:Be(f),font:c}},htmlBuilder(o,n){let s=Gl(o,n),c=Le(o.body,s,!0);return O.makeSpan(["mord","text"],c,s)},mathmlBuilder(o,n){let s=Gl(o,n);return jt(o.body,s)}}),j({type:"underline",names:["\\underline"],props:{numArgs:1,allowedInText:!0},handler(o,n){let{parser:s}=o;return{type:"underline",mode:s.mode,body:n[0]}},htmlBuilder(o,n){let s=fe(o.body,n),c=O.makeLineSpan("underline-line",n),f=n.fontMetrics().defaultRuleThickness,m=O.makeVList({positionType:"top",positionData:s.height,children:[{type:"kern",size:f},{type:"elem",elem:c},{type:"kern",size:3*f},{type:"elem",elem:s}]},n);return O.makeSpan(["mord","underline"],[m],n)},mathmlBuilder(o,n){let s=new V.MathNode("mo",[new V.TextNode("\u203E")]);s.setAttribute("stretchy","true");let c=new V.MathNode("munder",[ye(o.body,n),s]);return c.setAttribute("accentunder","true"),c}}),j({type:"vcenter",names:["\\vcenter"],props:{numArgs:1,argTypes:["original"],allowedInText:!1},handler(o,n){let{parser:s}=o;return{type:"vcenter",mode:s.mode,body:n[0]}},htmlBuilder(o,n){let s=fe(o.body,n),c=n.fontMetrics().axisHeight,f=.5*(s.height-c-(s.depth+c));return O.makeVList({positionType:"shift",positionData:f,children:[{type:"elem",elem:s}]},n)},mathmlBuilder(o,n){return new V.MathNode("mpadded",[ye(o.body,n)],["vcenter"])}}),j({type:"verb",names:["\\verb"],props:{numArgs:0,allowedInText:!0},handler(o,n,s){throw new i("\\verb ended by end of line instead of matching delimiter")},htmlBuilder(o,n){let s=Yl(o),c=[],f=n.havingStyle(n.style.text());for(let m=0;m<s.length;m++){let x=s[m];x==="~"&&(x="\\textasciitilde"),c.push(O.makeSymbol(x,"Typewriter-Regular",o.mode,f,["mord","texttt"]))}return O.makeSpan(["mord","text"].concat(f.sizingClasses(n)),O.tryCombineChars(c),f)},mathmlBuilder(o,n){let s=new V.TextNode(Yl(o)),c=new V.MathNode("mtext",[s]);return c.setAttribute("mathvariant","monospace"),c}});let Yl=o=>o.body.replace(/ /g,o.star?"\u2423":"\xA0");var Kt=Ko;let Ul=`[ \r
	]`,g1="\\\\[a-zA-Z@]+",x1="\\\\[^\uD800-\uDFFF]",y1="("+g1+")"+Ul+"*",b1=`\\\\(
|[ \r	]+
?)[ \r	]*`,di="[\u0300-\u036F]",v1=new RegExp(di+"+$"),w1="("+Ul+"+)|"+(b1+"|")+"([!-\\[\\]-\u2027\u202A-\uD7FF\uF900-\uFFFF]"+(di+"*")+"|[\uD800-\uDBFF][\uDC00-\uDFFF]"+(di+"*")+"|\\\\verb\\*([^]).*?\\4|\\\\verb([^*a-zA-Z]).*?\\5"+("|"+y1)+("|"+x1+")");class Xl{constructor(n,s){this.input=void 0,this.settings=void 0,this.tokenRegex=void 0,this.catcodes=void 0,this.input=n,this.settings=s,this.tokenRegex=new RegExp(w1,"g"),this.catcodes={"%":14,"~":13}}setCatcode(n,s){this.catcodes[n]=s}lex(){let n=this.input,s=this.tokenRegex.lastIndex;if(s===n.length)return new pt("EOF",new ut(this,s,s));let c=this.tokenRegex.exec(n);if(c===null||c.index!==s)throw new i("Unexpected character: '"+n[s]+"'",new pt(n[s],new ut(this,s,s+1)));let f=c[6]||c[3]||(c[2]?"\\ ":" ");if(this.catcodes[f]===14){let m=n.indexOf(`
`,this.tokenRegex.lastIndex);return m===-1?(this.tokenRegex.lastIndex=n.length,this.settings.reportNonstrict("commentAtEnd","% comment has no terminating newline; LaTeX would fail because of commenting the end of math mode (e.g. $)")):this.tokenRegex.lastIndex=m+1,this.lex()}return new pt(f,new ut(this,s,this.tokenRegex.lastIndex))}}class k1{constructor(n,s){n===void 0&&(n={}),s===void 0&&(s={}),this.current=void 0,this.builtins=void 0,this.undefStack=void 0,this.current=s,this.builtins=n,this.undefStack=[]}beginGroup(){this.undefStack.push({})}endGroup(){if(this.undefStack.length===0)throw new i("Unbalanced namespace destruction: attempt to pop global namespace; please report this as a bug");let n=this.undefStack.pop();for(let s in n)n.hasOwnProperty(s)&&(n[s]==null?delete this.current[s]:this.current[s]=n[s])}endGroups(){for(;this.undefStack.length>0;)this.endGroup()}has(n){return this.current.hasOwnProperty(n)||this.builtins.hasOwnProperty(n)}get(n){return this.current.hasOwnProperty(n)?this.current[n]:this.builtins[n]}set(n,s,c){if(c===void 0&&(c=!1),c){for(let f=0;f<this.undefStack.length;f++)delete this.undefStack[f][n];this.undefStack.length>0&&(this.undefStack[this.undefStack.length-1][n]=s)}else{let f=this.undefStack[this.undefStack.length-1];f&&!f.hasOwnProperty(n)&&(f[n]=this.current[n])}s==null?delete this.current[n]:this.current[n]=s}}var S1=kl;k("\\noexpand",function(o){let n=o.popToken();return o.isExpandable(n.text)&&(n.noexpand=!0,n.treatAsRelax=!0),{tokens:[n],numArgs:0}}),k("\\expandafter",function(o){let n=o.popToken();return o.expandOnce(!0),{tokens:[n],numArgs:0}}),k("\\@firstoftwo",function(o){return{tokens:o.consumeArgs(2)[0],numArgs:0}}),k("\\@secondoftwo",function(o){return{tokens:o.consumeArgs(2)[1],numArgs:0}}),k("\\@ifnextchar",function(o){let n=o.consumeArgs(3);o.consumeSpaces();let s=o.future();return n[0].length===1&&n[0][0].text===s.text?{tokens:n[1],numArgs:0}:{tokens:n[2],numArgs:0}}),k("\\@ifstar","\\@ifnextchar *{\\@firstoftwo{#1}}"),k("\\TextOrMath",function(o){let n=o.consumeArgs(2);return o.mode==="text"?{tokens:n[0],numArgs:0}:{tokens:n[1],numArgs:0}});let jl={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15};k("\\char",function(o){let n=o.popToken(),s,c="";if(n.text==="'")s=8,n=o.popToken();else if(n.text==='"')s=16,n=o.popToken();else if(n.text==="`")if(n=o.popToken(),n.text[0]==="\\")c=n.text.charCodeAt(1);else{if(n.text==="EOF")throw new i("\\char` missing argument");c=n.text.charCodeAt(0)}else s=10;if(s){if(c=jl[n.text],c==null||c>=s)throw new i("Invalid base-"+s+" digit "+n.text);let f;for(;(f=jl[o.future().text])!=null&&f<s;)c*=s,c+=f,o.popToken()}return"\\@char{"+c+"}"});let pi=(o,n,s,c)=>{let f=o.consumeArg().tokens;if(f.length!==1)throw new i("\\newcommand's first argument must be a macro name");let m=f[0].text,x=o.isDefined(m);if(x&&!n)throw new i("\\newcommand{"+m+"} attempting to redefine "+(m+"; use \\renewcommand"));if(!x&&!s)throw new i("\\renewcommand{"+m+"} when command "+m+" does not yet exist; use \\newcommand");let w=0;if(f=o.consumeArg().tokens,f.length===1&&f[0].text==="["){let M="",N=o.expandNextToken();for(;N.text!=="]"&&N.text!=="EOF";)M+=N.text,N=o.expandNextToken();if(!M.match(/^\s*[0-9]+\s*$/))throw new i("Invalid number of arguments: "+M);w=parseInt(M),f=o.consumeArg().tokens}return x&&c||o.macros.set(m,{tokens:f,numArgs:w}),""};k("\\newcommand",o=>pi(o,!1,!0,!1)),k("\\renewcommand",o=>pi(o,!0,!1,!1)),k("\\providecommand",o=>pi(o,!0,!0,!0)),k("\\message",o=>{let n=o.consumeArgs(1)[0];return console.log(n.reverse().map(s=>s.text).join("")),""}),k("\\errmessage",o=>{let n=o.consumeArgs(1)[0];return console.error(n.reverse().map(s=>s.text).join("")),""}),k("\\show",o=>{let n=o.popToken(),s=n.text;return console.log(n,o.macros.get(s),Kt[s],Te.math[s],Te.text[s]),""}),k("\\bgroup","{"),k("\\egroup","}"),k("~","\\nobreakspace"),k("\\lq","`"),k("\\rq","'"),k("\\aa","\\r a"),k("\\AA","\\r A"),k("\\textcopyright","\\html@mathml{\\textcircled{c}}{\\char`\xA9}"),k("\\copyright","\\TextOrMath{\\textcopyright}{\\text{\\textcopyright}}"),k("\\textregistered","\\html@mathml{\\textcircled{\\scriptsize R}}{\\char`\xAE}"),k("\u212C","\\mathscr{B}"),k("\u2130","\\mathscr{E}"),k("\u2131","\\mathscr{F}"),k("\u210B","\\mathscr{H}"),k("\u2110","\\mathscr{I}"),k("\u2112","\\mathscr{L}"),k("\u2133","\\mathscr{M}"),k("\u211B","\\mathscr{R}"),k("\u212D","\\mathfrak{C}"),k("\u210C","\\mathfrak{H}"),k("\u2128","\\mathfrak{Z}"),k("\\Bbbk","\\Bbb{k}"),k("\xB7","\\cdotp"),k("\\llap","\\mathllap{\\textrm{#1}}"),k("\\rlap","\\mathrlap{\\textrm{#1}}"),k("\\clap","\\mathclap{\\textrm{#1}}"),k("\\mathstrut","\\vphantom{(}"),k("\\underbar","\\underline{\\text{#1}}"),k("\\not",'\\html@mathml{\\mathrel{\\mathrlap\\@not}}{\\char"338}'),k("\\neq","\\html@mathml{\\mathrel{\\not=}}{\\mathrel{\\char`\u2260}}"),k("\\ne","\\neq"),k("\u2260","\\neq"),k("\\notin","\\html@mathml{\\mathrel{{\\in}\\mathllap{/\\mskip1mu}}}{\\mathrel{\\char`\u2209}}"),k("\u2209","\\notin"),k("\u2258","\\html@mathml{\\mathrel{=\\kern{-1em}\\raisebox{0.4em}{$\\scriptsize\\frown$}}}{\\mathrel{\\char`\u2258}}"),k("\u2259","\\html@mathml{\\stackrel{\\tiny\\wedge}{=}}{\\mathrel{\\char`\u2258}}"),k("\u225A","\\html@mathml{\\stackrel{\\tiny\\vee}{=}}{\\mathrel{\\char`\u225A}}"),k("\u225B","\\html@mathml{\\stackrel{\\scriptsize\\star}{=}}{\\mathrel{\\char`\u225B}}"),k("\u225D","\\html@mathml{\\stackrel{\\tiny\\mathrm{def}}{=}}{\\mathrel{\\char`\u225D}}"),k("\u225E","\\html@mathml{\\stackrel{\\tiny\\mathrm{m}}{=}}{\\mathrel{\\char`\u225E}}"),k("\u225F","\\html@mathml{\\stackrel{\\tiny?}{=}}{\\mathrel{\\char`\u225F}}"),k("\u27C2","\\perp"),k("\u203C","\\mathclose{!\\mkern-0.8mu!}"),k("\u220C","\\notni"),k("\u231C","\\ulcorner"),k("\u231D","\\urcorner"),k("\u231E","\\llcorner"),k("\u231F","\\lrcorner"),k("\xA9","\\copyright"),k("\xAE","\\textregistered"),k("\uFE0F","\\textregistered"),k("\\ulcorner",'\\html@mathml{\\@ulcorner}{\\mathop{\\char"231c}}'),k("\\urcorner",'\\html@mathml{\\@urcorner}{\\mathop{\\char"231d}}'),k("\\llcorner",'\\html@mathml{\\@llcorner}{\\mathop{\\char"231e}}'),k("\\lrcorner",'\\html@mathml{\\@lrcorner}{\\mathop{\\char"231f}}'),k("\\vdots","{\\varvdots\\rule{0pt}{15pt}}"),k("\u22EE","\\vdots"),k("\\varGamma","\\mathit{\\Gamma}"),k("\\varDelta","\\mathit{\\Delta}"),k("\\varTheta","\\mathit{\\Theta}"),k("\\varLambda","\\mathit{\\Lambda}"),k("\\varXi","\\mathit{\\Xi}"),k("\\varPi","\\mathit{\\Pi}"),k("\\varSigma","\\mathit{\\Sigma}"),k("\\varUpsilon","\\mathit{\\Upsilon}"),k("\\varPhi","\\mathit{\\Phi}"),k("\\varPsi","\\mathit{\\Psi}"),k("\\varOmega","\\mathit{\\Omega}"),k("\\substack","\\begin{subarray}{c}#1\\end{subarray}"),k("\\colon","\\nobreak\\mskip2mu\\mathpunct{}\\mathchoice{\\mkern-3mu}{\\mkern-3mu}{}{}{:}\\mskip6mu\\relax"),k("\\boxed","\\fbox{$\\displaystyle{#1}$}"),k("\\iff","\\DOTSB\\;\\Longleftrightarrow\\;"),k("\\implies","\\DOTSB\\;\\Longrightarrow\\;"),k("\\impliedby","\\DOTSB\\;\\Longleftarrow\\;"),k("\\dddot","{\\overset{\\raisebox{-0.1ex}{\\normalsize ...}}{#1}}"),k("\\ddddot","{\\overset{\\raisebox{-0.1ex}{\\normalsize ....}}{#1}}");let Wl={",":"\\dotsc","\\not":"\\dotsb","+":"\\dotsb","=":"\\dotsb","<":"\\dotsb",">":"\\dotsb","-":"\\dotsb","*":"\\dotsb",":":"\\dotsb","\\DOTSB":"\\dotsb","\\coprod":"\\dotsb","\\bigvee":"\\dotsb","\\bigwedge":"\\dotsb","\\biguplus":"\\dotsb","\\bigcap":"\\dotsb","\\bigcup":"\\dotsb","\\prod":"\\dotsb","\\sum":"\\dotsb","\\bigotimes":"\\dotsb","\\bigoplus":"\\dotsb","\\bigodot":"\\dotsb","\\bigsqcup":"\\dotsb","\\And":"\\dotsb","\\longrightarrow":"\\dotsb","\\Longrightarrow":"\\dotsb","\\longleftarrow":"\\dotsb","\\Longleftarrow":"\\dotsb","\\longleftrightarrow":"\\dotsb","\\Longleftrightarrow":"\\dotsb","\\mapsto":"\\dotsb","\\longmapsto":"\\dotsb","\\hookrightarrow":"\\dotsb","\\doteq":"\\dotsb","\\mathbin":"\\dotsb","\\mathrel":"\\dotsb","\\relbar":"\\dotsb","\\Relbar":"\\dotsb","\\xrightarrow":"\\dotsb","\\xleftarrow":"\\dotsb","\\DOTSI":"\\dotsi","\\int":"\\dotsi","\\oint":"\\dotsi","\\iint":"\\dotsi","\\iiint":"\\dotsi","\\iiiint":"\\dotsi","\\idotsint":"\\dotsi","\\DOTSX":"\\dotsx"};k("\\dots",function(o){let n="\\dotso",s=o.expandAfterFuture().text;return s in Wl?n=Wl[s]:(s.slice(0,4)==="\\not"||s in Te.math&&z.contains(["bin","rel"],Te.math[s].group))&&(n="\\dotsb"),n});let mi={")":!0,"]":!0,"\\rbrack":!0,"\\}":!0,"\\rbrace":!0,"\\rangle":!0,"\\rceil":!0,"\\rfloor":!0,"\\rgroup":!0,"\\rmoustache":!0,"\\right":!0,"\\bigr":!0,"\\biggr":!0,"\\Bigr":!0,"\\Biggr":!0,$:!0,";":!0,".":!0,",":!0};k("\\dotso",function(o){return o.future().text in mi?"\\ldots\\,":"\\ldots"}),k("\\dotsc",function(o){let n=o.future().text;return n in mi&&n!==","?"\\ldots\\,":"\\ldots"}),k("\\cdots",function(o){return o.future().text in mi?"\\@cdots\\,":"\\@cdots"}),k("\\dotsb","\\cdots"),k("\\dotsm","\\cdots"),k("\\dotsi","\\!\\cdots"),k("\\dotsx","\\ldots\\,"),k("\\DOTSI","\\relax"),k("\\DOTSB","\\relax"),k("\\DOTSX","\\relax"),k("\\tmspace","\\TextOrMath{\\kern#1#3}{\\mskip#1#2}\\relax"),k("\\,","\\tmspace+{3mu}{.1667em}"),k("\\thinspace","\\,"),k("\\>","\\mskip{4mu}"),k("\\:","\\tmspace+{4mu}{.2222em}"),k("\\medspace","\\:"),k("\\;","\\tmspace+{5mu}{.2777em}"),k("\\thickspace","\\;"),k("\\!","\\tmspace-{3mu}{.1667em}"),k("\\negthinspace","\\!"),k("\\negmedspace","\\tmspace-{4mu}{.2222em}"),k("\\negthickspace","\\tmspace-{5mu}{.277em}"),k("\\enspace","\\kern.5em "),k("\\enskip","\\hskip.5em\\relax"),k("\\quad","\\hskip1em\\relax"),k("\\qquad","\\hskip2em\\relax"),k("\\tag","\\@ifstar\\tag@literal\\tag@paren"),k("\\tag@paren","\\tag@literal{({#1})}"),k("\\tag@literal",o=>{if(o.macros.get("\\df@tag"))throw new i("Multiple \\tag");return"\\gdef\\df@tag{\\text{#1}}"}),k("\\bmod","\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}\\mathbin{\\rm mod}\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}"),k("\\pod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern8mu}{\\mkern8mu}{\\mkern8mu}(#1)"),k("\\pmod","\\pod{{\\rm mod}\\mkern6mu#1}"),k("\\mod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern12mu}{\\mkern12mu}{\\mkern12mu}{\\rm mod}\\,\\,#1"),k("\\newline","\\\\\\relax"),k("\\TeX","\\textrm{\\html@mathml{T\\kern-.1667em\\raisebox{-.5ex}{E}\\kern-.125emX}{TeX}}");let Kl=U(At["Main-Regular"]["T".charCodeAt(0)][1]-.7*At["Main-Regular"]["A".charCodeAt(0)][1]);k("\\LaTeX","\\textrm{\\html@mathml{"+("L\\kern-.36em\\raisebox{"+Kl+"}{\\scriptstyle A}")+"\\kern-.15em\\TeX}{LaTeX}}"),k("\\KaTeX","\\textrm{\\html@mathml{"+("K\\kern-.17em\\raisebox{"+Kl+"}{\\scriptstyle A}")+"\\kern-.15em\\TeX}{KaTeX}}"),k("\\hspace","\\@ifstar\\@hspacer\\@hspace"),k("\\@hspace","\\hskip #1\\relax"),k("\\@hspacer","\\rule{0pt}{0pt}\\hskip #1\\relax"),k("\\ordinarycolon",":"),k("\\vcentcolon","\\mathrel{\\mathop\\ordinarycolon}"),k("\\dblcolon",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-.9mu}\\vcentcolon}}{\\mathop{\\char"2237}}'),k("\\coloneqq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2254}}'),k("\\Coloneqq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2237\\char"3d}}'),k("\\coloneq",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"3a\\char"2212}}'),k("\\Coloneq",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"2237\\char"2212}}'),k("\\eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2255}}'),k("\\Eqqcolon",'\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"3d\\char"2237}}'),k("\\eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2239}}'),k("\\Eqcolon",'\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"2212\\char"2237}}'),k("\\colonapprox",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"3a\\char"2248}}'),k("\\Colonapprox",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"2237\\char"2248}}'),k("\\colonsim",'\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"3a\\char"223c}}'),k("\\Colonsim",'\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"2237\\char"223c}}'),k("\u2237","\\dblcolon"),k("\u2239","\\eqcolon"),k("\u2254","\\coloneqq"),k("\u2255","\\eqqcolon"),k("\u2A74","\\Coloneqq"),k("\\ratio","\\vcentcolon"),k("\\coloncolon","\\dblcolon"),k("\\colonequals","\\coloneqq"),k("\\coloncolonequals","\\Coloneqq"),k("\\equalscolon","\\eqqcolon"),k("\\equalscoloncolon","\\Eqqcolon"),k("\\colonminus","\\coloneq"),k("\\coloncolonminus","\\Coloneq"),k("\\minuscolon","\\eqcolon"),k("\\minuscoloncolon","\\Eqcolon"),k("\\coloncolonapprox","\\Colonapprox"),k("\\coloncolonsim","\\Colonsim"),k("\\simcolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),k("\\simcoloncolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\dblcolon}"),k("\\approxcolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),k("\\approxcoloncolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\dblcolon}"),k("\\notni","\\html@mathml{\\not\\ni}{\\mathrel{\\char`\u220C}}"),k("\\limsup","\\DOTSB\\operatorname*{lim\\,sup}"),k("\\liminf","\\DOTSB\\operatorname*{lim\\,inf}"),k("\\injlim","\\DOTSB\\operatorname*{inj\\,lim}"),k("\\projlim","\\DOTSB\\operatorname*{proj\\,lim}"),k("\\varlimsup","\\DOTSB\\operatorname*{\\overline{lim}}"),k("\\varliminf","\\DOTSB\\operatorname*{\\underline{lim}}"),k("\\varinjlim","\\DOTSB\\operatorname*{\\underrightarrow{lim}}"),k("\\varprojlim","\\DOTSB\\operatorname*{\\underleftarrow{lim}}"),k("\\gvertneqq","\\html@mathml{\\@gvertneqq}{\u2269}"),k("\\lvertneqq","\\html@mathml{\\@lvertneqq}{\u2268}"),k("\\ngeqq","\\html@mathml{\\@ngeqq}{\u2271}"),k("\\ngeqslant","\\html@mathml{\\@ngeqslant}{\u2271}"),k("\\nleqq","\\html@mathml{\\@nleqq}{\u2270}"),k("\\nleqslant","\\html@mathml{\\@nleqslant}{\u2270}"),k("\\nshortmid","\\html@mathml{\\@nshortmid}{\u2224}"),k("\\nshortparallel","\\html@mathml{\\@nshortparallel}{\u2226}"),k("\\nsubseteqq","\\html@mathml{\\@nsubseteqq}{\u2288}"),k("\\nsupseteqq","\\html@mathml{\\@nsupseteqq}{\u2289}"),k("\\varsubsetneq","\\html@mathml{\\@varsubsetneq}{\u228A}"),k("\\varsubsetneqq","\\html@mathml{\\@varsubsetneqq}{\u2ACB}"),k("\\varsupsetneq","\\html@mathml{\\@varsupsetneq}{\u228B}"),k("\\varsupsetneqq","\\html@mathml{\\@varsupsetneqq}{\u2ACC}"),k("\\imath","\\html@mathml{\\@imath}{\u0131}"),k("\\jmath","\\html@mathml{\\@jmath}{\u0237}"),k("\\llbracket","\\html@mathml{\\mathopen{[\\mkern-3.2mu[}}{\\mathopen{\\char`\u27E6}}"),k("\\rrbracket","\\html@mathml{\\mathclose{]\\mkern-3.2mu]}}{\\mathclose{\\char`\u27E7}}"),k("\u27E6","\\llbracket"),k("\u27E7","\\rrbracket"),k("\\lBrace","\\html@mathml{\\mathopen{\\{\\mkern-3.2mu[}}{\\mathopen{\\char`\u2983}}"),k("\\rBrace","\\html@mathml{\\mathclose{]\\mkern-3.2mu\\}}}{\\mathclose{\\char`\u2984}}"),k("\u2983","\\lBrace"),k("\u2984","\\rBrace"),k("\\minuso","\\mathbin{\\html@mathml{{\\mathrlap{\\mathchoice{\\kern{0.145em}}{\\kern{0.145em}}{\\kern{0.1015em}}{\\kern{0.0725em}}\\circ}{-}}}{\\char`\u29B5}}"),k("\u29B5","\\minuso"),k("\\darr","\\downarrow"),k("\\dArr","\\Downarrow"),k("\\Darr","\\Downarrow"),k("\\lang","\\langle"),k("\\rang","\\rangle"),k("\\uarr","\\uparrow"),k("\\uArr","\\Uparrow"),k("\\Uarr","\\Uparrow"),k("\\N","\\mathbb{N}"),k("\\R","\\mathbb{R}"),k("\\Z","\\mathbb{Z}"),k("\\alef","\\aleph"),k("\\alefsym","\\aleph"),k("\\Alpha","\\mathrm{A}"),k("\\Beta","\\mathrm{B}"),k("\\bull","\\bullet"),k("\\Chi","\\mathrm{X}"),k("\\clubs","\\clubsuit"),k("\\cnums","\\mathbb{C}"),k("\\Complex","\\mathbb{C}"),k("\\Dagger","\\ddagger"),k("\\diamonds","\\diamondsuit"),k("\\empty","\\emptyset"),k("\\Epsilon","\\mathrm{E}"),k("\\Eta","\\mathrm{H}"),k("\\exist","\\exists"),k("\\harr","\\leftrightarrow"),k("\\hArr","\\Leftrightarrow"),k("\\Harr","\\Leftrightarrow"),k("\\hearts","\\heartsuit"),k("\\image","\\Im"),k("\\infin","\\infty"),k("\\Iota","\\mathrm{I}"),k("\\isin","\\in"),k("\\Kappa","\\mathrm{K}"),k("\\larr","\\leftarrow"),k("\\lArr","\\Leftarrow"),k("\\Larr","\\Leftarrow"),k("\\lrarr","\\leftrightarrow"),k("\\lrArr","\\Leftrightarrow"),k("\\Lrarr","\\Leftrightarrow"),k("\\Mu","\\mathrm{M}"),k("\\natnums","\\mathbb{N}"),k("\\Nu","\\mathrm{N}"),k("\\Omicron","\\mathrm{O}"),k("\\plusmn","\\pm"),k("\\rarr","\\rightarrow"),k("\\rArr","\\Rightarrow"),k("\\Rarr","\\Rightarrow"),k("\\real","\\Re"),k("\\reals","\\mathbb{R}"),k("\\Reals","\\mathbb{R}"),k("\\Rho","\\mathrm{P}"),k("\\sdot","\\cdot"),k("\\sect","\\S"),k("\\spades","\\spadesuit"),k("\\sub","\\subset"),k("\\sube","\\subseteq"),k("\\supe","\\supseteq"),k("\\Tau","\\mathrm{T}"),k("\\thetasym","\\vartheta"),k("\\weierp","\\wp"),k("\\Zeta","\\mathrm{Z}"),k("\\argmin","\\DOTSB\\operatorname*{arg\\,min}"),k("\\argmax","\\DOTSB\\operatorname*{arg\\,max}"),k("\\plim","\\DOTSB\\mathop{\\operatorname{plim}}\\limits"),k("\\bra","\\mathinner{\\langle{#1}|}"),k("\\ket","\\mathinner{|{#1}\\rangle}"),k("\\braket","\\mathinner{\\langle{#1}\\rangle}"),k("\\Bra","\\left\\langle#1\\right|"),k("\\Ket","\\left|#1\\right\\rangle");let Zl=o=>n=>{let s=n.consumeArg().tokens,c=n.consumeArg().tokens,f=n.consumeArg().tokens,m=n.consumeArg().tokens,x=n.macros.get("|"),w=n.macros.get("\\|");n.macros.beginGroup();let M=B=>R=>{o&&(R.macros.set("|",x),f.length&&R.macros.set("\\|",w));let H=B;return!B&&f.length&&R.future().text==="|"&&(R.popToken(),H=!0),{tokens:H?f:c,numArgs:0}};n.macros.set("|",M(!1)),f.length&&n.macros.set("\\|",M(!0));let N=n.consumeArg().tokens,D=n.expandTokens([...m,...N,...s]);return n.macros.endGroup(),{tokens:D.reverse(),numArgs:0}};k("\\bra@ket",Zl(!1)),k("\\bra@set",Zl(!0)),k("\\Braket","\\bra@ket{\\left\\langle}{\\,\\middle\\vert\\,}{\\,\\middle\\vert\\,}{\\right\\rangle}"),k("\\Set","\\bra@set{\\left\\{\\:}{\\;\\middle\\vert\\;}{\\;\\middle\\Vert\\;}{\\:\\right\\}}"),k("\\set","\\bra@set{\\{\\,}{\\mid}{}{\\,\\}}"),k("\\angln","{\\angl n}"),k("\\blue","\\textcolor{##6495ed}{#1}"),k("\\orange","\\textcolor{##ffa500}{#1}"),k("\\pink","\\textcolor{##ff00af}{#1}"),k("\\red","\\textcolor{##df0030}{#1}"),k("\\green","\\textcolor{##28ae7b}{#1}"),k("\\gray","\\textcolor{gray}{#1}"),k("\\purple","\\textcolor{##9d38bd}{#1}"),k("\\blueA","\\textcolor{##ccfaff}{#1}"),k("\\blueB","\\textcolor{##80f6ff}{#1}"),k("\\blueC","\\textcolor{##63d9ea}{#1}"),k("\\blueD","\\textcolor{##11accd}{#1}"),k("\\blueE","\\textcolor{##0c7f99}{#1}"),k("\\tealA","\\textcolor{##94fff5}{#1}"),k("\\tealB","\\textcolor{##26edd5}{#1}"),k("\\tealC","\\textcolor{##01d1c1}{#1}"),k("\\tealD","\\textcolor{##01a995}{#1}"),k("\\tealE","\\textcolor{##208170}{#1}"),k("\\greenA","\\textcolor{##b6ffb0}{#1}"),k("\\greenB","\\textcolor{##8af281}{#1}"),k("\\greenC","\\textcolor{##74cf70}{#1}"),k("\\greenD","\\textcolor{##1fab54}{#1}"),k("\\greenE","\\textcolor{##0d923f}{#1}"),k("\\goldA","\\textcolor{##ffd0a9}{#1}"),k("\\goldB","\\textcolor{##ffbb71}{#1}"),k("\\goldC","\\textcolor{##ff9c39}{#1}"),k("\\goldD","\\textcolor{##e07d10}{#1}"),k("\\goldE","\\textcolor{##a75a05}{#1}"),k("\\redA","\\textcolor{##fca9a9}{#1}"),k("\\redB","\\textcolor{##ff8482}{#1}"),k("\\redC","\\textcolor{##f9685d}{#1}"),k("\\redD","\\textcolor{##e84d39}{#1}"),k("\\redE","\\textcolor{##bc2612}{#1}"),k("\\maroonA","\\textcolor{##ffbde0}{#1}"),k("\\maroonB","\\textcolor{##ff92c6}{#1}"),k("\\maroonC","\\textcolor{##ed5fa6}{#1}"),k("\\maroonD","\\textcolor{##ca337c}{#1}"),k("\\maroonE","\\textcolor{##9e034e}{#1}"),k("\\purpleA","\\textcolor{##ddd7ff}{#1}"),k("\\purpleB","\\textcolor{##c6b9fc}{#1}"),k("\\purpleC","\\textcolor{##aa87ff}{#1}"),k("\\purpleD","\\textcolor{##7854ab}{#1}"),k("\\purpleE","\\textcolor{##543b78}{#1}"),k("\\mintA","\\textcolor{##f5f9e8}{#1}"),k("\\mintB","\\textcolor{##edf2df}{#1}"),k("\\mintC","\\textcolor{##e0e5cc}{#1}"),k("\\grayA","\\textcolor{##f6f7f7}{#1}"),k("\\grayB","\\textcolor{##f0f1f2}{#1}"),k("\\grayC","\\textcolor{##e3e5e6}{#1}"),k("\\grayD","\\textcolor{##d6d8da}{#1}"),k("\\grayE","\\textcolor{##babec2}{#1}"),k("\\grayF","\\textcolor{##888d93}{#1}"),k("\\grayG","\\textcolor{##626569}{#1}"),k("\\grayH","\\textcolor{##3b3e40}{#1}"),k("\\grayI","\\textcolor{##21242c}{#1}"),k("\\kaBlue","\\textcolor{##314453}{#1}"),k("\\kaGreen","\\textcolor{##71B307}{#1}");let Jl={"^":!0,_:!0,"\\limits":!0,"\\nolimits":!0};class M1{constructor(n,s,c){this.settings=void 0,this.expansionCount=void 0,this.lexer=void 0,this.macros=void 0,this.stack=void 0,this.mode=void 0,this.settings=s,this.expansionCount=0,this.feed(n),this.macros=new k1(S1,s.macros),this.mode=c,this.stack=[]}feed(n){this.lexer=new Xl(n,this.settings)}switchMode(n){this.mode=n}beginGroup(){this.macros.beginGroup()}endGroup(){this.macros.endGroup()}endGroups(){this.macros.endGroups()}future(){return this.stack.length===0&&this.pushToken(this.lexer.lex()),this.stack[this.stack.length-1]}popToken(){return this.future(),this.stack.pop()}pushToken(n){this.stack.push(n)}pushTokens(n){this.stack.push(...n)}scanArgument(n){let s,c,f;if(n){if(this.consumeSpaces(),this.future().text!=="[")return null;s=this.popToken(),{tokens:f,end:c}=this.consumeArg(["]"])}else({tokens:f,start:s,end:c}=this.consumeArg());return this.pushToken(new pt("EOF",c.loc)),this.pushTokens(f),s.range(c,"")}consumeSpaces(){for(;this.future().text===" ";)this.stack.pop()}consumeArg(n){let s=[],c=n&&n.length>0;c||this.consumeSpaces();let f=this.future(),m,x=0,w=0;do{if(m=this.popToken(),s.push(m),m.text==="{")++x;else if(m.text==="}"){if(--x,x===-1)throw new i("Extra }",m)}else if(m.text==="EOF")throw new i("Unexpected end of input in a macro argument, expected '"+(n&&c?n[w]:"}")+"'",m);if(n&&c)if((x===0||x===1&&n[w]==="{")&&m.text===n[w]){if(++w,w===n.length){s.splice(-w,w);break}}else w=0}while(x!==0||c);return f.text==="{"&&s[s.length-1].text==="}"&&(s.pop(),s.shift()),s.reverse(),{tokens:s,start:f,end:m}}consumeArgs(n,s){if(s){if(s.length!==n+1)throw new i("The length of delimiters doesn't match the number of args!");let f=s[0];for(let m=0;m<f.length;m++){let x=this.popToken();if(f[m]!==x.text)throw new i("Use of the macro doesn't match its definition",x)}}let c=[];for(let f=0;f<n;f++)c.push(this.consumeArg(s&&s[f+1]).tokens);return c}countExpansion(n){if(this.expansionCount+=n,this.expansionCount>this.settings.maxExpand)throw new i("Too many expansions: infinite loop or need to increase maxExpand setting")}expandOnce(n){let s=this.popToken(),c=s.text,f=s.noexpand?null:this._getExpansion(c);if(f==null||n&&f.unexpandable){if(n&&f==null&&c[0]==="\\"&&!this.isDefined(c))throw new i("Undefined control sequence: "+c);return this.pushToken(s),!1}this.countExpansion(1);let m=f.tokens,x=this.consumeArgs(f.numArgs,f.delimiters);if(f.numArgs){m=m.slice();for(let w=m.length-1;w>=0;--w){let M=m[w];if(M.text==="#"){if(w===0)throw new i("Incomplete placeholder at end of macro body",M);if(M=m[--w],M.text==="#")m.splice(w+1,1);else if(/^[1-9]$/.test(M.text))m.splice(w,2,...x[+M.text-1]);else throw new i("Not a valid argument number",M)}}}return this.pushTokens(m),m.length}expandAfterFuture(){return this.expandOnce(),this.future()}expandNextToken(){for(;;)if(this.expandOnce()===!1){let n=this.stack.pop();return n.treatAsRelax&&(n.text="\\relax"),n}throw new Error}expandMacro(n){return this.macros.has(n)?this.expandTokens([new pt(n)]):void 0}expandTokens(n){let s=[],c=this.stack.length;for(this.pushTokens(n);this.stack.length>c;)if(this.expandOnce(!0)===!1){let f=this.stack.pop();f.treatAsRelax&&(f.noexpand=!1,f.treatAsRelax=!1),s.push(f)}return this.countExpansion(s.length),s}expandMacroAsText(n){let s=this.expandMacro(n);return s&&s.map(c=>c.text).join("")}_getExpansion(n){let s=this.macros.get(n);if(s==null)return s;if(n.length===1){let f=this.lexer.catcodes[n];if(f!=null&&f!==13)return}let c=typeof s=="function"?s(this):s;if(typeof c=="string"){let f=0;if(c.indexOf("#")!==-1){let N=c.replace(/##/g,"");for(;N.indexOf("#"+(f+1))!==-1;)++f}let m=new Xl(c,this.settings),x=[],w=m.lex();for(;w.text!=="EOF";)x.push(w),w=m.lex();return x.reverse(),{tokens:x,numArgs:f}}return c}isDefined(n){return this.macros.has(n)||Kt.hasOwnProperty(n)||Te.math.hasOwnProperty(n)||Te.text.hasOwnProperty(n)||Jl.hasOwnProperty(n)}isExpandable(n){let s=this.macros.get(n);return s!=null?typeof s=="string"||typeof s=="function"||!s.unexpandable:Kt.hasOwnProperty(n)&&!Kt[n].primitive}}let Ql=/^[₊₋₌₍₎₀₁₂₃₄₅₆₇₈₉ₐₑₕᵢⱼₖₗₘₙₒₚᵣₛₜᵤᵥₓᵦᵧᵨᵩᵪ]/,Fn=Object.freeze({"\u208A":"+","\u208B":"-","\u208C":"=","\u208D":"(","\u208E":")","\u2080":"0","\u2081":"1","\u2082":"2","\u2083":"3","\u2084":"4","\u2085":"5","\u2086":"6","\u2087":"7","\u2088":"8","\u2089":"9","\u2090":"a","\u2091":"e","\u2095":"h","\u1D62":"i","\u2C7C":"j","\u2096":"k","\u2097":"l","\u2098":"m","\u2099":"n","\u2092":"o","\u209A":"p","\u1D63":"r","\u209B":"s","\u209C":"t","\u1D64":"u","\u1D65":"v","\u2093":"x","\u1D66":"\u03B2","\u1D67":"\u03B3","\u1D68":"\u03C1","\u1D69":"\u03D5","\u1D6A":"\u03C7","\u207A":"+","\u207B":"-","\u207C":"=","\u207D":"(","\u207E":")","\u2070":"0","\xB9":"1","\xB2":"2","\xB3":"3","\u2074":"4","\u2075":"5","\u2076":"6","\u2077":"7","\u2078":"8","\u2079":"9","\u1D2C":"A","\u1D2E":"B","\u1D30":"D","\u1D31":"E","\u1D33":"G","\u1D34":"H","\u1D35":"I","\u1D36":"J","\u1D37":"K","\u1D38":"L","\u1D39":"M","\u1D3A":"N","\u1D3C":"O","\u1D3E":"P","\u1D3F":"R","\u1D40":"T","\u1D41":"U","\u2C7D":"V","\u1D42":"W","\u1D43":"a","\u1D47":"b","\u1D9C":"c","\u1D48":"d","\u1D49":"e","\u1DA0":"f","\u1D4D":"g",\u02B0:"h","\u2071":"i",\u02B2:"j","\u1D4F":"k",\u02E1:"l","\u1D50":"m",\u207F:"n","\u1D52":"o","\u1D56":"p",\u02B3:"r",\u02E2:"s","\u1D57":"t","\u1D58":"u","\u1D5B":"v",\u02B7:"w",\u02E3:"x",\u02B8:"y","\u1DBB":"z","\u1D5D":"\u03B2","\u1D5E":"\u03B3","\u1D5F":"\u03B4","\u1D60":"\u03D5","\u1D61":"\u03C7","\u1DBF":"\u03B8"}),gi={"\u0301":{text:"\\'",math:"\\acute"},"\u0300":{text:"\\`",math:"\\grave"},"\u0308":{text:'\\"',math:"\\ddot"},"\u0303":{text:"\\~",math:"\\tilde"},"\u0304":{text:"\\=",math:"\\bar"},"\u0306":{text:"\\u",math:"\\breve"},"\u030C":{text:"\\v",math:"\\check"},"\u0302":{text:"\\^",math:"\\hat"},"\u0307":{text:"\\.",math:"\\dot"},"\u030A":{text:"\\r",math:"\\mathring"},"\u030B":{text:"\\H"},"\u0327":{text:"\\c"}},es={\u00E1:"a\u0301",\u00E0:"a\u0300",\u00E4:"a\u0308",\u01DF:"a\u0308\u0304",\u00E3:"a\u0303",\u0101:"a\u0304",\u0103:"a\u0306",\u1EAF:"a\u0306\u0301",\u1EB1:"a\u0306\u0300",\u1EB5:"a\u0306\u0303",\u01CE:"a\u030C",\u00E2:"a\u0302",\u1EA5:"a\u0302\u0301",\u1EA7:"a\u0302\u0300",\u1EAB:"a\u0302\u0303",\u0227:"a\u0307",\u01E1:"a\u0307\u0304",\u00E5:"a\u030A",\u01FB:"a\u030A\u0301",\u1E03:"b\u0307",\u0107:"c\u0301",\u1E09:"c\u0327\u0301",\u010D:"c\u030C",\u0109:"c\u0302",\u010B:"c\u0307",\u00E7:"c\u0327",\u010F:"d\u030C",\u1E0B:"d\u0307",\u1E11:"d\u0327",\u00E9:"e\u0301",\u00E8:"e\u0300",\u00EB:"e\u0308",\u1EBD:"e\u0303",\u0113:"e\u0304",\u1E17:"e\u0304\u0301",\u1E15:"e\u0304\u0300",\u0115:"e\u0306",\u1E1D:"e\u0327\u0306",\u011B:"e\u030C",\u00EA:"e\u0302",\u1EBF:"e\u0302\u0301",\u1EC1:"e\u0302\u0300",\u1EC5:"e\u0302\u0303",\u0117:"e\u0307",\u0229:"e\u0327",\u1E1F:"f\u0307",\u01F5:"g\u0301",\u1E21:"g\u0304",\u011F:"g\u0306",\u01E7:"g\u030C",\u011D:"g\u0302",\u0121:"g\u0307",\u0123:"g\u0327",\u1E27:"h\u0308",\u021F:"h\u030C",\u0125:"h\u0302",\u1E23:"h\u0307",\u1E29:"h\u0327",\u00ED:"i\u0301",\u00EC:"i\u0300",\u00EF:"i\u0308",\u1E2F:"i\u0308\u0301",\u0129:"i\u0303",\u012B:"i\u0304",\u012D:"i\u0306",\u01D0:"i\u030C",\u00EE:"i\u0302",\u01F0:"j\u030C",\u0135:"j\u0302",\u1E31:"k\u0301",\u01E9:"k\u030C",\u0137:"k\u0327",\u013A:"l\u0301",\u013E:"l\u030C",\u013C:"l\u0327",\u1E3F:"m\u0301",\u1E41:"m\u0307",\u0144:"n\u0301",\u01F9:"n\u0300",\u00F1:"n\u0303",\u0148:"n\u030C",\u1E45:"n\u0307",\u0146:"n\u0327",\u00F3:"o\u0301",\u00F2:"o\u0300",\u00F6:"o\u0308",\u022B:"o\u0308\u0304",\u00F5:"o\u0303",\u1E4D:"o\u0303\u0301",\u1E4F:"o\u0303\u0308",\u022D:"o\u0303\u0304",\u014D:"o\u0304",\u1E53:"o\u0304\u0301",\u1E51:"o\u0304\u0300",\u014F:"o\u0306",\u01D2:"o\u030C",\u00F4:"o\u0302",\u1ED1:"o\u0302\u0301",\u1ED3:"o\u0302\u0300",\u1ED7:"o\u0302\u0303",\u022F:"o\u0307",\u0231:"o\u0307\u0304",\u0151:"o\u030B",\u1E55:"p\u0301",\u1E57:"p\u0307",\u0155:"r\u0301",\u0159:"r\u030C",\u1E59:"r\u0307",\u0157:"r\u0327",\u015B:"s\u0301",\u1E65:"s\u0301\u0307",\u0161:"s\u030C",\u1E67:"s\u030C\u0307",\u015D:"s\u0302",\u1E61:"s\u0307",\u015F:"s\u0327",\u1E97:"t\u0308",\u0165:"t\u030C",\u1E6B:"t\u0307",\u0163:"t\u0327",\u00FA:"u\u0301",\u00F9:"u\u0300",\u00FC:"u\u0308",\u01D8:"u\u0308\u0301",\u01DC:"u\u0308\u0300",\u01D6:"u\u0308\u0304",\u01DA:"u\u0308\u030C",\u0169:"u\u0303",\u1E79:"u\u0303\u0301",\u016B:"u\u0304",\u1E7B:"u\u0304\u0308",\u016D:"u\u0306",\u01D4:"u\u030C",\u00FB:"u\u0302",\u016F:"u\u030A",\u0171:"u\u030B",\u1E7D:"v\u0303",\u1E83:"w\u0301",\u1E81:"w\u0300",\u1E85:"w\u0308",\u0175:"w\u0302",\u1E87:"w\u0307",\u1E98:"w\u030A",\u1E8D:"x\u0308",\u1E8B:"x\u0307",\u00FD:"y\u0301",\u1EF3:"y\u0300",\u00FF:"y\u0308",\u1EF9:"y\u0303",\u0233:"y\u0304",\u0177:"y\u0302",\u1E8F:"y\u0307",\u1E99:"y\u030A",\u017A:"z\u0301",\u017E:"z\u030C",\u1E91:"z\u0302",\u017C:"z\u0307",\u00C1:"A\u0301",\u00C0:"A\u0300",\u00C4:"A\u0308",\u01DE:"A\u0308\u0304",\u00C3:"A\u0303",\u0100:"A\u0304",\u0102:"A\u0306",\u1EAE:"A\u0306\u0301",\u1EB0:"A\u0306\u0300",\u1EB4:"A\u0306\u0303",\u01CD:"A\u030C",\u00C2:"A\u0302",\u1EA4:"A\u0302\u0301",\u1EA6:"A\u0302\u0300",\u1EAA:"A\u0302\u0303",\u0226:"A\u0307",\u01E0:"A\u0307\u0304",\u00C5:"A\u030A",\u01FA:"A\u030A\u0301",\u1E02:"B\u0307",\u0106:"C\u0301",\u1E08:"C\u0327\u0301",\u010C:"C\u030C",\u0108:"C\u0302",\u010A:"C\u0307",\u00C7:"C\u0327",\u010E:"D\u030C",\u1E0A:"D\u0307",\u1E10:"D\u0327",\u00C9:"E\u0301",\u00C8:"E\u0300",\u00CB:"E\u0308",\u1EBC:"E\u0303",\u0112:"E\u0304",\u1E16:"E\u0304\u0301",\u1E14:"E\u0304\u0300",\u0114:"E\u0306",\u1E1C:"E\u0327\u0306",\u011A:"E\u030C",\u00CA:"E\u0302",\u1EBE:"E\u0302\u0301",\u1EC0:"E\u0302\u0300",\u1EC4:"E\u0302\u0303",\u0116:"E\u0307",\u0228:"E\u0327",\u1E1E:"F\u0307",\u01F4:"G\u0301",\u1E20:"G\u0304",\u011E:"G\u0306",\u01E6:"G\u030C",\u011C:"G\u0302",\u0120:"G\u0307",\u0122:"G\u0327",\u1E26:"H\u0308",\u021E:"H\u030C",\u0124:"H\u0302",\u1E22:"H\u0307",\u1E28:"H\u0327",\u00CD:"I\u0301",\u00CC:"I\u0300",\u00CF:"I\u0308",\u1E2E:"I\u0308\u0301",\u0128:"I\u0303",\u012A:"I\u0304",\u012C:"I\u0306",\u01CF:"I\u030C",\u00CE:"I\u0302",\u0130:"I\u0307",\u0134:"J\u0302",\u1E30:"K\u0301",\u01E8:"K\u030C",\u0136:"K\u0327",\u0139:"L\u0301",\u013D:"L\u030C",\u013B:"L\u0327",\u1E3E:"M\u0301",\u1E40:"M\u0307",\u0143:"N\u0301",\u01F8:"N\u0300",\u00D1:"N\u0303",\u0147:"N\u030C",\u1E44:"N\u0307",\u0145:"N\u0327",\u00D3:"O\u0301",\u00D2:"O\u0300",\u00D6:"O\u0308",\u022A:"O\u0308\u0304",\u00D5:"O\u0303",\u1E4C:"O\u0303\u0301",\u1E4E:"O\u0303\u0308",\u022C:"O\u0303\u0304",\u014C:"O\u0304",\u1E52:"O\u0304\u0301",\u1E50:"O\u0304\u0300",\u014E:"O\u0306",\u01D1:"O\u030C",\u00D4:"O\u0302",\u1ED0:"O\u0302\u0301",\u1ED2:"O\u0302\u0300",\u1ED6:"O\u0302\u0303",\u022E:"O\u0307",\u0230:"O\u0307\u0304",\u0150:"O\u030B",\u1E54:"P\u0301",\u1E56:"P\u0307",\u0154:"R\u0301",\u0158:"R\u030C",\u1E58:"R\u0307",\u0156:"R\u0327",\u015A:"S\u0301",\u1E64:"S\u0301\u0307",\u0160:"S\u030C",\u1E66:"S\u030C\u0307",\u015C:"S\u0302",\u1E60:"S\u0307",\u015E:"S\u0327",\u0164:"T\u030C",\u1E6A:"T\u0307",\u0162:"T\u0327",\u00DA:"U\u0301",\u00D9:"U\u0300",\u00DC:"U\u0308",\u01D7:"U\u0308\u0301",\u01DB:"U\u0308\u0300",\u01D5:"U\u0308\u0304",\u01D9:"U\u0308\u030C",\u0168:"U\u0303",\u1E78:"U\u0303\u0301",\u016A:"U\u0304",\u1E7A:"U\u0304\u0308",\u016C:"U\u0306",\u01D3:"U\u030C",\u00DB:"U\u0302",\u016E:"U\u030A",\u0170:"U\u030B",\u1E7C:"V\u0303",\u1E82:"W\u0301",\u1E80:"W\u0300",\u1E84:"W\u0308",\u0174:"W\u0302",\u1E86:"W\u0307",\u1E8C:"X\u0308",\u1E8A:"X\u0307",\u00DD:"Y\u0301",\u1EF2:"Y\u0300",\u0178:"Y\u0308",\u1EF8:"Y\u0303",\u0232:"Y\u0304",\u0176:"Y\u0302",\u1E8E:"Y\u0307",\u0179:"Z\u0301",\u017D:"Z\u030C",\u1E90:"Z\u0302",\u017B:"Z\u0307",\u03AC:"\u03B1\u0301",\u1F70:"\u03B1\u0300",\u1FB1:"\u03B1\u0304",\u1FB0:"\u03B1\u0306",\u03AD:"\u03B5\u0301",\u1F72:"\u03B5\u0300",\u03AE:"\u03B7\u0301",\u1F74:"\u03B7\u0300",\u03AF:"\u03B9\u0301",\u1F76:"\u03B9\u0300",\u03CA:"\u03B9\u0308",\u0390:"\u03B9\u0308\u0301",\u1FD2:"\u03B9\u0308\u0300",\u1FD1:"\u03B9\u0304",\u1FD0:"\u03B9\u0306",\u03CC:"\u03BF\u0301",\u1F78:"\u03BF\u0300",\u03CD:"\u03C5\u0301",\u1F7A:"\u03C5\u0300",\u03CB:"\u03C5\u0308",\u03B0:"\u03C5\u0308\u0301",\u1FE2:"\u03C5\u0308\u0300",\u1FE1:"\u03C5\u0304",\u1FE0:"\u03C5\u0306",\u03CE:"\u03C9\u0301",\u1F7C:"\u03C9\u0300",\u038E:"\u03A5\u0301",\u1FEA:"\u03A5\u0300",\u03AB:"\u03A5\u0308",\u1FE9:"\u03A5\u0304",\u1FE8:"\u03A5\u0306",\u038F:"\u03A9\u0301",\u1FFA:"\u03A9\u0300"};class Rn{constructor(n,s){this.mode=void 0,this.gullet=void 0,this.settings=void 0,this.leftrightDepth=void 0,this.nextToken=void 0,this.mode="math",this.gullet=new M1(n,s,this.mode),this.settings=s,this.leftrightDepth=0}expect(n,s){if(s===void 0&&(s=!0),this.fetch().text!==n)throw new i("Expected '"+n+"', got '"+this.fetch().text+"'",this.fetch());s&&this.consume()}consume(){this.nextToken=null}fetch(){return this.nextToken==null&&(this.nextToken=this.gullet.expandNextToken()),this.nextToken}switchMode(n){this.mode=n,this.gullet.switchMode(n)}parse(){this.settings.globalGroup||this.gullet.beginGroup(),this.settings.colorIsTextColor&&this.gullet.macros.set("\\color","\\textcolor");try{let n=this.parseExpression(!1);return this.expect("EOF"),this.settings.globalGroup||this.gullet.endGroup(),n}finally{this.gullet.endGroups()}}subparse(n){let s=this.nextToken;this.consume(),this.gullet.pushToken(new pt("}")),this.gullet.pushTokens(n);let c=this.parseExpression(!1);return this.expect("}"),this.nextToken=s,c}parseExpression(n,s){let c=[];for(;;){this.mode==="math"&&this.consumeSpaces();let f=this.fetch();if(Rn.endOfExpression.indexOf(f.text)!==-1||s&&f.text===s||n&&Kt[f.text]&&Kt[f.text].infix)break;let m=this.parseAtom(s);if(m){if(m.type==="internal")continue}else break;c.push(m)}return this.mode==="text"&&this.formLigatures(c),this.handleInfixNodes(c)}handleInfixNodes(n){let s=-1,c;for(let f=0;f<n.length;f++)if(n[f].type==="infix"){if(s!==-1)throw new i("only one infix operator per group",n[f].token);s=f,c=n[f].replaceWith}if(s!==-1&&c){let f,m,x=n.slice(0,s),w=n.slice(s+1);x.length===1&&x[0].type==="ordgroup"?f=x[0]:f={type:"ordgroup",mode:this.mode,body:x},w.length===1&&w[0].type==="ordgroup"?m=w[0]:m={type:"ordgroup",mode:this.mode,body:w};let M;return c==="\\\\abovefrac"?M=this.callFunction(c,[f,n[s],m],[]):M=this.callFunction(c,[f,m],[]),[M]}else return n}handleSupSubscript(n){let s=this.fetch(),c=s.text;this.consume(),this.consumeSpaces();let f;do{var m;f=this.parseGroup(n)}while(((m=f)==null?void 0:m.type)==="internal");if(!f)throw new i("Expected group after '"+c+"'",s);return f}formatUnsupportedCmd(n){let s=[];for(let m=0;m<n.length;m++)s.push({type:"textord",mode:"text",text:n[m]});let c={type:"text",mode:this.mode,body:s};return{type:"color",mode:this.mode,color:this.settings.errorColor,body:[c]}}parseAtom(n){let s=this.parseGroup("atom",n);if((s==null?void 0:s.type)==="internal"||this.mode==="text")return s;let c,f;for(;;){this.consumeSpaces();let m=this.fetch();if(m.text==="\\limits"||m.text==="\\nolimits"){if(s&&s.type==="op"){let x=m.text==="\\limits";s.limits=x,s.alwaysHandleSupSub=!0}else if(s&&s.type==="operatorname")s.alwaysHandleSupSub&&(s.limits=m.text==="\\limits");else throw new i("Limit controls must follow a math operator",m);this.consume()}else if(m.text==="^"){if(c)throw new i("Double superscript",m);c=this.handleSupSubscript("superscript")}else if(m.text==="_"){if(f)throw new i("Double subscript",m);f=this.handleSupSubscript("subscript")}else if(m.text==="'"){if(c)throw new i("Double superscript",m);let x={type:"textord",mode:this.mode,text:"\\prime"},w=[x];for(this.consume();this.fetch().text==="'";)w.push(x),this.consume();this.fetch().text==="^"&&w.push(this.handleSupSubscript("superscript")),c={type:"ordgroup",mode:this.mode,body:w}}else if(Fn[m.text]){let x=Ql.test(m.text),w=[];for(w.push(new pt(Fn[m.text])),this.consume();;){let N=this.fetch().text;if(!Fn[N]||Ql.test(N)!==x)break;w.unshift(new pt(Fn[N])),this.consume()}let M=this.subparse(w);x?f={type:"ordgroup",mode:"math",body:M}:c={type:"ordgroup",mode:"math",body:M}}else break}return c||f?{type:"supsub",mode:this.mode,base:s,sup:c,sub:f}:s}parseFunction(n,s){let c=this.fetch(),f=c.text,m=Kt[f];if(!m)return null;if(this.consume(),s&&s!=="atom"&&!m.allowedInArgument)throw new i("Got function '"+f+"' with no arguments"+(s?" as "+s:""),c);if(this.mode==="text"&&!m.allowedInText)throw new i("Can't use function '"+f+"' in text mode",c);if(this.mode==="math"&&m.allowedInMath===!1)throw new i("Can't use function '"+f+"' in math mode",c);let{args:x,optArgs:w}=this.parseArguments(f,m);return this.callFunction(f,x,w,c,n)}callFunction(n,s,c,f,m){let x={funcName:n,parser:this,token:f,breakOnTokenText:m},w=Kt[n];if(w&&w.handler)return w.handler(x,s,c);throw new i("No function handler for "+n)}parseArguments(n,s){let c=s.numArgs+s.numOptionalArgs;if(c===0)return{args:[],optArgs:[]};let f=[],m=[];for(let x=0;x<c;x++){let w=s.argTypes&&s.argTypes[x],M=x<s.numOptionalArgs;(s.primitive&&w==null||s.type==="sqrt"&&x===1&&m[0]==null)&&(w="primitive");let N=this.parseGroupOfType("argument to '"+n+"'",w,M);if(M)m.push(N);else if(N!=null)f.push(N);else throw new i("Null argument, please report this as a bug")}return{args:f,optArgs:m}}parseGroupOfType(n,s,c){switch(s){case"color":return this.parseColorGroup(c);case"size":return this.parseSizeGroup(c);case"url":return this.parseUrlGroup(c);case"math":case"text":return this.parseArgumentGroup(c,s);case"hbox":{let f=this.parseArgumentGroup(c,"text");return f!=null?{type:"styling",mode:f.mode,body:[f],style:"text"}:null}case"raw":{let f=this.parseStringGroup("raw",c);return f!=null?{type:"raw",mode:"text",string:f.text}:null}case"primitive":{if(c)throw new i("A primitive argument cannot be optional");let f=this.parseGroup(n);if(f==null)throw new i("Expected group as "+n,this.fetch());return f}case"original":case null:case void 0:return this.parseArgumentGroup(c);default:throw new i("Unknown group type as "+n,this.fetch())}}consumeSpaces(){for(;this.fetch().text===" ";)this.consume()}parseStringGroup(n,s){let c=this.gullet.scanArgument(s);if(c==null)return null;let f="",m;for(;(m=this.fetch()).text!=="EOF";)f+=m.text,this.consume();return this.consume(),c.text=f,c}parseRegexGroup(n,s){let c=this.fetch(),f=c,m="",x;for(;(x=this.fetch()).text!=="EOF"&&n.test(m+x.text);)f=x,m+=f.text,this.consume();if(m==="")throw new i("Invalid "+s+": '"+c.text+"'",c);return c.range(f,m)}parseColorGroup(n){let s=this.parseStringGroup("color",n);if(s==null)return null;let c=/^(#[a-f0-9]{3}|#?[a-f0-9]{6}|[a-z]+)$/i.exec(s.text);if(!c)throw new i("Invalid color: '"+s.text+"'",s);let f=c[0];return/^[0-9a-f]{6}$/i.test(f)&&(f="#"+f),{type:"color-token",mode:this.mode,color:f}}parseSizeGroup(n){let s,c=!1;if(this.gullet.consumeSpaces(),!n&&this.gullet.future().text!=="{"?s=this.parseRegexGroup(/^[-+]? *(?:$|\d+|\d+\.\d*|\.\d*) *[a-z]{0,2} *$/,"size"):s=this.parseStringGroup("size",n),!s)return null;!n&&s.text.length===0&&(s.text="0pt",c=!0);let f=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(s.text);if(!f)throw new i("Invalid size: '"+s.text+"'",s);let m={number:+(f[1]+f[2]),unit:f[3]};if(!Ro(m))throw new i("Invalid unit: '"+m.unit+"'",s);return{type:"size",mode:this.mode,value:m,isBlank:c}}parseUrlGroup(n){this.gullet.lexer.setCatcode("%",13),this.gullet.lexer.setCatcode("~",12);let s=this.parseStringGroup("url",n);if(this.gullet.lexer.setCatcode("%",14),this.gullet.lexer.setCatcode("~",13),s==null)return null;let c=s.text.replace(/\\([#$%&~_^{}])/g,"$1");return{type:"url",mode:this.mode,url:c}}parseArgumentGroup(n,s){let c=this.gullet.scanArgument(n);if(c==null)return null;let f=this.mode;s&&this.switchMode(s),this.gullet.beginGroup();let m=this.parseExpression(!1,"EOF");this.expect("EOF"),this.gullet.endGroup();let x={type:"ordgroup",mode:this.mode,loc:c.loc,body:m};return s&&this.switchMode(f),x}parseGroup(n,s){let c=this.fetch(),f=c.text,m;if(f==="{"||f==="\\begingroup"){this.consume();let x=f==="{"?"}":"\\endgroup";this.gullet.beginGroup();let w=this.parseExpression(!1,x),M=this.fetch();this.expect(x),this.gullet.endGroup(),m={type:"ordgroup",mode:this.mode,loc:ut.range(c,M),body:w,semisimple:f==="\\begingroup"||void 0}}else if(m=this.parseFunction(s,n)||this.parseSymbol(),m==null&&f[0]==="\\"&&!Jl.hasOwnProperty(f)){if(this.settings.throwOnError)throw new i("Undefined control sequence: "+f,c);m=this.formatUnsupportedCmd(f),this.consume()}return m}formLigatures(n){let s=n.length-1;for(let c=0;c<s;++c){let f=n[c],m=f.text;m==="-"&&n[c+1].text==="-"&&(c+1<s&&n[c+2].text==="-"?(n.splice(c,3,{type:"textord",mode:"text",loc:ut.range(f,n[c+2]),text:"---"}),s-=2):(n.splice(c,2,{type:"textord",mode:"text",loc:ut.range(f,n[c+1]),text:"--"}),s-=1)),(m==="'"||m==="`")&&n[c+1].text===m&&(n.splice(c,2,{type:"textord",mode:"text",loc:ut.range(f,n[c+1]),text:m+m}),s-=1)}}parseSymbol(){let n=this.fetch(),s=n.text;if(/^\\verb[^a-zA-Z]/.test(s)){this.consume();let m=s.slice(5),x=m.charAt(0)==="*";if(x&&(m=m.slice(1)),m.length<2||m.charAt(0)!==m.slice(-1))throw new i(`\\verb assertion failed --
                    please report what input caused this bug`);return m=m.slice(1,-1),{type:"verb",mode:"text",body:m,star:x}}es.hasOwnProperty(s[0])&&!Te[this.mode][s[0]]&&(this.settings.strict&&this.mode==="math"&&this.settings.reportNonstrict("unicodeTextInMathMode",'Accented Unicode text character "'+s[0]+'" used in math mode',n),s=es[s[0]]+s.slice(1));let c=v1.exec(s);c&&(s=s.substring(0,c.index),s==="i"?s="\u0131":s==="j"&&(s="\u0237"));let f;if(Te[this.mode][s]){this.settings.strict&&this.mode==="math"&&q0.indexOf(s)>=0&&this.settings.reportNonstrict("unicodeTextInMathMode",'Latin-1/Unicode text character "'+s[0]+'" used in math mode',n);let m=Te[this.mode][s].group,x=ut.range(n),w;if(vh.hasOwnProperty(m)){let M=m;w={type:"atom",mode:this.mode,family:M,loc:x,text:s}}else w={type:m,mode:this.mode,loc:x,text:s};f=w}else if(s.charCodeAt(0)>=128)this.settings.strict&&(ot(s.charCodeAt(0))?this.mode==="math"&&this.settings.reportNonstrict("unicodeTextInMathMode",'Unicode text character "'+s[0]+'" used in math mode',n):this.settings.reportNonstrict("unknownSymbol",'Unrecognized Unicode character "'+s[0]+'"'+(" ("+s.charCodeAt(0)+")"),n)),f={type:"textord",mode:"text",loc:ut.range(n),text:s};else return null;if(this.consume(),c)for(let m=0;m<c[0].length;m++){let x=c[0][m];if(!gi[x])throw new i("Unknown accent ' "+x+"'",n);let w=gi[x][this.mode]||gi[x].text;if(!w)throw new i("Accent "+x+" unsupported in "+this.mode+" mode",n);f={type:"accent",mode:this.mode,loc:ut.range(n),label:w,isStretchy:!1,isShifty:!0,base:f}}return f}}Rn.endOfExpression=["}","\\endgroup","\\end","\\right","&"];var xi=function(o,n){if(!(typeof o=="string"||o instanceof String))throw new TypeError("KaTeX can only parse string typed expression");let s=new Rn(o,n);delete s.gullet.macros.current["\\df@tag"];let c=s.parse();if(delete s.gullet.macros.current["\\current@color"],delete s.gullet.macros.current["\\color"],s.gullet.macros.get("\\df@tag")){if(!n.displayMode)throw new i("\\tag works only in display equations");c=[{type:"tag",mode:"text",body:c,tag:s.subparse([new pt("\\df@tag")])}]}return c};let ts=function(o,n,s){n.textContent="";let c=yi(o,s).toNode();n.appendChild(c)};typeof document!="undefined"&&document.compatMode!=="CSS1Compat"&&(typeof console!="undefined"&&console.warn("Warning: KaTeX doesn't work in quirks mode. Make sure your website has a suitable doctype."),ts=function(){throw new i("KaTeX doesn't work in quirks mode.")});let A1=function(o,n){return yi(o,n).toMarkup()},_1=function(o,n){let s=new X(n);return xi(o,s)},rs=function(o,n,s){if(s.throwOnError||!(o instanceof i))throw o;let c=O.makeSpan(["katex-error"],[new ft(n)]);return c.setAttribute("title",o.toString()),c.setAttribute("style","color:"+s.errorColor),c},yi=function(o,n){let s=new X(n);try{let c=xi(o,s);return Hh(c,o,s)}catch(c){return rs(c,o,s)}};var C1={version:"0.16.22",render:ts,renderToString:A1,ParseError:i,SETTINGS_SCHEMA:q,__parse:_1,__renderToDomTree:yi,__renderToHTMLTree:function(o,n){let s=new X(n);try{let c=xi(o,s);return $h(c,o,s)}catch(c){return rs(c,o,s)}},__setFontMetrics:hh,__defineSymbol:d,__defineFunction:j,__defineMacro:k,__domTree:{Span:Rr,Anchor:R0,SymbolNode:ft,SvgNode:It,PathNode:Xt,LineNode:L0}},T1=C1;return t=t.default,t}()})});var Ps=os((M6,qs)=>{"use strict";var Xd=(e,t)=>{let r="\\",i="$",a=(t||{}).delimiter||i;if(a.length!==1)throw new Error("invalid delimiter");let u=Ls(),h=(y,S)=>u.renderToString(y,{displayMode:S,throwOnError:!1}),p=(y,S,C)=>{let T=!1,F=y.bMarks[S]+y.tShift[S],z=y.eMarks[S];if(F+1>z)return!1;let q=y.src.charAt(F);if(q!==a)return!1;let L=F;F=y.skipChars(F,q);let X=F-L;if(X!==2)return!1;let re=S;for(;++re,!(re>=C||(F=L=y.bMarks[re]+y.tShift[re],z=y.eMarks[re],F<z&&y.tShift[re]<y.blkIndent));)if(y.src.charAt(F)===a&&!(y.tShift[re]-y.blkIndent>=4)&&(F=y.skipChars(F,q),!(F-L<X)&&(F=y.skipSpaces(F),!(F<z)))){T=!0;break}X=y.tShift[S],y.line=re+(T?1:0);let ce=y.getLines(S+1,re,X,!0).replace(/[ \n]+/g," ").trim();return y.tokens.push({type:"katex",params:null,content:ce,lines:[S,y.line],level:y.level,block:!0}),!0},b=(y,S)=>{let C=y.pos,T=y.posMax,F=C;if(y.src.charAt(F)!==a)return!1;for(++F;F<T&&y.src.charAt(F)===a;)++F;let z=y.src.slice(C,F);if(z.length>2)return!1;let q=F,L=0;for(;F<T;){let X=y.src.charAt(F);if(X==="{"&&(F==0||y.src.charAt(F-1)!=r))L+=1;else if(X==="}"&&(F==0||y.src.charAt(F-1)!=r)){if(L-=1,L<0)return!1}else if(X===a&&L===0){let re=F,ce=F+1;for(;ce<T&&y.src.charAt(ce)===a;)++ce;if(ce-re===z.length){if(!S){let me=y.src.slice(q,re).replace(/[ \n]+/g," ").trim();y.push({type:"katex",content:me,block:z.length>1,level:y.level})}return y.pos=ce,!0}}F+=1}return S||(y.pending+=z),y.pos+=z.length,!0};e.inline.ruler.push("katex",b,t),e.block.ruler.push("katex",p,t),e.renderer.rules.katex=(y,S)=>h(y[S].content,y[S].block),e.renderer.rules.katex.delimiter=a};qs.exports=Xd});var i6={};F1(i6,{default:()=>I0});module.exports=L1(i6);var Je=require("obsidian");var Pn;function ms(e){return Pn=Pn||document.createElement("textarea"),Pn.innerHTML="&"+e+";",Pn.value}var q1=Object.prototype.hasOwnProperty;function P1(e,t){return e?q1.call(e,t):!1}function gs(e){var t=[].slice.call(arguments,1);return t.forEach(function(r){if(r){if(typeof r!="object")throw new TypeError(r+"must be object");Object.keys(r).forEach(function(i){e[i]=r[i]})}}),e}var H1=/\\([\\!"#$%&'()*+,.\/:;<=>?@[\]^_`{|}~-])/g;function $r(e){return e.indexOf("\\")<0?e:e.replace(H1,"$1")}function xs(e){return!(e>=55296&&e<=57343||e>=64976&&e<=65007||(e&65535)===65535||(e&65535)===65534||e>=0&&e<=8||e===11||e>=14&&e<=31||e>=127&&e<=159||e>1114111)}function Mi(e){if(e>65535){e-=65536;var t=55296+(e>>10),r=56320+(e&1023);return String.fromCharCode(t,r)}return String.fromCharCode(e)}var $1=/&([a-z#][a-z0-9]{1,31});/gi,V1=/^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))/i;function G1(e,t){var r=0,i=ms(t);return t!==i?i:t.charCodeAt(0)===35&&V1.test(t)&&(r=t[1].toLowerCase()==="x"?parseInt(t.slice(2),16):parseInt(t.slice(1),10),xs(r))?Mi(r):e}function or(e){return e.indexOf("&")<0?e:e.replace($1,G1)}var Y1=/[&<>"]/,U1=/[&<>"]/g,X1={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};function j1(e){return X1[e]}function tt(e){return Y1.test(e)?e.replace(U1,j1):e}var J={};J.blockquote_open=function(){return`<blockquote>
`};J.blockquote_close=function(e,t){return"</blockquote>"+lr(e,t)};J.code=function(e,t){return e[t].block?"<pre><code>"+tt(e[t].content)+"</code></pre>"+lr(e,t):"<code>"+tt(e[t].content)+"</code>"};J.fence=function(e,t,r,i,l){var a=e[t],u="",h=r.langPrefix,p="",b,y,S;if(a.params){if(b=a.params.split(/\s+/g),y=b.join(" "),P1(l.rules.fence_custom,b[0]))return l.rules.fence_custom[b[0]](e,t,r,i,l);p=tt(or($r(y))),u=' class="'+h+p+'"'}return r.highlight?S=r.highlight.apply(r.highlight,[a.content].concat(b))||tt(a.content):S=tt(a.content),"<pre><code"+u+">"+S+"</code></pre>"+lr(e,t)};J.fence_custom={};J.heading_open=function(e,t){return"<h"+e[t].hLevel+">"};J.heading_close=function(e,t){return"</h"+e[t].hLevel+`>
`};J.hr=function(e,t,r){return(r.xhtmlOut?"<hr />":"<hr>")+lr(e,t)};J.bullet_list_open=function(){return`<ul>
`};J.bullet_list_close=function(e,t){return"</ul>"+lr(e,t)};J.list_item_open=function(){return"<li>"};J.list_item_close=function(){return`</li>
`};J.ordered_list_open=function(e,t){var r=e[t],i=r.order>1?' start="'+r.order+'"':"";return"<ol"+i+`>
`};J.ordered_list_close=function(e,t){return"</ol>"+lr(e,t)};J.paragraph_open=function(e,t){return e[t].tight?"":"<p>"};J.paragraph_close=function(e,t){var r=!(e[t].tight&&t&&e[t-1].type==="inline"&&!e[t-1].content);return(e[t].tight?"":"</p>")+(r?lr(e,t):"")};J.link_open=function(e,t,r){var i=e[t].title?' title="'+tt(or(e[t].title))+'"':"",l=r.linkTarget?' target="'+r.linkTarget+'"':"";return'<a href="'+tt(e[t].href)+'"'+i+l+">"};J.link_close=function(){return"</a>"};J.image=function(e,t,r){var i=' src="'+tt(e[t].src)+'"',l=e[t].title?' title="'+tt(or(e[t].title))+'"':"",a=' alt="'+(e[t].alt?tt(or($r(e[t].alt))):"")+'"',u=r.xhtmlOut?" /":"";return"<img"+i+a+l+u+">"};J.table_open=function(){return`<table>
`};J.table_close=function(){return`</table>
`};J.thead_open=function(){return`<thead>
`};J.thead_close=function(){return`</thead>
`};J.tbody_open=function(){return`<tbody>
`};J.tbody_close=function(){return`</tbody>
`};J.tr_open=function(){return"<tr>"};J.tr_close=function(){return`</tr>
`};J.th_open=function(e,t){var r=e[t];return"<th"+(r.align?' style="text-align:'+r.align+'"':"")+">"};J.th_close=function(){return"</th>"};J.td_open=function(e,t){var r=e[t];return"<td"+(r.align?' style="text-align:'+r.align+'"':"")+">"};J.td_close=function(){return"</td>"};J.strong_open=function(){return"<strong>"};J.strong_close=function(){return"</strong>"};J.em_open=function(){return"<em>"};J.em_close=function(){return"</em>"};J.del_open=function(){return"<del>"};J.del_close=function(){return"</del>"};J.ins_open=function(){return"<ins>"};J.ins_close=function(){return"</ins>"};J.mark_open=function(){return"<mark>"};J.mark_close=function(){return"</mark>"};J.sub=function(e,t){return"<sub>"+tt(e[t].content)+"</sub>"};J.sup=function(e,t){return"<sup>"+tt(e[t].content)+"</sup>"};J.hardbreak=function(e,t,r){return r.xhtmlOut?`<br />
`:`<br>
`};J.softbreak=function(e,t,r){return r.breaks?r.xhtmlOut?`<br />
`:`<br>
`:`
`};J.text=function(e,t){return tt(e[t].content)};J.htmlblock=function(e,t){return e[t].content};J.htmltag=function(e,t){return e[t].content};J.abbr_open=function(e,t){return'<abbr title="'+tt(or(e[t].title))+'">'};J.abbr_close=function(){return"</abbr>"};J.footnote_ref=function(e,t){var r=Number(e[t].id+1).toString(),i="fnref"+r;return e[t].subId>0&&(i+=":"+e[t].subId),'<sup class="footnote-ref"><a href="#fn'+r+'" id="'+i+'">['+r+"]</a></sup>"};J.footnote_block_open=function(e,t,r){var i=r.xhtmlOut?`<hr class="footnotes-sep" />
`:`<hr class="footnotes-sep">
`;return i+`<section class="footnotes">
<ol class="footnotes-list">
`};J.footnote_block_close=function(){return`</ol>
</section>
`};J.footnote_open=function(e,t){var r=Number(e[t].id+1).toString();return'<li id="fn'+r+'"  class="footnote-item">'};J.footnote_close=function(){return`</li>
`};J.footnote_anchor=function(e,t){var r=Number(e[t].id+1).toString(),i="fnref"+r;return e[t].subId>0&&(i+=":"+e[t].subId),' <a href="#'+i+'" class="footnote-backref">\u21A9</a>'};J.dl_open=function(){return`<dl>
`};J.dt_open=function(){return"<dt>"};J.dd_open=function(){return"<dd>"};J.dl_close=function(){return`</dl>
`};J.dt_close=function(){return`</dt>
`};J.dd_close=function(){return`</dd>
`};function ys(e,t){return++t>=e.length-2?t:e[t].type==="paragraph_open"&&e[t].tight&&e[t+1].type==="inline"&&e[t+1].content.length===0&&e[t+2].type==="paragraph_close"&&e[t+2].tight?ys(e,t+2):t}var lr=J.getBreak=function(t,r){return r=ys(t,r),r<t.length&&t[r].type==="list_item_close"?"":`
`};function _i(){this.rules=gs({},J),this.getBreak=J.getBreak}_i.prototype.renderInline=function(e,t,r){for(var i=this.rules,l=e.length,a=0,u="";l--;)u+=i[e[a].type](e,a++,t,r,this);return u};_i.prototype.render=function(e,t,r){for(var i=this.rules,l=e.length,a=-1,u="";++a<l;)e[a].type==="inline"?u+=this.renderInline(e[a].children,t,r):u+=i[e[a].type](e,a,t,r,this);return u};function ht(){this.__rules__=[],this.__cache__=null}ht.prototype.__find__=function(e){for(var t=this.__rules__.length,r=-1;t--;)if(this.__rules__[++r].name===e)return r;return-1};ht.prototype.__compile__=function(){var e=this,t=[""];e.__rules__.forEach(function(r){r.enabled&&r.alt.forEach(function(i){t.indexOf(i)<0&&t.push(i)})}),e.__cache__={},t.forEach(function(r){e.__cache__[r]=[],e.__rules__.forEach(function(i){i.enabled&&(r&&i.alt.indexOf(r)<0||e.__cache__[r].push(i.fn))})})};ht.prototype.at=function(e,t,r){var i=this.__find__(e),l=r||{};if(i===-1)throw new Error("Parser rule not found: "+e);this.__rules__[i].fn=t,this.__rules__[i].alt=l.alt||[],this.__cache__=null};ht.prototype.before=function(e,t,r,i){var l=this.__find__(e),a=i||{};if(l===-1)throw new Error("Parser rule not found: "+e);this.__rules__.splice(l,0,{name:t,enabled:!0,fn:r,alt:a.alt||[]}),this.__cache__=null};ht.prototype.after=function(e,t,r,i){var l=this.__find__(e),a=i||{};if(l===-1)throw new Error("Parser rule not found: "+e);this.__rules__.splice(l+1,0,{name:t,enabled:!0,fn:r,alt:a.alt||[]}),this.__cache__=null};ht.prototype.push=function(e,t,r){var i=r||{};this.__rules__.push({name:e,enabled:!0,fn:t,alt:i.alt||[]}),this.__cache__=null};ht.prototype.enable=function(e,t){e=Array.isArray(e)?e:[e],t&&this.__rules__.forEach(function(r){r.enabled=!1}),e.forEach(function(r){var i=this.__find__(r);if(i<0)throw new Error("Rules manager: invalid rule name "+r);this.__rules__[i].enabled=!0},this),this.__cache__=null};ht.prototype.disable=function(e){e=Array.isArray(e)?e:[e],e.forEach(function(t){var r=this.__find__(t);if(r<0)throw new Error("Rules manager: invalid rule name "+t);this.__rules__[r].enabled=!1},this),this.__cache__=null};ht.prototype.getRules=function(e){return this.__cache__===null&&this.__compile__(),this.__cache__[e]||[]};function W1(e){e.inlineMode?e.tokens.push({type:"inline",content:e.src.replace(/\n/g," ").trim(),level:0,lines:[0,1],children:[]}):e.block.parse(e.src,e.options,e.env,e.tokens)}function sr(e,t,r,i,l){this.src=e,this.env=i,this.options=r,this.parser=t,this.tokens=l,this.pos=0,this.posMax=this.src.length,this.level=0,this.pending="",this.pendingLevel=0,this.cache=[],this.isInLabel=!1,this.linkLevel=0,this.linkContent="",this.labelUnmatchedScopes=0}sr.prototype.pushPending=function(){this.tokens.push({type:"text",content:this.pending,level:this.pendingLevel}),this.pending=""};sr.prototype.push=function(e){this.pending&&this.pushPending(),this.tokens.push(e),this.pendingLevel=this.level};sr.prototype.cacheSet=function(e,t){for(var r=this.cache.length;r<=e;r++)this.cache.push(0);this.cache[e]=t};sr.prototype.cacheGet=function(e){return e<this.cache.length?this.cache[e]:0};function Vr(e,t){var r,i,l,a=-1,u=e.posMax,h=e.pos,p=e.isInLabel;if(e.isInLabel)return-1;if(e.labelUnmatchedScopes)return e.labelUnmatchedScopes--,-1;for(e.pos=t+1,e.isInLabel=!0,r=1;e.pos<u;){if(l=e.src.charCodeAt(e.pos),l===91)r++;else if(l===93&&(r--,r===0)){i=!0;break}e.parser.skipToken(e)}return i?(a=e.pos,e.labelUnmatchedScopes=0):e.labelUnmatchedScopes=r-1,e.pos=h,e.isInLabel=p,a}function K1(e,t,r,i){var l,a,u,h,p,b;if(e.charCodeAt(0)!==42||e.charCodeAt(1)!==91||e.indexOf("]:")===-1||(l=new sr(e,t,r,i,[]),a=Vr(l,1),a<0||e.charCodeAt(a+1)!==58))return-1;for(h=l.posMax,u=a+2;u<h&&l.src.charCodeAt(u)!==10;u++);return p=e.slice(2,a),b=e.slice(a+2,u).trim(),b.length===0?-1:(i.abbreviations||(i.abbreviations={}),typeof i.abbreviations[":"+p]=="undefined"&&(i.abbreviations[":"+p]=b),u)}function Z1(e){var t=e.tokens,r,i,l,a;if(!e.inlineMode){for(r=1,i=t.length-1;r<i;r++)if(t[r-1].type==="paragraph_open"&&t[r].type==="inline"&&t[r+1].type==="paragraph_close"){for(l=t[r].content;l.length&&(a=K1(l,e.inline,e.options,e.env),!(a<0));)l=l.slice(a).trim();t[r].content=l,l.length||(t[r-1].tight=!0,t[r+1].tight=!0)}}}function Ai(e){var t=or(e);try{t=decodeURI(t)}catch(r){}return encodeURI(t)}function bs(e,t){var r,i,l,a=t,u=e.posMax;if(e.src.charCodeAt(t)===60){for(t++;t<u;){if(r=e.src.charCodeAt(t),r===10)return!1;if(r===62)return l=Ai($r(e.src.slice(a+1,t))),e.parser.validateLink(l)?(e.pos=t+1,e.linkContent=l,!0):!1;if(r===92&&t+1<u){t+=2;continue}t++}return!1}for(i=0;t<u&&(r=e.src.charCodeAt(t),!(r===32||r<32||r===127));){if(r===92&&t+1<u){t+=2;continue}if(r===40&&(i++,i>1)||r===41&&(i--,i<0))break;t++}return a===t||(l=$r(e.src.slice(a,t)),!e.parser.validateLink(l))?!1:(e.linkContent=l,e.pos=t,!0)}function vs(e,t){var r,i=t,l=e.posMax,a=e.src.charCodeAt(t);if(a!==34&&a!==39&&a!==40)return!1;for(t++,a===40&&(a=41);t<l;){if(r=e.src.charCodeAt(t),r===a)return e.pos=t+1,e.linkContent=$r(e.src.slice(i+1,t)),!0;if(r===92&&t+1<l){t+=2;continue}t++}return!1}function ws(e){return e.trim().replace(/\s+/g," ").toUpperCase()}function J1(e,t,r,i){var l,a,u,h,p,b,y,S,C;if(e.charCodeAt(0)!==91||e.indexOf("]:")===-1||(l=new sr(e,t,r,i,[]),a=Vr(l,0),a<0||e.charCodeAt(a+1)!==58))return-1;for(h=l.posMax,u=a+2;u<h&&(p=l.src.charCodeAt(u),!(p!==32&&p!==10));u++);if(!bs(l,u))return-1;for(y=l.linkContent,u=l.pos,b=u,u=u+1;u<h&&(p=l.src.charCodeAt(u),!(p!==32&&p!==10));u++);for(u<h&&b!==u&&vs(l,u)?(S=l.linkContent,u=l.pos):(S="",u=b);u<h&&l.src.charCodeAt(u)===32;)u++;return u<h&&l.src.charCodeAt(u)!==10?-1:(C=ws(e.slice(1,a)),typeof i.references[C]=="undefined"&&(i.references[C]={title:S,href:y}),u)}function Q1(e){var t=e.tokens,r,i,l,a;if(e.env.references=e.env.references||{},!e.inlineMode){for(r=1,i=t.length-1;r<i;r++)if(t[r].type==="inline"&&t[r-1].type==="paragraph_open"&&t[r+1].type==="paragraph_close"){for(l=t[r].content;l.length&&(a=J1(l,e.inline,e.options,e.env),!(a<0));)l=l.slice(a).trim();t[r].content=l,l.length||(t[r-1].tight=!0,t[r+1].tight=!0)}}}function ef(e){var t=e.tokens,r,i,l;for(i=0,l=t.length;i<l;i++)r=t[i],r.type==="inline"&&e.inline.parse(r.content,e.options,e.env,r.children)}function tf(e){var t,r,i,l,a,u,h,p,b,y=0,S=!1,C={};if(e.env.footnotes&&(e.tokens=e.tokens.filter(function(T){return T.type==="footnote_reference_open"?(S=!0,p=[],b=T.label,!1):T.type==="footnote_reference_close"?(S=!1,C[":"+b]=p,!1):(S&&p.push(T),!S)}),!!e.env.footnotes.list)){for(u=e.env.footnotes.list,e.tokens.push({type:"footnote_block_open",level:y++}),t=0,r=u.length;t<r;t++){for(e.tokens.push({type:"footnote_open",id:t,level:y++}),u[t].tokens?(h=[],h.push({type:"paragraph_open",tight:!1,level:y++}),h.push({type:"inline",content:"",level:y,children:u[t].tokens}),h.push({type:"paragraph_close",tight:!1,level:--y})):u[t].label&&(h=C[":"+u[t].label]),e.tokens=e.tokens.concat(h),e.tokens[e.tokens.length-1].type==="paragraph_close"?a=e.tokens.pop():a=null,l=u[t].count>0?u[t].count:1,i=0;i<l;i++)e.tokens.push({type:"footnote_anchor",id:t,subId:i,level:y});a&&e.tokens.push(a),e.tokens.push({type:"footnote_close",level:--y})}e.tokens.push({type:"footnote_block_close",level:--y})}}var ss=` 
()[]'".,!?-`;function bi(e){return e.replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1")}function rf(e){var t,r,i,l,a,u,h,p,b,y,S,C,T=e.tokens;if(e.env.abbreviations){for(e.env.abbrRegExp||(C="(^|["+ss.split("").map(bi).join("")+"])("+Object.keys(e.env.abbreviations).map(function(F){return F.substr(1)}).sort(function(F,z){return z.length-F.length}).map(bi).join("|")+")($|["+ss.split("").map(bi).join("")+"])",e.env.abbrRegExp=new RegExp(C,"g")),y=e.env.abbrRegExp,r=0,i=T.length;r<i;r++)if(T[r].type==="inline"){for(l=T[r].children,t=l.length-1;t>=0;t--)if(a=l[t],a.type==="text"){for(p=0,u=a.content,y.lastIndex=0,b=a.level,h=[];S=y.exec(u);)y.lastIndex>p&&h.push({type:"text",content:u.slice(p,S.index+S[1].length),level:b}),h.push({type:"abbr_open",title:e.env.abbreviations[":"+S[2]],level:b++}),h.push({type:"text",content:S[2],level:b}),h.push({type:"abbr_close",level:--b}),p=y.lastIndex-S[3].length;h.length&&(p<u.length&&h.push({type:"text",content:u.slice(p),level:b}),T[r].children=l=[].concat(l.slice(0,t),h,l.slice(t+1)))}}}}var nf=/\+-|\.\.|\?\?\?\?|!!!!|,,|--/,of=/\((c|tm|r|p)\)/ig,lf={c:"\xA9",r:"\xAE",p:"\xA7",tm:"\u2122"};function sf(e){return e.indexOf("(")<0?e:e.replace(of,function(t,r){return lf[r.toLowerCase()]})}function af(e){var t,r,i,l,a;if(e.options.typographer){for(a=e.tokens.length-1;a>=0;a--)if(e.tokens[a].type==="inline")for(l=e.tokens[a].children,t=l.length-1;t>=0;t--)r=l[t],r.type==="text"&&(i=r.content,i=sf(i),nf.test(i)&&(i=i.replace(/\+-/g,"\xB1").replace(/\.{2,}/g,"\u2026").replace(/([?!])…/g,"$1..").replace(/([?!]){4,}/g,"$1$1$1").replace(/,{2,}/g,",").replace(/(^|[^-])---([^-]|$)/mg,"$1\u2014$2").replace(/(^|\s)--(\s|$)/mg,"$1\u2013$2").replace(/(^|[^-\s])--([^-\s]|$)/mg,"$1\u2013$2")),r.content=i)}}var cf=/['"]/,as=/['"]/g,uf=/[-\s()\[\]]/,cs="\u2019";function us(e,t){return t<0||t>=e.length?!1:!uf.test(e[t])}function Mr(e,t,r){return e.substr(0,t)+r+e.substr(t+1)}function hf(e){var t,r,i,l,a,u,h,p,b,y,S,C,T,F,z,q,L;if(e.options.typographer){for(L=[],z=e.tokens.length-1;z>=0;z--)if(e.tokens[z].type==="inline")for(q=e.tokens[z].children,L.length=0,t=0;t<q.length;t++){if(r=q[t],r.type!=="text"||cf.test(r.text))continue;for(h=q[t].level,T=L.length-1;T>=0&&!(L[T].level<=h);T--);L.length=T+1,i=r.content,a=0,u=i.length;e:for(;a<u&&(as.lastIndex=a,l=as.exec(i),!!l);){if(p=!us(i,l.index-1),a=l.index+1,F=l[0]==="'",b=!us(i,a),!b&&!p){F&&(r.content=Mr(r.content,l.index,cs));continue}if(S=!b,C=!p,C){for(T=L.length-1;T>=0&&(y=L[T],!(L[T].level<h));T--)if(y.single===F&&L[T].level===h){y=L[T],F?(q[y.token].content=Mr(q[y.token].content,y.pos,e.options.quotes[2]),r.content=Mr(r.content,l.index,e.options.quotes[3])):(q[y.token].content=Mr(q[y.token].content,y.pos,e.options.quotes[0]),r.content=Mr(r.content,l.index,e.options.quotes[1])),L.length=T;continue e}}S?L.push({token:t,pos:l.index,single:F,level:h}):C&&F&&(r.content=Mr(r.content,l.index,cs))}}}}var vi=[["block",W1],["abbr",Z1],["references",Q1],["inline",ef],["footnote_tail",tf],["abbr2",rf],["replacements",af],["smartquotes",hf]];function ks(){this.options={},this.ruler=new ht;for(var e=0;e<vi.length;e++)this.ruler.push(vi[e][0],vi[e][1])}ks.prototype.process=function(e){var t,r,i;for(i=this.ruler.getRules(""),t=0,r=i.length;t<r;t++)i[t](e)};function ar(e,t,r,i,l){var a,u,h,p,b,y,S;for(this.src=e,this.parser=t,this.options=r,this.env=i,this.tokens=l,this.bMarks=[],this.eMarks=[],this.tShift=[],this.blkIndent=0,this.line=0,this.lineMax=0,this.tight=!1,this.parentType="root",this.ddIndent=-1,this.level=0,this.result="",u=this.src,y=0,S=!1,h=p=y=0,b=u.length;p<b;p++){if(a=u.charCodeAt(p),!S)if(a===32){y++;continue}else S=!0;(a===10||p===b-1)&&(a!==10&&p++,this.bMarks.push(h),this.eMarks.push(p),this.tShift.push(y),S=!1,y=0,h=p+1)}this.bMarks.push(u.length),this.eMarks.push(u.length),this.tShift.push(0),this.lineMax=this.bMarks.length-1}ar.prototype.isEmpty=function(t){return this.bMarks[t]+this.tShift[t]>=this.eMarks[t]};ar.prototype.skipEmptyLines=function(t){for(var r=this.lineMax;t<r&&!(this.bMarks[t]+this.tShift[t]<this.eMarks[t]);t++);return t};ar.prototype.skipSpaces=function(t){for(var r=this.src.length;t<r&&this.src.charCodeAt(t)===32;t++);return t};ar.prototype.skipChars=function(t,r){for(var i=this.src.length;t<i&&this.src.charCodeAt(t)===r;t++);return t};ar.prototype.skipCharsBack=function(t,r,i){if(t<=i)return t;for(;t>i;)if(r!==this.src.charCodeAt(--t))return t+1;return t};ar.prototype.getLines=function(t,r,i,l){var a,u,h,p,b,y=t;if(t>=r)return"";if(y+1===r)return u=this.bMarks[y]+Math.min(this.tShift[y],i),h=l?this.eMarks[y]+1:this.eMarks[y],this.src.slice(u,h);for(p=new Array(r-t),a=0;y<r;y++,a++)b=this.tShift[y],b>i&&(b=i),b<0&&(b=0),u=this.bMarks[y]+b,y+1<r||l?h=this.eMarks[y]+1:h=this.eMarks[y],p[a]=this.src.slice(u,h);return p.join("")};function ff(e,t,r){var i,l;if(e.tShift[t]-e.blkIndent<4)return!1;for(l=i=t+1;i<r;){if(e.isEmpty(i)){i++;continue}if(e.tShift[i]-e.blkIndent>=4){i++,l=i;continue}break}return e.line=i,e.tokens.push({type:"code",content:e.getLines(t,l,4+e.blkIndent,!0),block:!0,lines:[t,e.line],level:e.level}),!0}function df(e,t,r,i){var l,a,u,h,p,b=!1,y=e.bMarks[t]+e.tShift[t],S=e.eMarks[t];if(y+3>S||(l=e.src.charCodeAt(y),l!==126&&l!==96)||(p=y,y=e.skipChars(y,l),a=y-p,a<3)||(u=e.src.slice(y,S).trim(),u.indexOf("`")>=0))return!1;if(i)return!0;for(h=t;h++,!(h>=r||(y=p=e.bMarks[h]+e.tShift[h],S=e.eMarks[h],y<S&&e.tShift[h]<e.blkIndent));)if(e.src.charCodeAt(y)===l&&!(e.tShift[h]-e.blkIndent>=4)&&(y=e.skipChars(y,l),!(y-p<a)&&(y=e.skipSpaces(y),!(y<S)))){b=!0;break}return a=e.tShift[t],e.line=h+(b?1:0),e.tokens.push({type:"fence",params:u,content:e.getLines(t+1,h,a,!0),lines:[t,e.line],level:e.level}),!0}function pf(e,t,r,i){var l,a,u,h,p,b,y,S,C,T,F,z=e.bMarks[t]+e.tShift[t],q=e.eMarks[t];if(z>q||e.src.charCodeAt(z++)!==62||e.level>=e.options.maxNesting)return!1;if(i)return!0;for(e.src.charCodeAt(z)===32&&z++,p=e.blkIndent,e.blkIndent=0,h=[e.bMarks[t]],e.bMarks[t]=z,z=z<q?e.skipSpaces(z):z,a=z>=q,u=[e.tShift[t]],e.tShift[t]=z-e.bMarks[t],S=e.parser.ruler.getRules("blockquote"),l=t+1;l<r&&(z=e.bMarks[l]+e.tShift[l],q=e.eMarks[l],!(z>=q));l++){if(e.src.charCodeAt(z++)===62){e.src.charCodeAt(z)===32&&z++,h.push(e.bMarks[l]),e.bMarks[l]=z,z=z<q?e.skipSpaces(z):z,a=z>=q,u.push(e.tShift[l]),e.tShift[l]=z-e.bMarks[l];continue}if(a)break;for(F=!1,C=0,T=S.length;C<T;C++)if(S[C](e,l,r,!0)){F=!0;break}if(F)break;h.push(e.bMarks[l]),u.push(e.tShift[l]),e.tShift[l]=-1337}for(b=e.parentType,e.parentType="blockquote",e.tokens.push({type:"blockquote_open",lines:y=[t,0],level:e.level++}),e.parser.tokenize(e,t,l),e.tokens.push({type:"blockquote_close",level:--e.level}),e.parentType=b,y[1]=e.line,C=0;C<u.length;C++)e.bMarks[C+t]=h[C],e.tShift[C+t]=u[C];return e.blkIndent=p,!0}function mf(e,t,r,i){var l,a,u,h=e.bMarks[t],p=e.eMarks[t];if(h+=e.tShift[t],h>p||(l=e.src.charCodeAt(h++),l!==42&&l!==45&&l!==95))return!1;for(a=1;h<p;){if(u=e.src.charCodeAt(h++),u!==l&&u!==32)return!1;u===l&&a++}return a<3?!1:(i||(e.line=t+1,e.tokens.push({type:"hr",lines:[t,e.line],level:e.level})),!0)}function hs(e,t){var r,i,l;return i=e.bMarks[t]+e.tShift[t],l=e.eMarks[t],i>=l||(r=e.src.charCodeAt(i++),r!==42&&r!==45&&r!==43)||i<l&&e.src.charCodeAt(i)!==32?-1:i}function fs(e,t){var r,i=e.bMarks[t]+e.tShift[t],l=e.eMarks[t];if(i+1>=l||(r=e.src.charCodeAt(i++),r<48||r>57))return-1;for(;;){if(i>=l)return-1;if(r=e.src.charCodeAt(i++),!(r>=48&&r<=57)){if(r===41||r===46)break;return-1}}return i<l&&e.src.charCodeAt(i)!==32?-1:i}function gf(e,t){var r,i,l=e.level+2;for(r=t+2,i=e.tokens.length-2;r<i;r++)e.tokens[r].level===l&&e.tokens[r].type==="paragraph_open"&&(e.tokens[r+2].tight=!0,e.tokens[r].tight=!0,r+=2)}function xf(e,t,r,i){var l,a,u,h,p,b,y,S,C,T,F,z,q,L,X,re,ce,me,be=!0,Se,$e,ze,Ve;if((S=fs(e,t))>=0)q=!0;else if((S=hs(e,t))>=0)q=!1;else return!1;if(e.level>=e.options.maxNesting)return!1;if(z=e.src.charCodeAt(S-1),i)return!0;for(X=e.tokens.length,q?(y=e.bMarks[t]+e.tShift[t],F=Number(e.src.substr(y,S-y-1)),e.tokens.push({type:"ordered_list_open",order:F,lines:ce=[t,0],level:e.level++})):e.tokens.push({type:"bullet_list_open",lines:ce=[t,0],level:e.level++}),l=t,re=!1,Se=e.parser.ruler.getRules("list");l<r&&(L=e.skipSpaces(S),C=e.eMarks[l],L>=C?T=1:T=L-S,T>4&&(T=1),T<1&&(T=1),a=S-e.bMarks[l]+T,e.tokens.push({type:"list_item_open",lines:me=[t,0],level:e.level++}),h=e.blkIndent,p=e.tight,u=e.tShift[t],b=e.parentType,e.tShift[t]=L-e.bMarks[t],e.blkIndent=a,e.tight=!0,e.parentType="list",e.parser.tokenize(e,t,r,!0),(!e.tight||re)&&(be=!1),re=e.line-t>1&&e.isEmpty(e.line-1),e.blkIndent=h,e.tShift[t]=u,e.tight=p,e.parentType=b,e.tokens.push({type:"list_item_close",level:--e.level}),l=t=e.line,me[1]=l,L=e.bMarks[t],!(l>=r||e.isEmpty(l)||e.tShift[l]<e.blkIndent));){for(Ve=!1,$e=0,ze=Se.length;$e<ze;$e++)if(Se[$e](e,l,r,!0)){Ve=!0;break}if(Ve)break;if(q){if(S=fs(e,l),S<0)break}else if(S=hs(e,l),S<0)break;if(z!==e.src.charCodeAt(S-1))break}return e.tokens.push({type:q?"ordered_list_close":"bullet_list_close",level:--e.level}),ce[1]=l,e.line=l,be&&gf(e,X),!0}function yf(e,t,r,i){var l,a,u,h,p,b=e.bMarks[t]+e.tShift[t],y=e.eMarks[t];if(b+4>y||e.src.charCodeAt(b)!==91||e.src.charCodeAt(b+1)!==94||e.level>=e.options.maxNesting)return!1;for(h=b+2;h<y;h++){if(e.src.charCodeAt(h)===32)return!1;if(e.src.charCodeAt(h)===93)break}return h===b+2||h+1>=y||e.src.charCodeAt(++h)!==58?!1:(i||(h++,e.env.footnotes||(e.env.footnotes={}),e.env.footnotes.refs||(e.env.footnotes.refs={}),p=e.src.slice(b+2,h-2),e.env.footnotes.refs[":"+p]=-1,e.tokens.push({type:"footnote_reference_open",label:p,level:e.level++}),l=e.bMarks[t],a=e.tShift[t],u=e.parentType,e.tShift[t]=e.skipSpaces(h)-h,e.bMarks[t]=h,e.blkIndent+=4,e.parentType="footnote",e.tShift[t]<e.blkIndent&&(e.tShift[t]+=e.blkIndent,e.bMarks[t]-=e.blkIndent),e.parser.tokenize(e,t,r,!0),e.parentType=u,e.blkIndent-=4,e.tShift[t]=a,e.bMarks[t]=l,e.tokens.push({type:"footnote_reference_close",level:--e.level})),!0)}function bf(e,t,r,i){var l,a,u,h=e.bMarks[t]+e.tShift[t],p=e.eMarks[t];if(h>=p||(l=e.src.charCodeAt(h),l!==35||h>=p))return!1;for(a=1,l=e.src.charCodeAt(++h);l===35&&h<p&&a<=6;)a++,l=e.src.charCodeAt(++h);return a>6||h<p&&l!==32?!1:(i||(p=e.skipCharsBack(p,32,h),u=e.skipCharsBack(p,35,h),u>h&&e.src.charCodeAt(u-1)===32&&(p=u),e.line=t+1,e.tokens.push({type:"heading_open",hLevel:a,lines:[t,e.line],level:e.level}),h<p&&e.tokens.push({type:"inline",content:e.src.slice(h,p).trim(),level:e.level+1,lines:[t,e.line],children:[]}),e.tokens.push({type:"heading_close",hLevel:a,level:e.level})),!0)}function vf(e,t,r){var i,l,a,u=t+1;return u>=r||e.tShift[u]<e.blkIndent||e.tShift[u]-e.blkIndent>3||(l=e.bMarks[u]+e.tShift[u],a=e.eMarks[u],l>=a)||(i=e.src.charCodeAt(l),i!==45&&i!==61)||(l=e.skipChars(l,i),l=e.skipSpaces(l),l<a)?!1:(l=e.bMarks[t]+e.tShift[t],e.line=u+1,e.tokens.push({type:"heading_open",hLevel:i===61?1:2,lines:[t,e.line],level:e.level}),e.tokens.push({type:"inline",content:e.src.slice(l,e.eMarks[t]).trim(),level:e.level+1,lines:[t,e.line-1],children:[]}),e.tokens.push({type:"heading_close",hLevel:i===61?1:2,level:e.level}),!0)}var Ss={};["article","aside","button","blockquote","body","canvas","caption","col","colgroup","dd","div","dl","dt","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","iframe","li","map","object","ol","output","p","pre","progress","script","section","style","table","tbody","td","textarea","tfoot","th","tr","thead","ul","video"].forEach(function(e){Ss[e]=!0});var wf=/^<([a-zA-Z]{1,15})[\s\/>]/,kf=/^<\/([a-zA-Z]{1,15})[\s>]/;function Sf(e){var t=e|32;return t>=97&&t<=122}function Mf(e,t,r,i){var l,a,u,h=e.bMarks[t],p=e.eMarks[t],b=e.tShift[t];if(h+=b,!e.options.html||b>3||h+2>=p||e.src.charCodeAt(h)!==60)return!1;if(l=e.src.charCodeAt(h+1),l===33||l===63){if(i)return!0}else if(l===47||Sf(l)){if(l===47){if(a=e.src.slice(h,p).match(kf),!a)return!1}else if(a=e.src.slice(h,p).match(wf),!a)return!1;if(Ss[a[1].toLowerCase()]!==!0)return!1;if(i)return!0}else return!1;for(u=t+1;u<e.lineMax&&!e.isEmpty(u);)u++;return e.line=u,e.tokens.push({type:"htmlblock",level:e.level,lines:[t,e.line],content:e.getLines(t,u,0,!0)}),!0}function wi(e,t){var r=e.bMarks[t]+e.blkIndent,i=e.eMarks[t];return e.src.substr(r,i-r)}function Af(e,t,r,i){var l,a,u,h,p,b,y,S,C,T,F;if(t+2>r||(p=t+1,e.tShift[p]<e.blkIndent)||(u=e.bMarks[p]+e.tShift[p],u>=e.eMarks[p])||(l=e.src.charCodeAt(u),l!==124&&l!==45&&l!==58)||(a=wi(e,t+1),!/^[-:| ]+$/.test(a))||(b=a.split("|"),b<=2))return!1;for(S=[],h=0;h<b.length;h++){if(C=b[h].trim(),!C){if(h===0||h===b.length-1)continue;return!1}if(!/^:?-+:?$/.test(C))return!1;C.charCodeAt(C.length-1)===58?S.push(C.charCodeAt(0)===58?"center":"right"):C.charCodeAt(0)===58?S.push("left"):S.push("")}if(a=wi(e,t).trim(),a.indexOf("|")===-1||(b=a.replace(/^\||\|$/g,"").split("|"),S.length!==b.length))return!1;if(i)return!0;for(e.tokens.push({type:"table_open",lines:T=[t,0],level:e.level++}),e.tokens.push({type:"thead_open",lines:[t,t+1],level:e.level++}),e.tokens.push({type:"tr_open",lines:[t,t+1],level:e.level++}),h=0;h<b.length;h++)e.tokens.push({type:"th_open",align:S[h],lines:[t,t+1],level:e.level++}),e.tokens.push({type:"inline",content:b[h].trim(),lines:[t,t+1],level:e.level,children:[]}),e.tokens.push({type:"th_close",level:--e.level});for(e.tokens.push({type:"tr_close",level:--e.level}),e.tokens.push({type:"thead_close",level:--e.level}),e.tokens.push({type:"tbody_open",lines:F=[t+2,0],level:e.level++}),p=t+2;p<r&&!(e.tShift[p]<e.blkIndent||(a=wi(e,p).trim(),a.indexOf("|")===-1));p++){for(b=a.replace(/^\||\|$/g,"").split("|"),e.tokens.push({type:"tr_open",level:e.level++}),h=0;h<b.length;h++)e.tokens.push({type:"td_open",align:S[h],level:e.level++}),y=b[h].substring(b[h].charCodeAt(0)===124?1:0,b[h].charCodeAt(b[h].length-1)===124?b[h].length-1:b[h].length).trim(),e.tokens.push({type:"inline",content:y,level:e.level,children:[]}),e.tokens.push({type:"td_close",level:--e.level});e.tokens.push({type:"tr_close",level:--e.level})}return e.tokens.push({type:"tbody_close",level:--e.level}),e.tokens.push({type:"table_close",level:--e.level}),T[1]=F[1]=p,e.line=p,!0}function Hn(e,t){var r,i,l=e.bMarks[t]+e.tShift[t],a=e.eMarks[t];return l>=a||(i=e.src.charCodeAt(l++),i!==126&&i!==58)||(r=e.skipSpaces(l),l===r)||r>=a?-1:r}function _f(e,t){var r,i,l=e.level+2;for(r=t+2,i=e.tokens.length-2;r<i;r++)e.tokens[r].level===l&&e.tokens[r].type==="paragraph_open"&&(e.tokens[r+2].tight=!0,e.tokens[r].tight=!0,r+=2)}function Cf(e,t,r,i){var l,a,u,h,p,b,y,S,C,T,F,z,q,L;if(i)return e.ddIndent<0?!1:Hn(e,t)>=0;if(y=t+1,e.isEmpty(y)&&++y>r||e.tShift[y]<e.blkIndent||(l=Hn(e,y),l<0)||e.level>=e.options.maxNesting)return!1;b=e.tokens.length,e.tokens.push({type:"dl_open",lines:p=[t,0],level:e.level++}),u=t,a=y;e:for(;;){for(L=!0,q=!1,e.tokens.push({type:"dt_open",lines:[u,u],level:e.level++}),e.tokens.push({type:"inline",content:e.getLines(u,u+1,e.blkIndent,!1).trim(),level:e.level+1,lines:[u,u],children:[]}),e.tokens.push({type:"dt_close",level:--e.level});;){if(e.tokens.push({type:"dd_open",lines:h=[y,0],level:e.level++}),z=e.tight,C=e.ddIndent,S=e.blkIndent,F=e.tShift[a],T=e.parentType,e.blkIndent=e.ddIndent=e.tShift[a]+2,e.tShift[a]=l-e.bMarks[a],e.tight=!0,e.parentType="deflist",e.parser.tokenize(e,a,r,!0),(!e.tight||q)&&(L=!1),q=e.line-a>1&&e.isEmpty(e.line-1),e.tShift[a]=F,e.tight=z,e.parentType=T,e.blkIndent=S,e.ddIndent=C,e.tokens.push({type:"dd_close",level:--e.level}),h[1]=y=e.line,y>=r||e.tShift[y]<e.blkIndent)break e;if(l=Hn(e,y),l<0)break;a=y}if(y>=r||(u=y,e.isEmpty(u))||e.tShift[u]<e.blkIndent||(a=u+1,a>=r)||(e.isEmpty(a)&&a++,a>=r)||e.tShift[a]<e.blkIndent||(l=Hn(e,a),l<0))break}return e.tokens.push({type:"dl_close",level:--e.level}),p[1]=y,e.line=y,L&&_f(e,b),!0}function Tf(e,t){var r,i,l,a,u,h=t+1,p;if(r=e.lineMax,h<r&&!e.isEmpty(h)){for(p=e.parser.ruler.getRules("paragraph");h<r&&!e.isEmpty(h);h++)if(!(e.tShift[h]-e.blkIndent>3)){for(l=!1,a=0,u=p.length;a<u;a++)if(p[a](e,h,r,!0)){l=!0;break}if(l)break}}return i=e.getLines(t,h,e.blkIndent,!1).trim(),e.line=h,i.length&&(e.tokens.push({type:"paragraph_open",tight:!1,lines:[t,e.line],level:e.level}),e.tokens.push({type:"inline",content:i,level:e.level+1,lines:[t,e.line],children:[]}),e.tokens.push({type:"paragraph_close",tight:!1,level:e.level})),!0}var $n=[["code",ff],["fences",df,["paragraph","blockquote","list"]],["blockquote",pf,["paragraph","blockquote","list"]],["hr",mf,["paragraph","blockquote","list"]],["list",xf,["paragraph","blockquote"]],["footnote",yf,["paragraph"]],["heading",bf,["paragraph","blockquote"]],["lheading",vf],["htmlblock",Mf,["paragraph","blockquote"]],["table",Af,["paragraph"]],["deflist",Cf,["paragraph"]],["paragraph",Tf]];function Ci(){this.ruler=new ht;for(var e=0;e<$n.length;e++)this.ruler.push($n[e][0],$n[e][1],{alt:($n[e][2]||[]).slice()})}Ci.prototype.tokenize=function(e,t,r){for(var i=this.ruler.getRules(""),l=i.length,a=t,u=!1,h,p;a<r&&(e.line=a=e.skipEmptyLines(a),!(a>=r||e.tShift[a]<e.blkIndent));){for(p=0;p<l&&(h=i[p](e,a,r,!1),!h);p++);if(e.tight=!u,e.isEmpty(e.line-1)&&(u=!0),a=e.line,a<r&&e.isEmpty(a)){if(u=!0,a++,a<r&&e.parentType==="list"&&e.isEmpty(a))break;e.line=a}}};var Nf=/[\n\t]/g,Ef=/\r[\n\u0085]|[\u2424\u2028\u0085]/g,zf=/\u00a0/g;Ci.prototype.parse=function(e,t,r,i){var l,a=0,u=0;if(!e)return[];e=e.replace(zf," "),e=e.replace(Ef,`
`),e.indexOf("	")>=0&&(e=e.replace(Nf,function(h,p){var b;return e.charCodeAt(p)===10?(a=p+1,u=0,h):(b="    ".slice((p-a-u)%4),u=p-a+1,b)})),l=new ar(e,this,t,r,i),this.tokenize(l,l.line,l.lineMax)};function Df(e){switch(e){case 10:case 92:case 96:case 42:case 95:case 94:case 91:case 93:case 33:case 38:case 60:case 62:case 123:case 125:case 36:case 37:case 64:case 126:case 43:case 61:case 58:return!0;default:return!1}}function If(e,t){for(var r=e.pos;r<e.posMax&&!Df(e.src.charCodeAt(r));)r++;return r===e.pos?!1:(t||(e.pending+=e.src.slice(e.pos,r)),e.pos=r,!0)}function Bf(e,t){var r,i,l=e.pos;if(e.src.charCodeAt(l)!==10)return!1;if(r=e.pending.length-1,i=e.posMax,!t)if(r>=0&&e.pending.charCodeAt(r)===32)if(r>=1&&e.pending.charCodeAt(r-1)===32){for(var a=r-2;a>=0;a--)if(e.pending.charCodeAt(a)!==32){e.pending=e.pending.substring(0,a+1);break}e.push({type:"hardbreak",level:e.level})}else e.pending=e.pending.slice(0,-1),e.push({type:"softbreak",level:e.level});else e.push({type:"softbreak",level:e.level});for(l++;l<i&&e.src.charCodeAt(l)===32;)l++;return e.pos=l,!0}var Ti=[];for(ki=0;ki<256;ki++)Ti.push(0);var ki;"\\!\"#$%&'()*+,./:;<=>?@[]^_`{|}~-".split("").forEach(function(e){Ti[e.charCodeAt(0)]=1});function Of(e,t){var r,i=e.pos,l=e.posMax;if(e.src.charCodeAt(i)!==92)return!1;if(i++,i<l){if(r=e.src.charCodeAt(i),r<256&&Ti[r]!==0)return t||(e.pending+=e.src[i]),e.pos+=2,!0;if(r===10){for(t||e.push({type:"hardbreak",level:e.level}),i++;i<l&&e.src.charCodeAt(i)===32;)i++;return e.pos=i,!0}}return t||(e.pending+="\\"),e.pos++,!0}function Ff(e,t){var r,i,l,a,u,h=e.pos,p=e.src.charCodeAt(h);if(p!==96)return!1;for(r=h,h++,i=e.posMax;h<i&&e.src.charCodeAt(h)===96;)h++;for(l=e.src.slice(r,h),a=u=h;(a=e.src.indexOf("`",u))!==-1;){for(u=a+1;u<i&&e.src.charCodeAt(u)===96;)u++;if(u-a===l.length)return t||e.push({type:"code",content:e.src.slice(h,a).replace(/[ \n]+/g," ").trim(),block:!1,level:e.level}),e.pos=u,!0}return t||(e.pending+=l),e.pos+=l.length,!0}function Rf(e,t){var r,i,l,a=e.posMax,u=e.pos,h,p;if(e.src.charCodeAt(u)!==126||t||u+4>=a||e.src.charCodeAt(u+1)!==126||e.level>=e.options.maxNesting||(h=u>0?e.src.charCodeAt(u-1):-1,p=e.src.charCodeAt(u+2),h===126)||p===126||p===32||p===10)return!1;for(i=u+2;i<a&&e.src.charCodeAt(i)===126;)i++;if(i>u+3)return e.pos+=i-u,t||(e.pending+=e.src.slice(u,i)),!0;for(e.pos=u+2,l=1;e.pos+1<a;){if(e.src.charCodeAt(e.pos)===126&&e.src.charCodeAt(e.pos+1)===126&&(h=e.src.charCodeAt(e.pos-1),p=e.pos+2<a?e.src.charCodeAt(e.pos+2):-1,p!==126&&h!==126&&(h!==32&&h!==10?l--:p!==32&&p!==10&&l++,l<=0))){r=!0;break}e.parser.skipToken(e)}return r?(e.posMax=e.pos,e.pos=u+2,t||(e.push({type:"del_open",level:e.level++}),e.parser.tokenize(e),e.push({type:"del_close",level:--e.level})),e.pos=e.posMax+2,e.posMax=a,!0):(e.pos=u,!1)}function Lf(e,t){var r,i,l,a=e.posMax,u=e.pos,h,p;if(e.src.charCodeAt(u)!==43||t||u+4>=a||e.src.charCodeAt(u+1)!==43||e.level>=e.options.maxNesting||(h=u>0?e.src.charCodeAt(u-1):-1,p=e.src.charCodeAt(u+2),h===43)||p===43||p===32||p===10)return!1;for(i=u+2;i<a&&e.src.charCodeAt(i)===43;)i++;if(i!==u+2)return e.pos+=i-u,t||(e.pending+=e.src.slice(u,i)),!0;for(e.pos=u+2,l=1;e.pos+1<a;){if(e.src.charCodeAt(e.pos)===43&&e.src.charCodeAt(e.pos+1)===43&&(h=e.src.charCodeAt(e.pos-1),p=e.pos+2<a?e.src.charCodeAt(e.pos+2):-1,p!==43&&h!==43&&(h!==32&&h!==10?l--:p!==32&&p!==10&&l++,l<=0))){r=!0;break}e.parser.skipToken(e)}return r?(e.posMax=e.pos,e.pos=u+2,t||(e.push({type:"ins_open",level:e.level++}),e.parser.tokenize(e),e.push({type:"ins_close",level:--e.level})),e.pos=e.posMax+2,e.posMax=a,!0):(e.pos=u,!1)}function qf(e,t){var r,i,l,a=e.posMax,u=e.pos,h,p;if(e.src.charCodeAt(u)!==61||t||u+4>=a||e.src.charCodeAt(u+1)!==61||e.level>=e.options.maxNesting||(h=u>0?e.src.charCodeAt(u-1):-1,p=e.src.charCodeAt(u+2),h===61)||p===61||p===32||p===10)return!1;for(i=u+2;i<a&&e.src.charCodeAt(i)===61;)i++;if(i!==u+2)return e.pos+=i-u,t||(e.pending+=e.src.slice(u,i)),!0;for(e.pos=u+2,l=1;e.pos+1<a;){if(e.src.charCodeAt(e.pos)===61&&e.src.charCodeAt(e.pos+1)===61&&(h=e.src.charCodeAt(e.pos-1),p=e.pos+2<a?e.src.charCodeAt(e.pos+2):-1,p!==61&&h!==61&&(h!==32&&h!==10?l--:p!==32&&p!==10&&l++,l<=0))){r=!0;break}e.parser.skipToken(e)}return r?(e.posMax=e.pos,e.pos=u+2,t||(e.push({type:"mark_open",level:e.level++}),e.parser.tokenize(e),e.push({type:"mark_close",level:--e.level})),e.pos=e.posMax+2,e.posMax=a,!0):(e.pos=u,!1)}function ds(e){return e>=48&&e<=57||e>=65&&e<=90||e>=97&&e<=122}function ps(e,t){var r=t,i,l,a,u=!0,h=!0,p=e.posMax,b=e.src.charCodeAt(t);for(i=t>0?e.src.charCodeAt(t-1):-1;r<p&&e.src.charCodeAt(r)===b;)r++;return r>=p&&(u=!1),a=r-t,a>=4?u=h=!1:(l=r<p?e.src.charCodeAt(r):-1,(l===32||l===10)&&(u=!1),(i===32||i===10)&&(h=!1),b===95&&(ds(i)&&(u=!1),ds(l)&&(h=!1))),{can_open:u,can_close:h,delims:a}}function Pf(e,t){var r,i,l,a,u,h,p,b=e.posMax,y=e.pos,S=e.src.charCodeAt(y);if(S!==95&&S!==42||t)return!1;if(p=ps(e,y),r=p.delims,!p.can_open)return e.pos+=r,t||(e.pending+=e.src.slice(y,e.pos)),!0;if(e.level>=e.options.maxNesting)return!1;for(e.pos=y+r,h=[r];e.pos<b;){if(e.src.charCodeAt(e.pos)===S){if(p=ps(e,e.pos),i=p.delims,p.can_close){for(a=h.pop(),u=i;a!==u;){if(u<a){h.push(a-u);break}if(u-=a,h.length===0)break;e.pos+=a,a=h.pop()}if(h.length===0){r=a,l=!0;break}e.pos+=i;continue}p.can_open&&h.push(i),e.pos+=i;continue}e.parser.skipToken(e)}return l?(e.posMax=e.pos,e.pos=y+r,t||((r===2||r===3)&&e.push({type:"strong_open",level:e.level++}),(r===1||r===3)&&e.push({type:"em_open",level:e.level++}),e.parser.tokenize(e),(r===1||r===3)&&e.push({type:"em_close",level:--e.level}),(r===2||r===3)&&e.push({type:"strong_close",level:--e.level})),e.pos=e.posMax+r,e.posMax=b,!0):(e.pos=y,!1)}var Hf=/\\([ \\!"#$%&'()*+,.\/:;<=>?@[\]^_`{|}~-])/g;function $f(e,t){var r,i,l=e.posMax,a=e.pos;if(e.src.charCodeAt(a)!==126||t||a+2>=l||e.level>=e.options.maxNesting)return!1;for(e.pos=a+1;e.pos<l;){if(e.src.charCodeAt(e.pos)===126){r=!0;break}e.parser.skipToken(e)}return!r||a+1===e.pos||(i=e.src.slice(a+1,e.pos),i.match(/(^|[^\\])(\\\\)*\s/))?(e.pos=a,!1):(e.posMax=e.pos,e.pos=a+1,t||e.push({type:"sub",level:e.level,content:i.replace(Hf,"$1")}),e.pos=e.posMax+1,e.posMax=l,!0)}var Vf=/\\([ \\!"#$%&'()*+,.\/:;<=>?@[\]^_`{|}~-])/g;function Gf(e,t){var r,i,l=e.posMax,a=e.pos;if(e.src.charCodeAt(a)!==94||t||a+2>=l||e.level>=e.options.maxNesting)return!1;for(e.pos=a+1;e.pos<l;){if(e.src.charCodeAt(e.pos)===94){r=!0;break}e.parser.skipToken(e)}return!r||a+1===e.pos||(i=e.src.slice(a+1,e.pos),i.match(/(^|[^\\])(\\\\)*\s/))?(e.pos=a,!1):(e.posMax=e.pos,e.pos=a+1,t||e.push({type:"sup",level:e.level,content:i.replace(Vf,"$1")}),e.pos=e.posMax+1,e.posMax=l,!0)}function Yf(e,t){var r,i,l,a,u,h,p,b,y=!1,S=e.pos,C=e.posMax,T=e.pos,F=e.src.charCodeAt(T);if(F===33&&(y=!0,F=e.src.charCodeAt(++T)),F!==91||e.level>=e.options.maxNesting||(r=T+1,i=Vr(e,T),i<0))return!1;if(h=i+1,h<C&&e.src.charCodeAt(h)===40){for(h++;h<C&&(b=e.src.charCodeAt(h),!(b!==32&&b!==10));h++);if(h>=C)return!1;for(T=h,bs(e,h)?(a=e.linkContent,h=e.pos):a="",T=h;h<C&&(b=e.src.charCodeAt(h),!(b!==32&&b!==10));h++);if(h<C&&T!==h&&vs(e,h))for(u=e.linkContent,h=e.pos;h<C&&(b=e.src.charCodeAt(h),!(b!==32&&b!==10));h++);else u="";if(h>=C||e.src.charCodeAt(h)!==41)return e.pos=S,!1;h++}else{if(e.linkLevel>0)return!1;for(;h<C&&(b=e.src.charCodeAt(h),!(b!==32&&b!==10));h++);if(h<C&&e.src.charCodeAt(h)===91&&(T=h+1,h=Vr(e,h),h>=0?l=e.src.slice(T,h++):h=T-1),l||(typeof l=="undefined"&&(h=i+1),l=e.src.slice(r,i)),p=e.env.references[ws(l)],!p)return e.pos=S,!1;a=p.href,u=p.title}return t||(e.pos=r,e.posMax=i,y?e.push({type:"image",src:a,title:u,alt:e.src.substr(r,i-r),level:e.level}):(e.push({type:"link_open",href:a,title:u,level:e.level++}),e.linkLevel++,e.parser.tokenize(e),e.linkLevel--,e.push({type:"link_close",level:--e.level}))),e.pos=h,e.posMax=C,!0}function Uf(e,t){var r,i,l,a,u=e.posMax,h=e.pos;return h+2>=u||e.src.charCodeAt(h)!==94||e.src.charCodeAt(h+1)!==91||e.level>=e.options.maxNesting||(r=h+2,i=Vr(e,h+1),i<0)?!1:(t||(e.env.footnotes||(e.env.footnotes={}),e.env.footnotes.list||(e.env.footnotes.list=[]),l=e.env.footnotes.list.length,e.pos=r,e.posMax=i,e.push({type:"footnote_ref",id:l,level:e.level}),e.linkLevel++,a=e.tokens.length,e.parser.tokenize(e),e.env.footnotes.list[l]={tokens:e.tokens.splice(a)},e.linkLevel--),e.pos=i+1,e.posMax=u,!0)}function Xf(e,t){var r,i,l,a,u=e.posMax,h=e.pos;if(h+3>u||!e.env.footnotes||!e.env.footnotes.refs||e.src.charCodeAt(h)!==91||e.src.charCodeAt(h+1)!==94||e.level>=e.options.maxNesting)return!1;for(i=h+2;i<u;i++){if(e.src.charCodeAt(i)===32||e.src.charCodeAt(i)===10)return!1;if(e.src.charCodeAt(i)===93)break}return i===h+2||i>=u||(i++,r=e.src.slice(h+2,i-1),typeof e.env.footnotes.refs[":"+r]=="undefined")?!1:(t||(e.env.footnotes.list||(e.env.footnotes.list=[]),e.env.footnotes.refs[":"+r]<0?(l=e.env.footnotes.list.length,e.env.footnotes.list[l]={label:r,count:0},e.env.footnotes.refs[":"+r]=l):l=e.env.footnotes.refs[":"+r],a=e.env.footnotes.list[l].count,e.env.footnotes.list[l].count++,e.push({type:"footnote_ref",id:l,subId:a,level:e.level})),e.pos=i,e.posMax=u,!0)}var jf=["coap","doi","javascript","aaa","aaas","about","acap","cap","cid","crid","data","dav","dict","dns","file","ftp","geo","go","gopher","h323","http","https","iax","icap","im","imap","info","ipp","iris","iris.beep","iris.xpc","iris.xpcs","iris.lwz","ldap","mailto","mid","msrp","msrps","mtqp","mupdate","news","nfs","ni","nih","nntp","opaquelocktoken","pop","pres","rtsp","service","session","shttp","sieve","sip","sips","sms","snmp","soap.beep","soap.beeps","tag","tel","telnet","tftp","thismessage","tn3270","tip","tv","urn","vemmi","ws","wss","xcon","xcon-userid","xmlrpc.beep","xmlrpc.beeps","xmpp","z39.50r","z39.50s","adiumxtra","afp","afs","aim","apt","attachment","aw","beshare","bitcoin","bolo","callto","chrome","chrome-extension","com-eventbrite-attendee","content","cvs","dlna-playsingle","dlna-playcontainer","dtn","dvb","ed2k","facetime","feed","finger","fish","gg","git","gizmoproject","gtalk","hcp","icon","ipn","irc","irc6","ircs","itms","jar","jms","keyparc","lastfm","ldaps","magnet","maps","market","message","mms","ms-help","msnim","mumble","mvn","notes","oid","palm","paparazzi","platform","proxy","psyc","query","res","resource","rmi","rsync","rtmp","secondlife","sftp","sgn","skype","smb","soldat","spotify","ssh","steam","svn","teamspeak","things","udp","unreal","ut2004","ventrilo","view-source","webcal","wtai","wyciwyg","xfire","xri","ymsgr"],Wf=/^<([a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)>/,Kf=/^<([a-zA-Z.\-]{1,25}):([^<>\x00-\x20]*)>/;function Zf(e,t){var r,i,l,a,u,h=e.pos;return e.src.charCodeAt(h)!==60||(r=e.src.slice(h),r.indexOf(">")<0)?!1:(i=r.match(Kf),i?jf.indexOf(i[1].toLowerCase())<0||(a=i[0].slice(1,-1),u=Ai(a),!e.parser.validateLink(a))?!1:(t||(e.push({type:"link_open",href:u,level:e.level}),e.push({type:"text",content:a,level:e.level+1}),e.push({type:"link_close",level:e.level})),e.pos+=i[0].length,!0):(l=r.match(Wf),l?(a=l[0].slice(1,-1),u=Ai("mailto:"+a),e.parser.validateLink(u)?(t||(e.push({type:"link_open",href:u,level:e.level}),e.push({type:"text",content:a,level:e.level+1}),e.push({type:"link_close",level:e.level})),e.pos+=l[0].length,!0):!1):!1))}function Vn(e,t){return e=e.source,t=t||"",function r(i,l){return i?(l=l.source||l,e=e.replace(i,l),r):new RegExp(e,t)}}var Jf=/[a-zA-Z_:][a-zA-Z0-9:._-]*/,Qf=/[^"'=<>`\x00-\x20]+/,ed=/'[^']*'/,td=/"[^"]*"/,rd=Vn(/(?:unquoted|single_quoted|double_quoted)/)("unquoted",Qf)("single_quoted",ed)("double_quoted",td)(),nd=Vn(/(?:\s+attr_name(?:\s*=\s*attr_value)?)/)("attr_name",Jf)("attr_value",rd)(),id=Vn(/<[A-Za-z][A-Za-z0-9]*attribute*\s*\/?>/)("attribute",nd)(),od=/<\/[A-Za-z][A-Za-z0-9]*\s*>/,ld=/<!---->|<!--(?:-?[^>-])(?:-?[^-])*-->/,sd=/<[?].*?[?]>/,ad=/<![A-Z]+\s+[^>]*>/,cd=/<!\[CDATA\[[\s\S]*?\]\]>/,ud=Vn(/^(?:open_tag|close_tag|comment|processing|declaration|cdata)/)("open_tag",id)("close_tag",od)("comment",ld)("processing",sd)("declaration",ad)("cdata",cd)();function hd(e){var t=e|32;return t>=97&&t<=122}function fd(e,t){var r,i,l,a=e.pos;return!e.options.html||(l=e.posMax,e.src.charCodeAt(a)!==60||a+2>=l)||(r=e.src.charCodeAt(a+1),r!==33&&r!==63&&r!==47&&!hd(r))||(i=e.src.slice(a).match(ud),!i)?!1:(t||e.push({type:"htmltag",content:e.src.slice(a,a+i[0].length),level:e.level}),e.pos+=i[0].length,!0)}var dd=/^&#((?:x[a-f0-9]{1,8}|[0-9]{1,8}));/i,pd=/^&([a-z][a-z0-9]{1,31});/i;function md(e,t){var r,i,l,a=e.pos,u=e.posMax;if(e.src.charCodeAt(a)!==38)return!1;if(a+1<u){if(r=e.src.charCodeAt(a+1),r===35){if(l=e.src.slice(a).match(dd),l)return t||(i=l[1][0].toLowerCase()==="x"?parseInt(l[1].slice(1),16):parseInt(l[1],10),e.pending+=xs(i)?Mi(i):Mi(65533)),e.pos+=l[0].length,!0}else if(l=e.src.slice(a).match(pd),l){var h=ms(l[1]);if(l[1]!==h)return t||(e.pending+=h),e.pos+=l[0].length,!0}}return t||(e.pending+="&"),e.pos++,!0}var Si=[["text",If],["newline",Bf],["escape",Of],["backticks",Ff],["del",Rf],["ins",Lf],["mark",qf],["emphasis",Pf],["sub",$f],["sup",Gf],["links",Yf],["footnote_inline",Uf],["footnote_ref",Xf],["autolink",Zf],["htmltag",fd],["entity",md]];function Gn(){this.ruler=new ht;for(var e=0;e<Si.length;e++)this.ruler.push(Si[e][0],Si[e][1]);this.validateLink=gd}Gn.prototype.skipToken=function(e){var t=this.ruler.getRules(""),r=t.length,i=e.pos,l,a;if((a=e.cacheGet(i))>0){e.pos=a;return}for(l=0;l<r;l++)if(t[l](e,!0)){e.cacheSet(i,e.pos);return}e.pos++,e.cacheSet(i,e.pos)};Gn.prototype.tokenize=function(e){for(var t=this.ruler.getRules(""),r=t.length,i=e.posMax,l,a;e.pos<i;){for(a=0;a<r&&(l=t[a](e,!1),!l);a++);if(l){if(e.pos>=i)break;continue}e.pending+=e.src[e.pos++]}e.pending&&e.pushPending()};Gn.prototype.parse=function(e,t,r,i){var l=new sr(e,this,t,r,i);this.tokenize(l)};function gd(e){var t=["vbscript","javascript","file","data"],r=e.trim().toLowerCase();return r=or(r),!(r.indexOf(":")!==-1&&t.indexOf(r.split(":")[0])!==-1)}var xd={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkTarget:"",typographer:!1,quotes:"\u201C\u201D\u2018\u2019",highlight:null,maxNesting:20},components:{core:{rules:["block","inline","references","replacements","smartquotes","references","abbr2","footnote_tail"]},block:{rules:["blockquote","code","fences","footnote","heading","hr","htmlblock","lheading","list","paragraph","table"]},inline:{rules:["autolink","backticks","del","emphasis","entity","escape","footnote_ref","htmltag","links","newline","text"]}}},yd={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkTarget:"",typographer:!1,quotes:"\u201C\u201D\u2018\u2019",highlight:null,maxNesting:20},components:{core:{},block:{},inline:{}}},bd={options:{html:!0,xhtmlOut:!0,breaks:!1,langPrefix:"language-",linkTarget:"",typographer:!1,quotes:"\u201C\u201D\u2018\u2019",highlight:null,maxNesting:20},components:{core:{rules:["block","inline","references","abbr2"]},block:{rules:["blockquote","code","fences","heading","hr","htmlblock","lheading","list","paragraph"]},inline:{rules:["autolink","backticks","emphasis","entity","escape","htmltag","links","newline","text"]}}},vd={default:xd,full:yd,commonmark:bd};function Ms(e,t,r){this.src=t,this.env=r,this.options=e.options,this.tokens=[],this.inlineMode=!1,this.inline=e.inline,this.block=e.block,this.renderer=e.renderer,this.typographer=e.typographer}function qt(e,t){typeof e!="string"&&(t=e,e="default"),t&&t.linkify!=null&&console.warn(`linkify option is removed. Use linkify plugin instead:

import Remarkable from 'remarkable';
import linkify from 'remarkable/linkify';
new Remarkable().use(linkify)
`),this.inline=new Gn,this.block=new Ci,this.core=new ks,this.renderer=new _i,this.ruler=new ht,this.options={},this.configure(vd[e]),this.set(t||{})}qt.prototype.set=function(e){gs(this.options,e)};qt.prototype.configure=function(e){var t=this;if(!e)throw new Error("Wrong `remarkable` preset, check name/content");e.options&&t.set(e.options),e.components&&Object.keys(e.components).forEach(function(r){e.components[r].rules&&t[r].ruler.enable(e.components[r].rules,!0)})};qt.prototype.use=function(e,t){return e(this,t),this};qt.prototype.parse=function(e,t){var r=new Ms(this,e,t);return this.core.process(r),r.tokens};qt.prototype.render=function(e,t){return t=t||{},this.renderer.render(this.parse(e,t),this.options,t)};qt.prototype.parseInline=function(e,t){var r=new Ms(this,e,t);return r.inlineMode=!0,this.core.process(r),r.tokens};qt.prototype.renderInline=function(e,t){return t=t||{},this.renderer.render(this.parseInline(e,t),this.options,t)};var wd="npm2url/dist/index.cjs",kd={jsdelivr:e=>`https://cdn.jsdelivr.net/npm/${e}`,unpkg:e=>`https://unpkg.com/${e}`};async function Sd(e,t){let r=await fetch(e,{signal:t});if(!r.ok)throw r;await r.text()}var Gr=class{constructor(){this.providers={...kd},this.provider="jsdelivr"}async getFastestProvider(t=5e3,r=wd){let i=new AbortController,l=0;try{return await new Promise((a,u)=>{Promise.all(Object.entries(this.providers).map(async([h,p])=>{try{await Sd(p(r),i.signal),a(h)}catch(b){}})).then(()=>u(new Error("All providers failed"))),l=setTimeout(u,t,new Error("Timed out"))})}finally{i.abort(),clearTimeout(l)}}async findFastestProvider(t,r){return this.provider=await this.getFastestProvider(t,r),this.provider}setProvider(t,r){r?this.providers[t]=r:delete this.providers[t]}getFullUrl(t,r=this.provider){if(t.includes("://"))return t;let i=this.providers[r];if(!i)throw new Error(`Provider ${r} not found`);return i(t)}},k6=new Gr,wt=class{constructor(){this.listeners=[]}tap(t){return this.listeners.push(t),()=>this.revoke(t)}revoke(t){let r=this.listeners.indexOf(t);r>=0&&this.listeners.splice(r,1)}revokeAll(){this.listeners.splice(0)}call(...t){for(let r of this.listeners)r(...t)}},Md={"&":"&amp;","<":"&lt;",'"':"&quot;"};function As(e){return e.replace(/[&<"]/g,t=>Md[t])}function Ad(e){return e.replace(/<(\/script>)/g,"\\x3c$2")}function _s(e,t){let r=t?Object.entries(t).map(([i,l])=>{if(!(l==null||l===!1))return i=` ${As(i)}`,l===!0?i:`${i}="${As(l)}"`}).filter(Boolean).join(""):"";return`<${e}${r}>`}function _d(e){return`</${e}>`}function Yn(e,t,r){return t==null?_s(e,r):_s(e,r)+(t||"")+_d(e)}function Cd(e,t){let r=t.map(i=>typeof i=="function"?i.toString():JSON.stringify(i!=null?i:null)).join(",");return`(${e.toString()})(${r})`}function Es(e,t){return e.map(r=>{if(r.type==="script"){let{textContent:i,...l}=r.data;return Yn("script",i||"",l)}if(r.type==="iife"){let{fn:i,getParams:l}=r.data;return Yn("script",Ad(Cd(i,(l==null?void 0:l(t))||[])))}return""})}function zs(e){return e.map(t=>t.type==="stylesheet"?Yn("link",null,{rel:"stylesheet",...t.data}):Yn("style",t.data))}var Td=Math.random().toString(36).slice(2,8),Cs=0;function Ds(){return Cs+=1,`mm-${Td}-${Cs}`}function cr(){}function Un(e,t){let r=(i,l)=>t(i,()=>{var a;(a=i.children)==null||a.forEach(u=>{r(u,i)})},l);r(e)}function Is(e,...t){let r=(e||"").split(" ").filter(Boolean);return t.forEach(i=>{i&&r.indexOf(i)<0&&r.push(i)}),r.join(" ")}function ur(e){if(typeof e=="string"){let r=e;e=i=>i.tagName===r}let t=e;return function(){let i=Array.from(this.childNodes);return t&&(i=i.filter(l=>t(l))),i}}function Bs(e,t){return(...r)=>t(e,...r)}function Nd(){let e={};return e.promise=new Promise((t,r)=>{e.resolve=t,e.reject=r}),e}function Ed(e){let t={};return function(...i){let l=`${i[0]}`,a=t[l];return a||(a={value:e(...i)},t[l]=a),a.value}}var Os=1,Fs=2,zd="http://www.w3.org/2000/svg",Ni="http://www.w3.org/1999/xlink",Dd={show:Ni,actuate:Ni,href:Ni},Id=e=>typeof e=="string"||typeof e=="number",Bd=e=>(e==null?void 0:e.vtype)===Os,Od=e=>(e==null?void 0:e.vtype)===Fs;function Fd(e,t,...r){return t=Object.assign({},t,{children:r.length===1?r[0]:r}),Rd(e,t)}function Rd(e,t){let r;if(typeof e=="string")r=Os;else if(typeof e=="function")r=Fs;else throw new Error("Invalid VNode type");return{vtype:r,type:e,props:t}}function Ld(e){return e.children}var qd={isSvg:!1};function Ts(e,t){Array.isArray(t)||(t=[t]),t=t.filter(Boolean),t.length&&e.append(...t)}function Pd(e,t,r){for(let i in t)if(!(i==="key"||i==="children"||i==="ref"))if(i==="dangerouslySetInnerHTML")e.innerHTML=t[i].__html;else if(i==="innerHTML"||i==="textContent"||i==="innerText"||i==="value"&&["textarea","select"].includes(e.tagName)){let l=t[i];l!=null&&(e[i]=l)}else i.startsWith("on")?e[i.toLowerCase()]=t[i]:$d(e,i,t[i],r.isSvg)}var Hd={className:"class",labelFor:"for"};function $d(e,t,r,i){if(t=Hd[t]||t,r===!0)e.setAttribute(t,"");else if(r===!1)e.removeAttribute(t);else{let l=i?Dd[t]:void 0;l!==void 0?e.setAttributeNS(l,t,r):e.setAttribute(t,r)}}function Vd(e){return e.reduce((t,r)=>t.concat(r),[])}function Ei(e,t){return Array.isArray(e)?Vd(e.map(r=>Ei(r,t))):zi(e,t)}function zi(e,t=qd){if(e==null||typeof e=="boolean")return null;if(e instanceof Node)return e;if(Od(e)){let{type:r,props:i}=e;if(r===Ld){let a=document.createDocumentFragment();if(i.children){let u=Ei(i.children,t);Ts(a,u)}return a}let l=r(i);return zi(l,t)}if(Id(e))return document.createTextNode(`${e}`);if(Bd(e)){let r,{type:i,props:l}=e;if(!t.isSvg&&i==="svg"&&(t=Object.assign({},t,{isSvg:!0})),t.isSvg?r=document.createElementNS(zd,i):r=document.createElement(i),Pd(r,l,t),l.children){let u=t;t.isSvg&&i==="foreignObject"&&(u=Object.assign({},u,{isSvg:!1}));let h=Ei(l.children,u);h!=null&&Ts(r,h)}let{ref:a}=l;return typeof a=="function"&&a(r),r}throw new Error("mount: Invalid Vnode!")}function Gd(e){return zi(e)}function Rs(...e){return Gd(Fd(...e))}var Yd=Ed(e=>{document.head.append(Rs("link",{rel:"preload",as:"script",href:e}))}),Ns={};async function Ud(e,t){var r;let i=e.type==="script"&&((r=e.data)==null?void 0:r.src)||"";if(e.loaded||(e.loaded=Ns[i]),!e.loaded){let l=Nd();if(e.loaded=l.promise,e.type==="script"&&(document.head.append(Rs("script",{...e.data,onLoad:()=>l.resolve(),onError:l.reject})),i?Ns[i]=e.loaded:l.resolve()),e.type==="iife"){let{fn:a,getParams:u}=e.data;a(...(u==null?void 0:u(t))||[]),l.resolve()}}await e.loaded}async function Xn(e,t){e.forEach(r=>{var i;r.type==="script"&&((i=r.data)!=null&&i.src)&&Yd(r.data.src)}),t={getMarkmap:()=>window.markmap,...t};for(let r of e)await Ud(r,t)}function Yr(e){return{type:"script",data:{src:e}}}function Di(e){return{type:"stylesheet",data:{href:e}}}var Ya=R1(Ps(),1);function ra(e){return typeof e=="undefined"||e===null}function jd(e){return typeof e=="object"&&e!==null}function Wd(e){return Array.isArray(e)?e:ra(e)?[]:[e]}function Kd(e,t){var r,i,l,a;if(t)for(a=Object.keys(t),r=0,i=a.length;r<i;r+=1)l=a[r],e[l]=t[l];return e}function Zd(e,t){var r="",i;for(i=0;i<t;i+=1)r+=e;return r}function Jd(e){return e===0&&Number.NEGATIVE_INFINITY===1/e}var Qd=ra,ep=jd,tp=Wd,rp=Zd,np=Jd,ip=Kd,Pe={isNothing:Qd,isObject:ep,toArray:tp,repeat:rp,isNegativeZero:np,extend:ip};function na(e,t){var r="",i=e.reason||"(unknown reason)";return e.mark?(e.mark.name&&(r+='in "'+e.mark.name+'" '),r+="("+(e.mark.line+1)+":"+(e.mark.column+1)+")",!t&&e.mark.snippet&&(r+=`

`+e.mark.snippet),i+" "+r):i}function jr(e,t){Error.call(this),this.name="YAMLException",this.reason=e,this.mark=t,this.message=na(this,!1),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack||""}jr.prototype=Object.create(Error.prototype);jr.prototype.constructor=jr;jr.prototype.toString=function(t){return this.name+": "+na(this,t)};var Ke=jr;function Bi(e,t,r,i,l){var a="",u="",h=Math.floor(l/2)-1;return i-t>h&&(a=" ... ",t=i-h+a.length),r-i>h&&(u=" ...",r=i+h-u.length),{str:a+e.slice(t,r).replace(/\t/g,"\u2192")+u,pos:i-t+a.length}}function Oi(e,t){return Pe.repeat(" ",t-e.length)+e}function op(e,t){if(t=Object.create(t||null),!e.buffer)return null;t.maxLength||(t.maxLength=79),typeof t.indent!="number"&&(t.indent=1),typeof t.linesBefore!="number"&&(t.linesBefore=3),typeof t.linesAfter!="number"&&(t.linesAfter=2);for(var r=/\r?\n|\r|\0/g,i=[0],l=[],a,u=-1;a=r.exec(e.buffer);)l.push(a.index),i.push(a.index+a[0].length),e.position<=a.index&&u<0&&(u=i.length-2);u<0&&(u=i.length-1);var h="",p,b,y=Math.min(e.line+t.linesAfter,l.length).toString().length,S=t.maxLength-(t.indent+y+3);for(p=1;p<=t.linesBefore&&!(u-p<0);p++)b=Bi(e.buffer,i[u-p],l[u-p],e.position-(i[u]-i[u-p]),S),h=Pe.repeat(" ",t.indent)+Oi((e.line-p+1).toString(),y)+" | "+b.str+`
`+h;for(b=Bi(e.buffer,i[u],l[u],e.position,S),h+=Pe.repeat(" ",t.indent)+Oi((e.line+1).toString(),y)+" | "+b.str+`
`,h+=Pe.repeat("-",t.indent+y+3+b.pos)+`^
`,p=1;p<=t.linesAfter&&!(u+p>=l.length);p++)b=Bi(e.buffer,i[u+p],l[u+p],e.position-(i[u]-i[u+p]),S),h+=Pe.repeat(" ",t.indent)+Oi((e.line+p+1).toString(),y)+" | "+b.str+`
`;return h.replace(/\n$/,"")}var lp=op,sp=["kind","multi","resolve","construct","instanceOf","predicate","represent","representName","defaultStyle","styleAliases"],ap=["scalar","sequence","mapping"];function cp(e){var t={};return e!==null&&Object.keys(e).forEach(function(r){e[r].forEach(function(i){t[String(i)]=r})}),t}function up(e,t){if(t=t||{},Object.keys(t).forEach(function(r){if(sp.indexOf(r)===-1)throw new Ke('Unknown option "'+r+'" is met in definition of "'+e+'" YAML type.')}),this.options=t,this.tag=e,this.kind=t.kind||null,this.resolve=t.resolve||function(){return!0},this.construct=t.construct||function(r){return r},this.instanceOf=t.instanceOf||null,this.predicate=t.predicate||null,this.represent=t.represent||null,this.representName=t.representName||null,this.defaultStyle=t.defaultStyle||null,this.multi=t.multi||!1,this.styleAliases=cp(t.styleAliases||null),ap.indexOf(this.kind)===-1)throw new Ke('Unknown kind "'+this.kind+'" is specified for "'+e+'" YAML type.')}var Ge=up;function Hs(e,t){var r=[];return e[t].forEach(function(i){var l=r.length;r.forEach(function(a,u){a.tag===i.tag&&a.kind===i.kind&&a.multi===i.multi&&(l=u)}),r[l]=i}),r}function hp(){var e={scalar:{},sequence:{},mapping:{},fallback:{},multi:{scalar:[],sequence:[],mapping:[],fallback:[]}},t,r;function i(l){l.multi?(e.multi[l.kind].push(l),e.multi.fallback.push(l)):e[l.kind][l.tag]=e.fallback[l.tag]=l}for(t=0,r=arguments.length;t<r;t+=1)arguments[t].forEach(i);return e}function Ri(e){return this.extend(e)}Ri.prototype.extend=function(t){var r=[],i=[];if(t instanceof Ge)i.push(t);else if(Array.isArray(t))i=i.concat(t);else if(t&&(Array.isArray(t.implicit)||Array.isArray(t.explicit)))t.implicit&&(r=r.concat(t.implicit)),t.explicit&&(i=i.concat(t.explicit));else throw new Ke("Schema.extend argument should be a Type, [ Type ], or a schema definition ({ implicit: [...], explicit: [...] })");r.forEach(function(a){if(!(a instanceof Ge))throw new Ke("Specified list of YAML types (or a single Type object) contains a non-Type object.");if(a.loadKind&&a.loadKind!=="scalar")throw new Ke("There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.");if(a.multi)throw new Ke("There is a multi type in the implicit list of a schema. Multi tags can only be listed as explicit.")}),i.forEach(function(a){if(!(a instanceof Ge))throw new Ke("Specified list of YAML types (or a single Type object) contains a non-Type object.")});var l=Object.create(Ri.prototype);return l.implicit=(this.implicit||[]).concat(r),l.explicit=(this.explicit||[]).concat(i),l.compiledImplicit=Hs(l,"implicit"),l.compiledExplicit=Hs(l,"explicit"),l.compiledTypeMap=hp(l.compiledImplicit,l.compiledExplicit),l};var ia=Ri,oa=new Ge("tag:yaml.org,2002:str",{kind:"scalar",construct:function(e){return e!==null?e:""}}),la=new Ge("tag:yaml.org,2002:seq",{kind:"sequence",construct:function(e){return e!==null?e:[]}}),sa=new Ge("tag:yaml.org,2002:map",{kind:"mapping",construct:function(e){return e!==null?e:{}}}),aa=new ia({explicit:[oa,la,sa]});function fp(e){if(e===null)return!0;var t=e.length;return t===1&&e==="~"||t===4&&(e==="null"||e==="Null"||e==="NULL")}function dp(){return null}function pp(e){return e===null}var ca=new Ge("tag:yaml.org,2002:null",{kind:"scalar",resolve:fp,construct:dp,predicate:pp,represent:{canonical:function(){return"~"},lowercase:function(){return"null"},uppercase:function(){return"NULL"},camelcase:function(){return"Null"},empty:function(){return""}},defaultStyle:"lowercase"});function mp(e){if(e===null)return!1;var t=e.length;return t===4&&(e==="true"||e==="True"||e==="TRUE")||t===5&&(e==="false"||e==="False"||e==="FALSE")}function gp(e){return e==="true"||e==="True"||e==="TRUE"}function xp(e){return Object.prototype.toString.call(e)==="[object Boolean]"}var ua=new Ge("tag:yaml.org,2002:bool",{kind:"scalar",resolve:mp,construct:gp,predicate:xp,represent:{lowercase:function(e){return e?"true":"false"},uppercase:function(e){return e?"TRUE":"FALSE"},camelcase:function(e){return e?"True":"False"}},defaultStyle:"lowercase"});function yp(e){return 48<=e&&e<=57||65<=e&&e<=70||97<=e&&e<=102}function bp(e){return 48<=e&&e<=55}function vp(e){return 48<=e&&e<=57}function wp(e){if(e===null)return!1;var t=e.length,r=0,i=!1,l;if(!t)return!1;if(l=e[r],(l==="-"||l==="+")&&(l=e[++r]),l==="0"){if(r+1===t)return!0;if(l=e[++r],l==="b"){for(r++;r<t;r++)if(l=e[r],l!=="_"){if(l!=="0"&&l!=="1")return!1;i=!0}return i&&l!=="_"}if(l==="x"){for(r++;r<t;r++)if(l=e[r],l!=="_"){if(!yp(e.charCodeAt(r)))return!1;i=!0}return i&&l!=="_"}if(l==="o"){for(r++;r<t;r++)if(l=e[r],l!=="_"){if(!bp(e.charCodeAt(r)))return!1;i=!0}return i&&l!=="_"}}if(l==="_")return!1;for(;r<t;r++)if(l=e[r],l!=="_"){if(!vp(e.charCodeAt(r)))return!1;i=!0}return!(!i||l==="_")}function kp(e){var t=e,r=1,i;if(t.indexOf("_")!==-1&&(t=t.replace(/_/g,"")),i=t[0],(i==="-"||i==="+")&&(i==="-"&&(r=-1),t=t.slice(1),i=t[0]),t==="0")return 0;if(i==="0"){if(t[1]==="b")return r*parseInt(t.slice(2),2);if(t[1]==="x")return r*parseInt(t.slice(2),16);if(t[1]==="o")return r*parseInt(t.slice(2),8)}return r*parseInt(t,10)}function Sp(e){return Object.prototype.toString.call(e)==="[object Number]"&&e%1===0&&!Pe.isNegativeZero(e)}var ha=new Ge("tag:yaml.org,2002:int",{kind:"scalar",resolve:wp,construct:kp,predicate:Sp,represent:{binary:function(e){return e>=0?"0b"+e.toString(2):"-0b"+e.toString(2).slice(1)},octal:function(e){return e>=0?"0o"+e.toString(8):"-0o"+e.toString(8).slice(1)},decimal:function(e){return e.toString(10)},hexadecimal:function(e){return e>=0?"0x"+e.toString(16).toUpperCase():"-0x"+e.toString(16).toUpperCase().slice(1)}},defaultStyle:"decimal",styleAliases:{binary:[2,"bin"],octal:[8,"oct"],decimal:[10,"dec"],hexadecimal:[16,"hex"]}}),Mp=new RegExp("^(?:[-+]?(?:[0-9][0-9_]*)(?:\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?|\\.[0-9_]+(?:[eE][-+]?[0-9]+)?|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN))$");function Ap(e){return!(e===null||!Mp.test(e)||e[e.length-1]==="_")}function _p(e){var t,r;return t=e.replace(/_/g,"").toLowerCase(),r=t[0]==="-"?-1:1,"+-".indexOf(t[0])>=0&&(t=t.slice(1)),t===".inf"?r===1?Number.POSITIVE_INFINITY:Number.NEGATIVE_INFINITY:t===".nan"?NaN:r*parseFloat(t,10)}var Cp=/^[-+]?[0-9]+e/;function Tp(e,t){var r;if(isNaN(e))switch(t){case"lowercase":return".nan";case"uppercase":return".NAN";case"camelcase":return".NaN"}else if(Number.POSITIVE_INFINITY===e)switch(t){case"lowercase":return".inf";case"uppercase":return".INF";case"camelcase":return".Inf"}else if(Number.NEGATIVE_INFINITY===e)switch(t){case"lowercase":return"-.inf";case"uppercase":return"-.INF";case"camelcase":return"-.Inf"}else if(Pe.isNegativeZero(e))return"-0.0";return r=e.toString(10),Cp.test(r)?r.replace("e",".e"):r}function Np(e){return Object.prototype.toString.call(e)==="[object Number]"&&(e%1!==0||Pe.isNegativeZero(e))}var fa=new Ge("tag:yaml.org,2002:float",{kind:"scalar",resolve:Ap,construct:_p,predicate:Np,represent:Tp,defaultStyle:"lowercase"}),da=aa.extend({implicit:[ca,ua,ha,fa]}),pa=da,ma=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9])-([0-9][0-9])$"),ga=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9]?)-([0-9][0-9]?)(?:[Tt]|[ \\t]+)([0-9][0-9]?):([0-9][0-9]):([0-9][0-9])(?:\\.([0-9]*))?(?:[ \\t]*(Z|([-+])([0-9][0-9]?)(?::([0-9][0-9]))?))?$");function Ep(e){return e===null?!1:ma.exec(e)!==null||ga.exec(e)!==null}function zp(e){var t,r,i,l,a,u,h,p=0,b=null,y,S,C;if(t=ma.exec(e),t===null&&(t=ga.exec(e)),t===null)throw new Error("Date resolve error");if(r=+t[1],i=+t[2]-1,l=+t[3],!t[4])return new Date(Date.UTC(r,i,l));if(a=+t[4],u=+t[5],h=+t[6],t[7]){for(p=t[7].slice(0,3);p.length<3;)p+="0";p=+p}return t[9]&&(y=+t[10],S=+(t[11]||0),b=(y*60+S)*6e4,t[9]==="-"&&(b=-b)),C=new Date(Date.UTC(r,i,l,a,u,h,p)),b&&C.setTime(C.getTime()-b),C}function Dp(e){return e.toISOString()}var xa=new Ge("tag:yaml.org,2002:timestamp",{kind:"scalar",resolve:Ep,construct:zp,instanceOf:Date,represent:Dp});function Ip(e){return e==="<<"||e===null}var ya=new Ge("tag:yaml.org,2002:merge",{kind:"scalar",resolve:Ip}),$i=`ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=
\r`;function Bp(e){if(e===null)return!1;var t,r,i=0,l=e.length,a=$i;for(r=0;r<l;r++)if(t=a.indexOf(e.charAt(r)),!(t>64)){if(t<0)return!1;i+=6}return i%8===0}function Op(e){var t,r,i=e.replace(/[\r\n=]/g,""),l=i.length,a=$i,u=0,h=[];for(t=0;t<l;t++)t%4===0&&t&&(h.push(u>>16&255),h.push(u>>8&255),h.push(u&255)),u=u<<6|a.indexOf(i.charAt(t));return r=l%4*6,r===0?(h.push(u>>16&255),h.push(u>>8&255),h.push(u&255)):r===18?(h.push(u>>10&255),h.push(u>>2&255)):r===12&&h.push(u>>4&255),new Uint8Array(h)}function Fp(e){var t="",r=0,i,l,a=e.length,u=$i;for(i=0;i<a;i++)i%3===0&&i&&(t+=u[r>>18&63],t+=u[r>>12&63],t+=u[r>>6&63],t+=u[r&63]),r=(r<<8)+e[i];return l=a%3,l===0?(t+=u[r>>18&63],t+=u[r>>12&63],t+=u[r>>6&63],t+=u[r&63]):l===2?(t+=u[r>>10&63],t+=u[r>>4&63],t+=u[r<<2&63],t+=u[64]):l===1&&(t+=u[r>>2&63],t+=u[r<<4&63],t+=u[64],t+=u[64]),t}function Rp(e){return Object.prototype.toString.call(e)==="[object Uint8Array]"}var ba=new Ge("tag:yaml.org,2002:binary",{kind:"scalar",resolve:Bp,construct:Op,predicate:Rp,represent:Fp}),Lp=Object.prototype.hasOwnProperty,qp=Object.prototype.toString;function Pp(e){if(e===null)return!0;var t=[],r,i,l,a,u,h=e;for(r=0,i=h.length;r<i;r+=1){if(l=h[r],u=!1,qp.call(l)!=="[object Object]")return!1;for(a in l)if(Lp.call(l,a))if(!u)u=!0;else return!1;if(!u)return!1;if(t.indexOf(a)===-1)t.push(a);else return!1}return!0}function Hp(e){return e!==null?e:[]}var va=new Ge("tag:yaml.org,2002:omap",{kind:"sequence",resolve:Pp,construct:Hp}),$p=Object.prototype.toString;function Vp(e){if(e===null)return!0;var t,r,i,l,a,u=e;for(a=new Array(u.length),t=0,r=u.length;t<r;t+=1){if(i=u[t],$p.call(i)!=="[object Object]"||(l=Object.keys(i),l.length!==1))return!1;a[t]=[l[0],i[l[0]]]}return!0}function Gp(e){if(e===null)return[];var t,r,i,l,a,u=e;for(a=new Array(u.length),t=0,r=u.length;t<r;t+=1)i=u[t],l=Object.keys(i),a[t]=[l[0],i[l[0]]];return a}var wa=new Ge("tag:yaml.org,2002:pairs",{kind:"sequence",resolve:Vp,construct:Gp}),Yp=Object.prototype.hasOwnProperty;function Up(e){if(e===null)return!0;var t,r=e;for(t in r)if(Yp.call(r,t)&&r[t]!==null)return!1;return!0}function Xp(e){return e!==null?e:{}}var ka=new Ge("tag:yaml.org,2002:set",{kind:"mapping",resolve:Up,construct:Xp}),Vi=pa.extend({implicit:[xa,ya],explicit:[ba,va,wa,ka]}),Jt=Object.prototype.hasOwnProperty,jn=1,Sa=2,Ma=3,Wn=4,Fi=1,jp=2,$s=3,Wp=/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,Kp=/[\x85\u2028\u2029]/,Zp=/[,\[\]\{\}]/,Aa=/^(?:!|!!|![a-z\-]+!)$/i,_a=/^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i;function Vs(e){return Object.prototype.toString.call(e)}function Et(e){return e===10||e===13}function fr(e){return e===9||e===32}function rt(e){return e===9||e===32||e===10||e===13}function _r(e){return e===44||e===91||e===93||e===123||e===125}function Jp(e){var t;return 48<=e&&e<=57?e-48:(t=e|32,97<=t&&t<=102?t-97+10:-1)}function Qp(e){return e===120?2:e===117?4:e===85?8:0}function em(e){return 48<=e&&e<=57?e-48:-1}function Gs(e){return e===48?"\0":e===97?"\x07":e===98?"\b":e===116||e===9?"	":e===110?`
`:e===118?"\v":e===102?"\f":e===114?"\r":e===101?"\x1B":e===32?" ":e===34?'"':e===47?"/":e===92?"\\":e===78?"\x85":e===95?"\xA0":e===76?"\u2028":e===80?"\u2029":""}function tm(e){return e<=65535?String.fromCharCode(e):String.fromCharCode((e-65536>>10)+55296,(e-65536&1023)+56320)}var Ca=new Array(256),Ta=new Array(256);for(hr=0;hr<256;hr++)Ca[hr]=Gs(hr)?1:0,Ta[hr]=Gs(hr);var hr;function rm(e,t){this.input=e,this.filename=t.filename||null,this.schema=t.schema||Vi,this.onWarning=t.onWarning||null,this.legacy=t.legacy||!1,this.json=t.json||!1,this.listener=t.listener||null,this.implicitTypes=this.schema.compiledImplicit,this.typeMap=this.schema.compiledTypeMap,this.length=e.length,this.position=0,this.line=0,this.lineStart=0,this.lineIndent=0,this.firstTabInLine=-1,this.documents=[]}function Na(e,t){var r={name:e.filename,buffer:e.input.slice(0,-1),position:e.position,line:e.line,column:e.position-e.lineStart};return r.snippet=lp(r),new Ke(t,r)}function te(e,t){throw Na(e,t)}function Kn(e,t){e.onWarning&&e.onWarning.call(null,Na(e,t))}var Ys={YAML:function(t,r,i){var l,a,u;t.version!==null&&te(t,"duplication of %YAML directive"),i.length!==1&&te(t,"YAML directive accepts exactly one argument"),l=/^([0-9]+)\.([0-9]+)$/.exec(i[0]),l===null&&te(t,"ill-formed argument of the YAML directive"),a=parseInt(l[1],10),u=parseInt(l[2],10),a!==1&&te(t,"unacceptable YAML version of the document"),t.version=i[0],t.checkLineBreaks=u<2,u!==1&&u!==2&&Kn(t,"unsupported YAML version of the document")},TAG:function(t,r,i){var l,a;i.length!==2&&te(t,"TAG directive accepts exactly two arguments"),l=i[0],a=i[1],Aa.test(l)||te(t,"ill-formed tag handle (first argument) of the TAG directive"),Jt.call(t.tagMap,l)&&te(t,'there is a previously declared suffix for "'+l+'" tag handle'),_a.test(a)||te(t,"ill-formed tag prefix (second argument) of the TAG directive");try{a=decodeURIComponent(a)}catch(u){te(t,"tag prefix is malformed: "+a)}t.tagMap[l]=a}};function Zt(e,t,r,i){var l,a,u,h;if(t<r){if(h=e.input.slice(t,r),i)for(l=0,a=h.length;l<a;l+=1)u=h.charCodeAt(l),u===9||32<=u&&u<=1114111||te(e,"expected valid JSON character");else Wp.test(h)&&te(e,"the stream contains non-printable characters");e.result+=h}}function Us(e,t,r,i){var l,a,u,h;for(Pe.isObject(r)||te(e,"cannot merge mappings; the provided source object is unacceptable"),l=Object.keys(r),u=0,h=l.length;u<h;u+=1)a=l[u],Jt.call(t,a)||(t[a]=r[a],i[a]=!0)}function Cr(e,t,r,i,l,a,u,h,p){var b,y;if(Array.isArray(l))for(l=Array.prototype.slice.call(l),b=0,y=l.length;b<y;b+=1)Array.isArray(l[b])&&te(e,"nested arrays are not supported inside keys"),typeof l=="object"&&Vs(l[b])==="[object Object]"&&(l[b]="[object Object]");if(typeof l=="object"&&Vs(l)==="[object Object]"&&(l="[object Object]"),l=String(l),t===null&&(t={}),i==="tag:yaml.org,2002:merge")if(Array.isArray(a))for(b=0,y=a.length;b<y;b+=1)Us(e,t,a[b],r);else Us(e,t,a,r);else!e.json&&!Jt.call(r,l)&&Jt.call(t,l)&&(e.line=u||e.line,e.lineStart=h||e.lineStart,e.position=p||e.position,te(e,"duplicated mapping key")),l==="__proto__"?Object.defineProperty(t,l,{configurable:!0,enumerable:!0,writable:!0,value:a}):t[l]=a,delete r[l];return t}function Gi(e){var t;t=e.input.charCodeAt(e.position),t===10?e.position++:t===13?(e.position++,e.input.charCodeAt(e.position)===10&&e.position++):te(e,"a line break is expected"),e.line+=1,e.lineStart=e.position,e.firstTabInLine=-1}function Re(e,t,r){for(var i=0,l=e.input.charCodeAt(e.position);l!==0;){for(;fr(l);)l===9&&e.firstTabInLine===-1&&(e.firstTabInLine=e.position),l=e.input.charCodeAt(++e.position);if(t&&l===35)do l=e.input.charCodeAt(++e.position);while(l!==10&&l!==13&&l!==0);if(Et(l))for(Gi(e),l=e.input.charCodeAt(e.position),i++,e.lineIndent=0;l===32;)e.lineIndent++,l=e.input.charCodeAt(++e.position);else break}return r!==-1&&i!==0&&e.lineIndent<r&&Kn(e,"deficient indentation"),i}function Qn(e){var t=e.position,r;return r=e.input.charCodeAt(t),!!((r===45||r===46)&&r===e.input.charCodeAt(t+1)&&r===e.input.charCodeAt(t+2)&&(t+=3,r=e.input.charCodeAt(t),r===0||rt(r)))}function Yi(e,t){t===1?e.result+=" ":t>1&&(e.result+=Pe.repeat(`
`,t-1))}function nm(e,t,r){var i,l,a,u,h,p,b,y,S=e.kind,C=e.result,T;if(T=e.input.charCodeAt(e.position),rt(T)||_r(T)||T===35||T===38||T===42||T===33||T===124||T===62||T===39||T===34||T===37||T===64||T===96||(T===63||T===45)&&(l=e.input.charCodeAt(e.position+1),rt(l)||r&&_r(l)))return!1;for(e.kind="scalar",e.result="",a=u=e.position,h=!1;T!==0;){if(T===58){if(l=e.input.charCodeAt(e.position+1),rt(l)||r&&_r(l))break}else if(T===35){if(i=e.input.charCodeAt(e.position-1),rt(i))break}else{if(e.position===e.lineStart&&Qn(e)||r&&_r(T))break;if(Et(T))if(p=e.line,b=e.lineStart,y=e.lineIndent,Re(e,!1,-1),e.lineIndent>=t){h=!0,T=e.input.charCodeAt(e.position);continue}else{e.position=u,e.line=p,e.lineStart=b,e.lineIndent=y;break}}h&&(Zt(e,a,u,!1),Yi(e,e.line-p),a=u=e.position,h=!1),fr(T)||(u=e.position+1),T=e.input.charCodeAt(++e.position)}return Zt(e,a,u,!1),e.result?!0:(e.kind=S,e.result=C,!1)}function im(e,t){var r,i,l;if(r=e.input.charCodeAt(e.position),r!==39)return!1;for(e.kind="scalar",e.result="",e.position++,i=l=e.position;(r=e.input.charCodeAt(e.position))!==0;)if(r===39)if(Zt(e,i,e.position,!0),r=e.input.charCodeAt(++e.position),r===39)i=e.position,e.position++,l=e.position;else return!0;else Et(r)?(Zt(e,i,l,!0),Yi(e,Re(e,!1,t)),i=l=e.position):e.position===e.lineStart&&Qn(e)?te(e,"unexpected end of the document within a single quoted scalar"):(e.position++,l=e.position);te(e,"unexpected end of the stream within a single quoted scalar")}function om(e,t){var r,i,l,a,u,h;if(h=e.input.charCodeAt(e.position),h!==34)return!1;for(e.kind="scalar",e.result="",e.position++,r=i=e.position;(h=e.input.charCodeAt(e.position))!==0;){if(h===34)return Zt(e,r,e.position,!0),e.position++,!0;if(h===92){if(Zt(e,r,e.position,!0),h=e.input.charCodeAt(++e.position),Et(h))Re(e,!1,t);else if(h<256&&Ca[h])e.result+=Ta[h],e.position++;else if((u=Qp(h))>0){for(l=u,a=0;l>0;l--)h=e.input.charCodeAt(++e.position),(u=Jp(h))>=0?a=(a<<4)+u:te(e,"expected hexadecimal character");e.result+=tm(a),e.position++}else te(e,"unknown escape sequence");r=i=e.position}else Et(h)?(Zt(e,r,i,!0),Yi(e,Re(e,!1,t)),r=i=e.position):e.position===e.lineStart&&Qn(e)?te(e,"unexpected end of the document within a double quoted scalar"):(e.position++,i=e.position)}te(e,"unexpected end of the stream within a double quoted scalar")}function lm(e,t){var r=!0,i,l,a,u=e.tag,h,p=e.anchor,b,y,S,C,T,F=Object.create(null),z,q,L,X;if(X=e.input.charCodeAt(e.position),X===91)y=93,T=!1,h=[];else if(X===123)y=125,T=!0,h={};else return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=h),X=e.input.charCodeAt(++e.position);X!==0;){if(Re(e,!0,t),X=e.input.charCodeAt(e.position),X===y)return e.position++,e.tag=u,e.anchor=p,e.kind=T?"mapping":"sequence",e.result=h,!0;r?X===44&&te(e,"expected the node content, but found ','"):te(e,"missed comma between flow collection entries"),q=z=L=null,S=C=!1,X===63&&(b=e.input.charCodeAt(e.position+1),rt(b)&&(S=C=!0,e.position++,Re(e,!0,t))),i=e.line,l=e.lineStart,a=e.position,Tr(e,t,jn,!1,!0),q=e.tag,z=e.result,Re(e,!0,t),X=e.input.charCodeAt(e.position),(C||e.line===i)&&X===58&&(S=!0,X=e.input.charCodeAt(++e.position),Re(e,!0,t),Tr(e,t,jn,!1,!0),L=e.result),T?Cr(e,h,F,q,z,L,i,l,a):S?h.push(Cr(e,null,F,q,z,L,i,l,a)):h.push(z),Re(e,!0,t),X=e.input.charCodeAt(e.position),X===44?(r=!0,X=e.input.charCodeAt(++e.position)):r=!1}te(e,"unexpected end of the stream within a flow collection")}function sm(e,t){var r,i,l=Fi,a=!1,u=!1,h=t,p=0,b=!1,y,S;if(S=e.input.charCodeAt(e.position),S===124)i=!1;else if(S===62)i=!0;else return!1;for(e.kind="scalar",e.result="";S!==0;)if(S=e.input.charCodeAt(++e.position),S===43||S===45)Fi===l?l=S===43?$s:jp:te(e,"repeat of a chomping mode identifier");else if((y=em(S))>=0)y===0?te(e,"bad explicit indentation width of a block scalar; it cannot be less than one"):u?te(e,"repeat of an indentation width identifier"):(h=t+y-1,u=!0);else break;if(fr(S)){do S=e.input.charCodeAt(++e.position);while(fr(S));if(S===35)do S=e.input.charCodeAt(++e.position);while(!Et(S)&&S!==0)}for(;S!==0;){for(Gi(e),e.lineIndent=0,S=e.input.charCodeAt(e.position);(!u||e.lineIndent<h)&&S===32;)e.lineIndent++,S=e.input.charCodeAt(++e.position);if(!u&&e.lineIndent>h&&(h=e.lineIndent),Et(S)){p++;continue}if(e.lineIndent<h){l===$s?e.result+=Pe.repeat(`
`,a?1+p:p):l===Fi&&a&&(e.result+=`
`);break}for(i?fr(S)?(b=!0,e.result+=Pe.repeat(`
`,a?1+p:p)):b?(b=!1,e.result+=Pe.repeat(`
`,p+1)):p===0?a&&(e.result+=" "):e.result+=Pe.repeat(`
`,p):e.result+=Pe.repeat(`
`,a?1+p:p),a=!0,u=!0,p=0,r=e.position;!Et(S)&&S!==0;)S=e.input.charCodeAt(++e.position);Zt(e,r,e.position,!1)}return!0}function Xs(e,t){var r,i=e.tag,l=e.anchor,a=[],u,h=!1,p;if(e.firstTabInLine!==-1)return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=a),p=e.input.charCodeAt(e.position);p!==0&&(e.firstTabInLine!==-1&&(e.position=e.firstTabInLine,te(e,"tab characters must not be used in indentation")),!(p!==45||(u=e.input.charCodeAt(e.position+1),!rt(u))));){if(h=!0,e.position++,Re(e,!0,-1)&&e.lineIndent<=t){a.push(null),p=e.input.charCodeAt(e.position);continue}if(r=e.line,Tr(e,t,Ma,!1,!0),a.push(e.result),Re(e,!0,-1),p=e.input.charCodeAt(e.position),(e.line===r||e.lineIndent>t)&&p!==0)te(e,"bad indentation of a sequence entry");else if(e.lineIndent<t)break}return h?(e.tag=i,e.anchor=l,e.kind="sequence",e.result=a,!0):!1}function am(e,t,r){var i,l,a,u,h,p,b=e.tag,y=e.anchor,S={},C=Object.create(null),T=null,F=null,z=null,q=!1,L=!1,X;if(e.firstTabInLine!==-1)return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=S),X=e.input.charCodeAt(e.position);X!==0;){if(!q&&e.firstTabInLine!==-1&&(e.position=e.firstTabInLine,te(e,"tab characters must not be used in indentation")),i=e.input.charCodeAt(e.position+1),a=e.line,(X===63||X===58)&&rt(i))X===63?(q&&(Cr(e,S,C,T,F,null,u,h,p),T=F=z=null),L=!0,q=!0,l=!0):q?(q=!1,l=!0):te(e,"incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line"),e.position+=1,X=i;else{if(u=e.line,h=e.lineStart,p=e.position,!Tr(e,r,Sa,!1,!0))break;if(e.line===a){for(X=e.input.charCodeAt(e.position);fr(X);)X=e.input.charCodeAt(++e.position);if(X===58)X=e.input.charCodeAt(++e.position),rt(X)||te(e,"a whitespace character is expected after the key-value separator within a block mapping"),q&&(Cr(e,S,C,T,F,null,u,h,p),T=F=z=null),L=!0,q=!1,l=!1,T=e.tag,F=e.result;else if(L)te(e,"can not read an implicit mapping pair; a colon is missed");else return e.tag=b,e.anchor=y,!0}else if(L)te(e,"can not read a block mapping entry; a multiline key may not be an implicit key");else return e.tag=b,e.anchor=y,!0}if((e.line===a||e.lineIndent>t)&&(q&&(u=e.line,h=e.lineStart,p=e.position),Tr(e,t,Wn,!0,l)&&(q?F=e.result:z=e.result),q||(Cr(e,S,C,T,F,z,u,h,p),T=F=z=null),Re(e,!0,-1),X=e.input.charCodeAt(e.position)),(e.line===a||e.lineIndent>t)&&X!==0)te(e,"bad indentation of a mapping entry");else if(e.lineIndent<t)break}return q&&Cr(e,S,C,T,F,null,u,h,p),L&&(e.tag=b,e.anchor=y,e.kind="mapping",e.result=S),L}function cm(e){var t,r=!1,i=!1,l,a,u;if(u=e.input.charCodeAt(e.position),u!==33)return!1;if(e.tag!==null&&te(e,"duplication of a tag property"),u=e.input.charCodeAt(++e.position),u===60?(r=!0,u=e.input.charCodeAt(++e.position)):u===33?(i=!0,l="!!",u=e.input.charCodeAt(++e.position)):l="!",t=e.position,r){do u=e.input.charCodeAt(++e.position);while(u!==0&&u!==62);e.position<e.length?(a=e.input.slice(t,e.position),u=e.input.charCodeAt(++e.position)):te(e,"unexpected end of the stream within a verbatim tag")}else{for(;u!==0&&!rt(u);)u===33&&(i?te(e,"tag suffix cannot contain exclamation marks"):(l=e.input.slice(t-1,e.position+1),Aa.test(l)||te(e,"named tag handle cannot contain such characters"),i=!0,t=e.position+1)),u=e.input.charCodeAt(++e.position);a=e.input.slice(t,e.position),Zp.test(a)&&te(e,"tag suffix cannot contain flow indicator characters")}a&&!_a.test(a)&&te(e,"tag name cannot contain such characters: "+a);try{a=decodeURIComponent(a)}catch(h){te(e,"tag name is malformed: "+a)}return r?e.tag=a:Jt.call(e.tagMap,l)?e.tag=e.tagMap[l]+a:l==="!"?e.tag="!"+a:l==="!!"?e.tag="tag:yaml.org,2002:"+a:te(e,'undeclared tag handle "'+l+'"'),!0}function um(e){var t,r;if(r=e.input.charCodeAt(e.position),r!==38)return!1;for(e.anchor!==null&&te(e,"duplication of an anchor property"),r=e.input.charCodeAt(++e.position),t=e.position;r!==0&&!rt(r)&&!_r(r);)r=e.input.charCodeAt(++e.position);return e.position===t&&te(e,"name of an anchor node must contain at least one character"),e.anchor=e.input.slice(t,e.position),!0}function hm(e){var t,r,i;if(i=e.input.charCodeAt(e.position),i!==42)return!1;for(i=e.input.charCodeAt(++e.position),t=e.position;i!==0&&!rt(i)&&!_r(i);)i=e.input.charCodeAt(++e.position);return e.position===t&&te(e,"name of an alias node must contain at least one character"),r=e.input.slice(t,e.position),Jt.call(e.anchorMap,r)||te(e,'unidentified alias "'+r+'"'),e.result=e.anchorMap[r],Re(e,!0,-1),!0}function Tr(e,t,r,i,l){var a,u,h,p=1,b=!1,y=!1,S,C,T,F,z,q;if(e.listener!==null&&e.listener("open",e),e.tag=null,e.anchor=null,e.kind=null,e.result=null,a=u=h=Wn===r||Ma===r,i&&Re(e,!0,-1)&&(b=!0,e.lineIndent>t?p=1:e.lineIndent===t?p=0:e.lineIndent<t&&(p=-1)),p===1)for(;cm(e)||um(e);)Re(e,!0,-1)?(b=!0,h=a,e.lineIndent>t?p=1:e.lineIndent===t?p=0:e.lineIndent<t&&(p=-1)):h=!1;if(h&&(h=b||l),(p===1||Wn===r)&&(jn===r||Sa===r?z=t:z=t+1,q=e.position-e.lineStart,p===1?h&&(Xs(e,q)||am(e,q,z))||lm(e,z)?y=!0:(u&&sm(e,z)||im(e,z)||om(e,z)?y=!0:hm(e)?(y=!0,(e.tag!==null||e.anchor!==null)&&te(e,"alias node should not have any properties")):nm(e,z,jn===r)&&(y=!0,e.tag===null&&(e.tag="?")),e.anchor!==null&&(e.anchorMap[e.anchor]=e.result)):p===0&&(y=h&&Xs(e,q))),e.tag===null)e.anchor!==null&&(e.anchorMap[e.anchor]=e.result);else if(e.tag==="?"){for(e.result!==null&&e.kind!=="scalar"&&te(e,'unacceptable node kind for !<?> tag; it should be "scalar", not "'+e.kind+'"'),S=0,C=e.implicitTypes.length;S<C;S+=1)if(F=e.implicitTypes[S],F.resolve(e.result)){e.result=F.construct(e.result),e.tag=F.tag,e.anchor!==null&&(e.anchorMap[e.anchor]=e.result);break}}else if(e.tag!=="!"){if(Jt.call(e.typeMap[e.kind||"fallback"],e.tag))F=e.typeMap[e.kind||"fallback"][e.tag];else for(F=null,T=e.typeMap.multi[e.kind||"fallback"],S=0,C=T.length;S<C;S+=1)if(e.tag.slice(0,T[S].tag.length)===T[S].tag){F=T[S];break}F||te(e,"unknown tag !<"+e.tag+">"),e.result!==null&&F.kind!==e.kind&&te(e,"unacceptable node kind for !<"+e.tag+'> tag; it should be "'+F.kind+'", not "'+e.kind+'"'),F.resolve(e.result,e.tag)?(e.result=F.construct(e.result,e.tag),e.anchor!==null&&(e.anchorMap[e.anchor]=e.result)):te(e,"cannot resolve a node with !<"+e.tag+"> explicit tag")}return e.listener!==null&&e.listener("close",e),e.tag!==null||e.anchor!==null||y}function fm(e){var t=e.position,r,i,l,a=!1,u;for(e.version=null,e.checkLineBreaks=e.legacy,e.tagMap=Object.create(null),e.anchorMap=Object.create(null);(u=e.input.charCodeAt(e.position))!==0&&(Re(e,!0,-1),u=e.input.charCodeAt(e.position),!(e.lineIndent>0||u!==37));){for(a=!0,u=e.input.charCodeAt(++e.position),r=e.position;u!==0&&!rt(u);)u=e.input.charCodeAt(++e.position);for(i=e.input.slice(r,e.position),l=[],i.length<1&&te(e,"directive name must not be less than one character in length");u!==0;){for(;fr(u);)u=e.input.charCodeAt(++e.position);if(u===35){do u=e.input.charCodeAt(++e.position);while(u!==0&&!Et(u));break}if(Et(u))break;for(r=e.position;u!==0&&!rt(u);)u=e.input.charCodeAt(++e.position);l.push(e.input.slice(r,e.position))}u!==0&&Gi(e),Jt.call(Ys,i)?Ys[i](e,i,l):Kn(e,'unknown document directive "'+i+'"')}if(Re(e,!0,-1),e.lineIndent===0&&e.input.charCodeAt(e.position)===45&&e.input.charCodeAt(e.position+1)===45&&e.input.charCodeAt(e.position+2)===45?(e.position+=3,Re(e,!0,-1)):a&&te(e,"directives end mark is expected"),Tr(e,e.lineIndent-1,Wn,!1,!0),Re(e,!0,-1),e.checkLineBreaks&&Kp.test(e.input.slice(t,e.position))&&Kn(e,"non-ASCII line breaks are interpreted as content"),e.documents.push(e.result),e.position===e.lineStart&&Qn(e)){e.input.charCodeAt(e.position)===46&&(e.position+=3,Re(e,!0,-1));return}if(e.position<e.length-1)te(e,"end of the stream or a document separator is expected");else return}function Ea(e,t){e=String(e),t=t||{},e.length!==0&&(e.charCodeAt(e.length-1)!==10&&e.charCodeAt(e.length-1)!==13&&(e+=`
`),e.charCodeAt(0)===65279&&(e=e.slice(1)));var r=new rm(e,t),i=e.indexOf("\0");for(i!==-1&&(r.position=i,te(r,"null byte is not allowed in input")),r.input+="\0";r.input.charCodeAt(r.position)===32;)r.lineIndent+=1,r.position+=1;for(;r.position<r.length-1;)fm(r);return r.documents}function dm(e,t,r){t!==null&&typeof t=="object"&&typeof r=="undefined"&&(r=t,t=null);var i=Ea(e,r);if(typeof t!="function")return i;for(var l=0,a=i.length;l<a;l+=1)t(i[l])}function pm(e,t){var r=Ea(e,t);if(r.length!==0){if(r.length===1)return r[0];throw new Ke("expected a single document in the stream, but found more")}}var mm=dm,gm=pm,za={loadAll:mm,load:gm},Da=Object.prototype.toString,Ia=Object.prototype.hasOwnProperty,Ui=65279,xm=9,Wr=10,ym=13,bm=32,vm=33,wm=34,Li=35,km=37,Sm=38,Mm=39,Am=42,Ba=44,_m=45,Zn=58,Cm=61,Tm=62,Nm=63,Em=64,Oa=91,Fa=93,zm=96,Ra=123,Dm=124,La=125,Ue={};Ue[0]="\\0";Ue[7]="\\a";Ue[8]="\\b";Ue[9]="\\t";Ue[10]="\\n";Ue[11]="\\v";Ue[12]="\\f";Ue[13]="\\r";Ue[27]="\\e";Ue[34]='\\"';Ue[92]="\\\\";Ue[133]="\\N";Ue[160]="\\_";Ue[8232]="\\L";Ue[8233]="\\P";var Im=["y","Y","yes","Yes","YES","on","On","ON","n","N","no","No","NO","off","Off","OFF"],Bm=/^[-+]?[0-9_]+(?::[0-9_]+)+(?:\.[0-9_]*)?$/;function Om(e,t){var r,i,l,a,u,h,p;if(t===null)return{};for(r={},i=Object.keys(t),l=0,a=i.length;l<a;l+=1)u=i[l],h=String(t[u]),u.slice(0,2)==="!!"&&(u="tag:yaml.org,2002:"+u.slice(2)),p=e.compiledTypeMap.fallback[u],p&&Ia.call(p.styleAliases,h)&&(h=p.styleAliases[h]),r[u]=h;return r}function Fm(e){var t,r,i;if(t=e.toString(16).toUpperCase(),e<=255)r="x",i=2;else if(e<=65535)r="u",i=4;else if(e<=4294967295)r="U",i=8;else throw new Ke("code point within a string may not be greater than 0xFFFFFFFF");return"\\"+r+Pe.repeat("0",i-t.length)+t}var Rm=1,Kr=2;function Lm(e){this.schema=e.schema||Vi,this.indent=Math.max(1,e.indent||2),this.noArrayIndent=e.noArrayIndent||!1,this.skipInvalid=e.skipInvalid||!1,this.flowLevel=Pe.isNothing(e.flowLevel)?-1:e.flowLevel,this.styleMap=Om(this.schema,e.styles||null),this.sortKeys=e.sortKeys||!1,this.lineWidth=e.lineWidth||80,this.noRefs=e.noRefs||!1,this.noCompatMode=e.noCompatMode||!1,this.condenseFlow=e.condenseFlow||!1,this.quotingType=e.quotingType==='"'?Kr:Rm,this.forceQuotes=e.forceQuotes||!1,this.replacer=typeof e.replacer=="function"?e.replacer:null,this.implicitTypes=this.schema.compiledImplicit,this.explicitTypes=this.schema.compiledExplicit,this.tag=null,this.result="",this.duplicates=[],this.usedDuplicates=null}function js(e,t){for(var r=Pe.repeat(" ",t),i=0,l=-1,a="",u,h=e.length;i<h;)l=e.indexOf(`
`,i),l===-1?(u=e.slice(i),i=h):(u=e.slice(i,l+1),i=l+1),u.length&&u!==`
`&&(a+=r),a+=u;return a}function qi(e,t){return`
`+Pe.repeat(" ",e.indent*t)}function qm(e,t){var r,i,l;for(r=0,i=e.implicitTypes.length;r<i;r+=1)if(l=e.implicitTypes[r],l.resolve(t))return!0;return!1}function Jn(e){return e===bm||e===xm}function Zr(e){return 32<=e&&e<=126||161<=e&&e<=55295&&e!==8232&&e!==8233||57344<=e&&e<=65533&&e!==Ui||65536<=e&&e<=1114111}function Ws(e){return Zr(e)&&e!==Ui&&e!==ym&&e!==Wr}function Ks(e,t,r){var i=Ws(e),l=i&&!Jn(e);return(r?i:i&&e!==Ba&&e!==Oa&&e!==Fa&&e!==Ra&&e!==La)&&e!==Li&&!(t===Zn&&!l)||Ws(t)&&!Jn(t)&&e===Li||t===Zn&&l}function Pm(e){return Zr(e)&&e!==Ui&&!Jn(e)&&e!==_m&&e!==Nm&&e!==Zn&&e!==Ba&&e!==Oa&&e!==Fa&&e!==Ra&&e!==La&&e!==Li&&e!==Sm&&e!==Am&&e!==vm&&e!==Dm&&e!==Cm&&e!==Tm&&e!==Mm&&e!==wm&&e!==km&&e!==Em&&e!==zm}function Hm(e){return!Jn(e)&&e!==Zn}function Xr(e,t){var r=e.charCodeAt(t),i;return r>=55296&&r<=56319&&t+1<e.length&&(i=e.charCodeAt(t+1),i>=56320&&i<=57343)?(r-55296)*1024+i-56320+65536:r}function qa(e){var t=/^\n* /;return t.test(e)}var Pa=1,Pi=2,Ha=3,$a=4,Ar=5;function $m(e,t,r,i,l,a,u,h){var p,b=0,y=null,S=!1,C=!1,T=i!==-1,F=-1,z=Pm(Xr(e,0))&&Hm(Xr(e,e.length-1));if(t||u)for(p=0;p<e.length;b>=65536?p+=2:p++){if(b=Xr(e,p),!Zr(b))return Ar;z=z&&Ks(b,y,h),y=b}else{for(p=0;p<e.length;b>=65536?p+=2:p++){if(b=Xr(e,p),b===Wr)S=!0,T&&(C=C||p-F-1>i&&e[F+1]!==" ",F=p);else if(!Zr(b))return Ar;z=z&&Ks(b,y,h),y=b}C=C||T&&p-F-1>i&&e[F+1]!==" "}return!S&&!C?z&&!u&&!l(e)?Pa:a===Kr?Ar:Pi:r>9&&qa(e)?Ar:u?a===Kr?Ar:Pi:C?$a:Ha}function Vm(e,t,r,i,l){e.dump=function(){if(t.length===0)return e.quotingType===Kr?'""':"''";if(!e.noCompatMode&&(Im.indexOf(t)!==-1||Bm.test(t)))return e.quotingType===Kr?'"'+t+'"':"'"+t+"'";var a=e.indent*Math.max(1,r),u=e.lineWidth===-1?-1:Math.max(Math.min(e.lineWidth,40),e.lineWidth-a),h=i||e.flowLevel>-1&&r>=e.flowLevel;function p(b){return qm(e,b)}switch($m(t,h,e.indent,u,p,e.quotingType,e.forceQuotes&&!i,l)){case Pa:return t;case Pi:return"'"+t.replace(/'/g,"''")+"'";case Ha:return"|"+Zs(t,e.indent)+Js(js(t,a));case $a:return">"+Zs(t,e.indent)+Js(js(Gm(t,u),a));case Ar:return'"'+Ym(t)+'"';default:throw new Ke("impossible error: invalid scalar style")}}()}function Zs(e,t){var r=qa(e)?String(t):"",i=e[e.length-1]===`
`,l=i&&(e[e.length-2]===`
`||e===`
`),a=l?"+":i?"":"-";return r+a+`
`}function Js(e){return e[e.length-1]===`
`?e.slice(0,-1):e}function Gm(e,t){for(var r=/(\n+)([^\n]*)/g,i=function(){var b=e.indexOf(`
`);return b=b!==-1?b:e.length,r.lastIndex=b,Qs(e.slice(0,b),t)}(),l=e[0]===`
`||e[0]===" ",a,u;u=r.exec(e);){var h=u[1],p=u[2];a=p[0]===" ",i+=h+(!l&&!a&&p!==""?`
`:"")+Qs(p,t),l=a}return i}function Qs(e,t){if(e===""||e[0]===" ")return e;for(var r=/ [^ ]/g,i,l=0,a,u=0,h=0,p="";i=r.exec(e);)h=i.index,h-l>t&&(a=u>l?u:h,p+=`
`+e.slice(l,a),l=a+1),u=h;return p+=`
`,e.length-l>t&&u>l?p+=e.slice(l,u)+`
`+e.slice(u+1):p+=e.slice(l),p.slice(1)}function Ym(e){for(var t="",r=0,i,l=0;l<e.length;r>=65536?l+=2:l++)r=Xr(e,l),i=Ue[r],!i&&Zr(r)?(t+=e[l],r>=65536&&(t+=e[l+1])):t+=i||Fm(r);return t}function Um(e,t,r){var i="",l=e.tag,a,u,h;for(a=0,u=r.length;a<u;a+=1)h=r[a],e.replacer&&(h=e.replacer.call(r,String(a),h)),(Pt(e,t,h,!1,!1)||typeof h=="undefined"&&Pt(e,t,null,!1,!1))&&(i!==""&&(i+=","+(e.condenseFlow?"":" ")),i+=e.dump);e.tag=l,e.dump="["+i+"]"}function ea(e,t,r,i){var l="",a=e.tag,u,h,p;for(u=0,h=r.length;u<h;u+=1)p=r[u],e.replacer&&(p=e.replacer.call(r,String(u),p)),(Pt(e,t+1,p,!0,!0,!1,!0)||typeof p=="undefined"&&Pt(e,t+1,null,!0,!0,!1,!0))&&((!i||l!=="")&&(l+=qi(e,t)),e.dump&&Wr===e.dump.charCodeAt(0)?l+="-":l+="- ",l+=e.dump);e.tag=a,e.dump=l||"[]"}function Xm(e,t,r){var i="",l=e.tag,a=Object.keys(r),u,h,p,b,y;for(u=0,h=a.length;u<h;u+=1)y="",i!==""&&(y+=", "),e.condenseFlow&&(y+='"'),p=a[u],b=r[p],e.replacer&&(b=e.replacer.call(r,p,b)),Pt(e,t,p,!1,!1)&&(e.dump.length>1024&&(y+="? "),y+=e.dump+(e.condenseFlow?'"':"")+":"+(e.condenseFlow?"":" "),Pt(e,t,b,!1,!1)&&(y+=e.dump,i+=y));e.tag=l,e.dump="{"+i+"}"}function jm(e,t,r,i){var l="",a=e.tag,u=Object.keys(r),h,p,b,y,S,C;if(e.sortKeys===!0)u.sort();else if(typeof e.sortKeys=="function")u.sort(e.sortKeys);else if(e.sortKeys)throw new Ke("sortKeys must be a boolean or a function");for(h=0,p=u.length;h<p;h+=1)C="",(!i||l!=="")&&(C+=qi(e,t)),b=u[h],y=r[b],e.replacer&&(y=e.replacer.call(r,b,y)),Pt(e,t+1,b,!0,!0,!0)&&(S=e.tag!==null&&e.tag!=="?"||e.dump&&e.dump.length>1024,S&&(e.dump&&Wr===e.dump.charCodeAt(0)?C+="?":C+="? "),C+=e.dump,S&&(C+=qi(e,t)),Pt(e,t+1,y,!0,S)&&(e.dump&&Wr===e.dump.charCodeAt(0)?C+=":":C+=": ",C+=e.dump,l+=C));e.tag=a,e.dump=l||"{}"}function ta(e,t,r){var i,l,a,u,h,p;for(l=r?e.explicitTypes:e.implicitTypes,a=0,u=l.length;a<u;a+=1)if(h=l[a],(h.instanceOf||h.predicate)&&(!h.instanceOf||typeof t=="object"&&t instanceof h.instanceOf)&&(!h.predicate||h.predicate(t))){if(r?h.multi&&h.representName?e.tag=h.representName(t):e.tag=h.tag:e.tag="?",h.represent){if(p=e.styleMap[h.tag]||h.defaultStyle,Da.call(h.represent)==="[object Function]")i=h.represent(t,p);else if(Ia.call(h.represent,p))i=h.represent[p](t,p);else throw new Ke("!<"+h.tag+'> tag resolver accepts not "'+p+'" style');e.dump=i}return!0}return!1}function Pt(e,t,r,i,l,a,u){e.tag=null,e.dump=r,ta(e,r,!1)||ta(e,r,!0);var h=Da.call(e.dump),p=i,b;i&&(i=e.flowLevel<0||e.flowLevel>t);var y=h==="[object Object]"||h==="[object Array]",S,C;if(y&&(S=e.duplicates.indexOf(r),C=S!==-1),(e.tag!==null&&e.tag!=="?"||C||e.indent!==2&&t>0)&&(l=!1),C&&e.usedDuplicates[S])e.dump="*ref_"+S;else{if(y&&C&&!e.usedDuplicates[S]&&(e.usedDuplicates[S]=!0),h==="[object Object]")i&&Object.keys(e.dump).length!==0?(jm(e,t,e.dump,l),C&&(e.dump="&ref_"+S+e.dump)):(Xm(e,t,e.dump),C&&(e.dump="&ref_"+S+" "+e.dump));else if(h==="[object Array]")i&&e.dump.length!==0?(e.noArrayIndent&&!u&&t>0?ea(e,t-1,e.dump,l):ea(e,t,e.dump,l),C&&(e.dump="&ref_"+S+e.dump)):(Um(e,t,e.dump),C&&(e.dump="&ref_"+S+" "+e.dump));else if(h==="[object String]")e.tag!=="?"&&Vm(e,e.dump,t,a,p);else{if(h==="[object Undefined]")return!1;if(e.skipInvalid)return!1;throw new Ke("unacceptable kind of an object to dump "+h)}e.tag!==null&&e.tag!=="?"&&(b=encodeURI(e.tag[0]==="!"?e.tag.slice(1):e.tag).replace(/!/g,"%21"),e.tag[0]==="!"?b="!"+b:b.slice(0,18)==="tag:yaml.org,2002:"?b="!!"+b.slice(18):b="!<"+b+">",e.dump=b+" "+e.dump)}return!0}function Wm(e,t){var r=[],i=[],l,a;for(Hi(e,r,i),l=0,a=i.length;l<a;l+=1)t.duplicates.push(r[i[l]]);t.usedDuplicates=new Array(a)}function Hi(e,t,r){var i,l,a;if(e!==null&&typeof e=="object")if(l=t.indexOf(e),l!==-1)r.indexOf(l)===-1&&r.push(l);else if(t.push(e),Array.isArray(e))for(l=0,a=e.length;l<a;l+=1)Hi(e[l],t,r);else for(i=Object.keys(e),l=0,a=i.length;l<a;l+=1)Hi(e[i[l]],t,r)}function Km(e,t){t=t||{};var r=new Lm(t);r.noRefs||Wm(e,r);var i=e;return r.replacer&&(i=r.replacer.call({"":i},"",i)),Pt(r,0,i,!0,!0)?r.dump+`
`:""}var Zm=Km,Jm={dump:Zm};function Xi(e,t){return function(){throw new Error("Function yaml."+e+" is removed in js-yaml 4. Use yaml."+t+" instead, which is now safe by default.")}}var Qm=Ge,e4=ia,t4=aa,r4=da,n4=pa,i4=Vi,o4=za.load,l4=za.loadAll,s4=Jm.dump,a4=Ke,c4={binary:ba,float:fa,map:sa,null:ca,pairs:wa,set:ka,timestamp:xa,bool:ua,int:ha,merge:ya,omap:va,seq:la,str:oa},u4=Xi("safeLoad","load"),h4=Xi("safeLoadAll","loadAll"),f4=Xi("safeDump","dump"),d4={Type:Qm,Schema:e4,FAILSAFE_SCHEMA:t4,JSON_SCHEMA:r4,CORE_SCHEMA:n4,DEFAULT_SCHEMA:i4,load:o4,loadAll:l4,dump:s4,YAMLException:a4,types:c4,safeLoad:u4,safeLoadAll:h4,safeDump:f4},Va=d4;var p4=`<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<title>Markmap</title>
<style>
* {
  margin: 0;
  padding: 0;
}
#mindmap {
  display: block;
  width: 100vw;
  height: 100vh;
}
</style>
<!--CSS-->
</head>
<body>
<svg id="mindmap"></svg>
<!--JS-->
</body>
</html>
`,m4=["d3@7.8.5/dist/d3.min.js","markmap-view@0.15.8/dist/browser/index.js"],ji="katex",g4=["katex@0.16.8/dist/katex.min.js"].map(e=>Yr(e)),Ua=Yr("webfontloader@1.6.28/webfontloader.js");Ua.data.defer=!0;var x4=["katex@0.16.8/dist/katex.min.css"].map(e=>Di(e)),y4={versions:{katex:"0.16.8",webfontloader:"1.6.28"},preloadScripts:g4,scripts:[{type:"iife",data:{fn:e=>{window.WebFontConfig={custom:{families:["KaTeX_AMS","KaTeX_Caligraphic:n4,n7","KaTeX_Fraktur:n4,n7","KaTeX_Main:n4,n7,i4,i7","KaTeX_Math:i4,i7","KaTeX_Script","KaTeX_SansSerif:n4,n7,i4","KaTeX_Size1","KaTeX_Size2","KaTeX_Size3","KaTeX_Size4","KaTeX_Typewriter"]},active:()=>{e().refreshHook.call()}}},getParams({getMarkmap:e}){return[e]}}},Ua],styles:x4};function b4(e,t,r){return e.map(i=>{if(typeof i=="string"&&!i.includes("://")){i.startsWith("npm:")||(i=`npm:${i}`);let l=4+t.length;i.startsWith(`npm:${t}/`)&&(i=`${i.slice(0,l)}@${r}${i.slice(l)}`)}return i})}function Ki(e,t){return t.type==="script"&&t.data.src?{...t,data:{...t.data,src:e.getFullUrl(t.data.src)}}:t}function v4(e,t){return t.type==="stylesheet"&&t.data.href?{...t,data:{...t.data,href:e.getFullUrl(t.data.href)}}:t}function w4(e){return{transformer:e,parser:new wt,beforeParse:new wt,afterParse:new wt,htmltag:new wt,retransform:new wt}}var Jr={name:ji,config:y4,transform(e){var t,r,i,l;let a,u=((r=(t=Jr.config)==null?void 0:t.preloadScripts)==null?void 0:r.map(y=>Ki(e.transformer.urlBuilder,y)))||[],h=()=>(a||(a=Xn(u)),a),p=(y,S)=>{let{katex:C}=window;return C?C.renderToString(y,{displayMode:S,throwOnError:!1}):(h().then(()=>{e.retransform.call()}),y)},b=cr;return e.parser.tap(y=>{y.use(Ya.default),y.renderer.rules.katex=(S,C)=>(b(),p(S[C].content,!!S[C].block))}),e.beforeParse.tap((y,S)=>{b=()=>{S.features[ji]=!0}}),e.afterParse.tap((y,S)=>{var C;let T=(C=S.frontmatter)==null?void 0:C.markmap;T&&["extraJs","extraCss"].forEach(F=>{var z,q;let L=T[F];L&&(T[F]=b4(L,ji,((q=(z=Jr.config)==null?void 0:z.versions)==null?void 0:q.katex)||""))})}),{styles:(i=Jr.config)==null?void 0:i.styles,scripts:(l=Jr.config)==null?void 0:l.scripts}}},k4="frontmatter",S4={name:k4,transform(e){return e.beforeParse.tap((t,r)=>{let{content:i}=r;if(!/^---\r?\n/.test(i))return;let l=/\n---\r?\n/.exec(i);if(!l)return;let a=i.slice(4,l.index),u;try{u=Va.load(a),u!=null&&u.markmap&&(u.markmap=M4(u.markmap))}catch(h){return}r.frontmatter=u,r.content=i.slice(l.index+l[0].length),r.contentLineOffset=i.slice(0,l.index).split(`
`).length+1}),{}}};function M4(e){if(e)return["color","extraJs","extraCss"].forEach(t=>{e[t]!=null&&(e[t]=A4(e[t]))}),["duration","maxWidth","initialExpandLevel"].forEach(t=>{e[t]!=null&&(e[t]=_4(e[t]))}),e}function A4(e){let t;return typeof e=="string"?t=[e]:Array.isArray(e)&&(t=e.filter(r=>r&&typeof r=="string")),t!=null&&t.length?t:void 0}function _4(e){if(!isNaN(+e))return+e}var C4="npmUrl",T4={name:C4,transform(e){return e.afterParse.tap((t,r)=>{let{frontmatter:i}=r,l=i==null?void 0:i.markmap;l&&["extraJs","extraCss"].forEach(a=>{let u=l[a];u&&(l[a]=u.map(h=>h.startsWith("npm:")?e.transformer.urlBuilder.getFullUrl(h.slice(4)):h))})}),{}}},Ga="hljs",N4=["@highlightjs/cdn-assets@11.8.0/highlight.min.js"].map(e=>Yr(e)),E4=["@highlightjs/cdn-assets@11.8.0/styles/default.min.css"].map(e=>Di(e)),z4={versions:{hljs:"11.8.0"},preloadScripts:N4,styles:E4},Wi={name:Ga,config:z4,transform(e){var t,r,i;let l,a=((r=(t=Wi.config)==null?void 0:t.preloadScripts)==null?void 0:r.map(p=>Ki(e.transformer.urlBuilder,p)))||[],u=()=>(l||(l=Xn(a)),l),h=cr;return e.parser.tap(p=>{p.set({highlight:(b,y)=>{h();let{hljs:S}=window;return S?S.highlightAuto(b,y?[y]:void 0).value:(u().then(()=>{e.retransform.call()}),b)}})}),e.beforeParse.tap((p,b)=>{h=()=>{b.features[Ga]=!0}}),{styles:(i=Wi.config)==null?void 0:i.styles}}},D4=[S4,Jr,Wi,T4];function Xa(e){var i;var t,r;if(e.type==="heading")e.children=e.children.filter(l=>l.type!=="paragraph");else if(e.type==="list_item")e.children=e.children.filter(l=>["paragraph","fence"].includes(l.type)?(e.content||(e.content=l.content,e.payload={...e.payload,...l.payload}),!1):!0),((t=e.payload)==null?void 0:t.index)!=null&&(e.content=`${e.payload.index}. ${e.content}`);else if(e.type==="ordered_list"){let l=(i=(r=e.payload)==null?void 0:r.startIndex)!=null?i:1;e.children.forEach(a=>{a.type==="list_item"&&(a.payload={...a.payload,index:l},l+=1)})}e.children.length>0&&(e.children.forEach(l=>Xa(l)),e.children.length===1&&!e.children[0].content&&(e.children=e.children[0].children))}function ja(e,t=0){e.depth=t,e.children.forEach(r=>{ja(r,t+1)})}var e0=class{constructor(t=D4){this.assetsMap={},this.urlBuilder=new Gr,this.hooks=w4(this),this.plugins=t.map(l=>typeof l=="function"?l():l);let r={};for(let{name:l,transform:a}of this.plugins)r[l]=a(this.hooks);this.assetsMap=r;let i=new qt("full",{html:!0,breaks:!0,maxNesting:1/0});i.renderer.rules.htmltag=Bs(i.renderer.rules.htmltag,(l,...a)=>{let u=l(...a);return this.hooks.htmltag.call({args:a,result:u}),u}),this.md=i,this.hooks.parser.call(i)}buildTree(t){let{md:r}=this,i={type:"root",depth:0,content:"",children:[],payload:{}},l=[i],a=0;for(let u of t){let h={};u.lines&&(h.lines=u.lines);let p=l[l.length-1];if(u.type.endsWith("_open")){let b=u.type.slice(0,-5);if(b==="heading")for(a=u.hLevel;(p==null?void 0:p.depth)>=a;)l.pop(),p=l[l.length-1];else a=Math.max(a,(p==null?void 0:p.depth)||0)+1,b==="ordered_list"&&(h.startIndex=u.order);let y={type:b,depth:a,payload:h,content:"",children:[]};p.children.push(y),l.push(y)}else if(p){if(u.type===`${p.type}_close`)p.type==="heading"?a=p.depth:(l.pop(),a=0);else if(u.type==="inline"){let b=this.hooks.htmltag.tap(S=>{var C;let T=(C=S.result)==null?void 0:C.match(/^<!--([\s\S]*?)-->$/),F=T==null?void 0:T[1].trim().split(" ");(F==null?void 0:F[0])==="fold"&&(p.payload={...p.payload,fold:["all","recursively"].includes(F[1])?2:1},S.result="")}),y=r.renderer.render([u],r.options,{});b(),p.content=`${p.content||""}${y}`}else if(u.type==="fence"){let b=r.renderer.render([u],r.options,{});p.children.push({type:u.type,depth:a+1,content:b,children:[],payload:h})}}else continue}return i}transform(t){var r;let i={content:t,features:{},contentLineOffset:0};this.hooks.beforeParse.call(this.md,i);let l=this.md.parse(i.content,{});this.hooks.afterParse.call(this.md,i);let a=this.buildTree(l);return Xa(a),((r=a.children)==null?void 0:r.length)===1&&(a=a.children[0]),ja(a),{...i,root:a}}getAssets(t){let r=[],i=[];t!=null||(t=this.plugins.map(l=>l.name));for(let l of t.map(a=>this.assetsMap[a]))l&&(l.styles&&r.push(...l.styles),l.scripts&&i.push(...l.scripts));return{styles:r.map(l=>v4(this.urlBuilder,l)),scripts:i.map(l=>Ki(this.urlBuilder,l))}}getUsedAssets(t){let r=this.plugins.map(i=>i.name).filter(i=>t[i]);return this.getAssets(r)}fillTemplate(t,r,i){var y;i={...i},(y=i.baseJs)!=null||(i.baseJs=m4.map(S=>this.urlBuilder.getFullUrl(S)).map(S=>Yr(S)));let{scripts:l,styles:a}=r,u=[...a?zs(a):[]],h={getMarkmap:()=>window.markmap,getOptions:i.getOptions,jsonOptions:i.jsonOptions,root:t},p=[...Es([...i.baseJs,...l||[],{type:"iife",data:{fn:(S,C,T,F)=>{let z=S();window.mm=z.Markmap.create("svg#mindmap",(C||z.deriveOptions)(F),T)},getParams:({getMarkmap:S,getOptions:C,root:T,jsonOptions:F})=>[S,C,T,F]}}],h)];return p4.replace("<!--CSS-->",()=>u.join("")).replace("<!--JS-->",()=>p.join(""))}};var Nr=class extends Map{constructor(t,r=O4){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:r}}),t!=null)for(let[i,l]of t)this.set(i,l)}get(t){return super.get(Wa(this,t))}has(t){return super.has(Wa(this,t))}set(t,r){return super.set(I4(this,t),r)}delete(t){return super.delete(B4(this,t))}};function Wa({_intern:e,_key:t},r){let i=t(r);return e.has(i)?e.get(i):r}function I4({_intern:e,_key:t},r){let i=t(r);return e.has(i)?e.get(i):(e.set(i,r),r)}function B4({_intern:e,_key:t},r){let i=t(r);return e.has(i)&&(r=e.get(i),e.delete(i)),r}function O4(e){return e!==null&&typeof e=="object"?e.valueOf():e}function Qr(e,t){let r;if(t===void 0)for(let i of e)i!=null&&(r<i||r===void 0&&i>=i)&&(r=i);else{let i=-1;for(let l of e)(l=t(l,++i,e))!=null&&(r<l||r===void 0&&l>=l)&&(r=l)}return r}function en(e,t){let r;if(t===void 0)for(let i of e)i!=null&&(r>i||r===void 0&&i>=i)&&(r=i);else{let i=-1;for(let l of e)(l=t(l,++i,e))!=null&&(r>l||r===void 0&&l>=l)&&(r=l)}return r}function t0(e,t){let r,i=-1,l=-1;if(t===void 0)for(let a of e)++l,a!=null&&(r>a||r===void 0&&a>=a)&&(r=a,i=l);else for(let a of e)(a=t(a,++l,e))!=null&&(r>a||r===void 0&&a>=a)&&(r=a,i=l);return i}var F4={value:()=>{}};function Za(){for(var e=0,t=arguments.length,r={},i;e<t;++e){if(!(i=arguments[e]+"")||i in r||/[\s.]/.test(i))throw new Error("illegal type: "+i);r[i]=[]}return new r0(r)}function r0(e){this._=e}function R4(e,t){return e.trim().split(/^|\s+/).map(function(r){var i="",l=r.indexOf(".");if(l>=0&&(i=r.slice(l+1),r=r.slice(0,l)),r&&!t.hasOwnProperty(r))throw new Error("unknown type: "+r);return{type:r,name:i}})}r0.prototype=Za.prototype={constructor:r0,on:function(e,t){var r=this._,i=R4(e+"",r),l,a=-1,u=i.length;if(arguments.length<2){for(;++a<u;)if((l=(e=i[a]).type)&&(l=L4(r[l],e.name)))return l;return}if(t!=null&&typeof t!="function")throw new Error("invalid callback: "+t);for(;++a<u;)if(l=(e=i[a]).type)r[l]=Ka(r[l],e.name,t);else if(t==null)for(l in r)r[l]=Ka(r[l],e.name,null);return this},copy:function(){var e={},t=this._;for(var r in t)e[r]=t[r].slice();return new r0(e)},call:function(e,t){if((l=arguments.length-2)>0)for(var r=new Array(l),i=0,l,a;i<l;++i)r[i]=arguments[i+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(a=this._[e],i=0,l=a.length;i<l;++i)a[i].value.apply(t,r)},apply:function(e,t,r){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var i=this._[e],l=0,a=i.length;l<a;++l)i[l].value.apply(t,r)}};function L4(e,t){for(var r=0,i=e.length,l;r<i;++r)if((l=e[r]).name===t)return l.value}function Ka(e,t,r){for(var i=0,l=e.length;i<l;++i)if(e[i].name===t){e[i]=F4,e=e.slice(0,i).concat(e.slice(i+1));break}return r!=null&&e.push({name:t,value:r}),e}var tn=Za;var n0="http://www.w3.org/1999/xhtml",Zi={svg:"http://www.w3.org/2000/svg",xhtml:n0,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function Ht(e){var t=e+="",r=t.indexOf(":");return r>=0&&(t=e.slice(0,r))!=="xmlns"&&(e=e.slice(r+1)),Zi.hasOwnProperty(t)?{space:Zi[t],local:e}:e}function q4(e){return function(){var t=this.ownerDocument,r=this.namespaceURI;return r===n0&&t.documentElement.namespaceURI===n0?t.createElement(e):t.createElementNS(r,e)}}function P4(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function i0(e){var t=Ht(e);return(t.local?P4:q4)(t)}function H4(){}function dr(e){return e==null?H4:function(){return this.querySelector(e)}}function Ja(e){typeof e!="function"&&(e=dr(e));for(var t=this._groups,r=t.length,i=new Array(r),l=0;l<r;++l)for(var a=t[l],u=a.length,h=i[l]=new Array(u),p,b,y=0;y<u;++y)(p=a[y])&&(b=e.call(p,p.__data__,y,a))&&("__data__"in p&&(b.__data__=p.__data__),h[y]=b);return new Ie(i,this._parents)}function Ji(e){return e==null?[]:Array.isArray(e)?e:Array.from(e)}function $4(){return[]}function rn(e){return e==null?$4:function(){return this.querySelectorAll(e)}}function V4(e){return function(){return Ji(e.apply(this,arguments))}}function Qa(e){typeof e=="function"?e=V4(e):e=rn(e);for(var t=this._groups,r=t.length,i=[],l=[],a=0;a<r;++a)for(var u=t[a],h=u.length,p,b=0;b<h;++b)(p=u[b])&&(i.push(e.call(p,p.__data__,b,u)),l.push(p));return new Ie(i,l)}function nn(e){return function(){return this.matches(e)}}function o0(e){return function(t){return t.matches(e)}}var G4=Array.prototype.find;function Y4(e){return function(){return G4.call(this.children,e)}}function U4(){return this.firstElementChild}function ec(e){return this.select(e==null?U4:Y4(typeof e=="function"?e:o0(e)))}var X4=Array.prototype.filter;function j4(){return Array.from(this.children)}function W4(e){return function(){return X4.call(this.children,e)}}function tc(e){return this.selectAll(e==null?j4:W4(typeof e=="function"?e:o0(e)))}function rc(e){typeof e!="function"&&(e=nn(e));for(var t=this._groups,r=t.length,i=new Array(r),l=0;l<r;++l)for(var a=t[l],u=a.length,h=i[l]=[],p,b=0;b<u;++b)(p=a[b])&&e.call(p,p.__data__,b,a)&&h.push(p);return new Ie(i,this._parents)}function l0(e){return new Array(e.length)}function nc(){return new Ie(this._enter||this._groups.map(l0),this._parents)}function on(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}on.prototype={constructor:on,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};function ic(e){return function(){return e}}function K4(e,t,r,i,l,a){for(var u=0,h,p=t.length,b=a.length;u<b;++u)(h=t[u])?(h.__data__=a[u],i[u]=h):r[u]=new on(e,a[u]);for(;u<p;++u)(h=t[u])&&(l[u]=h)}function Z4(e,t,r,i,l,a,u){var h,p,b=new Map,y=t.length,S=a.length,C=new Array(y),T;for(h=0;h<y;++h)(p=t[h])&&(C[h]=T=u.call(p,p.__data__,h,t)+"",b.has(T)?l[h]=p:b.set(T,p));for(h=0;h<S;++h)T=u.call(e,a[h],h,a)+"",(p=b.get(T))?(i[h]=p,p.__data__=a[h],b.delete(T)):r[h]=new on(e,a[h]);for(h=0;h<y;++h)(p=t[h])&&b.get(C[h])===p&&(l[h]=p)}function J4(e){return e.__data__}function oc(e,t){if(!arguments.length)return Array.from(this,J4);var r=t?Z4:K4,i=this._parents,l=this._groups;typeof e!="function"&&(e=ic(e));for(var a=l.length,u=new Array(a),h=new Array(a),p=new Array(a),b=0;b<a;++b){var y=i[b],S=l[b],C=S.length,T=Q4(e.call(y,y&&y.__data__,b,i)),F=T.length,z=h[b]=new Array(F),q=u[b]=new Array(F),L=p[b]=new Array(C);r(y,S,z,q,L,T,t);for(var X=0,re=0,ce,me;X<F;++X)if(ce=z[X]){for(X>=re&&(re=X+1);!(me=q[re])&&++re<F;);ce._next=me||null}}return u=new Ie(u,i),u._enter=h,u._exit=p,u}function Q4(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function lc(){return new Ie(this._exit||this._groups.map(l0),this._parents)}function sc(e,t,r){var i=this.enter(),l=this,a=this.exit();return typeof e=="function"?(i=e(i),i&&(i=i.selection())):i=i.append(e+""),t!=null&&(l=t(l),l&&(l=l.selection())),r==null?a.remove():r(a),i&&l?i.merge(l).order():l}function ac(e){for(var t=e.selection?e.selection():e,r=this._groups,i=t._groups,l=r.length,a=i.length,u=Math.min(l,a),h=new Array(l),p=0;p<u;++p)for(var b=r[p],y=i[p],S=b.length,C=h[p]=new Array(S),T,F=0;F<S;++F)(T=b[F]||y[F])&&(C[F]=T);for(;p<l;++p)h[p]=r[p];return new Ie(h,this._parents)}function cc(){for(var e=this._groups,t=-1,r=e.length;++t<r;)for(var i=e[t],l=i.length-1,a=i[l],u;--l>=0;)(u=i[l])&&(a&&u.compareDocumentPosition(a)^4&&a.parentNode.insertBefore(u,a),a=u);return this}function uc(e){e||(e=e2);function t(S,C){return S&&C?e(S.__data__,C.__data__):!S-!C}for(var r=this._groups,i=r.length,l=new Array(i),a=0;a<i;++a){for(var u=r[a],h=u.length,p=l[a]=new Array(h),b,y=0;y<h;++y)(b=u[y])&&(p[y]=b);p.sort(t)}return new Ie(l,this._parents).order()}function e2(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function hc(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this}function fc(){return Array.from(this)}function dc(){for(var e=this._groups,t=0,r=e.length;t<r;++t)for(var i=e[t],l=0,a=i.length;l<a;++l){var u=i[l];if(u)return u}return null}function pc(){let e=0;for(let t of this)++e;return e}function mc(){return!this.node()}function gc(e){for(var t=this._groups,r=0,i=t.length;r<i;++r)for(var l=t[r],a=0,u=l.length,h;a<u;++a)(h=l[a])&&e.call(h,h.__data__,a,l);return this}function t2(e){return function(){this.removeAttribute(e)}}function r2(e){return function(){this.removeAttributeNS(e.space,e.local)}}function n2(e,t){return function(){this.setAttribute(e,t)}}function i2(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function o2(e,t){return function(){var r=t.apply(this,arguments);r==null?this.removeAttribute(e):this.setAttribute(e,r)}}function l2(e,t){return function(){var r=t.apply(this,arguments);r==null?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,r)}}function xc(e,t){var r=Ht(e);if(arguments.length<2){var i=this.node();return r.local?i.getAttributeNS(r.space,r.local):i.getAttribute(r)}return this.each((t==null?r.local?r2:t2:typeof t=="function"?r.local?l2:o2:r.local?i2:n2)(r,t))}function s0(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function s2(e){return function(){this.style.removeProperty(e)}}function a2(e,t,r){return function(){this.style.setProperty(e,t,r)}}function c2(e,t,r){return function(){var i=t.apply(this,arguments);i==null?this.style.removeProperty(e):this.style.setProperty(e,i,r)}}function yc(e,t,r){return arguments.length>1?this.each((t==null?s2:typeof t=="function"?c2:a2)(e,t,r==null?"":r)):Qt(this.node(),e)}function Qt(e,t){return e.style.getPropertyValue(t)||s0(e).getComputedStyle(e,null).getPropertyValue(t)}function u2(e){return function(){delete this[e]}}function h2(e,t){return function(){this[e]=t}}function f2(e,t){return function(){var r=t.apply(this,arguments);r==null?delete this[e]:this[e]=r}}function bc(e,t){return arguments.length>1?this.each((t==null?u2:typeof t=="function"?f2:h2)(e,t)):this.node()[e]}function vc(e){return e.trim().split(/^|\s+/)}function Qi(e){return e.classList||new wc(e)}function wc(e){this._node=e,this._names=vc(e.getAttribute("class")||"")}wc.prototype={add:function(e){var t=this._names.indexOf(e);t<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};function kc(e,t){for(var r=Qi(e),i=-1,l=t.length;++i<l;)r.add(t[i])}function Sc(e,t){for(var r=Qi(e),i=-1,l=t.length;++i<l;)r.remove(t[i])}function d2(e){return function(){kc(this,e)}}function p2(e){return function(){Sc(this,e)}}function m2(e,t){return function(){(t.apply(this,arguments)?kc:Sc)(this,e)}}function Mc(e,t){var r=vc(e+"");if(arguments.length<2){for(var i=Qi(this.node()),l=-1,a=r.length;++l<a;)if(!i.contains(r[l]))return!1;return!0}return this.each((typeof t=="function"?m2:t?d2:p2)(r,t))}function g2(){this.textContent=""}function x2(e){return function(){this.textContent=e}}function y2(e){return function(){var t=e.apply(this,arguments);this.textContent=t==null?"":t}}function Ac(e){return arguments.length?this.each(e==null?g2:(typeof e=="function"?y2:x2)(e)):this.node().textContent}function b2(){this.innerHTML=""}function v2(e){return function(){this.innerHTML=e}}function w2(e){return function(){var t=e.apply(this,arguments);this.innerHTML=t==null?"":t}}function _c(e){return arguments.length?this.each(e==null?b2:(typeof e=="function"?w2:v2)(e)):this.node().innerHTML}function k2(){this.nextSibling&&this.parentNode.appendChild(this)}function Cc(){return this.each(k2)}function S2(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Tc(){return this.each(S2)}function Nc(e){var t=typeof e=="function"?e:i0(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})}function M2(){return null}function Ec(e,t){var r=typeof e=="function"?e:i0(e),i=t==null?M2:typeof t=="function"?t:dr(t);return this.select(function(){return this.insertBefore(r.apply(this,arguments),i.apply(this,arguments)||null)})}function A2(){var e=this.parentNode;e&&e.removeChild(this)}function zc(){return this.each(A2)}function _2(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function C2(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function Dc(e){return this.select(e?C2:_2)}function Ic(e){return arguments.length?this.property("__data__",e):this.node().__data__}function T2(e){return function(t){e.call(this,t,this.__data__)}}function N2(e){return e.trim().split(/^|\s+/).map(function(t){var r="",i=t.indexOf(".");return i>=0&&(r=t.slice(i+1),t=t.slice(0,i)),{type:t,name:r}})}function E2(e){return function(){var t=this.__on;if(t){for(var r=0,i=-1,l=t.length,a;r<l;++r)a=t[r],(!e.type||a.type===e.type)&&a.name===e.name?this.removeEventListener(a.type,a.listener,a.options):t[++i]=a;++i?t.length=i:delete this.__on}}}function z2(e,t,r){return function(){var i=this.__on,l,a=T2(t);if(i){for(var u=0,h=i.length;u<h;++u)if((l=i[u]).type===e.type&&l.name===e.name){this.removeEventListener(l.type,l.listener,l.options),this.addEventListener(l.type,l.listener=a,l.options=r),l.value=t;return}}this.addEventListener(e.type,a,r),l={type:e.type,name:e.name,value:t,listener:a,options:r},i?i.push(l):this.__on=[l]}}function Bc(e,t,r){var i=N2(e+""),l,a=i.length,u;if(arguments.length<2){var h=this.node().__on;if(h){for(var p=0,b=h.length,y;p<b;++p)for(l=0,y=h[p];l<a;++l)if((u=i[l]).type===y.type&&u.name===y.name)return y.value}return}for(h=t?z2:E2,l=0;l<a;++l)this.each(h(i[l],t,r));return this}function Oc(e,t,r){var i=s0(e),l=i.CustomEvent;typeof l=="function"?l=new l(t,r):(l=i.document.createEvent("Event"),r?(l.initEvent(t,r.bubbles,r.cancelable),l.detail=r.detail):l.initEvent(t,!1,!1)),e.dispatchEvent(l)}function D2(e,t){return function(){return Oc(this,e,t)}}function I2(e,t){return function(){return Oc(this,e,t.apply(this,arguments))}}function Fc(e,t){return this.each((typeof t=="function"?I2:D2)(e,t))}function*Rc(){for(var e=this._groups,t=0,r=e.length;t<r;++t)for(var i=e[t],l=0,a=i.length,u;l<a;++l)(u=i[l])&&(yield u)}var eo=[null];function Ie(e,t){this._groups=e,this._parents=t}function Lc(){return new Ie([[document.documentElement]],eo)}function B2(){return this}Ie.prototype=Lc.prototype={constructor:Ie,select:Ja,selectAll:Qa,selectChild:ec,selectChildren:tc,filter:rc,data:oc,enter:nc,exit:lc,join:sc,merge:ac,selection:B2,order:cc,sort:uc,call:hc,nodes:fc,node:dc,size:pc,empty:mc,each:gc,attr:xc,style:yc,property:bc,classed:Mc,text:Ac,html:_c,raise:Cc,lower:Tc,append:Nc,insert:Ec,remove:zc,clone:Dc,datum:Ic,on:Bc,dispatch:Fc,[Symbol.iterator]:Rc};var $t=Lc;function nt(e){return typeof e=="string"?new Ie([[document.querySelector(e)]],[document.documentElement]):new Ie([[e]],eo)}function qc(e){let t;for(;t=e.sourceEvent;)e=t;return e}function Vt(e,t){if(e=qc(e),t===void 0&&(t=e.currentTarget),t){var r=t.ownerSVGElement||t;if(r.createSVGPoint){var i=r.createSVGPoint();return i.x=e.clientX,i.y=e.clientY,i=i.matrixTransform(t.getScreenCTM().inverse()),[i.x,i.y]}if(t.getBoundingClientRect){var l=t.getBoundingClientRect();return[e.clientX-l.left-t.clientLeft,e.clientY-l.top-t.clientTop]}}return[e.pageX,e.pageY]}var a0={capture:!0,passive:!1};function c0(e){e.preventDefault(),e.stopImmediatePropagation()}function to(e){var t=e.document.documentElement,r=nt(e).on("dragstart.drag",c0,a0);"onselectstart"in t?r.on("selectstart.drag",c0,a0):(t.__noselect=t.style.MozUserSelect,t.style.MozUserSelect="none")}function ro(e,t){var r=e.document.documentElement,i=nt(e).on("dragstart.drag",null);t&&(i.on("click.drag",c0,a0),setTimeout(function(){i.on("click.drag",null)},0)),"onselectstart"in r?i.on("selectstart.drag",null):(r.style.MozUserSelect=r.__noselect,delete r.__noselect)}function u0(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function no(e,t){var r=Object.create(e.prototype);for(var i in t)r[i]=t[i];return r}function an(){}var ln=.7,d0=1/ln,Er="\\s*([+-]?\\d+)\\s*",sn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",zt="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",O2=/^#([0-9a-f]{3,8})$/,F2=new RegExp(`^rgb\\(${Er},${Er},${Er}\\)$`),R2=new RegExp(`^rgb\\(${zt},${zt},${zt}\\)$`),L2=new RegExp(`^rgba\\(${Er},${Er},${Er},${sn}\\)$`),q2=new RegExp(`^rgba\\(${zt},${zt},${zt},${sn}\\)$`),P2=new RegExp(`^hsl\\(${sn},${zt},${zt}\\)$`),H2=new RegExp(`^hsla\\(${sn},${zt},${zt},${sn}\\)$`),Pc={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};u0(an,er,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Hc,formatHex:Hc,formatHex8:$2,formatHsl:V2,formatRgb:$c,toString:$c});function Hc(){return this.rgb().formatHex()}function $2(){return this.rgb().formatHex8()}function V2(){return jc(this).formatHsl()}function $c(){return this.rgb().formatRgb()}function er(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=O2.exec(e))?(r=t[1].length,t=parseInt(t[1],16),r===6?Vc(t):r===3?new it(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):r===8?h0(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):r===4?h0(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=F2.exec(e))?new it(t[1],t[2],t[3],1):(t=R2.exec(e))?new it(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=L2.exec(e))?h0(t[1],t[2],t[3],t[4]):(t=q2.exec(e))?h0(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=P2.exec(e))?Uc(t[1],t[2]/100,t[3]/100,1):(t=H2.exec(e))?Uc(t[1],t[2]/100,t[3]/100,t[4]):Pc.hasOwnProperty(e)?Vc(Pc[e]):e==="transparent"?new it(NaN,NaN,NaN,0):null}function Vc(e){return new it(e>>16&255,e>>8&255,e&255,1)}function h0(e,t,r,i){return i<=0&&(e=t=r=NaN),new it(e,t,r,i)}function G2(e){return e instanceof an||(e=er(e)),e?(e=e.rgb(),new it(e.r,e.g,e.b,e.opacity)):new it}function zr(e,t,r,i){return arguments.length===1?G2(e):new it(e,t,r,i==null?1:i)}function it(e,t,r,i){this.r=+e,this.g=+t,this.b=+r,this.opacity=+i}u0(it,zr,no(an,{brighter(e){return e=e==null?d0:Math.pow(d0,e),new it(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?ln:Math.pow(ln,e),new it(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new it(mr(this.r),mr(this.g),mr(this.b),p0(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Gc,formatHex:Gc,formatHex8:Y2,formatRgb:Yc,toString:Yc}));function Gc(){return`#${pr(this.r)}${pr(this.g)}${pr(this.b)}`}function Y2(){return`#${pr(this.r)}${pr(this.g)}${pr(this.b)}${pr((isNaN(this.opacity)?1:this.opacity)*255)}`}function Yc(){let e=p0(this.opacity);return`${e===1?"rgb(":"rgba("}${mr(this.r)}, ${mr(this.g)}, ${mr(this.b)}${e===1?")":`, ${e})`}`}function p0(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function mr(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function pr(e){return e=mr(e),(e<16?"0":"")+e.toString(16)}function Uc(e,t,r,i){return i<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new kt(e,t,r,i)}function jc(e){if(e instanceof kt)return new kt(e.h,e.s,e.l,e.opacity);if(e instanceof an||(e=er(e)),!e)return new kt;if(e instanceof kt)return e;e=e.rgb();var t=e.r/255,r=e.g/255,i=e.b/255,l=Math.min(t,r,i),a=Math.max(t,r,i),u=NaN,h=a-l,p=(a+l)/2;return h?(t===a?u=(r-i)/h+(r<i)*6:r===a?u=(i-t)/h+2:u=(t-r)/h+4,h/=p<.5?a+l:2-a-l,u*=60):h=p>0&&p<1?0:u,new kt(u,h,p,e.opacity)}function Wc(e,t,r,i){return arguments.length===1?jc(e):new kt(e,t,r,i==null?1:i)}function kt(e,t,r,i){this.h=+e,this.s=+t,this.l=+r,this.opacity=+i}u0(kt,Wc,no(an,{brighter(e){return e=e==null?d0:Math.pow(d0,e),new kt(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?ln:Math.pow(ln,e),new kt(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,i=r+(r<.5?r:1-r)*t,l=2*r-i;return new it(io(e>=240?e-240:e+120,l,i),io(e,l,i),io(e<120?e+240:e-120,l,i),this.opacity)},clamp(){return new kt(Xc(this.h),f0(this.s),f0(this.l),p0(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=p0(this.opacity);return`${e===1?"hsl(":"hsla("}${Xc(this.h)}, ${f0(this.s)*100}%, ${f0(this.l)*100}%${e===1?")":`, ${e})`}`}}));function Xc(e){return e=(e||0)%360,e<0?e+360:e}function f0(e){return Math.max(0,Math.min(1,e||0))}function io(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function oo(e,t,r,i,l){var a=e*e,u=a*e;return((1-3*e+3*a-u)*t+(4-6*a+3*u)*r+(1+3*e+3*a-3*u)*i+u*l)/6}function Kc(e){var t=e.length-1;return function(r){var i=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),l=e[i],a=e[i+1],u=i>0?e[i-1]:2*l-a,h=i<t-1?e[i+2]:2*a-l;return oo((r-i/t)*t,u,l,a,h)}}function Zc(e){var t=e.length;return function(r){var i=Math.floor(((r%=1)<0?++r:r)*t),l=e[(i+t-1)%t],a=e[i%t],u=e[(i+1)%t],h=e[(i+2)%t];return oo((r-i/t)*t,l,a,u,h)}}var lo=e=>()=>e;function U2(e,t){return function(r){return e+r*t}}function X2(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(i){return Math.pow(e+i*t,r)}}function Jc(e){return(e=+e)==1?m0:function(t,r){return r-t?X2(t,r,e):lo(isNaN(t)?r:t)}}function m0(e,t){var r=t-e;return r?U2(e,r):lo(isNaN(e)?t:e)}var g0=function e(t){var r=Jc(t);function i(l,a){var u=r((l=zr(l)).r,(a=zr(a)).r),h=r(l.g,a.g),p=r(l.b,a.b),b=m0(l.opacity,a.opacity);return function(y){return l.r=u(y),l.g=h(y),l.b=p(y),l.opacity=b(y),l+""}}return i.gamma=e,i}(1);function Qc(e){return function(t){var r=t.length,i=new Array(r),l=new Array(r),a=new Array(r),u,h;for(u=0;u<r;++u)h=zr(t[u]),i[u]=h.r||0,l[u]=h.g||0,a[u]=h.b||0;return i=e(i),l=e(l),a=e(a),h.opacity=1,function(p){return h.r=i(p),h.g=l(p),h.b=a(p),h+""}}}var j2=Qc(Kc),W2=Qc(Zc);function xt(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}var ao=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,so=new RegExp(ao.source,"g");function K2(e){return function(){return e}}function Z2(e){return function(t){return e(t)+""}}function co(e,t){var r=ao.lastIndex=so.lastIndex=0,i,l,a,u=-1,h=[],p=[];for(e=e+"",t=t+"";(i=ao.exec(e))&&(l=so.exec(t));)(a=l.index)>r&&(a=t.slice(r,a),h[u]?h[u]+=a:h[++u]=a),(i=i[0])===(l=l[0])?h[u]?h[u]+=l:h[++u]=l:(h[++u]=null,p.push({i:u,x:xt(i,l)})),r=so.lastIndex;return r<t.length&&(a=t.slice(r),h[u]?h[u]+=a:h[++u]=a),h.length<2?p[0]?Z2(p[0].x):K2(t):(t=p.length,function(b){for(var y=0,S;y<t;++y)h[(S=p[y]).i]=S.x(b);return h.join("")})}var eu=180/Math.PI,x0={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function uo(e,t,r,i,l,a){var u,h,p;return(u=Math.sqrt(e*e+t*t))&&(e/=u,t/=u),(p=e*r+t*i)&&(r-=e*p,i-=t*p),(h=Math.sqrt(r*r+i*i))&&(r/=h,i/=h,p/=h),e*i<t*r&&(e=-e,t=-t,p=-p,u=-u),{translateX:l,translateY:a,rotate:Math.atan2(t,e)*eu,skewX:Math.atan(p)*eu,scaleX:u,scaleY:h}}var y0;function tu(e){let t=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?x0:uo(t.a,t.b,t.c,t.d,t.e,t.f)}function ru(e){return e==null?x0:(y0||(y0=document.createElementNS("http://www.w3.org/2000/svg","g")),y0.setAttribute("transform",e),(e=y0.transform.baseVal.consolidate())?(e=e.matrix,uo(e.a,e.b,e.c,e.d,e.e,e.f)):x0)}function nu(e,t,r,i){function l(b){return b.length?b.pop()+" ":""}function a(b,y,S,C,T,F){if(b!==S||y!==C){var z=T.push("translate(",null,t,null,r);F.push({i:z-4,x:xt(b,S)},{i:z-2,x:xt(y,C)})}else(S||C)&&T.push("translate("+S+t+C+r)}function u(b,y,S,C){b!==y?(b-y>180?y+=360:y-b>180&&(b+=360),C.push({i:S.push(l(S)+"rotate(",null,i)-2,x:xt(b,y)})):y&&S.push(l(S)+"rotate("+y+i)}function h(b,y,S,C){b!==y?C.push({i:S.push(l(S)+"skewX(",null,i)-2,x:xt(b,y)}):y&&S.push(l(S)+"skewX("+y+i)}function p(b,y,S,C,T,F){if(b!==S||y!==C){var z=T.push(l(T)+"scale(",null,",",null,")");F.push({i:z-4,x:xt(b,S)},{i:z-2,x:xt(y,C)})}else(S!==1||C!==1)&&T.push(l(T)+"scale("+S+","+C+")")}return function(b,y){var S=[],C=[];return b=e(b),y=e(y),a(b.translateX,b.translateY,y.translateX,y.translateY,S,C),u(b.rotate,y.rotate,S,C),h(b.skewX,y.skewX,S,C),p(b.scaleX,b.scaleY,y.scaleX,y.scaleY,S,C),b=y=null,function(T){for(var F=-1,z=C.length,q;++F<z;)S[(q=C[F]).i]=q.x(T);return S.join("")}}}var ho=nu(tu,"px, ","px)","deg)"),fo=nu(ru,", ",")",")");var J2=1e-12;function iu(e){return((e=Math.exp(e))+1/e)/2}function Q2(e){return((e=Math.exp(e))-1/e)/2}function e5(e){return((e=Math.exp(2*e))-1)/(e+1)}var po=function e(t,r,i){function l(a,u){var h=a[0],p=a[1],b=a[2],y=u[0],S=u[1],C=u[2],T=y-h,F=S-p,z=T*T+F*F,q,L;if(z<J2)L=Math.log(C/b)/t,q=function(Se){return[h+Se*T,p+Se*F,b*Math.exp(t*Se*L)]};else{var X=Math.sqrt(z),re=(C*C-b*b+i*z)/(2*b*r*X),ce=(C*C-b*b-i*z)/(2*C*r*X),me=Math.log(Math.sqrt(re*re+1)-re),be=Math.log(Math.sqrt(ce*ce+1)-ce);L=(be-me)/t,q=function(Se){var $e=Se*L,ze=iu(me),Ve=b/(r*X)*(ze*e5(t*$e+me)-Q2(me));return[h+Ve*T,p+Ve*F,b*ze/iu(t*$e+me)]}}return q.duration=L*1e3*t/Math.SQRT2,q}return l.rho=function(a){var u=Math.max(.001,+a),h=u*u,p=h*h;return e(u,h,p)},l}(Math.SQRT2,2,4);var Dr=0,un=0,cn=0,lu=1e3,b0,hn,v0=0,gr=0,w0=0,fn=typeof performance=="object"&&performance.now?performance:Date,su=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function pn(){return gr||(su(t5),gr=fn.now()+w0)}function t5(){gr=0}function dn(){this._call=this._time=this._next=null}dn.prototype=k0.prototype={constructor:dn,restart:function(e,t,r){if(typeof e!="function")throw new TypeError("callback is not a function");r=(r==null?pn():+r)+(t==null?0:+t),!this._next&&hn!==this&&(hn?hn._next=this:b0=this,hn=this),this._call=e,this._time=r,mo()},stop:function(){this._call&&(this._call=null,this._time=1/0,mo())}};function k0(e,t,r){var i=new dn;return i.restart(e,t,r),i}function au(){pn(),++Dr;for(var e=b0,t;e;)(t=gr-e._time)>=0&&e._call.call(void 0,t),e=e._next;--Dr}function ou(){gr=(v0=fn.now())+w0,Dr=un=0;try{au()}finally{Dr=0,n5(),gr=0}}function r5(){var e=fn.now(),t=e-v0;t>lu&&(w0-=t,v0=e)}function n5(){for(var e,t=b0,r,i=1/0;t;)t._call?(i>t._time&&(i=t._time),e=t,t=t._next):(r=t._next,t._next=null,t=e?e._next=r:b0=r);hn=e,mo(i)}function mo(e){if(!Dr){un&&(un=clearTimeout(un));var t=e-gr;t>24?(e<1/0&&(un=setTimeout(ou,e-fn.now()-w0)),cn&&(cn=clearInterval(cn))):(cn||(v0=fn.now(),cn=setInterval(r5,lu)),Dr=1,su(ou))}}function S0(e,t,r){var i=new dn;return t=t==null?0:+t,i.restart(l=>{i.stop(),e(l+t)},t,r),i}var i5=tn("start","end","cancel","interrupt"),o5=[],hu=0,cu=1,A0=2,M0=3,uu=4,_0=5,mn=6;function tr(e,t,r,i,l,a){var u=e.__transition;if(!u)e.__transition={};else if(r in u)return;l5(e,r,{name:t,index:i,group:l,on:i5,tween:o5,time:a.time,delay:a.delay,duration:a.duration,ease:a.ease,timer:null,state:hu})}function gn(e,t){var r=He(e,t);if(r.state>hu)throw new Error("too late; already scheduled");return r}function Ye(e,t){var r=He(e,t);if(r.state>M0)throw new Error("too late; already running");return r}function He(e,t){var r=e.__transition;if(!r||!(r=r[t]))throw new Error("transition not found");return r}function l5(e,t,r){var i=e.__transition,l;i[t]=r,r.timer=k0(a,0,r.time);function a(b){r.state=cu,r.timer.restart(u,r.delay,r.time),r.delay<=b&&u(b-r.delay)}function u(b){var y,S,C,T;if(r.state!==cu)return p();for(y in i)if(T=i[y],T.name===r.name){if(T.state===M0)return S0(u);T.state===uu?(T.state=mn,T.timer.stop(),T.on.call("interrupt",e,e.__data__,T.index,T.group),delete i[y]):+y<t&&(T.state=mn,T.timer.stop(),T.on.call("cancel",e,e.__data__,T.index,T.group),delete i[y])}if(S0(function(){r.state===M0&&(r.state=uu,r.timer.restart(h,r.delay,r.time),h(b))}),r.state=A0,r.on.call("start",e,e.__data__,r.index,r.group),r.state===A0){for(r.state=M0,l=new Array(C=r.tween.length),y=0,S=-1;y<C;++y)(T=r.tween[y].value.call(e,e.__data__,r.index,r.group))&&(l[++S]=T);l.length=S+1}}function h(b){for(var y=b<r.duration?r.ease.call(null,b/r.duration):(r.timer.restart(p),r.state=_0,1),S=-1,C=l.length;++S<C;)l[S].call(e,y);r.state===_0&&(r.on.call("end",e,e.__data__,r.index,r.group),p())}function p(){r.state=mn,r.timer.stop(),delete i[t];for(var b in i)return;delete e.__transition}}function rr(e,t){var r=e.__transition,i,l,a=!0,u;if(r){t=t==null?null:t+"";for(u in r){if((i=r[u]).name!==t){a=!1;continue}l=i.state>A0&&i.state<_0,i.state=mn,i.timer.stop(),i.on.call(l?"interrupt":"cancel",e,e.__data__,i.index,i.group),delete r[u]}a&&delete e.__transition}}function fu(e){return this.each(function(){rr(this,e)})}function s5(e,t){var r,i;return function(){var l=Ye(this,e),a=l.tween;if(a!==r){i=r=a;for(var u=0,h=i.length;u<h;++u)if(i[u].name===t){i=i.slice(),i.splice(u,1);break}}l.tween=i}}function a5(e,t,r){var i,l;if(typeof r!="function")throw new Error;return function(){var a=Ye(this,e),u=a.tween;if(u!==i){l=(i=u).slice();for(var h={name:t,value:r},p=0,b=l.length;p<b;++p)if(l[p].name===t){l[p]=h;break}p===b&&l.push(h)}a.tween=l}}function du(e,t){var r=this._id;if(e+="",arguments.length<2){for(var i=He(this.node(),r).tween,l=0,a=i.length,u;l<a;++l)if((u=i[l]).name===e)return u.value;return null}return this.each((t==null?s5:a5)(r,e,t))}function Ir(e,t,r){var i=e._id;return e.each(function(){var l=Ye(this,i);(l.value||(l.value={}))[t]=r.apply(this,arguments)}),function(l){return He(l,i).value[t]}}function C0(e,t){var r;return(typeof t=="number"?xt:t instanceof er?g0:(r=er(t))?(t=r,g0):co)(e,t)}function c5(e){return function(){this.removeAttribute(e)}}function u5(e){return function(){this.removeAttributeNS(e.space,e.local)}}function h5(e,t,r){var i,l=r+"",a;return function(){var u=this.getAttribute(e);return u===l?null:u===i?a:a=t(i=u,r)}}function f5(e,t,r){var i,l=r+"",a;return function(){var u=this.getAttributeNS(e.space,e.local);return u===l?null:u===i?a:a=t(i=u,r)}}function d5(e,t,r){var i,l,a;return function(){var u,h=r(this),p;return h==null?void this.removeAttribute(e):(u=this.getAttribute(e),p=h+"",u===p?null:u===i&&p===l?a:(l=p,a=t(i=u,h)))}}function p5(e,t,r){var i,l,a;return function(){var u,h=r(this),p;return h==null?void this.removeAttributeNS(e.space,e.local):(u=this.getAttributeNS(e.space,e.local),p=h+"",u===p?null:u===i&&p===l?a:(l=p,a=t(i=u,h)))}}function pu(e,t){var r=Ht(e),i=r==="transform"?fo:C0;return this.attrTween(e,typeof t=="function"?(r.local?p5:d5)(r,i,Ir(this,"attr."+e,t)):t==null?(r.local?u5:c5)(r):(r.local?f5:h5)(r,i,t))}function m5(e,t){return function(r){this.setAttribute(e,t.call(this,r))}}function g5(e,t){return function(r){this.setAttributeNS(e.space,e.local,t.call(this,r))}}function x5(e,t){var r,i;function l(){var a=t.apply(this,arguments);return a!==i&&(r=(i=a)&&g5(e,a)),r}return l._value=t,l}function y5(e,t){var r,i;function l(){var a=t.apply(this,arguments);return a!==i&&(r=(i=a)&&m5(e,a)),r}return l._value=t,l}function mu(e,t){var r="attr."+e;if(arguments.length<2)return(r=this.tween(r))&&r._value;if(t==null)return this.tween(r,null);if(typeof t!="function")throw new Error;var i=Ht(e);return this.tween(r,(i.local?x5:y5)(i,t))}function b5(e,t){return function(){gn(this,e).delay=+t.apply(this,arguments)}}function v5(e,t){return t=+t,function(){gn(this,e).delay=t}}function gu(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?b5:v5)(t,e)):He(this.node(),t).delay}function w5(e,t){return function(){Ye(this,e).duration=+t.apply(this,arguments)}}function k5(e,t){return t=+t,function(){Ye(this,e).duration=t}}function xu(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?w5:k5)(t,e)):He(this.node(),t).duration}function S5(e,t){if(typeof t!="function")throw new Error;return function(){Ye(this,e).ease=t}}function yu(e){var t=this._id;return arguments.length?this.each(S5(t,e)):He(this.node(),t).ease}function M5(e,t){return function(){var r=t.apply(this,arguments);if(typeof r!="function")throw new Error;Ye(this,e).ease=r}}function bu(e){if(typeof e!="function")throw new Error;return this.each(M5(this._id,e))}function vu(e){typeof e!="function"&&(e=nn(e));for(var t=this._groups,r=t.length,i=new Array(r),l=0;l<r;++l)for(var a=t[l],u=a.length,h=i[l]=[],p,b=0;b<u;++b)(p=a[b])&&e.call(p,p.__data__,b,a)&&h.push(p);return new Ze(i,this._parents,this._name,this._id)}function wu(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,r=e._groups,i=t.length,l=r.length,a=Math.min(i,l),u=new Array(i),h=0;h<a;++h)for(var p=t[h],b=r[h],y=p.length,S=u[h]=new Array(y),C,T=0;T<y;++T)(C=p[T]||b[T])&&(S[T]=C);for(;h<i;++h)u[h]=t[h];return new Ze(u,this._parents,this._name,this._id)}function A5(e){return(e+"").trim().split(/^|\s+/).every(function(t){var r=t.indexOf(".");return r>=0&&(t=t.slice(0,r)),!t||t==="start"})}function _5(e,t,r){var i,l,a=A5(t)?gn:Ye;return function(){var u=a(this,e),h=u.on;h!==i&&(l=(i=h).copy()).on(t,r),u.on=l}}function ku(e,t){var r=this._id;return arguments.length<2?He(this.node(),r).on.on(e):this.each(_5(r,e,t))}function C5(e){return function(){var t=this.parentNode;for(var r in this.__transition)if(+r!==e)return;t&&t.removeChild(this)}}function Su(){return this.on("end.remove",C5(this._id))}function Mu(e){var t=this._name,r=this._id;typeof e!="function"&&(e=dr(e));for(var i=this._groups,l=i.length,a=new Array(l),u=0;u<l;++u)for(var h=i[u],p=h.length,b=a[u]=new Array(p),y,S,C=0;C<p;++C)(y=h[C])&&(S=e.call(y,y.__data__,C,h))&&("__data__"in y&&(S.__data__=y.__data__),b[C]=S,tr(b[C],t,r,C,b,He(y,r)));return new Ze(a,this._parents,t,r)}function Au(e){var t=this._name,r=this._id;typeof e!="function"&&(e=rn(e));for(var i=this._groups,l=i.length,a=[],u=[],h=0;h<l;++h)for(var p=i[h],b=p.length,y,S=0;S<b;++S)if(y=p[S]){for(var C=e.call(y,y.__data__,S,p),T,F=He(y,r),z=0,q=C.length;z<q;++z)(T=C[z])&&tr(T,t,r,z,C,F);a.push(C),u.push(y)}return new Ze(a,u,t,r)}var T5=$t.prototype.constructor;function _u(){return new T5(this._groups,this._parents)}function N5(e,t){var r,i,l;return function(){var a=Qt(this,e),u=(this.style.removeProperty(e),Qt(this,e));return a===u?null:a===r&&u===i?l:l=t(r=a,i=u)}}function Cu(e){return function(){this.style.removeProperty(e)}}function E5(e,t,r){var i,l=r+"",a;return function(){var u=Qt(this,e);return u===l?null:u===i?a:a=t(i=u,r)}}function z5(e,t,r){var i,l,a;return function(){var u=Qt(this,e),h=r(this),p=h+"";return h==null&&(p=h=(this.style.removeProperty(e),Qt(this,e))),u===p?null:u===i&&p===l?a:(l=p,a=t(i=u,h))}}function D5(e,t){var r,i,l,a="style."+t,u="end."+a,h;return function(){var p=Ye(this,e),b=p.on,y=p.value[a]==null?h||(h=Cu(t)):void 0;(b!==r||l!==y)&&(i=(r=b).copy()).on(u,l=y),p.on=i}}function Tu(e,t,r){var i=(e+="")=="transform"?ho:C0;return t==null?this.styleTween(e,N5(e,i)).on("end.style."+e,Cu(e)):typeof t=="function"?this.styleTween(e,z5(e,i,Ir(this,"style."+e,t))).each(D5(this._id,e)):this.styleTween(e,E5(e,i,t),r).on("end.style."+e,null)}function I5(e,t,r){return function(i){this.style.setProperty(e,t.call(this,i),r)}}function B5(e,t,r){var i,l;function a(){var u=t.apply(this,arguments);return u!==l&&(i=(l=u)&&I5(e,u,r)),i}return a._value=t,a}function Nu(e,t,r){var i="style."+(e+="");if(arguments.length<2)return(i=this.tween(i))&&i._value;if(t==null)return this.tween(i,null);if(typeof t!="function")throw new Error;return this.tween(i,B5(e,t,r==null?"":r))}function O5(e){return function(){this.textContent=e}}function F5(e){return function(){var t=e(this);this.textContent=t==null?"":t}}function Eu(e){return this.tween("text",typeof e=="function"?F5(Ir(this,"text",e)):O5(e==null?"":e+""))}function R5(e){return function(t){this.textContent=e.call(this,t)}}function L5(e){var t,r;function i(){var l=e.apply(this,arguments);return l!==r&&(t=(r=l)&&R5(l)),t}return i._value=e,i}function zu(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(e==null)return this.tween(t,null);if(typeof e!="function")throw new Error;return this.tween(t,L5(e))}function Du(){for(var e=this._name,t=this._id,r=T0(),i=this._groups,l=i.length,a=0;a<l;++a)for(var u=i[a],h=u.length,p,b=0;b<h;++b)if(p=u[b]){var y=He(p,t);tr(p,e,r,b,u,{time:y.time+y.delay+y.duration,delay:0,duration:y.duration,ease:y.ease})}return new Ze(i,this._parents,e,r)}function Iu(){var e,t,r=this,i=r._id,l=r.size();return new Promise(function(a,u){var h={value:u},p={value:function(){--l===0&&a()}};r.each(function(){var b=Ye(this,i),y=b.on;y!==e&&(t=(e=y).copy(),t._.cancel.push(h),t._.interrupt.push(h),t._.end.push(p)),b.on=t}),l===0&&a()})}var q5=0;function Ze(e,t,r,i){this._groups=e,this._parents=t,this._name=r,this._id=i}function Bu(e){return $t().transition(e)}function T0(){return++q5}var Gt=$t.prototype;Ze.prototype=Bu.prototype={constructor:Ze,select:Mu,selectAll:Au,selectChild:Gt.selectChild,selectChildren:Gt.selectChildren,filter:vu,merge:wu,selection:_u,transition:Du,call:Gt.call,nodes:Gt.nodes,node:Gt.node,size:Gt.size,empty:Gt.empty,each:Gt.each,on:ku,attr:pu,attrTween:mu,style:Tu,styleTween:Nu,text:Eu,textTween:zu,remove:Su,tween:du,delay:gu,duration:xu,ease:yu,easeVarying:bu,end:Iu,[Symbol.iterator]:Gt[Symbol.iterator]};function N0(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}var P5={time:null,delay:0,duration:250,ease:N0};function H5(e,t){for(var r;!(r=e.__transition)||!(r=r[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return r}function Ou(e){var t,r;e instanceof Ze?(t=e._id,e=e._name):(t=T0(),(r=P5).time=pn(),e=e==null?null:e+"");for(var i=this._groups,l=i.length,a=0;a<l;++a)for(var u=i[a],h=u.length,p,b=0;b<h;++b)(p=u[b])&&tr(p,e,t,b,u,r||H5(p,t));return new Ze(i,this._parents,e,t)}$t.prototype.interrupt=fu;$t.prototype.transition=Ou;var{abs:Ux,max:Xx,min:jx}=Math;function Fu(e){return[+e[0],+e[1]]}function $5(e){return[Fu(e[0]),Fu(e[1])]}var Wx={name:"x",handles:["w","e"].map(go),input:function(e,t){return e==null?null:[[+e[0],t[0][1]],[+e[1],t[1][1]]]},output:function(e){return e&&[e[0][0],e[1][0]]}},Kx={name:"y",handles:["n","s"].map(go),input:function(e,t){return e==null?null:[[t[0][0],+e[0]],[t[1][0],+e[1]]]},output:function(e){return e&&[e[0][1],e[1][1]]}},Zx={name:"xy",handles:["n","w","e","s","nw","ne","sw","se"].map(go),input:function(e){return e==null?null:$5(e)},output:function(e){return e}};function go(e){return{type:e}}var xo=Math.PI,yo=2*xo,xr=1e-6,V5=yo-xr;function Ru(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}function G5(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return Ru;let r=10**t;return function(i){this._+=i[0];for(let l=1,a=i.length;l<a;++l)this._+=Math.round(arguments[l]*r)/r+i[l]}}var yr=class{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=t==null?Ru:G5(t)}moveTo(t,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,r){this._append`L${this._x1=+t},${this._y1=+r}`}quadraticCurveTo(t,r,i,l){this._append`Q${+t},${+r},${this._x1=+i},${this._y1=+l}`}bezierCurveTo(t,r,i,l,a,u){this._append`C${+t},${+r},${+i},${+l},${this._x1=+a},${this._y1=+u}`}arcTo(t,r,i,l,a){if(t=+t,r=+r,i=+i,l=+l,a=+a,a<0)throw new Error(`negative radius: ${a}`);let u=this._x1,h=this._y1,p=i-t,b=l-r,y=u-t,S=h-r,C=y*y+S*S;if(this._x1===null)this._append`M${this._x1=t},${this._y1=r}`;else if(C>xr)if(!(Math.abs(S*p-b*y)>xr)||!a)this._append`L${this._x1=t},${this._y1=r}`;else{let T=i-u,F=l-h,z=p*p+b*b,q=T*T+F*F,L=Math.sqrt(z),X=Math.sqrt(C),re=a*Math.tan((xo-Math.acos((z+C-q)/(2*L*X)))/2),ce=re/X,me=re/L;Math.abs(ce-1)>xr&&this._append`L${t+ce*y},${r+ce*S}`,this._append`A${a},${a},0,0,${+(S*T>y*F)},${this._x1=t+me*p},${this._y1=r+me*b}`}}arc(t,r,i,l,a,u){if(t=+t,r=+r,i=+i,u=!!u,i<0)throw new Error(`negative radius: ${i}`);let h=i*Math.cos(l),p=i*Math.sin(l),b=t+h,y=r+p,S=1^u,C=u?l-a:a-l;this._x1===null?this._append`M${b},${y}`:(Math.abs(this._x1-b)>xr||Math.abs(this._y1-y)>xr)&&this._append`L${b},${y}`,i&&(C<0&&(C=C%yo+yo),C>V5?this._append`A${i},${i},0,1,${S},${t-h},${r-p}A${i},${i},0,1,${S},${this._x1=b},${this._y1=y}`:C>xr&&this._append`A${i},${i},0,${+(C>=xo)},${S},${this._x1=t+i*Math.cos(a)},${this._y1=r+i*Math.sin(a)}`)}rect(t,r,i,l){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}h${i=+i}v${+l}h${-i}Z`}toString(){return this._}};function Lu(){return new yr}Lu.prototype=yr.prototype;function qu(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e);break}return this}var bo=Symbol("implicit");function xn(){var e=new Nr,t=[],r=[],i=bo;function l(a){let u=e.get(a);if(u===void 0){if(i!==bo)return i;e.set(a,u=t.push(a)-1)}return r[u%r.length]}return l.domain=function(a){if(!arguments.length)return t.slice();t=[],e=new Nr;for(let u of a)e.has(u)||e.set(u,t.push(u)-1);return l},l.range=function(a){return arguments.length?(r=Array.from(a),l):r.slice()},l.unknown=function(a){return arguments.length?(i=a,l):i},l.copy=function(){return xn(t,r).unknown(i)},qu.apply(l,arguments),l}function Pu(e){for(var t=e.length/6|0,r=new Array(t),i=0;i<t;)r[i]="#"+e.slice(i*6,++i*6);return r}var vo=Pu("1f77b4ff7f0e2ca02cd627289467bd8c564be377c27f7f7fbcbd2217becf");function wo(e){return function(){return e}}function Hu(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(r==null)t=null;else{let i=Math.floor(r);if(!(i>=0))throw new RangeError(`invalid digits: ${r}`);t=i}return e},()=>new yr(t)}var $u=Array.prototype.slice;function Vu(e){return e[0]}function Gu(e){return e[1]}var ko=class{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}};function Yu(e){return new ko(e,!0)}function Y5(e){return e.source}function U5(e){return e.target}function Uu(e){let t=Y5,r=U5,i=Vu,l=Gu,a=null,u=null,h=Hu(p);function p(){let b,y=$u.call(arguments),S=t.apply(this,y),C=r.apply(this,y);if(a==null&&(u=e(b=h())),u.lineStart(),y[0]=S,u.point(+i.apply(this,y),+l.apply(this,y)),y[0]=C,u.point(+i.apply(this,y),+l.apply(this,y)),u.lineEnd(),b)return u=null,b+""||null}return p.source=function(b){return arguments.length?(t=b,p):t},p.target=function(b){return arguments.length?(r=b,p):r},p.x=function(b){return arguments.length?(i=typeof b=="function"?b:wo(+b),p):i},p.y=function(b){return arguments.length?(l=typeof b=="function"?b:wo(+b),p):l},p.context=function(b){return arguments.length?(b==null?a=u=null:u=e(a=b),p):a},p}function So(){return Uu(Yu)}var yn=e=>()=>e;function Mo(e,{sourceEvent:t,target:r,transform:i,dispatch:l}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},transform:{value:i,enumerable:!0,configurable:!0},_:{value:l}})}function St(e,t,r){this.k=e,this.x=t,this.y=r}St.prototype={constructor:St,scale:function(e){return e===1?this:new St(this.k*e,this.x,this.y)},translate:function(e,t){return e===0&t===0?this:new St(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var br=new St(1,0,0);vr.prototype=St.prototype;function vr(e){for(;!e.__zoom;)if(!(e=e.parentNode))return br;return e.__zoom}function E0(e){e.stopImmediatePropagation()}function Br(e){e.preventDefault(),e.stopImmediatePropagation()}function X5(e){return(!e.ctrlKey||e.type==="wheel")&&!e.button}function j5(){var e=this;return e instanceof SVGElement?(e=e.ownerSVGElement||e,e.hasAttribute("viewBox")?(e=e.viewBox.baseVal,[[e.x,e.y],[e.x+e.width,e.y+e.height]]):[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]):[[0,0],[e.clientWidth,e.clientHeight]]}function Xu(){return this.__zoom||br}function W5(e){return-e.deltaY*(e.deltaMode===1?.05:e.deltaMode?1:.002)*(e.ctrlKey?10:1)}function K5(){return navigator.maxTouchPoints||"ontouchstart"in this}function Z5(e,t,r){var i=e.invertX(t[0][0])-r[0][0],l=e.invertX(t[1][0])-r[1][0],a=e.invertY(t[0][1])-r[0][1],u=e.invertY(t[1][1])-r[1][1];return e.translate(l>i?(i+l)/2:Math.min(0,i)||Math.max(0,l),u>a?(a+u)/2:Math.min(0,a)||Math.max(0,u))}function Ao(){var e=X5,t=j5,r=Z5,i=W5,l=K5,a=[0,1/0],u=[[-1/0,-1/0],[1/0,1/0]],h=250,p=po,b=tn("start","zoom","end"),y,S,C,T=500,F=150,z=0,q=10;function L(I){I.property("__zoom",Xu).on("wheel.zoom",$e,{passive:!1}).on("mousedown.zoom",ze).on("dblclick.zoom",Ve).filter(l).on("touchstart.zoom",Oe).on("touchmove.zoom",Xe).on("touchend.zoom touchcancel.zoom",P).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}L.transform=function(I,W,G,ee){var oe=I.selection?I.selection():I;oe.property("__zoom",Xu),I!==oe?me(I,W,G,ee):oe.interrupt().each(function(){be(this,arguments).event(ee).start().zoom(null,typeof W=="function"?W.apply(this,arguments):W).end()})},L.scaleBy=function(I,W,G,ee){L.scaleTo(I,function(){var oe=this.__zoom.k,Y=typeof W=="function"?W.apply(this,arguments):W;return oe*Y},G,ee)},L.scaleTo=function(I,W,G,ee){L.transform(I,function(){var oe=t.apply(this,arguments),Y=this.__zoom,se=G==null?ce(oe):typeof G=="function"?G.apply(this,arguments):G,xe=Y.invert(se),ve=typeof W=="function"?W.apply(this,arguments):W;return r(re(X(Y,ve),se,xe),oe,u)},G,ee)},L.translateBy=function(I,W,G,ee){L.transform(I,function(){return r(this.__zoom.translate(typeof W=="function"?W.apply(this,arguments):W,typeof G=="function"?G.apply(this,arguments):G),t.apply(this,arguments),u)},null,ee)},L.translateTo=function(I,W,G,ee,oe){L.transform(I,function(){var Y=t.apply(this,arguments),se=this.__zoom,xe=ee==null?ce(Y):typeof ee=="function"?ee.apply(this,arguments):ee;return r(br.translate(xe[0],xe[1]).scale(se.k).translate(typeof W=="function"?-W.apply(this,arguments):-W,typeof G=="function"?-G.apply(this,arguments):-G),Y,u)},ee,oe)};function X(I,W){return W=Math.max(a[0],Math.min(a[1],W)),W===I.k?I:new St(W,I.x,I.y)}function re(I,W,G){var ee=W[0]-G[0]*I.k,oe=W[1]-G[1]*I.k;return ee===I.x&&oe===I.y?I:new St(I.k,ee,oe)}function ce(I){return[(+I[0][0]+ +I[1][0])/2,(+I[0][1]+ +I[1][1])/2]}function me(I,W,G,ee){I.on("start.zoom",function(){be(this,arguments).event(ee).start()}).on("interrupt.zoom end.zoom",function(){be(this,arguments).event(ee).end()}).tween("zoom",function(){var oe=this,Y=arguments,se=be(oe,Y).event(ee),xe=t.apply(oe,Y),ve=G==null?ce(xe):typeof G=="function"?G.apply(oe,Y):G,ot=Math.max(xe[1][0]-xe[0][0],xe[1][1]-xe[0][1]),Me=oe.__zoom,lt=typeof W=="function"?W.apply(oe,Y):W,yt=p(Me.invert(ve).concat(ot/Me.k),lt.invert(ve).concat(ot/lt.k));return function(st){if(st===1)st=lt;else{var bt=yt(st),Or=ot/bt[2];st=new St(Or,ve[0]-bt[0]*Or,ve[1]-bt[1]*Or)}se.zoom(null,st)}})}function be(I,W,G){return!G&&I.__zooming||new Se(I,W)}function Se(I,W){this.that=I,this.args=W,this.active=0,this.sourceEvent=null,this.extent=t.apply(I,W),this.taps=0}Se.prototype={event:function(I){return I&&(this.sourceEvent=I),this},start:function(){return++this.active===1&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(I,W){return this.mouse&&I!=="mouse"&&(this.mouse[1]=W.invert(this.mouse[0])),this.touch0&&I!=="touch"&&(this.touch0[1]=W.invert(this.touch0[0])),this.touch1&&I!=="touch"&&(this.touch1[1]=W.invert(this.touch1[0])),this.that.__zoom=W,this.emit("zoom"),this},end:function(){return--this.active===0&&(delete this.that.__zooming,this.emit("end")),this},emit:function(I){var W=nt(this.that).datum();b.call(I,this.that,new Mo(I,{sourceEvent:this.sourceEvent,target:L,type:I,transform:this.that.__zoom,dispatch:b}),W)}};function $e(I,...W){if(!e.apply(this,arguments))return;var G=be(this,W).event(I),ee=this.__zoom,oe=Math.max(a[0],Math.min(a[1],ee.k*Math.pow(2,i.apply(this,arguments)))),Y=Vt(I);if(G.wheel)(G.mouse[0][0]!==Y[0]||G.mouse[0][1]!==Y[1])&&(G.mouse[1]=ee.invert(G.mouse[0]=Y)),clearTimeout(G.wheel);else{if(ee.k===oe)return;G.mouse=[Y,ee.invert(Y)],rr(this),G.start()}Br(I),G.wheel=setTimeout(se,F),G.zoom("mouse",r(re(X(ee,oe),G.mouse[0],G.mouse[1]),G.extent,u));function se(){G.wheel=null,G.end()}}function ze(I,...W){if(C||!e.apply(this,arguments))return;var G=I.currentTarget,ee=be(this,W,!0).event(I),oe=nt(I.view).on("mousemove.zoom",ve,!0).on("mouseup.zoom",ot,!0),Y=Vt(I,G),se=I.clientX,xe=I.clientY;to(I.view),E0(I),ee.mouse=[Y,this.__zoom.invert(Y)],rr(this),ee.start();function ve(Me){if(Br(Me),!ee.moved){var lt=Me.clientX-se,yt=Me.clientY-xe;ee.moved=lt*lt+yt*yt>z}ee.event(Me).zoom("mouse",r(re(ee.that.__zoom,ee.mouse[0]=Vt(Me,G),ee.mouse[1]),ee.extent,u))}function ot(Me){oe.on("mousemove.zoom mouseup.zoom",null),ro(Me.view,ee.moved),Br(Me),ee.event(Me).end()}}function Ve(I,...W){if(e.apply(this,arguments)){var G=this.__zoom,ee=Vt(I.changedTouches?I.changedTouches[0]:I,this),oe=G.invert(ee),Y=G.k*(I.shiftKey?.5:2),se=r(re(X(G,Y),ee,oe),t.apply(this,W),u);Br(I),h>0?nt(this).transition().duration(h).call(me,se,ee,I):nt(this).call(L.transform,se,ee,I)}}function Oe(I,...W){if(e.apply(this,arguments)){var G=I.touches,ee=G.length,oe=be(this,W,I.changedTouches.length===ee).event(I),Y,se,xe,ve;for(E0(I),se=0;se<ee;++se)xe=G[se],ve=Vt(xe,this),ve=[ve,this.__zoom.invert(ve),xe.identifier],oe.touch0?!oe.touch1&&oe.touch0[2]!==ve[2]&&(oe.touch1=ve,oe.taps=0):(oe.touch0=ve,Y=!0,oe.taps=1+!!y);y&&(y=clearTimeout(y)),Y&&(oe.taps<2&&(S=ve[0],y=setTimeout(function(){y=null},T)),rr(this),oe.start())}}function Xe(I,...W){if(this.__zooming){var G=be(this,W).event(I),ee=I.changedTouches,oe=ee.length,Y,se,xe,ve;for(Br(I),Y=0;Y<oe;++Y)se=ee[Y],xe=Vt(se,this),G.touch0&&G.touch0[2]===se.identifier?G.touch0[0]=xe:G.touch1&&G.touch1[2]===se.identifier&&(G.touch1[0]=xe);if(se=G.that.__zoom,G.touch1){var ot=G.touch0[0],Me=G.touch0[1],lt=G.touch1[0],yt=G.touch1[1],st=(st=lt[0]-ot[0])*st+(st=lt[1]-ot[1])*st,bt=(bt=yt[0]-Me[0])*bt+(bt=yt[1]-Me[1])*bt;se=X(se,Math.sqrt(st/bt)),xe=[(ot[0]+lt[0])/2,(ot[1]+lt[1])/2],ve=[(Me[0]+yt[0])/2,(Me[1]+yt[1])/2]}else if(G.touch0)xe=G.touch0[0],ve=G.touch0[1];else return;G.zoom("touch",r(re(se,xe,ve),G.extent,u))}}function P(I,...W){if(this.__zooming){var G=be(this,W).event(I),ee=I.changedTouches,oe=ee.length,Y,se;for(E0(I),C&&clearTimeout(C),C=setTimeout(function(){C=null},T),Y=0;Y<oe;++Y)se=ee[Y],G.touch0&&G.touch0[2]===se.identifier?delete G.touch0:G.touch1&&G.touch1[2]===se.identifier&&delete G.touch1;if(G.touch1&&!G.touch0&&(G.touch0=G.touch1,delete G.touch1),G.touch0)G.touch0[1]=this.__zoom.invert(G.touch0[0]);else if(G.end(),G.taps===2&&(se=Vt(se,this),Math.hypot(S[0]-se[0],S[1]-se[1])<q)){var xe=nt(this).on("dblclick.zoom");xe&&xe.apply(this,arguments)}}}return L.wheelDelta=function(I){return arguments.length?(i=typeof I=="function"?I:yn(+I),L):i},L.filter=function(I){return arguments.length?(e=typeof I=="function"?I:yn(!!I),L):e},L.touchable=function(I){return arguments.length?(l=typeof I=="function"?I:yn(!!I),L):l},L.extent=function(I){return arguments.length?(t=typeof I=="function"?I:yn([[+I[0][0],+I[0][1]],[+I[1][0],+I[1][1]]]),L):t},L.scaleExtent=function(I){return arguments.length?(a[0]=+I[0],a[1]=+I[1],L):[a[0],a[1]]},L.translateExtent=function(I){return arguments.length?(u[0][0]=+I[0][0],u[1][0]=+I[1][0],u[0][1]=+I[0][1],u[1][1]=+I[1][1],L):[[u[0][0],u[0][1]],[u[1][0],u[1][1]]]},L.constrain=function(I){return arguments.length?(r=I,L):r},L.duration=function(I){return arguments.length?(h=+I,L):h},L.interpolate=function(I){return arguments.length?(p=I,L):p},L.on=function(){var I=b.on.apply(b,arguments);return I===b?L:I},L.clickDistance=function(I){return arguments.length?(z=(I=+I)*I,L):Math.sqrt(z)},L.tapDistance=function(I){return arguments.length?(q=+I,L):q},L}var Zu=1,Ju=2,Q5="http://www.w3.org/2000/svg",_o="http://www.w3.org/1999/xlink",e3={show:_o,actuate:_o,href:_o},t3=e=>typeof e=="string"||typeof e=="number",r3=e=>(e==null?void 0:e.vtype)===Zu,n3=e=>(e==null?void 0:e.vtype)===Ju;function z0(e,t){let r;if(typeof e=="string")r=Zu;else if(typeof e=="function")r=Ju;else throw new Error("Invalid VNode type");return{vtype:r,type:e,props:t}}function i3(e){return e.children}var o3={isSvg:!1};function ju(e,t){Array.isArray(t)||(t=[t]),t=t.filter(Boolean),t.length&&e.append(...t)}function l3(e,t,r){for(let i in t)if(!(i==="key"||i==="children"||i==="ref"))if(i==="dangerouslySetInnerHTML")e.innerHTML=t[i].__html;else if(i==="innerHTML"||i==="textContent"||i==="innerText"||i==="value"&&["textarea","select"].includes(e.tagName)){let l=t[i];l!=null&&(e[i]=l)}else i.startsWith("on")?e[i.toLowerCase()]=t[i]:a3(e,i,t[i],r.isSvg)}var s3={className:"class",labelFor:"for"};function a3(e,t,r,i){if(t=s3[t]||t,r===!0)e.setAttribute(t,"");else if(r===!1)e.removeAttribute(t);else{let l=i?e3[t]:void 0;l!==void 0?e.setAttributeNS(l,t,r):e.setAttribute(t,r)}}function c3(e){return e.reduce((t,r)=>t.concat(r),[])}function No(e,t){return Array.isArray(e)?c3(e.map(r=>No(r,t))):zo(e,t)}function zo(e,t=o3){if(e==null||typeof e=="boolean")return null;if(e instanceof Node)return e;if(n3(e)){let{type:r,props:i}=e;if(r===i3){let a=document.createDocumentFragment();if(i.children){let u=No(i.children,t);ju(a,u)}return a}let l=r(i);return zo(l,t)}if(t3(e))return document.createTextNode(`${e}`);if(r3(e)){let r,{type:i,props:l}=e;if(!t.isSvg&&i==="svg"&&(t=Object.assign({},t,{isSvg:!0})),t.isSvg?r=document.createElementNS(Q5,i):r=document.createElement(i),l3(r,l,t),l.children){let u=t;t.isSvg&&i==="foreignObject"&&(u=Object.assign({},u,{isSvg:!1}));let h=No(l.children,u);h!=null&&ju(r,h)}let{ref:a}=l;return typeof a=="function"&&a(r),r}throw new Error("mount: Invalid Vnode!")}function Co(e){return zo(e)}function u3(e){var t=0,r=e.children,i=r&&r.length;if(!i)t=1;else for(;--i>=0;)t+=r[i].value;e.value=t}function h3(){return this.eachAfter(u3)}function f3(e){var t=this,r,i=[t],l,a,u;do for(r=i.reverse(),i=[];t=r.pop();)if(e(t),l=t.children,l)for(a=0,u=l.length;a<u;++a)i.push(l[a]);while(i.length);return this}function d3(e){for(var t=this,r=[t],i,l;t=r.pop();)if(e(t),i=t.children,i)for(l=i.length-1;l>=0;--l)r.push(i[l]);return this}function p3(e){for(var t=this,r=[t],i=[],l,a,u;t=r.pop();)if(i.push(t),l=t.children,l)for(a=0,u=l.length;a<u;++a)r.push(l[a]);for(;t=i.pop();)e(t);return this}function m3(e){return this.eachAfter(function(t){for(var r=+e(t.data)||0,i=t.children,l=i&&i.length;--l>=0;)r+=i[l].value;t.value=r})}function g3(e){return this.eachBefore(function(t){t.children&&t.children.sort(e)})}function x3(e){for(var t=this,r=y3(t,e),i=[t];t!==r;)t=t.parent,i.push(t);for(var l=i.length;e!==r;)i.splice(l,0,e),e=e.parent;return i}function y3(e,t){if(e===t)return e;var r=e.ancestors(),i=t.ancestors(),l=null;for(e=r.pop(),t=i.pop();e===t;)l=e,e=r.pop(),t=i.pop();return l}function b3(){for(var e=this,t=[e];e=e.parent;)t.push(e);return t}function v3(){var e=[];return this.each(function(t){e.push(t)}),e}function w3(){var e=[];return this.eachBefore(function(t){t.children||e.push(t)}),e}function k3(){var e=this,t=[];return e.each(function(r){r!==e&&t.push({source:r.parent,target:r})}),t}function Do(e,t){var r=new D0(e),i=+e.value&&(r.value=e.value),l,a=[r],u,h,p,b;for(t==null&&(t=M3);l=a.pop();)if(i&&(l.value=+l.data.value),(h=t(l.data))&&(b=h.length))for(l.children=new Array(b),p=b-1;p>=0;--p)a.push(u=l.children[p]=new D0(h[p])),u.parent=l,u.depth=l.depth+1;return r.eachBefore(_3)}function S3(){return Do(this).eachBefore(A3)}function M3(e){return e.children}function A3(e){e.data=e.data.data}function _3(e){var t=0;do e.height=t;while((e=e.parent)&&e.height<++t)}function D0(e){this.data=e,this.depth=this.height=0,this.parent=null}D0.prototype=Do.prototype={constructor:D0,count:h3,each:f3,eachAfter:p3,eachBefore:d3,sum:m3,sort:g3,path:x3,ancestors:b3,descendants:v3,leaves:w3,links:k3,copy:S3};var C3="d3-flextree",T3="2.1.2",N3="build/d3-flextree.js",E3="index",z3={name:"Chris Maloney",url:"http://chrismaloney.org"},D3="Flexible tree layout algorithm that allows for variable node sizes.",I3=["d3","d3-module","layout","tree","hierarchy","d3-hierarchy","plugin","d3-plugin","infovis","visualization","2d"],B3="https://github.com/klortho/d3-flextree",O3="WTFPL",F3={type:"git",url:"https://github.com/klortho/d3-flextree.git"},R3={clean:"rm -rf build demo test","build:demo":"rollup -c --environment BUILD:demo","build:dev":"rollup -c --environment BUILD:dev","build:prod":"rollup -c --environment BUILD:prod","build:test":"rollup -c --environment BUILD:test",build:"rollup -c",lint:"eslint index.js src","test:main":"node test/bundle.js","test:browser":"node test/browser-tests.js",test:"npm-run-all test:*",prepare:"npm-run-all clean build lint test"},L3={"d3-hierarchy":"^1.1.5"},q3={"babel-plugin-external-helpers":"^6.22.0","babel-preset-es2015-rollup":"^3.0.0",d3:"^4.13.0","d3-selection-multi":"^1.0.1",eslint:"^4.19.1",jsdom:"^11.6.2","npm-run-all":"^4.1.2",rollup:"^0.55.3","rollup-plugin-babel":"^2.7.1","rollup-plugin-commonjs":"^8.0.2","rollup-plugin-copy":"^0.2.3","rollup-plugin-json":"^2.3.0","rollup-plugin-node-resolve":"^3.0.2","rollup-plugin-uglify":"^3.0.0","uglify-es":"^3.3.9"},P3={name:C3,version:T3,main:N3,module:E3,"jsnext:main":"index",author:z3,description:D3,keywords:I3,homepage:B3,license:O3,repository:F3,scripts:R3,dependencies:L3,devDependencies:q3},{version:H3}=P3,$3=Object.freeze({children:e=>e.children,nodeSize:e=>e.data.size,spacing:0});function eh(e){let t=Object.assign({},$3,e);function r(h){let p=t[h];return typeof p=="function"?p:()=>p}function i(h){let p=u(a(),h,b=>b.children);return p.update(),p.data}function l(){let h=r("nodeSize"),p=r("spacing");return class Qu extends Do.prototype.constructor{constructor(y){super(y)}copy(){let y=u(this.constructor,this,S=>S.children);return y.each(S=>S.data=S.data.data),y}get size(){return h(this)}spacing(y){return p(this,y)}get nodes(){return this.descendants()}get xSize(){return this.size[0]}get ySize(){return this.size[1]}get top(){return this.y}get bottom(){return this.y+this.ySize}get left(){return this.x-this.xSize/2}get right(){return this.x+this.xSize/2}get root(){let y=this.ancestors();return y[y.length-1]}get numChildren(){return this.hasChildren?this.children.length:0}get hasChildren(){return!this.noChildren}get noChildren(){return this.children===null}get firstChild(){return this.hasChildren?this.children[0]:null}get lastChild(){return this.hasChildren?this.children[this.numChildren-1]:null}get extents(){return(this.children||[]).reduce((y,S)=>Qu.maxExtents(y,S.extents),this.nodeExtents)}get nodeExtents(){return{top:this.top,bottom:this.bottom,left:this.left,right:this.right}}static maxExtents(y,S){return{top:Math.min(y.top,S.top),bottom:Math.max(y.bottom,S.bottom),left:Math.min(y.left,S.left),right:Math.max(y.right,S.right)}}}}function a(){let h=l(),p=r("nodeSize"),b=r("spacing");return class extends h{constructor(y){super(y),Object.assign(this,{x:0,y:0,relX:0,prelim:0,shift:0,change:0,lExt:this,lExtRelX:0,lThr:null,rExt:this,rExtRelX:0,rThr:null})}get size(){return p(this.data)}spacing(y){return b(this.data,y.data)}get x(){return this.data.x}set x(y){this.data.x=y}get y(){return this.data.y}set y(y){this.data.y=y}update(){return th(this),rh(this),this}}}function u(h,p,b){let y=(S,C)=>{let T=new h(S);Object.assign(T,{parent:C,depth:C===null?0:C.depth+1,height:0,length:1});let F=b(S)||[];return T.children=F.length===0?null:F.map(z=>y(z,T)),T.children&&Object.assign(T,T.children.reduce((z,q)=>({height:Math.max(z.height,q.height+1),length:z.length+q.length}),T)),T};return y(p,null)}return Object.assign(i,{nodeSize(h){return arguments.length?(t.nodeSize=h,i):t.nodeSize},spacing(h){return arguments.length?(t.spacing=h,i):t.spacing},children(h){return arguments.length?(t.children=h,i):t.children},hierarchy(h,p){let b=typeof p>"u"?t.children:p;return u(l(),h,b)},dump(h){let p=r("nodeSize"),b=y=>S=>{let C=y+"  ",T=y+"    ",{x:F,y:z}=S,q=p(S),L=S.children||[],X=L.length===0?" ":`,${C}children: [${T}${L.map(b(T)).join(T)}${C}],${y}`;return`{ size: [${q.join(", ")}],${C}x: ${F}, y: ${z}${X}},`};return b(`
`)(h)}}),i}eh.version=H3;var th=(e,t=0)=>(e.y=t,(e.children||[]).reduce((r,i)=>{let[l,a]=r;th(i,e.y+e.ySize);let u=(l===0?i.lExt:i.rExt).bottom;l!==0&&G3(e,l,a);let h=J3(u,l,a);return[l+1,h]},[0,null]),V3(e),Z3(e),e),rh=(e,t,r)=>{typeof t>"u"&&(t=-e.relX-e.prelim,r=0);let i=t+e.relX;return e.relX=i+e.prelim-r,e.prelim=0,e.x=r+e.relX,(e.children||[]).forEach(l=>rh(l,i,e.x)),e},V3=e=>{(e.children||[]).reduce((t,r)=>{let[i,l]=t,a=i+r.shift,u=l+a+r.change;return r.relX+=u,[a,u]},[0,0])},G3=(e,t,r)=>{let i=e.children[t-1],l=e.children[t],a=i,u=i.relX,h=l,p=l.relX,b=!0;for(;a&&h;){a.bottom>r.lowY&&(r=r.next);let y=u+a.prelim-(p+h.prelim)+a.xSize/2+h.xSize/2+a.spacing(h);(y>0||y<0&&b)&&(p+=y,Y3(l,y),U3(e,t,r.index,y)),b=!1;let S=a.bottom,C=h.bottom;S<=C&&(a=j3(a),a&&(u+=a.relX)),S>=C&&(h=X3(h),h&&(p+=h.relX))}!a&&h?W3(e,t,h,p):a&&!h&&K3(e,t,a,u)},Y3=(e,t)=>{e.relX+=t,e.lExtRelX+=t,e.rExtRelX+=t},U3=(e,t,r,i)=>{let l=e.children[t],a=t-r;if(a>1){let u=i/a;e.children[r+1].shift+=u,l.shift-=u,l.change-=i-u}},X3=e=>e.hasChildren?e.firstChild:e.lThr,j3=e=>e.hasChildren?e.lastChild:e.rThr,W3=(e,t,r,i)=>{let l=e.firstChild,a=l.lExt,u=e.children[t];a.lThr=r;let h=i-r.relX-l.lExtRelX;a.relX+=h,a.prelim-=h,l.lExt=u.lExt,l.lExtRelX=u.lExtRelX},K3=(e,t,r,i)=>{let l=e.children[t],a=l.rExt,u=e.children[t-1];a.rThr=r;let h=i-r.relX-l.rExtRelX;a.relX+=h,a.prelim-=h,l.rExt=u.rExt,l.rExtRelX=u.rExtRelX},Z3=e=>{if(e.hasChildren){let t=e.firstChild,r=e.lastChild,i=(t.prelim+t.relX-t.xSize/2+r.relX+r.prelim+r.xSize/2)/2;Object.assign(e,{prelim:i,lExt:t.lExt,lExtRelX:t.lExtRelX,rExt:r.rExt,rExtRelX:r.rExtRelX})}},J3=(e,t,r)=>{for(;r!==null&&e>=r.lowY;)r=r.next;return{lowY:e,index:t,next:r}},Q3=".markmap{font:300 16px/20px sans-serif}.markmap-link{fill:none}.markmap-node>circle{cursor:pointer}.markmap-foreign{display:inline-block}.markmap-foreign a{color:#0097e6}.markmap-foreign a:hover{color:#00a8ff}.markmap-foreign code{padding:.25em;font-size:calc(1em - 2px);color:#555;background-color:#f0f0f0;border-radius:2px}.markmap-foreign pre{margin:0}.markmap-foreign pre>code{display:block}.markmap-foreign del{text-decoration:line-through}.markmap-foreign em{font-style:italic}.markmap-foreign strong{font-weight:700}.markmap-foreign mark{background:#ffeaa7}",e6=".markmap-container{position:absolute;width:0;height:0;top:-100px;left:-100px;overflow:hidden}.markmap-container>.markmap-foreign{display:inline-block}.markmap-container>.markmap-foreign>div:last-child,.markmap-container>.markmap-foreign>div:last-child :not(pre){white-space:nowrap}.markmap-container>.markmap-foreign>div:last-child code{white-space:inherit}";function Wu(e){let t=e.data;return Math.max(4-2*t.depth,1.5)}function Ku(e,t){let r=t0(e,t);return e[r]}function To(e){e.stopPropagation()}function t6(){return{transformHtml:new wt}}var r6=new wt,n6=xn(vo),nh=typeof navigator<"u"&&navigator.userAgent.includes("Macintosh"),ih=class Eo{constructor(t,r){this.options=Eo.defaultOptions,this.revokers=[],this.handleZoom=i=>{let{transform:l}=i;this.g.attr("transform",l)},this.handlePan=i=>{i.preventDefault();let l=vr(this.svg.node()),a=l.translate(-i.deltaX/l.k,-i.deltaY/l.k);this.svg.call(this.zoom.transform,a)},this.handleClick=(i,l)=>{let a=this.options.toggleRecursively;(nh?i.metaKey:i.ctrlKey)&&(a=!a),this.toggleNode(l.data,a)},this.viewHooks=t6(),this.svg=t.datum?t:nt(t),this.styleNode=this.svg.append("style"),this.zoom=Ao().filter(i=>this.options.scrollForPan&&i.type==="wheel"?i.ctrlKey&&!i.button:(!i.ctrlKey||i.type==="wheel")&&!i.button).on("zoom",this.handleZoom),this.setOptions(r),this.state={id:this.options.id||this.svg.attr("id")||Ds(),minX:0,maxX:0,minY:0,maxY:0},this.g=this.svg.append("g"),this.revokers.push(r6.tap(()=>{this.setData()}))}getStyleContent(){let{style:t}=this.options,{id:r}=this.state,i=typeof t=="function"?t(r):"";return[this.options.embedGlobalCSS&&Q3,i].filter(Boolean).join(`
`)}updateStyle(){this.svg.attr("class",Is(this.svg.attr("class"),"markmap",this.state.id));let t=this.getStyleContent();this.styleNode.text(t)}toggleNode(t,r=!1){var i,l;let a=(i=t.payload)!=null&&i.fold?0:1;r?Un(t,(u,h)=>{u.payload={...u.payload,fold:a},h()}):t.payload={...t.payload,fold:(l=t.payload)!=null&&l.fold?0:1},this.renderData(t)}initializeData(t){let r=0,{color:i,nodeMinHeight:l,maxWidth:a,initialExpandLevel:u}=this.options,{id:h}=this.state,p=Co(z0("div",{className:`markmap-container markmap ${h}-g`})),b=Co(z0("style",{children:[this.getStyleContent(),e6].join(`
`)}));document.body.append(p,b);let y=a?`max-width: ${a}px`:"",S=0;Un(t,(T,F,z)=>{var q,L,X;T.children=(q=T.children)==null?void 0:q.map(me=>({...me})),r+=1;let re=Co(z0("div",{className:"markmap-foreign",style:y,children:z0("div",{dangerouslySetInnerHTML:{__html:T.content}})}));p.append(re),T.state={...T.state,id:r,el:re.firstChild},T.state.path=[(L=z==null?void 0:z.state)==null?void 0:L.path,T.state.id].filter(Boolean).join("."),i(T);let ce=((X=T.payload)==null?void 0:X.fold)===2;ce?S+=1:(S||u>=0&&T.depth>=u)&&(T.payload={...T.payload,fold:1}),F(),ce&&(S-=1)});let C=Array.from(p.childNodes).map(T=>T.firstChild);this.viewHooks.transformHtml.call(this,C),C.forEach(T=>{var F;(F=T.parentNode)==null||F.append(T.cloneNode(!0))}),Un(t,(T,F,z)=>{var q;let L=T.state,X=L.el.getBoundingClientRect();T.content=L.el.innerHTML,L.size=[Math.ceil(X.width)+1,Math.max(Math.ceil(X.height),l)],L.key=[(q=z==null?void 0:z.state)==null?void 0:q.id,L.id].filter(Boolean).join(".")+T.content,F()}),p.remove(),b.remove()}setOptions(t){this.options={...this.options,...t},this.options.zoom?this.svg.call(this.zoom):this.svg.on(".zoom",null),this.options.pan?this.svg.on("wheel",this.handlePan):this.svg.on("wheel",null)}setData(t,r){r&&this.setOptions(r),t&&(this.state.data=t),this.state.data&&(this.initializeData(this.state.data),this.updateStyle(),this.renderData())}renderData(t){var Oe,Xe;if(!this.state.data)return;let{spacingHorizontal:r,paddingX:i,spacingVertical:l,autoFit:a,color:u}=this.options,h=eh({}).children(P=>{var I;if(!((I=P.payload)!=null&&I.fold))return P.children}).nodeSize(P=>{let[I,W]=P.data.state.size;return[W,I+(I?i*2:0)+r]}).spacing((P,I)=>P.parent===I.parent?l:l*2),p=h.hierarchy(this.state.data);h(p);let b=p.descendants().reverse(),y=p.links(),S=So(),C=en(b,P=>P.x-P.xSize/2),T=Qr(b,P=>P.x+P.xSize/2),F=en(b,P=>P.y),z=Qr(b,P=>P.y+P.ySize-r);Object.assign(this.state,{minX:C,maxX:T,minY:F,maxY:z}),a&&this.fit();let q=t&&b.find(P=>P.data===t)||p,L=(Oe=q.data.state.x0)!=null?Oe:q.x,X=(Xe=q.data.state.y0)!=null?Xe:q.y,re=this.g.selectAll(ur("g")).data(b,P=>P.data.state.key),ce=re.enter().append("g").attr("data-depth",P=>P.data.depth).attr("data-path",P=>P.data.state.path).attr("transform",P=>`translate(${X+q.ySize-P.ySize},${L+q.xSize/2-P.xSize})`),me=this.transition(re.exit());me.select("line").attr("x1",P=>P.ySize-r).attr("x2",P=>P.ySize-r),me.select("foreignObject").style("opacity",0),me.attr("transform",P=>`translate(${q.y+q.ySize-P.ySize},${q.x+q.xSize/2-P.xSize})`).remove();let be=re.merge(ce).attr("class",P=>{var I;return["markmap-node",((I=P.data.payload)==null?void 0:I.fold)&&"markmap-fold"].filter(Boolean).join(" ")});this.transition(be).attr("transform",P=>`translate(${P.y},${P.x-P.xSize/2})`);let Se=be.selectAll(ur("line")).data(P=>[P],P=>P.data.state.key).join(P=>P.append("line").attr("x1",I=>I.ySize-r).attr("x2",I=>I.ySize-r),P=>P,P=>P.remove());this.transition(Se).attr("x1",-1).attr("x2",P=>P.ySize-r+2).attr("y1",P=>P.xSize).attr("y2",P=>P.xSize).attr("stroke",P=>u(P.data)).attr("stroke-width",Wu);let $e=be.selectAll(ur("circle")).data(P=>{var I;return(I=P.data.children)!=null&&I.length?[P]:[]},P=>P.data.state.key).join(P=>P.append("circle").attr("stroke-width","1.5").attr("cx",I=>I.ySize-r).attr("cy",I=>I.xSize).attr("r",0).on("click",(I,W)=>this.handleClick(I,W)).on("mousedown",To),P=>P,P=>P.remove());this.transition($e).attr("r",6).attr("cx",P=>P.ySize-r).attr("cy",P=>P.xSize).attr("stroke",P=>u(P.data)).attr("fill",P=>{var I;return(I=P.data.payload)!=null&&I.fold&&P.data.children?u(P.data):"#fff"});let ze=be.selectAll(ur("foreignObject")).data(P=>[P],P=>P.data.state.key).join(P=>{let I=P.append("foreignObject").attr("class","markmap-foreign").attr("x",i).attr("y",0).style("opacity",0).on("mousedown",To).on("dblclick",To);return I.append("xhtml:div").select(function(W){let G=W.data.state.el.cloneNode(!0);return this.replaceWith(G),G}).attr("xmlns","http://www.w3.org/1999/xhtml"),I},P=>P,P=>P.remove()).attr("width",P=>Math.max(0,P.ySize-r-i*2)).attr("height",P=>P.xSize);this.transition(ze).style("opacity",1);let Ve=this.g.selectAll(ur("path")).data(y,P=>P.target.data.state.key).join(P=>{let I=[X+q.ySize-r,L+q.xSize/2];return P.insert("path","g").attr("class","markmap-link").attr("data-depth",W=>W.target.data.depth).attr("data-path",W=>W.target.data.state.path).attr("d",S({source:I,target:I}))},P=>P,P=>{let I=[q.y+q.ySize-r,q.x+q.xSize/2];return this.transition(P).attr("d",S({source:I,target:I})).remove()});this.transition(Ve).attr("stroke",P=>u(P.target.data)).attr("stroke-width",P=>Wu(P.target)).attr("d",P=>{let I=P.source,W=P.target,G=[I.y+I.ySize-r,I.x+I.xSize/2],ee=[W.y,W.x+W.xSize/2];return S({source:G,target:ee})}),b.forEach(P=>{P.data.state.x0=P.x,P.data.state.y0=P.y})}transition(t){let{duration:r}=this.options;return t.transition().duration(r)}async fit(){let t=this.svg.node(),{width:r,height:i}=t.getBoundingClientRect(),{fitRatio:l}=this.options,{minX:a,maxX:u,minY:h,maxY:p}=this.state,b=p-h,y=u-a,S=Math.min(r/b*l,i/y*l,2),C=br.translate((r-b*S)/2-h*S,(i-y*S)/2-a*S).scale(S);return this.transition(this.svg).call(this.zoom.transform,C).end().catch(cr)}async ensureView(t,r){let i;if(this.g.selectAll(ur("g")).each(function(L){L.data===t&&(i=L)}),!i)return;let l=this.svg.node(),{spacingHorizontal:a}=this.options,u=l.getBoundingClientRect(),h=vr(l),[p,b]=[i.y,i.y+i.ySize-a+2].map(L=>L*h.k+h.x),[y,S]=[i.x-i.xSize/2,i.x+i.xSize/2].map(L=>L*h.k+h.y),C={left:0,right:0,top:0,bottom:0,...r},T=[C.left-p,u.width-C.right-b],F=[C.top-y,u.height-C.bottom-S],z=T[0]*T[1]>0?Ku(T,Math.abs)/h.k:0,q=F[0]*F[1]>0?Ku(F,Math.abs)/h.k:0;if(z||q){let L=h.translate(z,q);return this.transition(this.svg).call(this.zoom.transform,L).end().catch(cr)}}async rescale(t){let r=this.svg.node(),{width:i,height:l}=r.getBoundingClientRect(),a=i/2,u=l/2,h=vr(r),p=h.translate((a-h.x)*(1-t)/h.k,(u-h.y)*(1-t)/h.k).scale(t);return this.transition(this.svg).call(this.zoom.transform,p).end().catch(cr)}destroy(){this.svg.on(".zoom",null),this.svg.html(null),this.revokers.forEach(t=>{t()})}static create(t,r,i=null){let l=new Eo(t,r);return i&&(l.setData(i),l.fit()),l}};ih.defaultOptions={autoFit:!1,color:e=>{var t;return n6(`${((t=e.state)==null?void 0:t.path)||""}`)},duration:500,embedGlobalCSS:!0,fitRatio:.95,maxWidth:0,nodeMinHeight:16,paddingX:8,scrollForPan:nh,spacingHorizontal:80,spacingVertical:5,initialExpandLevel:-1,zoom:!0,pan:!0,toggleRecursively:!1};var oh=ih;var Mt="mindmap",I0=class extends Je.Plugin{constructor(){super(...arguments);this.mindmap=null;this.rootNode=null;this.currentNode=null;this.editingNode=null;this.selectedNode=null}async onload(){this.transformer=new e0,this.registerView(Mt,r=>new Yt(r,this)),this.addCommand({id:"create-mindmap",name:"\u521B\u5EFA\u65B0\u7684\u601D\u7EF4\u5BFC\u56FE",callback:()=>this.createNewMindMap()}),this.addCommand({id:"select-parent-node",name:"\u9009\u62E9\u7236\u8282\u70B9",callback:()=>this.selectParentNode()}),this.addCommand({id:"select-first-child",name:"\u9009\u62E9\u7B2C\u4E00\u4E2A\u5B50\u8282\u70B9",callback:()=>this.selectFirstChild()}),this.addCommand({id:"select-next-sibling",name:"\u9009\u62E9\u4E0B\u4E00\u4E2A\u540C\u7EA7\u8282\u70B9",callback:()=>this.selectNextSibling()}),this.addCommand({id:"select-previous-sibling",name:"\u9009\u62E9\u4E0A\u4E00\u4E2A\u540C\u7EA7\u8282\u70B9",callback:()=>this.selectPreviousSibling()}),this.addCommand({id:"debug-mindmap",name:"\u8C03\u8BD5\u601D\u7EF4\u5BFC\u56FE\u72B6\u6001",callback:()=>this.debugMindMapState()}),this.addCommand({id:"force-select-root",name:"\u5F3A\u5236\u9009\u62E9\u6839\u8282\u70B9",callback:()=>{this.rootNode&&(console.log("Force selecting root node:",this.rootNode),this.selectNode(this.rootNode))}}),this.addCommand({id:"create-simple-test",name:"\u521B\u5EFA\u7B80\u5355\u6D4B\u8BD5\u89C6\u56FE",callback:()=>this.createSimpleTestView()}),this.addCommand({id:"test-edit-selected",name:"\u6D4B\u8BD5\u7F16\u8F91\u9009\u4E2D\u8282\u70B9",callback:()=>{if(this.selectedNode){console.log("Testing edit for selected node:",this.selectedNode.content);let r=document.querySelector(`[data-node-id="${this.selectedNode.id}"]`);r?this.startSimpleEditing(this.selectedNode,r):console.log("Node element not found")}else console.log("No node selected")}}),this.addCommand({id:"force-simple-view",name:"\u5F3A\u5236\u4F7F\u7528\u7B80\u5355HTML\u89C6\u56FE",callback:()=>{this.rootNode?this.createSimpleHTMLMindMap():this.createSimpleTestView()}}),this.addCommand({id:"edit-root-node",name:"\u7F16\u8F91\u6839\u8282\u70B9",callback:()=>{this.rootNode?(console.log("Forcing edit of root node"),this.startEditing(this.rootNode)):console.log("No root node to edit")}}),this.addCommand({id:"export-to-markdown",name:"\u5BFC\u51FA\u4E3AMarkdown\u6587\u6863",callback:()=>this.exportToMarkdown()}),this.addCommand({id:"import-from-markdown",name:"\u4ECEMarkdown\u6587\u6863\u5BFC\u5165",callback:()=>this.importFromMarkdown()}),this.addCommand({id:"toggle-mindmap-markdown",name:"\u5207\u6362\u601D\u7EF4\u5BFC\u56FE/Markdown\u89C6\u56FE",hotkeys:[{modifiers:["Ctrl"],key:"M"}],callback:()=>this.toggleMindMapMarkdown()}),this.registerDomEvent(document,"keydown",r=>{if(!(!this.app.workspace.getActiveViewOfType(Yt)||!this.selectedNode))switch(r.key){case"Tab":r.preventDefault(),r.shiftKey?this.createSiblingNode(this.selectedNode):this.createChildNode(this.selectedNode);break;case"Enter":r.preventDefault(),this.editingNode?this.finishEditing().catch(console.error):this.startEditing(this.selectedNode).catch(console.error);break;case"Delete":case"Backspace":!this.editingNode&&r.ctrlKey&&(r.preventDefault(),this.deleteNode(this.selectedNode));break;case"ArrowUp":r.preventDefault(),this.selectPreviousSibling();break;case"ArrowDown":r.preventDefault(),this.selectNextSibling();break;case"ArrowLeft":r.preventDefault(),this.selectedNode&&this.selectedNode.isExpanded?this.collapseNode(this.selectedNode):this.selectParentNode();break;case"ArrowRight":r.preventDefault(),this.selectedNode&&!this.selectedNode.isExpanded?this.expandNode(this.selectedNode):this.selectFirstChild();break;case"Escape":this.editingNode&&(r.preventDefault(),this.cancelEditing());break}})}debugMindMapState(){console.log("=== \u601D\u7EF4\u5BFC\u56FE\u8C03\u8BD5\u4FE1\u606F ==="),console.log("Root node:",this.rootNode),console.log("Selected node:",this.selectedNode),console.log("Editing node:",this.editingNode),console.log("Mindmap instance:",this.mindmap);let r=this.app.workspace.getActiveViewOfType(Yt);if(console.log("Active view:",r),r){let i=r.containerEl.querySelector(".mindmap-container");if(console.log("Container:",i),i){let l=i.querySelector("svg");if(console.log("SVG element:",l),l){let a=l.querySelectorAll("g");console.log("Found groups:",a.length);let u=l.querySelectorAll("text");console.log("Found text elements:",u.length);for(let h=0;h<u.length;h++){let p=u[h];console.log(`Text ${h}:`,p.textContent,p)}}}}this.rootNode&&console.log("All nodes:",this.getAllNodes(this.rootNode)),console.log("=== \u8C03\u8BD5\u4FE1\u606F\u7ED3\u675F ===")}async createSimpleTestView(){this.rootNode={id:"root",content:"\u4E2D\u5FC3\u4E3B\u9898",children:[{id:"child1",content:"\u5B50\u8282\u70B91",children:[],parent:void 0,isExpanded:!0},{id:"child2",content:"\u5B50\u8282\u70B92",children:[],parent:void 0,isExpanded:!0}],isExpanded:!0},this.rootNode.children.forEach(i=>{i.parent=this.rootNode});let r=this.app.workspace.getLeaf(!0);await r.setViewState({type:Mt,state:{data:this.rootNode}}),this.app.workspace.revealLeaf(r),setTimeout(()=>{this.createSimpleHTMLMindMap()},200)}createSimpleHTMLMindMap(){let r=this.app.workspace.getActiveViewOfType(Yt);if(!r||!this.rootNode)return;let i=r.containerEl.querySelector(".mindmap-container");if(!i)return;i.innerHTML="",i.style.cssText=`
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            background: var(--background-primary);
        `;let l=this.createNodeElement(this.rootNode,0);i.appendChild(l);let a=i.createDiv("children-container");a.style.cssText=`
            display: flex;
            gap: 20px;
            margin-top: 20px;
        `,this.rootNode.children.forEach(u=>{let h=this.createNodeElement(u,1);a.appendChild(h)}),this.selectNode(this.rootNode)}createNodeElement(r,i){let l=document.createElement("div");l.className="simple-mindmap-node",l.setAttribute("data-node-id",r.id),l.setAttribute("tabindex","0");let a=this.selectedNode===r;return l.style.cssText=`
            padding: 10px 15px;
            border: 2px solid ${a?"var(--text-accent)":"var(--background-modifier-border)"};
            border-radius: 8px;
            background: var(--background-primary);
            cursor: pointer;
            user-select: none;
            font-weight: ${i===0?"600":"400"};
            font-size: ${i===0?"16px":"14px"};
            color: ${a?"var(--text-accent)":"var(--text-normal)"};
            transition: all 0.2s ease;
            min-width: 100px;
            text-align: center;
            outline: none;
        `,l.textContent=r.content,l.addEventListener("click",u=>{u.stopPropagation(),console.log("Simple node clicked:",r.content),this.selectNode(r),this.updateSimpleNodeStyles()}),l.addEventListener("dblclick",u=>{u.stopPropagation(),u.preventDefault(),console.log("Simple node double-clicked:",r.content),this.startSimpleEditing(r,l)}),l.addEventListener("keydown",u=>{u.key==="Enter"&&this.selectedNode===r&&(u.preventDefault(),console.log("Enter pressed on selected node:",r.content),this.startSimpleEditing(r,l))}),l}updateSimpleNodeStyles(){document.querySelectorAll(".simple-mindmap-node").forEach(i=>{var u;let l=i.getAttribute("data-node-id"),a=((u=this.selectedNode)==null?void 0:u.id)===l;i.style.borderColor=a?"var(--text-accent)":"var(--background-modifier-border)",i.style.color=a?"var(--text-accent)":"var(--text-normal)"})}startSimpleEditing(r,i){var h;console.log("Starting simple editing for node:",r.content);let l=document.createElement("input");l.type="text",l.value=r.content,l.className="simple-mindmap-input",l.style.cssText=`
            padding: 10px 15px;
            border: 2px solid var(--text-accent);
            border-radius: 8px;
            background: var(--background-primary);
            font-weight: ${r===this.rootNode?"600":"400"};
            font-size: ${r===this.rootNode?"16px":"14px"};
            color: var(--text-accent);
            min-width: 100px;
            text-align: center;
            outline: none;
            box-shadow: 0 0 0 2px var(--background-modifier-border-hover);
        `,console.log("Replacing node element with input"),(h=i.parentNode)==null||h.replaceChild(l,i),setTimeout(()=>{l.focus(),l.select()},10);let a=()=>{console.log("Finishing edit, new content:",l.value),r.content=l.value,this.createSimpleHTMLMindMap(),this.saveData()},u=()=>{console.log("Cancelling edit"),this.createSimpleHTMLMindMap()};l.addEventListener("blur",a),l.addEventListener("keydown",p=>{console.log("Key pressed in input:",p.key),p.key==="Enter"?(p.preventDefault(),a()):p.key==="Escape"&&(p.preventDefault(),u())})}selectNode(r){this.selectedNode&&(this.selectedNode.isSelected=!1),this.selectedNode=r,r&&(r.isSelected=!0,this.highlightSelectedNode())}selectParentNode(){var r;(r=this.selectedNode)!=null&&r.parent&&this.selectNode(this.selectedNode.parent)}selectFirstChild(){this.selectedNode&&this.selectedNode.children&&this.selectedNode.children.length>0&&this.selectNode(this.selectedNode.children[0])}selectNextSibling(){var l;if(!((l=this.selectedNode)!=null&&l.parent))return;let r=this.selectedNode.parent.children,i=r.indexOf(this.selectedNode);i<r.length-1&&this.selectNode(r[i+1])}selectPreviousSibling(){var l;if(!((l=this.selectedNode)!=null&&l.parent))return;let r=this.selectedNode.parent.children,i=r.indexOf(this.selectedNode);i>0&&this.selectNode(r[i-1])}async expandNode(r){r.isExpanded=!0,await this.renderMindMap()}async collapseNode(r){r.isExpanded=!1,await this.renderMindMap()}deleteNode(r){if(!r.parent||r===this.rootNode)return;let i=r.parent.children,l=i.indexOf(r);i.splice(l,1),i.length>0?this.selectNode(i[Math.min(l,i.length-1)]):this.selectNode(r.parent),this.saveData().catch(console.error)}findNodeIdFromElement(r){var a;if(!r.getAttribute("transform"))return null;let l=this.getAllNodes(this.rootNode);for(let u of l)if((a=r.textContent)!=null&&a.includes(u.content))return u.id;return null}getAllNodes(r){let i=[r];for(let l of r.children)i.push(...this.getAllNodes(l));return i}async startEditing(r){if(console.log("Starting editing for node:",r.content),this.editingNode===r){console.log("Already editing this node");return}await this.finishEditing(),this.editingNode=r;let i=document.querySelector(`[data-node-id="${r.id}"]`);if(i){console.log("Using simple HTML editing"),this.startSimpleEditing(r,i);return}console.log("Using markmap editing mode"),this.startMarkmapEditing(r)}startMarkmapEditing(r){var p;let i=document.querySelector(".mindmap-container svg");if(!i){console.log("No SVG found for editing");return}let l=i.querySelectorAll("text, div[xmlns], div"),a=null;for(let b=0;b<l.length;b++){let y=l[b];if(((p=y.textContent)==null?void 0:p.trim())===r.content){a=y;break}}if(!a){console.log("No target element found for editing");return}console.log("Found target element for editing:",a);let u=document.createElement("input");u.value=r.content,u.className="mindmap-node-input";let h=a.getBoundingClientRect();Object.assign(u.style,{position:"fixed",left:`${h.left}px`,top:`${h.top}px`,width:`${Math.max(h.width+40,100)}px`,height:`${Math.max(h.height+8,30)}px`,zIndex:"10000",fontSize:"14px",fontFamily:"var(--font-text)",background:"var(--background-primary)",border:"2px solid var(--text-accent)",borderRadius:"4px",padding:"4px 8px",color:"var(--text-normal)",outline:"none"}),document.body.appendChild(u),setTimeout(()=>{u.focus(),u.select()},10),u.addEventListener("blur",()=>{this.finishEditing().catch(console.error)}),u.addEventListener("keydown",b=>{b.key==="Enter"?(b.preventDefault(),this.finishEditing().catch(console.error)):b.key==="Escape"&&(b.preventDefault(),this.cancelEditing())})}getSVGElementPosition(r){let l=r.ownerSVGElement.createSVGPoint(),a=r.getBBox();l.x=a.x,l.y=a.y+a.height;let u=r.getScreenCTM();if(u){let h=l.matrixTransform(u);return{x:h.x,y:h.y}}return{x:0,y:0}}async finishEditing(){let r=document.querySelector(".mindmap-node-input");if(r&&this.editingNode){this.editingNode.content=r.value;try{r.parentNode&&r.parentNode.removeChild(r)}catch(i){console.warn("Error removing input element:",i)}this.editingNode=null,this.renderMindMap().catch(console.error);try{await this.saveData(),console.log("Data saved successfully after editing")}catch(i){console.error("Error saving data after editing:",i)}}}cancelEditing(){let r=document.querySelector(".mindmap-node-input");if(r&&this.editingNode){try{r.parentNode&&r.parentNode.removeChild(r)}catch(i){console.warn("Error removing input element:",i)}this.editingNode=null}}async createNewMindMap(){this.rootNode={id:"root",content:"\u4E2D\u5FC3\u4E3B\u9898",children:[],isExpanded:!0};let r={id:"child1",content:"\u5B50\u8282\u70B91",children:[],parent:this.rootNode,isExpanded:!0},i={id:"child2",content:"\u5B50\u8282\u70B92",children:[],parent:this.rootNode,isExpanded:!0};this.rootNode.children.push(r,i);let l=this.app.workspace.getLeaf(!0);await l.setViewState({type:Mt,state:{data:this.rootNode}}),this.app.workspace.revealLeaf(l),this.selectNode(this.rootNode),this.saveData().catch(console.error)}async saveData(){if(console.log("saveData called, rootNode:",this.rootNode),!this.rootNode)return Promise.resolve();let r=this.app.workspace.getActiveViewOfType(Yt);if(console.log("Found MindMapView:",r),r){let i=r.leaf.getViewState().state,l=(i==null?void 0:i.sourceFile)||r.sourceFilePath;console.log("Current view state:",i),console.log("Source file path from state:",i==null?void 0:i.sourceFile),console.log("Source file path from view:",r.sourceFilePath),console.log("Final source file path:",l);let a=r.leaf.setViewState({type:Mt,state:{data:this.rootNode,sourceFile:l}});if(l){console.log("Attempting to sync to source file:",l);try{await this.syncToSourceFile(l)}catch(u){console.error("Error syncing to source file:",u),new Je.Notice("\u540C\u6B65\u5230\u6E90\u6587\u4EF6\u5931\u8D25: "+u.message)}}else console.log("No source file path found, skipping sync");return a}else console.log("No MindMapView found");return Promise.resolve()}async syncToSourceFile(r){if(this.rootNode)try{console.log("Starting sync to source file:",r),console.log("Root node data:",this.rootNode);let i=this.generateMarkdownFromMindMap(this.rootNode);console.log("Generated markdown content:",i);let l=this.app.vault.getAbstractFileByPath(r);if(l){let a=this.app.workspace.getActiveViewOfType(Yt),u=!1;a&&a.fileWatcher&&a.sourceFilePath===r&&(console.log("Temporarily disabling file watcher for sync"),this.app.vault.offref(a.fileWatcher),a.fileWatcher=null,u=!0),await this.app.vault.modify(l,i),console.log(`Successfully synced mind map to source file: ${r}`),a&&u&&setTimeout(()=>{console.log("Re-enabling file watcher after sync"),a.setupFileWatcher(r)},200)}else console.error("Source file not found:",r)}catch(i){throw console.error("Error syncing to source file:",i),i}}async loadData(r){r&&(this.rootNode=this.reconstructNode(r),this.currentNode=this.rootNode,await this.renderMindMap())}getRootNode(){return this.rootNode}updateMindMapData(r){console.log("Updating mind map data"),this.rootNode=this.reconstructNode(r),this.currentNode=this.rootNode,this.selectedNode=this.rootNode,console.log("Mind map data updated")}reconstructNode(r,i=void 0){let l={id:r.id,content:r.content,children:[],parent:i};for(let a of r.children)l.children.push(this.reconstructNode(a,l));return l}createSiblingNode(r){if(!r.parent)return;let i={id:Date.now().toString(),content:"\u65B0\u8282\u70B9",children:[],parent:r.parent,isExpanded:!0},l=r.parent.children.indexOf(r);r.parent.children.splice(l+1,0,i),this.renderMindMap().catch(console.error),this.selectNode(i),this.saveData().catch(console.error)}createParentNode(r){let i={id:Date.now().toString(),content:"\u65B0\u7236\u8282\u70B9",children:[r]};if(r===this.rootNode)this.rootNode=i;else if(r.parent){let l=r.parent.children,a=l.indexOf(r);l[a]=i,i.parent=r.parent}r.parent=i,this.currentNode=i,this.renderMindMap().catch(console.error),this.saveData().catch(console.error)}createChildNode(r){let i={id:Date.now().toString(),content:"\u65B0\u8282\u70B9",children:[],parent:r,isExpanded:!0};r.children.push(i),r.isExpanded=!0,this.renderMindMap().catch(console.error),this.selectNode(i),this.saveData().catch(console.error)}findNode(r,i){if(r.id===i)return r;for(let l of r.children){let a=this.findNode(l,i);if(a)return a}return null}async renderMindMap(){let r=this.app.workspace.getActiveViewOfType(Yt);if(!r||!this.rootNode)return;let i=r.containerEl.querySelector(".mindmap-container");if(!i)return;i.innerHTML="",await new Promise(b=>setTimeout(b,50));let l=i.getBoundingClientRect(),a=l.width,u=l.height,h=40;a=Math.max(a-h,600),u=Math.max(u-h,400),(!a||a<=0||isNaN(a))&&(a=800),(!u||u<=0||isNaN(u))&&(u=600),console.log("Container dimensions (after padding):",a,u);let p=nt(i).append("svg").attr("width","100%").attr("height","100%").attr("viewBox",`0 0 ${a} ${u}`).attr("preserveAspectRatio","xMidYMid meet").style("width","100%").style("height","100%").style("min-width",a+"px").style("min-height",u+"px");requestAnimationFrame(()=>{try{this.mindmap=oh.create(p.node(),{autoFit:!0,duration:300,maxWidth:Math.max(a-100,200),initialExpandLevel:3,spacingVertical:10,spacingHorizontal:80,paddingX:20});let b=this.mindmapNodeToMarkdown(this.rootNode),{root:y}=this.transformer.transform(b);console.log("Markmap data:",y),this.addCustomNodeIds(y,this.rootNode),this.mindmap.setData(y),this.setupEventListeners(i),this.selectedNode&&setTimeout(()=>this.highlightSelectedNode(),100)}catch(b){console.error("Error creating markmap:",b),this.createFallbackDisplay(i)}})}createFallbackDisplay(r){r.innerHTML="";let i=r.createDiv("mindmap-fallback");i.style.cssText=`
            padding: 20px;
            text-align: center;
            color: var(--text-muted);
            font-size: 14px;
        `,i.innerHTML=`
            <h3>\u601D\u7EF4\u5BFC\u56FE\u6E32\u67D3\u5931\u8D25</h3>
            <p>\u8BF7\u5C1D\u8BD5\u4EE5\u4E0B\u64CD\u4F5C\uFF1A</p>
            <ul style="text-align: left; display: inline-block;">
                <li>\u91CD\u65B0\u52A0\u8F7D\u63D2\u4EF6</li>
                <li>\u68C0\u67E5\u6D4F\u89C8\u5668\u63A7\u5236\u53F0\u7684\u9519\u8BEF\u4FE1\u606F</li>
                <li>\u4F7F\u7528\u8C03\u8BD5\u547D\u4EE4\u67E5\u770B\u8BE6\u7EC6\u4FE1\u606F</li>
            </ul>
            <button onclick="this.closest('.mindmap-container').dispatchEvent(new CustomEvent('retry-render'))">
                \u91CD\u8BD5\u6E32\u67D3
            </button>
        `,r.addEventListener("retry-render",()=>{setTimeout(()=>this.renderMindMap().catch(console.error),100)})}addCustomNodeIds(r,i){if(r.customId=i.id,r.children&&i.children)for(let l=0;l<r.children.length;l++)i.children[l]&&this.addCustomNodeIds(r.children[l],i.children[l])}setupEventListeners(r){let i=r.querySelector("svg");if(!i){console.log("No SVG found in container");return}console.log("Setting up event listeners on SVG:",i);let l=h=>{console.log("Click event captured on:",h.target),this.handleSVGClick(h)},a=h=>{console.log("Double click event captured on:",h.target),h.preventDefault(),h.stopPropagation(),this.handleSVGDoubleClick(h)};i.addEventListener("click",l,!0),i.addEventListener("dblclick",a,!0),r.addEventListener("click",l,!0),r.addEventListener("dblclick",a,!0);let u=h=>{h.target.closest(".mindmap-container")&&h.type==="dblclick"&&(console.log("Document level double click captured"),h.preventDefault(),h.stopPropagation(),this.handleSVGDoubleClick(h))};document.addEventListener("dblclick",u,!0),r._mindmapHandlers={clickHandler:l,dblClickHandler:a,documentHandler:u}}handleSVGClick(r){var u;console.log("SVG Click event triggered"),console.log("Event target:",r.target);let i=r.target;console.log("Target element:",i.tagName,i.className);let l=null,a=null;if(i.textContent&&i.textContent.trim()&&(l=i.textContent.trim(),a=i.closest("g[data-depth]")||i.closest("g")),!l){let h=((u=i.parentElement)==null?void 0:u.closest("g"))||null;if(a=i.closest("g[data-depth]")||i.closest("g")||h,a){let p=a.querySelector("text")||a.querySelector("div[xmlns]")||a.querySelector("div");p&&p.textContent&&(l=p.textContent.trim())}}if(console.log("Found node group:",a),console.log("Extracted content:",l),l&&this.rootNode){let h=this.findNodeByContent(this.rootNode,l);h?(console.log("Found matching node:",h),this.selectNode(h)):(console.log("Node not found for content:",l),console.log("Available nodes:",this.getAllNodes(this.rootNode)))}else console.log("No content found or no root node"),this.editingNode&&(console.log("Finishing editing"),this.finishEditing().catch(console.error))}handleSVGDoubleClick(r){console.log("SVG Double click event triggered"),r.preventDefault(),r.stopPropagation();let i=r.target;console.log("Double click target:",i);let l=null;if(i.textContent&&i.textContent.trim()&&(l=i.textContent.trim()),!l){let a=i.closest("g[data-depth]")||i.closest("g");if(a){let u=a.querySelector("text")||a.querySelector("div[xmlns]")||a.querySelector("div");u&&u.textContent&&(l=u.textContent.trim())}}if(console.log("Double click extracted content:",l),l&&this.rootNode){let a=this.findNodeByContent(this.rootNode,l);a?(console.log("Starting editing for double-clicked node:",a),this.startEditing(a).catch(console.error)):console.log("Node not found for double-click content:",l)}}findNodeByContent(r,i){if(r.content===i)return r;for(let l of r.children){let a=this.findNodeByContent(l,i);if(a)return a}return null}highlightSelectedNode(){var i;if(!this.selectedNode||!this.mindmap)return;let r=document.querySelector(".mindmap-container svg");if(r){let l=r.querySelectorAll(".selected-node");for(let u=0;u<l.length;u++)l[u].classList.remove("selected-node");let a=r.querySelectorAll("text");for(let u=0;u<a.length;u++){let h=a[u];if(((i=h.textContent)==null?void 0:i.trim())===this.selectedNode.content){let p=h.closest("g");p&&p.classList.add("selected-node")}}}}mindmapNodeToMarkdown(r,i=0){let a=`${"  ".repeat(i)}- ${r.content}
`;if(r.children)for(let u of r.children)a+=this.mindmapNodeToMarkdown(u,i+1);return a}async exportToMarkdown(){if(!this.rootNode){console.log("No mind map data to export");return}try{let r=this.generateMarkdownFromMindMap(this.rootNode),i=this.sanitizeFileName(this.rootNode.content)+".md";if(this.app.vault.getAbstractFileByPath(i)&&!await this.confirmOverwrite(i))return;await this.app.vault.create(i,r),console.log(`Mind map exported to: ${i}`);let a=this.app.vault.getAbstractFileByPath(i);a&&await this.app.workspace.getLeaf(!1).openFile(a)}catch(r){console.error("Error exporting to markdown:",r)}}generateMarkdownFromMindMap(r){let i=r.content,l=new Date().toISOString().split("T")[0],a=`# ${i}

`;if(a+=`> \u601D\u7EF4\u5BFC\u56FE\u5BFC\u51FA - ${l}

`,r.children&&r.children.length>0)for(let u of r.children)a+=this.nodeToMarkdownSection(u,2);else a+=`## ${i}

`,a+=`*\u6B64\u601D\u7EF4\u5BFC\u56FE\u6682\u65E0\u5B50\u8282\u70B9*

`;return a+=`---

`,a+=`*\u7531 Mind Map \u63D2\u4EF6\u751F\u6210*
`,a}nodeToMarkdownSection(r,i){let a=`${"#".repeat(i)} ${r.content}

`;if(r.children&&r.children.length>0)for(let u of r.children)i<6?a+=this.nodeToMarkdownSection(u,i+1):a+=this.nodeToMarkdownList(u,0);return a}nodeToMarkdownList(r,i){let a=`${"  ".repeat(i)}- ${r.content}
`;if(r.children&&r.children.length>0)for(let u of r.children)a+=this.nodeToMarkdownList(u,i+1);return a}sanitizeFileName(r){return r.replace(/[<>:"/\\|?*]/g,"-").replace(/\s+/g,"_").substring(0,100)}async confirmOverwrite(r){return new Promise(i=>{var a,u;let l=document.createElement("div");l.className="modal-container",l.innerHTML=`
                <div class="modal">
                    <div class="modal-title">\u6587\u4EF6\u5DF2\u5B58\u5728</div>
                    <div class="modal-content">
                        <p>\u6587\u4EF6 "${r}" \u5DF2\u5B58\u5728\u3002\u662F\u5426\u8981\u8986\u76D6\u5B83\uFF1F</p>
                    </div>
                    <div class="modal-button-container">
                        <button class="mod-cta" id="confirm-overwrite">\u8986\u76D6</button>
                        <button id="cancel-overwrite">\u53D6\u6D88</button>
                    </div>
                </div>
            `,document.body.appendChild(l),(a=l.querySelector("#confirm-overwrite"))==null||a.addEventListener("click",()=>{document.body.removeChild(l),i(!0)}),(u=l.querySelector("#cancel-overwrite"))==null||u.addEventListener("click",()=>{document.body.removeChild(l),i(!1)})})}async importFromMarkdown(){try{let r=this.app.workspace.getActiveFile();if(!r||!r.path.endsWith(".md")){console.log("Please open a markdown file to import"),new Je.Notice("\u8BF7\u5148\u6253\u5F00\u4E00\u4E2AMarkdown\u6587\u4EF6");return}console.log("\u6B63\u5728\u5BFC\u5165\u6587\u4EF6:",r.path);let i=await this.app.vault.read(r);console.log("\u8BFB\u53D6\u5230\u7684\u6587\u4EF6\u5185\u5BB9:",i);let l=this.parseMarkdownToMindMap(i,r.basename);if(console.log("\u89E3\u6790\u540E\u7684\u601D\u7EF4\u5BFC\u56FE\u6570\u636E:",l),l){this.rootNode=l,this.selectedNode=this.rootNode;let a=this.app.workspace.getLeaf(!0);await a.setViewState({type:Mt,state:{data:this.rootNode}}),this.app.workspace.revealLeaf(a),console.log(`Imported mind map from: ${r.path}`),new Je.Notice(`\u6210\u529F\u4ECE ${r.basename} \u5BFC\u5165\u601D\u7EF4\u5BFC\u56FE`)}else console.log("Failed to parse markdown content"),new Je.Notice("\u89E3\u6790Markdown\u5185\u5BB9\u5931\u8D25")}catch(r){console.error("Error importing from markdown:",r),new Je.Notice("\u5BFC\u5165\u5931\u8D25: "+r.message)}}parseMarkdownToMindMap(r,i){var l;try{console.log("\u5F00\u59CB\u89E3\u6790Markdown\u5185\u5BB9:",r);let u=r.replace(/^---[\s\S]*?---\n/,"").split(`
`);console.log("\u5206\u5272\u540E\u7684\u884C\u6570:",u.length);let h=null,p=[],b=1;for(let y=0;y<u.length;y++){let C=u[y].trim();if(console.log(`\u5904\u7406\u7B2C${y+1}\u884C: "${C}"`),!C||C.startsWith(">")||C.startsWith("---")||C.startsWith("```")||C.match(/^-\s*\*.*\*\s*$/)||C.match(/^\*.*\*$/)||C.match(/^\|.*\|$/)){console.log("\u8DF3\u8FC7\u7279\u6B8A\u884C");continue}let T=C.match(/^(#{1,6})\s+(.+)$/);if(T){let z=T[1].length,q=T[2];console.log(`\u53D1\u73B0\u6807\u9898: \u7EA7\u522B${z}, \u5185\u5BB9"${q}"`);let L={id:`node_${b++}`,content:q,children:[],isExpanded:!0};if(!h||z===1)if(h){if(z===1){L.parent=h,h.children.push(L),p.splice(1),p.push({node:L,level:z}),console.log("\u6DFB\u52A0\u4E00\u7EA7\u5B50\u8282\u70B9:",q);continue}}else{h=L,p.push({node:h,level:z}),console.log("\u8BBE\u7F6E\u6839\u8282\u70B9:",q);continue}for(;p.length>0&&p[p.length-1].level>=z;)p.pop();if(p.length===0)h?(L.parent=h,h.children.push(L),p.push({node:h,level:0}),p.push({node:L,level:z})):(h=L,p.push({node:h,level:z}));else{let X=p[p.length-1].node;L.parent=X,X.children.push(L),p.push({node:L,level:z})}console.log(`\u6DFB\u52A0\u8282\u70B9"${q}"\u5230\u7236\u8282\u70B9"${((l=L.parent)==null?void 0:l.content)||"root"}"`);continue}let F=C.match(/^(\s*)([-*+]|\d+\.)\s+(.+)$/);if(F){let z=F[1].length,q=F[3];if(q.includes("Mind Map \u63D2\u4EF6\u751F\u6210")){console.log("\u8DF3\u8FC7\u63D2\u4EF6\u6CE8\u91CA");continue}let L=Math.floor(z/2)+(h?2:1);console.log(`\u53D1\u73B0\u5217\u8868\u9879: \u7F29\u8FDB${z}, \u7EA7\u522B${L}, \u5185\u5BB9"${q}"`);let X={id:`node_${b++}`,content:q,children:[],isExpanded:!0};for(h||(h={id:"root",content:i||"\u5BFC\u5165\u7684\u601D\u7EF4\u5BFC\u56FE",children:[],isExpanded:!0},p.push({node:h,level:0}));p.length>0&&p[p.length-1].level>=L;)p.pop();let re=p.length>0?p[p.length-1].node:h;X.parent=re,re.children.push(X),p.push({node:X,level:L}),console.log(`\u6DFB\u52A0\u5217\u8868\u9879"${q}"\u5230\u7236\u8282\u70B9"${re.content}"`);continue}}if(!h){h={id:"root",content:i||"\u5BFC\u5165\u7684\u601D\u7EF4\u5BFC\u56FE",children:[],isExpanded:!0};let y={id:"default_child",content:"\u4ECEMarkdown\u5BFC\u5165\u7684\u5185\u5BB9",children:[],parent:h,isExpanded:!0};h.children.push(y)}return console.log("\u89E3\u6790\u5B8C\u6210\uFF0C\u6839\u8282\u70B9:",h),console.log("\u6839\u8282\u70B9\u5B50\u8282\u70B9\u6570\u91CF:",h.children.length),h}catch(a){return console.error("Error parsing markdown:",a),null}}async toggleMindMapMarkdown(){var l;let r=((l=this.app.workspace.getActiveViewOfType(Je.ItemView))==null?void 0:l.leaf)||this.app.workspace.getMostRecentLeaf();if(!r)return;let i=r.view;i.getViewType()===Mt?await this.closeMindMapView(r):i.getViewType()==="markdown"?await this.createMindMapPreview():await this.createNewMindMap()}async createMindMapPreview(){try{let r=this.app.workspace.getActiveFile();if(!r||r.extension!=="md"){new Je.Notice("\u8BF7\u5148\u6253\u5F00\u4E00\u4E2AMarkdown\u6587\u4EF6");return}let i=this.app.workspace.getLeavesOfType(Mt);if(i.length>0){let u=i[0],h=await this.app.vault.read(r),p=this.parseMarkdownToMindMap(h,r.basename);p&&(this.rootNode=p,this.selectedNode=this.rootNode,await u.setViewState({type:Mt,state:{data:this.rootNode,sourceFile:r.path}})),this.app.workspace.revealLeaf(u);return}let l=await this.app.vault.read(r),a=this.parseMarkdownToMindMap(l,r.basename);if(a){this.rootNode=a,this.selectedNode=this.rootNode;let u=this.app.workspace.getLeaf("split","vertical");await u.setViewState({type:Mt,state:{data:this.rootNode,sourceFile:r.path}}),this.app.workspace.revealLeaf(u),console.log(`Created mind map preview for: ${r.path}`),new Je.Notice(`\u4E3A ${r.basename} \u521B\u5EFA\u4E86\u601D\u7EF4\u5BFC\u56FE\u9884\u89C8`)}else console.log("Failed to parse markdown content"),new Je.Notice("\u89E3\u6790Markdown\u5185\u5BB9\u5931\u8D25")}catch(r){console.error("Error creating mind map preview:",r),new Je.Notice("\u521B\u5EFA\u601D\u7EF4\u5BFC\u56FE\u9884\u89C8\u5931\u8D25")}}async closeMindMapView(r){try{r.detach(),console.log("Closed mind map view")}catch(i){console.error("Error closing mind map view:",i)}}onunload(){this.mindmap=null}},Yt=class extends Je.ItemView{constructor(r,i){super(r);this.sourceFilePath=null;this.fileWatcher=null;this.plugin=i}getViewType(){return Mt}getDisplayText(){return"\u601D\u7EF4\u5BFC\u56FE"}getState(){return{type:Mt,data:this.plugin.getRootNode()}}async setState(r,i){let l=r.data,a=r.sourceFile;this.isMindMapNode(l)&&(await this.plugin.loadData(l),console.log("setState - sourceFile:",a,"current sourceFilePath:",this.sourceFilePath),a?a!==this.sourceFilePath?(console.log("Setting up file watcher because source file changed"),this.setupFileWatcher(a)):console.log("Source file unchanged, keeping existing watcher"):console.log("No source file provided in state"))}setupFileWatcher(r){console.log("Setting up file watcher for:",r),this.fileWatcher&&(console.log("Clearing previous file watcher"),this.app.vault.offref(this.fileWatcher)),this.sourceFilePath=r,this.fileWatcher=this.app.vault.on("modify",async i=>{console.log("File modified event:",i.path,"watching:",this.sourceFilePath),i.path===this.sourceFilePath&&(console.log("Source file modified, updating mind map:",i.path),await this.updateFromSourceFile(i))}),console.log("Mind map view linked to source file:",r),console.log("File watcher active:",!!this.fileWatcher)}async updateFromSourceFile(r){try{console.log("Reading source file content:",r.path);let i=await this.app.vault.read(r);console.log("File content length:",i.length);let l=this.plugin.parseMarkdownToMindMap(i,r.basename);console.log("Parsed mind map data:",l),l?(console.log("Updating mind map view with new data"),this.plugin.updateMindMapData(l),this.refreshView(),console.log("Mind map view updated from source file successfully")):console.log("Failed to parse mind map data from source file")}catch(i){console.error("Error updating mind map from source file:",i)}}isMindMapNode(r){if(!r||typeof r!="object")return!1;let i=r;return typeof i.id=="string"&&typeof i.content=="string"&&Array.isArray(i.children)&&i.children.every(l=>this.isMindMapNode(l))}async onOpen(){let r=this.containerEl.children[1];r.empty();let i=r.createDiv("mindmap-container");i.style.width="100%",i.style.height="100%",i.style.minHeight="400px",i.style.position="relative",i.style.overflow="hidden",console.log("MindMap view opened, container created"),setTimeout(async()=>{let l=this.getState();l.data&&this.isMindMapNode(l.data)?await this.plugin.loadData(l.data):this.plugin.getRootNode()?this.plugin.renderMindMap().catch(console.error):await this.plugin.createNewMindMap()},100)}async refreshView(){console.log("Refreshing mind map view");let r=this.containerEl.querySelector(".mindmap-container");r&&this.plugin.getRootNode()?(console.log("Container found, triggering render"),r.innerHTML="",await this.plugin.renderMindMap()):console.log("Container or root node not found for refresh")}async onClose(){this.fileWatcher&&(this.app.vault.offref(this.fileWatcher),this.fileWatcher=null),this.sourceFilePath=null}};
/*! Bundled license information:

markmap-common/dist/index.mjs:
  (*! @gera2ld/jsx-dom v2.2.2 | ISC License *)

js-yaml/dist/js-yaml.mjs:
  (*! js-yaml 4.1.0 https://github.com/nodeca/js-yaml @license MIT *)

markmap-view/dist/index.js:
  (*! @gera2ld/jsx-dom v2.2.2 | ISC License *)
*/
